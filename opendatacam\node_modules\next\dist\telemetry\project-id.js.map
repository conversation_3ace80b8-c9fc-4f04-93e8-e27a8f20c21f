{"version": 3, "sources": ["../../telemetry/project-id.ts"], "names": ["_getProjectIdByGit", "<PERSON><PERSON><PERSON><PERSON>", "timeout", "stdio", "String", "trim", "_", "getRawProjectId", "process", "env", "REPOSITORY_URL", "cwd"], "mappings": "6EAAA,4CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,QAASA,CAAAA,kBAAT,EAA8B,CAC5B,GAAI,CACF,KAAMC,CAAAA,YAAY,CAAG,4BAClB,4CADkB,CAEnB,CACEC,OAAO,CAAE,IADX,CAEEC,KAAK,CAAG,MAFV,CAFmB,CAArB,CAQA,MAAOC,CAAAA,MAAM,CAACH,YAAD,CAAN,CAAqBI,IAArB,EAAP,CACD,CAAC,MAAOC,CAAP,CAAU,CACV,MAAO,KAAP,CACD,CACF,CAEM,QAASC,CAAAA,eAAT,EAAmC,CACxC,MAAOP,CAAAA,kBAAkB,IAAMQ,OAAO,CAACC,GAAR,CAAYC,cAApC,EAAsDF,OAAO,CAACG,GAAR,EAA7D,CACD", "sourcesContent": ["import { execSync } from 'child_process'\n\n// Q: Why does Next.js need a project ID? Why is it looking at my git remote?\n// A:\n// Next.js' telemetry is and always will be completely anonymous. Because of\n// this, we need a way to differentiate different projects to track feature\n// usage accurately. For example, to prevent a feature from appearing to be\n// constantly `used` and then `unused` when switching between local projects.\n// To reiterate,\n// we **never** can read your actual git remote. The value is hashed one-way\n// with random salt data, making it impossible for us to reverse or try to\n// guess the remote by re-computing hashes.\n\nfunction _getProjectIdByGit() {\n  try {\n    const originBuffer = execSync(\n      `git config --local --get remote.origin.url`,\n      {\n        timeout: 1000,\n        stdio: `pipe`,\n      }\n    )\n\n    return String(originBuffer).trim()\n  } catch (_) {\n    return null\n  }\n}\n\nexport function getRawProjectId(): string {\n  return _getProjectIdByGit() || process.env.REPOSITORY_URL || process.cwd()\n}\n"]}