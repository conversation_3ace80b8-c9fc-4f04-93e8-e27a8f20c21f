{"version": 3, "sources": ["../../client/route-announcer.tsx"], "names": ["RouteAnnouncer", "<PERSON><PERSON><PERSON>", "routeAnnouncement", "setRouteAnnouncement", "initialPathLoaded", "current", "newRouteAnnouncement", "pageHeader", "document", "querySelector", "innerText", "textContent", "title", "border", "clip", "height", "margin", "overflow", "padding", "position", "width", "whiteSpace", "wordWrap"], "mappings": "uLAAA,qDACA,gCAEO,QAASA,CAAAA,cAAT,EAA0B,CAC/B,KAAM,CAAEC,MAAF,EAAa,uBAAnB,CACA,KAAM,CAACC,iBAAD,CAAoBC,oBAApB,EAA4C,oBAAS,EAAT,CAAlD,CAEA;AACA,KAAMC,CAAAA,iBAAiB,CAAG,kBAAO,KAAP,CAA1B,CAEA;AACA;AACA;AACA;AACA,qBACE,IAAM,CACJ,GAAI,CAACA,iBAAiB,CAACC,OAAvB,CAAgC,CAC9BD,iBAAiB,CAACC,OAAlB,CAA4B,IAA5B,CACA,OACD,CAED,GAAIC,CAAAA,oBAAJ,CACA,KAAMC,CAAAA,UAAU,CAAGC,QAAQ,CAACC,aAAT,CAAuB,IAAvB,CAAnB,CAEA,GAAIF,UAAJ,CAAgB,CACdD,oBAAoB,CAAGC,UAAU,CAACG,SAAX,EAAwBH,UAAU,CAACI,WAA1D,CACD,CACD,GAAI,CAACL,oBAAL,CAA2B,CACzB,GAAIE,QAAQ,CAACI,KAAb,CAAoB,CAClBN,oBAAoB,CAAGE,QAAQ,CAACI,KAAhC,CACD,CAFD,IAEO,CACLN,oBAAoB,CAAGL,MAAvB,CACD,CACF,CAEDE,oBAAoB,CAACG,oBAAD,CAApB,CACD,CAtBH,CAuBE;AACA,CAACL,MAAD,CAxBF,EA2BA,mBACE,kCACE,YAAU,WAAY;AADxB,CAEE,EAAE,CAAC,0BAFL,CAGE,IAAI,CAAC,OAHP,CAIE,KAAK,CAAE,CACLY,MAAM,CAAE,CADH,CAELC,IAAI,CAAE,eAFD,CAGLC,MAAM,CAAE,KAHH,CAILC,MAAM,CAAE,MAJH,CAKLC,QAAQ,CAAE,QALL,CAMLC,OAAO,CAAE,CANJ,CAOLC,QAAQ,CAAE,UAPL,CAQLC,KAAK,CAAE,KARF,CAUL;AACAC,UAAU,CAAE,QAXP,CAYLC,QAAQ,CAAE,QAZL,CAJT,EAmBGpB,iBAnBH,CADF,CAuBD,C,aAEcF,c", "sourcesContent": ["import React, { useEffect, useState, useRef } from 'react'\nimport { useRouter } from './router'\n\nexport function RouteAnnouncer() {\n  const { asPath } = useRouter()\n  const [routeAnnouncement, setRouteAnnouncement] = useState('')\n\n  // Only announce the path change, but not for the first load because screen reader will do that automatically.\n  const initialPathLoaded = useRef(false)\n\n  // Every time the path changes, announce the route change. The announcement will be prioritized by h1, then title\n  // (from metadata), and finally if those don't exist, then the pathName that is in the URL. This methodology is\n  // inspired by <PERSON><PERSON>'s accessible client routing user testing. More information can be found here:\n  // https://www.gatsbyjs.com/blog/2019-07-11-user-testing-accessible-client-routing/\n  useEffect(\n    () => {\n      if (!initialPathLoaded.current) {\n        initialPathLoaded.current = true\n        return\n      }\n\n      let newRouteAnnouncement\n      const pageHeader = document.querySelector('h1')\n\n      if (pageHeader) {\n        newRouteAnnouncement = pageHeader.innerText || pageHeader.textContent\n      }\n      if (!newRouteAnnouncement) {\n        if (document.title) {\n          newRouteAnnouncement = document.title\n        } else {\n          newRouteAnnouncement = asPath\n        }\n      }\n\n      setRouteAnnouncement(newRouteAnnouncement)\n    },\n    // TODO: switch to pathname + query object of dynamic route requirements\n    [asPath]\n  )\n\n  return (\n    <p\n      aria-live=\"assertive\" // Make the announcement immediately.\n      id=\"__next-route-announcer__\"\n      role=\"alert\"\n      style={{\n        border: 0,\n        clip: 'rect(0 0 0 0)',\n        height: '1px',\n        margin: '-1px',\n        overflow: 'hidden',\n        padding: 0,\n        position: 'absolute',\n        width: '1px',\n\n        // https://medium.com/@jessebeach/beware-smushed-off-screen-accessible-text-5952a4c2cbfe\n        whiteSpace: 'nowrap',\n        wordWrap: 'normal',\n      }}\n    >\n      {routeAnnouncement}\n    </p>\n  )\n}\n\nexport default RouteAnnouncer\n"]}