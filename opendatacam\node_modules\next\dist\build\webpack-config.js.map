{"version": 3, "sources": ["../../build/webpack-config.ts"], "names": ["devtoolRevertWarning", "devtool", "console", "warn", "chalk", "yellow", "bold", "parseJsonFile", "filePath", "JSON5", "require", "contents", "trim", "parse", "err", "codeFrame", "String", "start", "line", "lineNumber", "column", "columnNumber", "message", "highlightCode", "Error", "getOptimizedAliases", "isServer", "stubWindowFetch", "path", "join", "__dirname", "stubObjectAssign", "shim<PERSON><PERSON>", "Object", "assign", "unfetch$", "url", "resolve", "attachReactRefresh", "webpackConfig", "target<PERSON><PERSON><PERSON>", "injections", "reactRefreshLoaderName", "reactRefreshLoader", "module", "rules", "for<PERSON>ach", "rule", "curr", "use", "Array", "isArray", "some", "r", "idx", "findIndex", "splice", "Log", "info", "WEBPACK_RESOLVE_OPTIONS", "dependencyType", "NODE_RESOLVE_OPTIONS", "modules", "alias", "fallback", "exportsFields", "importsFields", "conditionNames", "descriptionFiles", "extensions", "enforceExtensions", "symlinks", "mainFields", "mainFiles", "roots", "fullySpecified", "preferRelative", "preferAbsolute", "restrictions", "getBaseWebpackConfig", "dir", "buildId", "config", "dev", "pagesDir", "target", "reactProductionProfiling", "entrypoints", "rewrites", "isDev<PERSON><PERSON><PERSON>", "hasRewrites", "beforeFiles", "length", "afterFiles", "reactVersion", "cwd", "name", "hasReactRefresh", "hasJsxRuntime", "Boolean", "semver", "gte", "babelConfigFile", "reduce", "memo", "filename", "config<PERSON><PERSON><PERSON><PERSON>", "undefined", "Promise", "distDir", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "experimental", "turboMode", "defaultLoaders", "babel", "loader", "options", "configFile", "cache", "isWebpack5", "development", "hotSelfAccept", "babelIncludeRegexes", "nodePathList", "process", "env", "NODE_PATH", "split", "platform", "filter", "p", "isServerless", "isServerlessTrace", "isLikeServerless", "outputDir", "SERVERLESS_DIRECTORY", "SERVER_DIRECTORY", "outputPath", "totalPages", "keys", "clientEntries", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_AMP", "NEXT_PROJECT_ROOT_DIST_CLIENT", "replace", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "relative", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS", "typeScriptPath", "paths", "_", "tsConfigPath", "useTypeScript", "jsConfig", "ts", "tsConfig", "compilerOptions", "jsConfigPath", "resolvedBaseUrl", "baseUrl", "getReactProfilingInProduction", "clientResolveRewrites", "clientResolveRewritesNoop", "resolveConfig", "next", "NEXT_PROJECT_ROOT", "PAGES_DIR_ALIAS", "DOT_NEXT_ALIAS", "assert", "buffer", "constants", "crypto", "domain", "http", "https", "os", "punycode", "querystring", "stream", "string_decoder", "sys", "timers", "tty", "util", "vm", "zlib", "plugins", "terserOptions", "ecma", "compress", "warnings", "comparisons", "inline", "mangle", "safari10", "output", "comments", "ascii_only", "isModuleCSS", "type", "splitChunksConfigs", "cacheGroups", "default", "vendors", "prodGranular", "chunks", "chunk", "test", "framework", "priority", "enforce", "lib", "size", "nameForCondition", "hash", "createHash", "updateHash", "libIdent", "update", "context", "digest", "substring", "minChunks", "reuseExistingChunk", "commons", "shared", "acc", "maxInitialRequests", "minSize", "splitChunksConfig", "crossOrigin", "customAppFile", "pageExtensions", "conformanceConfig", "ReactSyncScriptsConformanceCheck", "enabled", "MinificationConformanceCheck", "DuplicatePolyfillsConformanceCheck", "BlockedAPIToBePolyfilled", "conformance", "GranularChunksConformanceCheck", "handleExternals", "request", "getResolve", "isLocal", "startsWith", "posix", "isAbsolute", "win32", "notExternalModules", "res", "isNextExternal", "externalRequest", "baseRes", "baseResolve", "match", "emacsLockfilePattern", "externals", "callback", "resolveContext", "requestToResolve", "then", "result", "optimizeCss", "optimization", "emitOnErrors", "noEmitOnErrors", "checkWasmTypes", "nodeEnv", "splitChunks", "runtimeChunk", "CLIENT_STATIC_FILES_RUNTIME_WEBPACK", "minimize", "minimizer", "compiler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cacheDir", "parallel", "cpus", "apply", "CssMinimizerPlugin", "postcssOptions", "map", "annotation", "node", "setImmediate", "entry", "watchOptions", "aggregateTimeout", "ignored", "environment", "arrowFunction", "bigIntLiteral", "const", "destructuring", "dynamicImport", "forOf", "library", "libraryTarget", "hotUpdateChunkFilename", "hotUpdateMainFilename", "chunkFilename", "strictModuleExceptionHandling", "crossOriginLoading", "futureEmitAssets", "webassemblyModuleFilename", "performance", "<PERSON><PERSON><PERSON><PERSON>", "externalDir", "include", "exclude", "excludePath", "ReactRefreshWebpackPlugin", "webpack", "ProvidePlugin", "<PERSON><PERSON><PERSON>", "ChunkNamesPlugin", "DefinePlugin", "prev", "key", "JSON", "stringify", "__NEXT_TEST_MODE", "trailingSlash", "devIndicators", "buildActivity", "reactStrictMode", "reactRoot", "optimizeFonts", "optimizeImages", "<PERSON><PERSON><PERSON><PERSON>", "scrollRestoration", "deviceSizes", "images", "imageSizes", "domains", "enableBlurryPlaceholder", "basePath", "i18n", "analyticsId", "pageEnv", "ReactLoadablePlugin", "REACT_LOADABLE_MANIFEST", "DropClientPage", "future", "excludeDefaultMomentLocales", "IgnorePlugin", "resourceRegExp", "contextRegExp", "NextJsRequireCacheHotReloader", "devP<PERSON><PERSON>", "push", "HotModuleReplacementPlugin", "HashedModuleIdsPlugin", "ServerlessPlugin", "PagesManifestPlugin", "serverless", "NextJsSSRModuleCachePlugin", "NextJsSsrImportPlugin", "BuildManifestPlugin", "stats", "BuildStatsPlugin", "Profiling<PERSON><PERSON><PERSON>", "FontStylesheetGatheringPlugin", "WebpackConformancePlugin", "tests", "AllowedSources", "allowedSources", "WellKnownErrorsPlugin", "unshift", "JsConfigPathsPlugin", "dependencies", "snapshot", "versions", "pnp", "exec", "managedPaths", "immutablePaths", "providedExports", "usedExports", "configVars", "reactMode", "assetPrefix", "disableOptimizedLoading", "version", "__NEXT_VERSION", "cacheDirectory", "buildDependencies", "NEXT_WEBPACK_LOGGING", "logInfra", "includes", "logProfileClient", "logProfileServer", "logDefault", "infrastructureLogging", "level", "debug", "hooks", "done", "tap", "log", "toString", "colors", "logging", "ProgressPlugin", "profile", "rootDirectory", "isDevelopment", "sassOptions", "productionBrowserSourceMaps", "originalDevtool", "webpackDevMiddleware", "canMatchCss", "fileNames", "RegExp", "input", "hasUserCssConfig", "oneOf", "__next_css_remove", "e", "strictPostcssConfiguration", "foundTsRule", "call", "isSass", "source", "isLess", "isCss", "isStylus", "prototype", "hasOwnProperty", "correctNextCss", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "originalEntry", "updatedEntry", "originalFile", "dependOn", "old", "import"], "mappings": "wHAAA,sHACA,oDACA,sDACA,sBACA,8DACA,yEACA,2DACA,mDACA,2CAMA,8CACA,6DAEA,wFACA,wDAUA,+CAEA,0DAEA,yDACA,wCACA,8FACA,oGACA,8FACA,8FACA,4EACA,wFACA,4FACA,uGACA,oGACA,mEACA,4EACA,qEACA,+GAMA,gF,w4BAIA,KAAMA,CAAAA,oBAAoB,CAAG,oBAC1BC,OAAD,EAA+C,CAC7CC,OAAO,CAACC,IAAR,CACEC,eAAMC,MAAN,CAAaC,IAAb,CAAkB,WAAlB,EACEF,eAAME,IAAN,CAAY,iCAAgCL,OAAQ,MAApD,CADF,CAEE,+FAFF,CAGE,8DAJJ,EAMD,CAR0B,CAA7B,CAWA,QAASM,CAAAA,aAAT,CAAuBC,QAAvB,CAAyC,CACvC,KAAMC,CAAAA,KAAK,CAAGC,OAAO,CAAC,0BAAD,CAArB,CACA,KAAMC,CAAAA,QAAQ,CAAG,qBAAaH,QAAb,CAAuB,MAAvB,CAAjB,CAEA;AACA,GAAIG,QAAQ,CAACC,IAAT,KAAoB,EAAxB,CAA4B,CAC1B,MAAO,EAAP,CACD,CAED,GAAI,CACF,MAAOH,CAAAA,KAAK,CAACI,KAAN,CAAYF,QAAZ,CAAP,CACD,CAAC,MAAOG,GAAP,CAAY,CACZ,KAAMC,CAAAA,SAAS,CAAG,gCAChBC,MAAM,CAACL,QAAD,CADU,CAEhB,CAAEM,KAAK,CAAE,CAAEC,IAAI,CAAEJ,GAAG,CAACK,UAAZ,CAAwBC,MAAM,CAAEN,GAAG,CAACO,YAApC,CAAT,CAFgB,CAGhB,CAAEC,OAAO,CAAER,GAAG,CAACQ,OAAf,CAAwBC,aAAa,CAAE,IAAvC,CAHgB,CAAlB,CAKA,KAAM,IAAIC,CAAAA,KAAJ,CAAW,oBAAmBhB,QAAS,OAAMO,SAAU,EAAvD,CAAN,CACD,CACF,CAED,QAASU,CAAAA,mBAAT,CAA6BC,QAA7B,CAA2E,CACzE,GAAIA,QAAJ,CAAc,CACZ,MAAO,EAAP,CACD,CAED,KAAMC,CAAAA,eAAe,CAAGC,cAAKC,IAAL,CAAUC,SAAV,CAAqB,WAArB,CAAkC,OAAlC,CAA2C,UAA3C,CAAxB,CACA,KAAMC,CAAAA,gBAAgB,CAAGH,cAAKC,IAAL,CAAUC,SAAV,CAAqB,WAArB,CAAkC,kBAAlC,CAAzB,CAEA,KAAME,CAAAA,UAAU,CAAGJ,cAAKC,IAAL,CAAUC,SAAV,CAAqB,WAArB,CAAkC,eAAlC,CAAnB,CACA,MAAOG,CAAAA,MAAM,CAACC,MAAP,CACL,EADK,CAEL,CACEC,QAAQ,CAAER,eADZ,CAEE,sBAAuBA,eAFzB,CAGE,gBAAiBC,cAAKC,IAAL,CACfC,SADe,CAEf,WAFe,CAGf,OAHe,CAIf,iBAJe,CAHnB,CAFK,CAYL,CACE,iBAAkBC,gBADpB,CAGE;AACA,qBAAsBH,cAAKC,IAAL,CAAUG,UAAV,CAAsB,SAAtB,CAJxB,CAKE,+BAAgCJ,cAAKC,IAAL,CAC9BG,UAD8B,CAE9B,mBAF8B,CALlC,CASE,iBAAkBJ,cAAKC,IAAL,CAAUG,UAAV,CAAsB,UAAtB,CATpB,CAUE,yBAA0BJ,cAAKC,IAAL,CAAUG,UAAV,CAAsB,aAAtB,CAV5B,CAWE,qBAAsBJ,cAAKC,IAAL,CAAUG,UAAV,CAAsB,SAAtB,CAXxB,CAaE;AACAI,GAAG,CAAE1B,OAAO,CAAC2B,OAAR,CAAgB,YAAhB,CAdP,CAZK,CAAP,CA6BD,CAMM,QAASC,CAAAA,kBAAT,CACLC,aADK,CAELC,YAFK,CAGL,2BACA,GAAIC,CAAAA,UAAU,CAAG,CAAjB,CACA,KAAMC,CAAAA,sBAAsB,CAAG,kCAA/B,CACA,KAAMC,CAAAA,kBAAkB,CAAGjC,OAAO,CAAC2B,OAAR,CAAgBK,sBAAhB,CAA3B,CACA,uBAAAH,aAAa,CAACK,MAAd,qCAAsBC,KAAtB,CAA4BC,OAA5B,CAAqCC,IAAD,EAAU,CAC5C,KAAMC,CAAAA,IAAI,CAAGD,IAAI,CAACE,GAAlB,CACA;AACA,GAAID,IAAI,GAAKR,YAAb,CAA2B,CACzB,EAAEC,UAAF,CACAM,IAAI,CAACE,GAAL,CAAW,CAACN,kBAAD,CAAqBK,IAArB,CAAX,CACD,CAHD,IAGO,IACLE,KAAK,CAACC,OAAN,CAAcH,IAAd,GACAA,IAAI,CAACI,IAAL,CAAWC,CAAD,EAAOA,CAAC,GAAKb,YAAvB,CADA,EAEA;AACA,CAACQ,IAAI,CAACI,IAAL,CACEC,CAAD,EAAOA,CAAC,GAAKV,kBAAN,EAA4BU,CAAC,GAAKX,sBAD1C,CAJI,CAOL,CACA,EAAED,UAAF,CACA,KAAMa,CAAAA,GAAG,CAAGN,IAAI,CAACO,SAAL,CAAgBF,CAAD,EAAOA,CAAC,GAAKb,YAA5B,CAAZ,CACA;AACAO,IAAI,CAACE,GAAL,CAAW,CAAC,GAAGD,IAAJ,CAAX,CAEA;AACAD,IAAI,CAACE,GAAL,CAASO,MAAT,CAAgBF,GAAhB,CAAqB,CAArB,CAAwBX,kBAAxB,EACD,CACF,CAtBD,EAwBA,GAAIF,UAAJ,CAAgB,CACdgB,GAAG,CAACC,IAAJ,CACG,0CAAyCjB,UAAW,iBACnDA,UAAU,CAAG,CAAb,CAAiB,GAAjB,CAAuB,EACxB,EAHH,EAKD,CACF,CAED,KAAMkB,CAAAA,uBAAuB,CAAG,CAC9B;AACA;AACA;AACA;AACAC,cAAc,CAAE,UALc,CAAhC,CAQA,KAAMC,CAAAA,oBAAoB,CAAG,CAC3BD,cAAc,CAAE,UADW,CAE3BE,OAAO,CAAE,CAAC,cAAD,CAFkB,CAG3BC,KAAK,CAAE,KAHoB,CAI3BC,QAAQ,CAAE,KAJiB,CAK3BC,aAAa,CAAE,CAAC,SAAD,CALY,CAM3BC,aAAa,CAAE,CAAC,SAAD,CANY,CAO3BC,cAAc,CAAE,CAAC,MAAD,CAAS,SAAT,CAAoB,QAApB,CAPW,CAQ3BC,gBAAgB,CAAE,CAAC,cAAD,CARS,CAS3BC,UAAU,CAAE,CAAC,KAAD,CAAQ,OAAR,CAAiB,OAAjB,CATe,CAU3BC,iBAAiB,CAAE,KAVQ,CAW3BC,QAAQ,CAAE,IAXiB,CAY3BC,UAAU,CAAE,CAAC,MAAD,CAZe,CAa3BC,SAAS,CAAE,CAAC,OAAD,CAbgB,CAc3BC,KAAK,CAAE,EAdoB,CAe3BC,cAAc,CAAE,KAfW,CAgB3BC,cAAc,CAAE,KAhBW,CAiB3BC,cAAc,CAAE,KAjBW,CAkB3BC,YAAY,CAAE,EAlBa,CAA7B,CAqBe,cAAeC,CAAAA,oBAAf,CACbC,GADa,CAEb,CACEC,OADF,CAEEC,MAFF,CAGEC,GAAG,CAAG,KAHR,CAIEzD,QAAQ,CAAG,KAJb,CAKE0D,QALF,CAMEC,MAAM,CAAG,QANX,CAOEC,wBAAwB,CAAG,KAP7B,CAQEC,WARF,CASEC,QATF,CAUEC,aAAa,CAAG,KAVlB,CAFa,CAyBmB,2KAChC,KAAMC,CAAAA,WAAW,CACfF,QAAQ,CAACG,WAAT,CAAqBC,MAArB,CAA8B,CAA9B,EACAJ,QAAQ,CAACK,UAAT,CAAoBD,MAApB,CAA6B,CAD7B,EAEAJ,QAAQ,CAACxB,QAAT,CAAkB4B,MAAlB,CAA2B,CAH7B,CAKA,KAAME,CAAAA,YAAY,CAAG,KAAM,yCAAkB,CAAEC,GAAG,CAAEf,GAAP,CAAYgB,IAAI,CAAE,OAAlB,CAAlB,CAA3B,CACA,KAAMC,CAAAA,eAAwB,CAAGd,GAAG,EAAI,CAACzD,QAAzC,CACA,KAAMwE,CAAAA,aAAsB,CAC1BC,OAAO,CAACL,YAAD,CAAP,EACA;AACA;AACAM,gBAAOC,GAAP,CAAWP,YAAX,CAA0B,aAA1B,CAJF,CAMA,KAAMQ,CAAAA,eAAe,CAAG,KAAM,CAC5B,UAD4B,CAE5B,eAF4B,CAG5B,aAH4B,CAI5B,cAJ4B,CAK5B,cAL4B,CAM5B,iBAN4B,CAO5B,mBAP4B,CAQ5B,kBAR4B,CAS5B,kBAT4B,EAU5BC,MAV4B,CAUrB,MAAOC,IAAP,CAA0CC,QAA1C,GAAuD,CAC9D,KAAMC,CAAAA,cAAc,CAAG9E,cAAKC,IAAL,CAAUmD,GAAV,CAAeyB,QAAf,CAAvB,CACA,MACE,CAAC,KAAMD,CAAAA,IAAP,IACC,CAAC,KAAM,2BAAWE,cAAX,CAAP,EAAqCA,cAArC,CAAsDC,SADvD,CADF,CAID,CAhB6B,CAgB3BC,OAAO,CAACvE,OAAR,CAAgBsE,SAAhB,CAhB2B,CAA9B,CAkBA,KAAME,CAAAA,OAAO,CAAGjF,cAAKC,IAAL,CAAUmD,GAAV,CAAeE,MAAM,CAAC2B,OAAtB,CAAhB,CAEA,KAAMC,CAAAA,WAAW,CAAG5B,MAAM,CAAC6B,YAAP,CAAoBC,SAApB,CAChBtG,OAAO,CAAC2B,OAAR,CAAgB,sBAAhB,CADgB,CAEhB,mBAFJ,CAGA,KAAM4E,CAAAA,cAAc,CAAG,CACrBC,KAAK,CAAE,CACLC,MAAM,CAAEL,WADH,CAELM,OAAO,CAAE,CACPC,UAAU,CAAEf,eADL,CAEP5E,QAFO,CAGPmF,OAHO,CAIPzB,QAJO,CAKPW,GAAG,CAAEf,GALE,CAMP;AACAsC,KAAK,CAAE,CAACC,mBAPD,CAQPC,WAAW,CAAErC,GARN,CASPc,eATO,CAUPC,aAVO,CAFJ,CADc,CAgBrB;AACAuB,aAAa,CAAE,CACbN,MAAM,CAAE,aADK,CAjBM,CAAvB,CAsBA,KAAMO,CAAAA,mBAA6B,CAAG,CACpC,uCADoC,CAEpC,0BAFoC,CAGpC,yBAHoC,CAIpC,mCAJoC,CAAtC,CAOA;AACA,KAAMC,CAAAA,YAAY,CAAG,CAACC,OAAO,CAACC,GAAR,CAAYC,SAAZ,EAAyB,EAA1B,EAClBC,KADkB,CACZH,OAAO,CAACI,QAAR,GAAqB,OAArB,CAA+B,GAA/B,CAAqC,GADzB,EAElBC,MAFkB,CAEVC,CAAD,EAAO,CAAC,CAACA,CAFE,CAArB,CAIA,KAAMC,CAAAA,YAAY,CAAG9C,MAAM,GAAK,YAAhC,CACA,KAAM+C,CAAAA,iBAAiB,CAAG/C,MAAM,GAAK,+BAArC,CACA;AACA,KAAMgD,CAAAA,gBAAgB,CAAGF,YAAY,EAAIC,iBAAzC,CAEA,KAAME,CAAAA,SAAS,CAAGD,gBAAgB,CAAGE,gCAAH,CAA0BC,4BAA5D,CACA,KAAMC,CAAAA,UAAU,CAAG7G,cAAKC,IAAL,CAAUgF,OAAV,CAAmBnF,QAAQ,CAAG4G,SAAH,CAAe,EAA1C,CAAnB,CACA,KAAMI,CAAAA,UAAU,CAAGzG,MAAM,CAAC0G,IAAP,CAAYpD,WAAZ,EAAyBK,MAA5C,CACA,KAAMgD,CAAAA,aAAa,CAAG,CAAClH,QAAD,CACjB,CACC;AACA,UAAW,EAFZ,CAGC,IAAIyD,GAAG,CACH,CACE,CAAC0D,qDAAD,EAA6CnI,OAAO,CAAC2B,OAAR,CAC1C,mCAD0C,CAD/C,CAIE,CAACyG,2CAAD,EACG,IAAD,CACA,mBACE9D,GADF,CAEE,eAAS+D,wCAAT,CAAwC,KAAxC,CAA+C,SAA/C,CAFF,EAGEC,OAHF,CAGU,KAHV,CAGiB,GAHjB,CANJ,CADG,CAYH,EAZJ,CAHD,CAgBC,CAACC,4CAAD,EACG,IAAD,CACArH,cACGsH,QADH,CAEIlE,GAFJ,CAGIpD,cAAKC,IAAL,CACEkH,wCADF,CAEE5D,GAAG,CAAI,aAAJ,CAAmB,SAFxB,CAHJ,EAQG6D,OARH,CAQW,KARX,CAQkB,GARlB,CAlBH,CA2BC,CAACG,iDAAD,EAAyCvH,cAAKC,IAAL,CACvCkH,wCADuC,CAEvC,cAFuC,CA3B1C,CADiB,CAiClBpC,SAjCJ,CAmCA,GAAIyC,CAAAA,cAAJ,CACA,GAAI,CACFA,cAAc,CAAG1I,OAAO,CAAC2B,OAAR,CAAgB,YAAhB,CAA8B,CAAEgH,KAAK,CAAE,CAACrE,GAAD,CAAT,CAA9B,CAAjB,CACD,CAAC,MAAOsE,CAAP,CAAU,CAAE,CACd,KAAMC,CAAAA,YAAY,CAAG3H,cAAKC,IAAL,CAAUmD,GAAV,CAAe,eAAf,CAArB,CACA,KAAMwE,CAAAA,aAAa,CAAGrD,OAAO,CAC3BiD,cAAc,GAAK,KAAM,2BAAWG,YAAX,CAAX,CADa,CAA7B,CAIA,GAAIE,CAAAA,QAAJ,CACA;AACA,GAAID,aAAJ,CAAmB,CACjB,KAAME,CAAAA,EAAE,CAAI,yBAAaN,cAAb,gDAAZ,CACA,KAAMO,CAAAA,QAAQ,CAAG,KAAM,2DAA2BD,EAA3B,CAA+BH,YAA/B,CAA6C,IAA7C,CAAvB,CACAE,QAAQ,CAAG,CAAEG,eAAe,CAAED,QAAQ,CAACvC,OAA5B,CAAX,CACD,CAED,KAAMyC,CAAAA,YAAY,CAAGjI,cAAKC,IAAL,CAAUmD,GAAV,CAAe,eAAf,CAArB,CACA,GAAI,CAACwE,aAAD,GAAmB,KAAM,2BAAWK,YAAX,CAAzB,CAAJ,CAAwD,CACtDJ,QAAQ,CAAGlJ,aAAa,CAACsJ,YAAD,CAAxB,CACD,CAED,GAAIC,CAAAA,eAAJ,CACA,cAAIL,QAAJ,gCAAI,UAAUG,eAAd,SAAI,sBAA2BG,OAA/B,CAAwC,CACtCD,eAAe,CAAGlI,cAAKS,OAAL,CAAa2C,GAAb,CAAkByE,QAAQ,CAACG,eAAT,CAAyBG,OAA3C,CAAlB,CACD,CAED,QAASC,CAAAA,6BAAT,EAAyC,CACvC,GAAI1E,wBAAJ,CAA8B,CAC5B,MAAO,CACL,aAAc,qBADT,CAEL,oBAAqB,6BAFhB,CAAP,CAID,CACF,CAED,KAAM2E,CAAAA,qBAAqB,CAAGvJ,OAAO,CAAC2B,OAAR,CAC5B,kDAD4B,CAA9B,CAGA,KAAM6H,CAAAA,yBAAyB,CAAGxJ,OAAO,CAAC2B,OAAR,CAChC,uDADgC,CAAlC,CAIA,KAAM8H,CAAAA,aAAa,CAAG,CACpB;AACA9F,UAAU,CAAE3C,QAAQ,CAChB,CACE,KADF,CAEE,MAFF,CAGE,IAAI8H,aAAa,CAAG,CAAC,MAAD,CAAS,KAAT,CAAH,CAAqB,EAAtC,CAHF,CAIE,MAJF,CAKE,OALF,CAME,OANF,CADgB,CAShB,CACE,MADF,CAEE,KAFF,CAGE,IAAIA,aAAa,CAAG,CAAC,MAAD,CAAS,KAAT,CAAH,CAAqB,EAAtC,CAHF,CAIE,MAJF,CAKE,OALF,CAME,OANF,CAXgB,CAmBpB1F,OAAO,CAAE,CACP,cADO,CAEP,GAAG6D,YAAc;AAFV,CAnBW,CAuBpB5D,KAAK,CAAE,CACLqG,IAAI,CAAEC,4BADD,CAEL,CAACC,0BAAD,EAAmBlF,QAFd,CAGL,CAACmF,yBAAD,EAAkB1D,OAHb,CAIL,GAAGpF,mBAAmB,CAACC,QAAD,CAJjB,CAKL,GAAGsI,6BAA6B,EAL3B,CAML,CAACC,qBAAD,EAAyBvE,WAAW,CAChCuE,qBADgC,CAEhC;AACF1C,oBACE,KADF,CAEE2C,yBAXC,CAvBa,CAoCpB,IAAI3C,qBAAc,CAAC7F,QAAf,CACA,CACE;AACA;AACAsC,QAAQ,CAAE,CACRwG,MAAM,CAAE9J,OAAO,CAAC2B,OAAR,CAAgB,SAAhB,CADA,CAERoI,MAAM,CAAE/J,OAAO,CAAC2B,OAAR,CAAgB,SAAhB,CAFA,CAGRqI,SAAS,CAAEhK,OAAO,CAAC2B,OAAR,CAAgB,sBAAhB,CAHH,CAIRsI,MAAM,CAAEjK,OAAO,CAAC2B,OAAR,CAAgB,mBAAhB,CAJA,CAKRuI,MAAM,CAAElK,OAAO,CAAC2B,OAAR,CAAgB,gBAAhB,CALA,CAMRwI,IAAI,CAAEnK,OAAO,CAAC2B,OAAR,CAAgB,aAAhB,CANE,CAORyI,KAAK,CAAEpK,OAAO,CAAC2B,OAAR,CAAgB,kBAAhB,CAPC,CAQR0I,EAAE,CAAErK,OAAO,CAAC2B,OAAR,CAAgB,uBAAhB,CARI,CASRT,IAAI,CAAElB,OAAO,CAAC2B,OAAR,CAAgB,iBAAhB,CATE,CAUR2I,QAAQ,CAAEtK,OAAO,CAAC2B,OAAR,CAAgB,UAAhB,CAVF,CAWRuF,OAAO,CAAElH,OAAO,CAAC2B,OAAR,CAAgB,iBAAhB,CAXD,CAYR;AACA4I,WAAW,CAAEvK,OAAO,CAAC2B,OAAR,CAAgB,iBAAhB,CAbL,CAcR6I,MAAM,CAAExK,OAAO,CAAC2B,OAAR,CAAgB,mBAAhB,CAdA,CAeR8I,cAAc,CAAEzK,OAAO,CAAC2B,OAAR,CAAgB,gBAAhB,CAfR,CAgBR+I,GAAG,CAAE1K,OAAO,CAAC2B,OAAR,CAAgB,OAAhB,CAhBG,CAiBRgJ,MAAM,CAAE3K,OAAO,CAAC2B,OAAR,CAAgB,mBAAhB,CAjBA,CAkBRiJ,GAAG,CAAE5K,OAAO,CAAC2B,OAAR,CAAgB,gBAAhB,CAlBG,CAmBR;AACA;AACAkJ,IAAI,CAAE7K,OAAO,CAAC2B,OAAR,CAAgB,OAAhB,CArBE,CAsBRmJ,EAAE,CAAE9K,OAAO,CAAC2B,OAAR,CAAgB,eAAhB,CAtBI,CAuBRoJ,IAAI,CAAE/K,OAAO,CAAC2B,OAAR,CAAgB,iBAAhB,CAvBE,CAHZ,CADA,CA8BAsE,SA9BJ,CApCoB,CAmEpBnC,UAAU,CAAE9C,QAAQ,CAAG,CAAC,MAAD,CAAS,QAAT,CAAH,CAAwB,CAAC,SAAD,CAAY,QAAZ,CAAsB,MAAtB,CAnExB,CAoEpBgK,OAAO,CAAEnE,oBACL;AACA,EAFK,CAGL,CAAC7G,OAAO,CAAC,oBAAD,CAAR,CAvEgB,CAAtB,CA0EA,KAAMiL,CAAAA,aAAkB,CAAG,CACzB9K,KAAK,CAAE,CACL+K,IAAI,CAAE,CADD,CADkB,CAIzBC,QAAQ,CAAE,CACRD,IAAI,CAAE,CADE,CAERE,QAAQ,CAAE,KAFF,CAGR;AACAC,WAAW,CAAE,KAJL,CAKRC,MAAM,CAAE,CAAG;AALH,CAJe,CAWzBC,MAAM,CAAE,CAAEC,QAAQ,CAAE,IAAZ,CAXiB,CAYzBC,MAAM,CAAE,CACNP,IAAI,CAAE,CADA,CAENM,QAAQ,CAAE,IAFJ,CAGNE,QAAQ,CAAE,KAHJ,CAIN;AACAC,UAAU,CAAE,IALN,CAZiB,CAA3B,CAqBA,KAAMC,CAAAA,WAAW,CAAI1J,MAAD,EAAuC,CACzD,MACE;AACAA,MAAM,CAAC2J,IAAP,GAAiB,kBAAjB,EACA;AACA3J,MAAM,CAAC2J,IAAP,GAAiB,oBAFjB,EAGA;AACA3J,MAAM,CAAC2J,IAAP,GAAiB,wBANnB,EAQD,CATD,CAWA;AACA,KAAMC,CAAAA,kBAEL,CAAG,CACFrH,GAAG,CAAE,CACHsH,WAAW,CAAE,CACXC,OAAO,CAAE,KADE,CAEXC,OAAO,CAAE,KAFE,CADV,CADH,CAOFC,YAAY,CAAE,CACZ;AACA;AACA;AACA;AACAC,MAAM,CAAEtF,oBACHuF,KAAD,EAAW,CAAC,iCAAiCC,IAAjC,CAAsCD,KAAK,CAAC9G,IAA5C,CADR,CAEJ,KAPQ,CAQZyG,WAAW,CAAE,CACXO,SAAS,CAAE,CACTH,MAAM,CAAE,KADC,CAET7G,IAAI,CAAE,WAFG,CAGT;AACA;AACA;AACA+G,IAAI,CAAE,uGANG,CAOTE,QAAQ,CAAE,EAPD,CAQT;AACA;AACAC,OAAO,CAAE,IAVA,CADA,CAaXC,GAAG,CAAE,CACHJ,IAAI,CAACnK,MAAD,CAGQ,CACV,MACEA,CAAAA,MAAM,CAACwK,IAAP,GAAgB,MAAhB,EACA,oBAAoBL,IAApB,CAAyBnK,MAAM,CAACyK,gBAAP,IAA6B,EAAtD,CAFF,CAID,CATE,CAUHrH,IAAI,CAACpD,MAAD,CAIO,CACT,KAAM0K,CAAAA,IAAI,CAAG3C,gBAAO4C,UAAP,CAAkB,MAAlB,CAAb,CACA,GAAIjB,WAAW,CAAC1J,MAAD,CAAf,CAAyB,CACvBA,MAAM,CAAC4K,UAAP,CAAkBF,IAAlB,EACD,CAFD,IAEO,CACL,GAAI,CAAC1K,MAAM,CAAC6K,QAAZ,CAAsB,CACpB,KAAM,IAAIjM,CAAAA,KAAJ,CACH,oCAAmCoB,MAAM,CAAC2J,IAAK,yBAD5C,CAAN,CAGD,CAEDe,IAAI,CAACI,MAAL,CAAY9K,MAAM,CAAC6K,QAAP,CAAgB,CAAEE,OAAO,CAAE3I,GAAX,CAAhB,CAAZ,EACD,CAED,MAAOsI,CAAAA,IAAI,CAACM,MAAL,CAAY,KAAZ,EAAmBC,SAAnB,CAA6B,CAA7B,CAAgC,CAAhC,CAAP,CACD,CA7BE,CA8BHZ,QAAQ,CAAE,EA9BP,CA+BHa,SAAS,CAAE,CA/BR,CAgCHC,kBAAkB,CAAE,IAhCjB,CAbM,CA+CXC,OAAO,CAAE,CACPhI,IAAI,CAAE,SADC,CAEP8H,SAAS,CAAEpF,UAFJ,CAGPuE,QAAQ,CAAE,EAHH,CA/CE,CAoDX,IAAI1F,oBACAZ,SADA,CAEA,CACE+F,OAAO,CAAE,KADX,CAEEC,OAAO,CAAE,KAFX,CAGEsB,MAAM,CAAE,CACNjI,IAAI,CAACpD,MAAD,CAASiK,MAAT,CAAiB,CACnB,MACElC,iBACG4C,UADH,CACc,MADd,EAEGG,MAFH,CAGIb,MAAM,CAACtG,MAAP,CACE,CAAC2H,GAAD,CAAcpB,KAAd,GAAmD,CACjD,MAAOoB,CAAAA,GAAG,CAAGpB,KAAK,CAAC9G,IAAnB,CACD,CAHH,CAIE,EAJF,CAHJ,EAUG4H,MAVH,CAUU,KAVV,GAUoBtB,WAAW,CAAC1J,MAAD,CAAX,CAAsB,MAAtB,CAA+B,EAVnD,CADF,CAaD,CAfK,CAgBNqK,QAAQ,CAAE,EAhBJ,CAiBNa,SAAS,CAAE,CAjBL,CAkBNC,kBAAkB,CAAE,IAlBd,CAHV,CAFJ,CApDW,CARD,CAuFZI,kBAAkB,CAAE,EAvFR,CAwFZC,OAAO,CAAE,KAxFG,CAPZ,CAFJ,CAqGA;AACA,GAAIC,CAAAA,iBAAJ,CACA,GAAIlJ,GAAJ,CAAS,CACPkJ,iBAAiB,CAAG7B,kBAAkB,CAACrH,GAAvC,CACD,CAFD,IAEO,CACLkJ,iBAAiB,CAAG7B,kBAAkB,CAACI,YAAvC,CACD,CAED,KAAM0B,CAAAA,WAAW,CAAGpJ,MAAM,CAACoJ,WAA3B,CAEA,GAAIC,CAAAA,aAA4B,CAAG,KAAM,+BACvCnJ,QADuC,CAEvC,OAFuC,CAGvCF,MAAM,CAACsJ,cAHgC,CAAzC,CAKA,GAAID,aAAJ,CAAmB,CACjBA,aAAa,CAAG3M,cAAKS,OAAL,CAAaT,cAAKC,IAAL,CAAUuD,QAAV,CAAoBmJ,aAApB,CAAb,CAAhB,CACD,CAED,KAAME,CAAAA,iBAAiB,CAAGxM,MAAM,CAACC,MAAP,CACxB,CACEwM,gCAAgC,CAAE,CAChCC,OAAO,CAAE,IADuB,CADpC,CAIEC,4BAA4B,CAAE,CAC5BD,OAAO,CAAE,IADmB,CAJhC,CAOEE,kCAAkC,CAAE,CAClCF,OAAO,CAAE,IADyB,CAElCG,wBAAwB,CAAE7M,MAAM,CAACC,MAAP,CACxB,EADwB,CAExB,CAAC,OAAD,CAFwB,CAGxB,sBAAAgD,MAAM,CAAC6J,WAAP,0DAAoBF,kCAApB,qCACIC,wBADJ,GACgC,EAJR,CAFQ,CAPtC,CAgBEE,8BAA8B,CAAE,CAC9BL,OAAO,CAAE,IADqB,CAhBlC,CADwB,CAqBxBzJ,MAAM,CAAC6J,WArBiB,CAA1B,CAwBA,cAAeE,CAAAA,eAAf,CACEtB,OADF,CAEEuB,OAFF,CAGEC,UAHF,CAME,CACA;AACA;AAEA,KAAMC,CAAAA,OAAgB,CACpBF,OAAO,CAACG,UAAR,CAAmB,GAAnB,GACA;AACA;AACAzN,cAAK0N,KAAL,CAAWC,UAAX,CAAsBL,OAAtB,CAHA,EAIA;AACA;AACCtH,OAAO,CAACI,QAAR,GAAqB,OAArB,EAAgCpG,cAAK4N,KAAL,CAAWD,UAAX,CAAsBL,OAAtB,CAPnC,CASA;AACA;AACA;AACA;AACA,GAAIE,OAAJ,CAAa,CACX,GAAI,CAAC,wBAAwBrC,IAAxB,CAA6BmC,OAA7B,CAAL,CAA4C,CAC1C,OACD,CACF,CAJD,IAIO,CACL,GAAI,2BAA2BnC,IAA3B,CAAgCmC,OAAhC,CAAJ,CAA8C,CAC5C,MAAQ,YAAWA,OAAQ,EAA3B,CACD,CAED,KAAMO,CAAAA,kBAAkB,CAAG,uGAA3B,CACA,GAAIA,kBAAkB,CAAC1C,IAAnB,CAAwBmC,OAAxB,CAAJ,CAAsC,CACpC,OACD,CACF,CAED,KAAM7M,CAAAA,OAAO,CAAG8M,UAAU,CAACxL,uBAAD,CAA1B,CAEA;AACA;AACA;AACA,GAAI+L,CAAAA,GAAJ,CACA,GAAI,CACFA,GAAG,CAAG,KAAMrN,CAAAA,OAAO,CAACsL,OAAD,CAAUuB,OAAV,CAAnB,CACD,CAAC,MAAOpO,GAAP,CAAY,CACZ;AACA;AACA;AACA,OACD,CAED;AACA;AACA,GAAI,CAAC4O,GAAL,CAAU,CACR,OACD,CAED,GAAIN,OAAJ,CAAa,CACX;AACA;AACA,KAAMO,CAAAA,cAAc,CAAG,kEAAkE5C,IAAlE,CACrB2C,GADqB,CAAvB,CAIA,GAAIC,cAAJ,CAAoB,CAClB;AACA,KAAMC,CAAAA,eAAe,CAAGhO,cAAK0N,KAAL,CAAWzN,IAAX,CACtB,MADsB,CAEtB,MAFsB,CAGtBD,cACGsH,QADH,CAEI;AACAtH,cAAKC,IAAL,CAAUC,SAAV,CAAqB,IAArB,CAHJ,CAII4N,GAJJ,CAME;AANF,CAOG1G,OAPH,CAOW,KAPX,CAOkB,GAPlB,CAHsB,CAAxB,CAYA,MAAQ,YAAW4G,eAAgB,EAAnC,CACD,CAfD,IAeO,CACL,OACD,CACF,CAED;AACA;AACA;AACA;AACA,GAAIC,CAAAA,OAAJ,CACA,GAAI,CACF,KAAMC,CAAAA,WAAW,CAAGX,UAAU,CAACtL,oBAAD,CAA9B,CACAgM,OAAO,CAAG,KAAMC,CAAAA,WAAW,CAAC9K,GAAD,CAAMkK,OAAN,CAA3B,CACD,CAAC,MAAOpO,GAAP,CAAY,CACZ+O,OAAO,CAAG,IAAV,CACD,CAED;AACA;AACA;AACA;AACA;AACA,GAAIA,OAAO,GAAKH,GAAhB,CAAqB,CACnB,OACD,CAED;AACA,GACEA,GAAG,CAACK,KAAJ,CAAU,yBAAV,GACA;AACAL,GAAG,CAACK,KAAJ,CAAU,0CAAV,CAHF,CAIE,CACA,OACD,CAED;AACA,GACEL,GAAG,CAACK,KAAJ,CAAU,0BAAV,GACAL,GAAG,CAACK,KAAJ,CAAU,6BAAV,CAFF,CAGE,CACA,OACD,CAED;AACA;AACA,GAAI,6BAA6BhD,IAA7B,CAAkC2C,GAAlC,CAAJ,CAA4C,CAC1C,MAAQ,YAAWR,OAAQ,EAA3B,CACD,CAED;AACD,CAED,KAAMc,CAAAA,oBAAoB,CAAG,QAA7B,CAEA,GAAIzN,CAAAA,aAAoC,CAAG,CACzC0N,SAAS,CAAE,CAACvO,QAAD,CACP;AACA;AACA;AACA,CAAC,MAAD,CAJO,CAKP,CAACyG,YAAD,CACA,CACEZ,oBACI,CAAC,CACCoG,OADD,CAECuB,OAFD,CAGCC,UAHD,CAAD,GAaMF,eAAe,CAACtB,OAAD,CAAUuB,OAAV,CAAmBC,UAAnB,CAdzB,CAeI,CACExB,OADF,CAEEuB,OAFF,CAGEgB,QAHF,GAKEjB,eAAe,CACbtB,OADa,CAEbuB,OAFa,CAGb,IAAM,CAACiB,cAAD,CAAyBC,gBAAzB,GACJ,GAAIxJ,CAAAA,OAAJ,CAAavE,OAAD,EACVA,OAAO,CACL3B,OAAO,CAAC2B,OAAR,CAAgB+N,gBAAhB,CAAkC,CAChC/G,KAAK,CAAE,CAAC8G,cAAD,CADyB,CAAlC,CADK,CADT,CAJW,CAAf,CAWEE,IAXF,CAWQC,MAAD,EAAYJ,QAAQ,CAACvJ,SAAD,CAAY2J,MAAZ,CAX3B,CAWgDJ,QAXhD,CArBR,CADA,CAmCA,CACE;AACA;AACA,kDAHF,CAGsD;AAEpD;AACA;AACA,IAAIhL,MAAM,CAAC6B,YAAP,CAAoBwJ,WAApB,CAAkC,EAAlC,CAAuC,CAAC,UAAD,CAA3C,CAPF,CAzCqC,CAkDzCC,YAAY,CAAE,CACZ;AACA,IAAIjJ,oBAAa,CAAEkJ,YAAY,CAAE,CAACtL,GAAjB,CAAb,CAAsC,CAAEuL,cAAc,CAAEvL,GAAlB,CAA1C,CAFY,CAGZwL,cAAc,CAAE,KAHJ,CAIZC,OAAO,CAAE,KAJG,CAKZC,WAAW,CAAEnP,QAAQ,CACjB6F,oBACG,CACCd,QAAQ,CAAE,WADX,CAEC;AACAoG,MAAM,CAAE,KAHT,CAIC;AACA;AACAuB,OAAO,CAAE,IANV,CADH,CASE,KAVe,CAWjBC,iBAhBQ,CAiBZyC,YAAY,CAAEpP,QAAQ,CAClB6F,qBAAc,CAACc,gBAAf,CACE,CAAErC,IAAI,CAAE,iBAAR,CADF,CAEEW,SAHgB,CAIlB,CAAEX,IAAI,CAAE+K,+CAAR,CArBQ,CAsBZC,QAAQ,CAAE,EAAE7L,GAAG,EAAIzD,QAAT,CAtBE,CAuBZuP,SAAS,CAAE,CACT;AACCC,QAAD,EAAgC,CAC9B;AACA,KAAM,CACJC,YADI,EAEFzQ,OAAO,CAAC,sDAAD,CAFX,CAGA,GAAIyQ,CAAAA,YAAJ,CAAiB,CACfC,QAAQ,CAAExP,cAAKC,IAAL,CAAUgF,OAAV,CAAmB,OAAnB,CAA4B,eAA5B,CADK,CAEfwK,QAAQ,CAAEnM,MAAM,CAAC6B,YAAP,CAAoBuK,IAFf,CAGf3F,aAHe,CAAjB,EAIG4F,KAJH,CAISL,QAJT,EAKD,CAZQ,CAaT;AACCA,QAAD,EAAgC,CAC9B,KAAM,CACJM,kBADI,EAEF9Q,OAAO,CAAC,wCAAD,CAFX,CAGA,GAAI8Q,CAAAA,kBAAJ,CAAuB,CACrBC,cAAc,CAAE,CACdC,GAAG,CAAE,CACH;AACA;AACA1F,MAAM,CAAE,KAHL,CAIH;AACA;AACA2F,UAAU,CAAE,KANT,CADS,CADK,CAAvB,EAWGJ,KAXH,CAWSL,QAXT,EAYD,CA9BQ,CAvBC,CAlD2B,CA0GzCvD,OAAO,CAAE3I,GA1GgC,CA2GzC4M,IAAI,CAAE,CACJC,YAAY,CAAE,KADV,CA3GmC,CA8GzC;AACA;AACAC,KAAK,CAAE,SAAY,CACjB,MAAO,CACL,IAAIlJ,aAAa,CAAGA,aAAH,CAAmB,EAApC,CADK,CAEL,GAAGrD,WAFE,CAAP,CAID,CArHwC,CAsHzCwM,YAAY,CAAE,CACZC,gBAAgB,CAAE,CADN,CAEZC,OAAO,CAAE,CACP,YADO,CAEP,oBAFO,CAGP,aAHO,CAIP;AACAjC,oBALO,CAFG,CAtH2B,CAgIzC7D,MAAM,CAAE,CACN,IAAI5E,oBACA,CACE2K,WAAW,CAAE,CACXC,aAAa,CAAE,KADJ,CAEXC,aAAa,CAAE,KAFJ,CAGXC,KAAK,CAAE,KAHI,CAIXC,aAAa,CAAE,KAJJ,CAKXC,aAAa,CAAE,KALJ,CAMXC,KAAK,CAAE,KANI,CAOX5P,MAAM,CAAE,KAPG,CADf,CADA,CAYA,EAZJ,CADM,CAcNhB,IAAI,CACFF,QAAQ,EAAI6F,mBAAZ,EAA0B,CAACpC,GAA3B,CACIvD,cAAKC,IAAL,CAAU4G,UAAV,CAAsB,QAAtB,CADJ,CAEIA,UAjBA,CAkBN;AACAhC,QAAQ,CAAE/E,QAAQ,CACd6F,qBAAc,CAACpC,GAAf,CACE,cADF,CAEE,WAHY,CAIb,iBAAgBM,aAAa,CAAG,WAAH,CAAiB,EAAG,SAChDN,GAAG,CAAG,EAAH,CAAQoC,oBAAa,gBAAb,CAAgC,cAC5C,KAzBC,CA0BNkL,OAAO,CAAE/Q,QAAQ,CAAGiF,SAAH,CAAe,MA1B1B,CA2BN+L,aAAa,CAAEhR,QAAQ,CAAG,WAAH,CAAiB,QA3BlC,CA4BNiR,sBAAsB,CAAEpL,oBACpB,8CADoB,CAEpB,0CA9BE,CA+BNqL,qBAAqB,CAAErL,oBACnB,2CADmB,CAEnB,uCAjCE,CAkCN;AACAsL,aAAa,CAAEnR,QAAQ,CACnB,WADmB,CAElB,iBAAgB+D,aAAa,CAAG,WAAH,CAAiB,EAAG,GAChDN,GAAG,CAAG,QAAH,CAAc,sBAClB,KAvCC,CAwCN2N,6BAA6B,CAAE,IAxCzB,CAyCNC,kBAAkB,CAAEzE,WAzCd,CA0CN0E,gBAAgB,CAAE,CAAC7N,GA1Cb,CA2CN8N,yBAAyB,CAAE,+BA3CrB,CAhIiC,CA6KzCC,WAAW,CAAE,KA7K4B,CA8KzC7Q,OAAO,CAAE8H,aA9KgC,CA+KzCgJ,aAAa,CAAE,CACb;AACApP,KAAK,CAAE,CACL,kBADK,CAEL,cAFK,CAGL,mBAHK,CAIL,0BAJK,CAKL,wBALK,CAML,aANK,CAOL,mBAPK,EAQLwC,MARK,CAQE,CAACxC,KAAD,CAAQoD,MAAR,GAAmB,CAC1B;AACApD,KAAK,CAACoD,MAAD,CAAL,CAAgBvF,cAAKC,IAAL,CAAUC,SAAV,CAAqB,SAArB,CAAgC,SAAhC,CAA2CqF,MAA3C,CAAhB,CAEA,MAAOpD,CAAAA,KAAP,CACD,CAbM,CAaJ,EAbI,CAFM,CAgBbD,OAAO,CAAE,CACP,cADO,CAEP,GAAG6D,YAAc;AAFV,CAhBI,CAoBb+D,OAAO,CAAEnE,oBAAa,EAAb,CAAkB,CAAC7G,OAAO,CAAC,oBAAD,CAAR,CApBd,CA/K0B,CAqMzCkC,MAAM,CAAE,CACNC,KAAK,CAAE,CACL,IAAI0E,oBACA,CACE;AACA;AACA,CACEwF,IAAI,CAAE,QADR,CAEE1K,OAAO,CAAE,CACPsC,cAAc,CAAE,KADT,CAFX,CAHF,CADA,CAWA,EAXJ,CADK,CAaL,CACEoI,IAAI,CAAE,wBADR,CAEE,IAAI7H,MAAM,CAAC6B,YAAP,CAAoBqM,WAApB,CACA;AACA,EAFA,CAGA,CAAEC,OAAO,CAAE,CAACrO,GAAD,CAAM,GAAG0C,mBAAT,CAAX,CAHJ,CAFF,CAME4L,OAAO,CAAGC,WAAD,EAAyB,CAChC,GAAI7L,mBAAmB,CAACtE,IAApB,CAA0BC,CAAD,EAAOA,CAAC,CAAC0J,IAAF,CAAOwG,WAAP,CAAhC,CAAJ,CAA0D,CACxD,MAAO,MAAP,CACD,CACD,MAAO,gBAAexG,IAAf,CAAoBwG,WAApB,CAAP,CACD,CAXH,CAYEtQ,GAAG,CAAEgD,eAAe,CAChB,CACEvF,OAAO,CAAC2B,OAAR,CAAgB,kCAAhB,CADF,CAEE4E,cAAc,CAACC,KAFjB,CADgB,CAKhBD,cAAc,CAACC,KAjBrB,CAbK,EAgCLe,MAhCK,CAgCE9B,OAhCF,CADD,CArMiC,CAwOzCuF,OAAO,CAAE,CACPzF,eAAe,EAAI,GAAIuN,mCAAJ,CAA8BC,gBAA9B,CADZ,CAEP;AACAlM,qBACE,CAAC7F,QADH,EAEE,GAAI+R,kBAAQC,aAAZ,CAA0B,CACxBC,MAAM,CAAE,CAACjT,OAAO,CAAC2B,OAAR,CAAgB,QAAhB,CAAD,CAA4B,QAA5B,CADgB,CAExBuF,OAAO,CAAE,CAAClH,OAAO,CAAC2B,OAAR,CAAgB,SAAhB,CAAD,CAFe,CAA1B,CALK,CASP;AACA,CAACkF,mBAAD,EAAe,GAAIqM,0BAAJ,EAVR,CAWP,GAAIH,kBAAQI,YAAZ,CAAyB,CACvB,GAAG5R,MAAM,CAAC0G,IAAP,CAAYf,OAAO,CAACC,GAApB,EAAyBtB,MAAzB,CACD,CAACuN,IAAD,CAAkCC,GAAlC,GAAkD,CAChD,GAAIA,GAAG,CAAC1E,UAAJ,CAAe,cAAf,CAAJ,CAAoC,CAClCyE,IAAI,CAAE,eAAcC,GAAI,EAApB,CAAJ,CAA6BC,IAAI,CAACC,SAAL,CAAerM,OAAO,CAACC,GAAR,CAAYkM,GAAZ,CAAf,CAA7B,CACD,CACD,MAAOD,CAAAA,IAAP,CACD,CANA,CAOD,EAPC,CADoB,CAUvB,GAAG7R,MAAM,CAAC0G,IAAP,CAAYzD,MAAM,CAAC2C,GAAnB,EAAwBtB,MAAxB,CAA+B,CAAC2H,GAAD,CAAM6F,GAAN,GAAc,CAC9C,GAAI,2BAA2BhH,IAA3B,CAAgCgH,GAAhC,CAAJ,CAA0C,CACxC,KAAM,IAAIvS,CAAAA,KAAJ,CACH,YAAWuS,GAAI,sGADZ,CAAN,CAGD,CAED,MAAO,CACL,GAAG7F,GADE,CAEL,CAAE,eAAc6F,GAAI,EAApB,EAAwBC,IAAI,CAACC,SAAL,CAAe/O,MAAM,CAAC2C,GAAP,CAAWkM,GAAX,CAAf,CAFnB,CAAP,CAID,CAXE,CAWA,EAXA,CAVoB,CAsBvB;AACA,uBAAwBC,IAAI,CAACC,SAAL,CACtB9O,GAAG,CAAG,aAAH,CAAmB,YADA,CAvBD,CA0BvB,kCAAmC6O,IAAI,CAACC,SAAL,CAAe3F,WAAf,CA1BZ,CA2BvB,kBAAmB0F,IAAI,CAACC,SAAL,CAAe,CAACvS,QAAhB,CA3BI,CA4BvB,+BAAgCsS,IAAI,CAACC,SAAL,CAC9BrM,OAAO,CAACC,GAAR,CAAYqM,gBADkB,CA5BT,CA+BvB;AACA,IAAI/O,GAAG,EAAI,CAACzD,QAAR,CACA,CACE,8BAA+BsS,IAAI,CAACC,SAAL,CAAepN,OAAf,CADjC,CADA,CAIA,EAJJ,CAhCuB,CAqCvB,oCAAqCmN,IAAI,CAACC,SAAL,CACnC/O,MAAM,CAACiP,aAD4B,CArCd,CAwCvB,qCAAsCH,IAAI,CAACC,SAAL,CACpC/O,MAAM,CAACkP,aAAP,CAAqBC,aADe,CAxCf,CA2CvB,6BAA8BL,IAAI,CAACC,SAAL,CAC5B/O,MAAM,CAAC6B,YAAP,CAAoB2E,OADQ,CA3CP,CA8CvB,iCAAkCsI,IAAI,CAACC,SAAL,CAChC/O,MAAM,CAACoP,eADyB,CA9CX,CAiDvB,gCAAiCN,IAAI,CAACC,SAAL,CAC/B/O,MAAM,CAAC6B,YAAP,CAAoBwN,SADW,CAjDV,CAoDvB,oCAAqCP,IAAI,CAACC,SAAL,CACnC/O,MAAM,CAACsP,aAAP,EAAwB,CAACrP,GADU,CApDd,CAuDvB,qCAAsC6O,IAAI,CAACC,SAAL,CACpC/O,MAAM,CAAC6B,YAAP,CAAoB0N,cADgB,CAvDf,CA0DvB,kCAAmCT,IAAI,CAACC,SAAL,CACjC/O,MAAM,CAAC6B,YAAP,CAAoBwJ,WAApB,EAAmC,CAACpL,GADH,CA1DZ,CA6DvB,mCAAoC6O,IAAI,CAACC,SAAL,CAClC,CAAC,CAAC/O,MAAM,CAAC6B,YAAP,CAAoB2N,YADY,CA7Db,CAgEvB,wCAAyCV,IAAI,CAACC,SAAL,CACvC/O,MAAM,CAAC6B,YAAP,CAAoB4N,iBADmB,CAhElB,CAmEvB,gCAAiCX,IAAI,CAACC,SAAL,CAAe,CAC9CW,WAAW,CAAE1P,MAAM,CAAC2P,MAAP,CAAcD,WADmB,CAE9CE,UAAU,CAAE5P,MAAM,CAAC2P,MAAP,CAAcC,UAFoB,CAG9ClT,IAAI,CAAEsD,MAAM,CAAC2P,MAAP,CAAcjT,IAH0B,CAI9CuF,MAAM,CAAEjC,MAAM,CAAC2P,MAAP,CAAc1N,MAJwB,CAK9C,IAAIhC,GAAG,CACH,CACE;AACA4P,OAAO,CAAE7P,MAAM,CAAC2P,MAAP,CAAcE,OAFzB,CADG,CAKH,EALJ,CAL8C,CAW9CC,uBAAuB,CAAE9P,MAAM,CAAC6B,YAAP,CAAoBiO,uBAXC,CAAf,CAnEV,CAgFvB,qCAAsChB,IAAI,CAACC,SAAL,CAAe/O,MAAM,CAAC+P,QAAtB,CAhFf,CAiFvB,kCAAmCjB,IAAI,CAACC,SAAL,CAAevO,WAAf,CAjFZ,CAkFvB,kCAAmCsO,IAAI,CAACC,SAAL,CAAe,CAAC,CAAC/O,MAAM,CAACgQ,IAAxB,CAlFZ,CAmFvB,kCAAmClB,IAAI,CAACC,SAAL,eAAe/O,MAAM,CAACgQ,IAAtB,eAAe,aAAaH,OAA5B,CAnFZ,CAoFvB,kCAAmCf,IAAI,CAACC,SAAL,CAAe/O,MAAM,CAACiQ,WAAtB,CApFZ,CAqFvB,IAAIzT,QAAQ,CACR,CACE;AACA;AACA;AACA,gBAAiBsS,IAAI,CAACC,SAAL,CAAe,KAAf,CAJnB,CADQ,CAORtN,SAPJ,CArFuB,CA6FvB;AACA;AACA,IAAIzB,MAAM,CAAC6B,YAAP,CAAoBqO,OAApB,EAA+BjQ,GAA/B,CACA,CACE,cAAgB;AAC9B,wBAAwBzD,QAAQ,CAAG,aAAH,CAAmB,IAAK;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAVY,CADA,CAaA,EAbJ,CA/FuB,CAAzB,CAXO,CAyHP,CAACA,QAAD,EACE,GAAI2T,yCAAJ,CAAwB,CACtB5O,QAAQ,CAAE6O,mCADY,CAEtBlQ,QAFsB,CAAxB,CA1HK,CA8HP,CAAC1D,QAAD,EAAa,GAAI6T,yCAAJ,EA9HN,CA+HP;AACA;AACA;AACA;AACArQ,MAAM,CAACsQ,MAAP,CAAcC,2BAAd,EACE,GAAIhC,kBAAQiC,YAAZ,CAAyB,CACvBC,cAAc,CAAE,cADO,CAEvBC,aAAa,CAAE,SAFQ,CAAzB,CApIK,CAwIP,IAAIzQ,GAAG,CACH,CAAC,IAAM,CACL;AACA;AACA,KAAM,CACJ0Q,6BADI,EAEFnV,OAAO,CAAC,qDAAD,CAFX,CAGA,KAAMoV,CAAAA,UAAU,CAAG,CAAC,GAAID,CAAAA,6BAAJ,EAAD,CAAnB,CAEA,GAAI,CAACnU,QAAL,CAAe,CACboU,UAAU,CAACC,IAAX,CAAgB,GAAItC,kBAAQuC,0BAAZ,EAAhB,EACD,CAED,MAAOF,CAAAA,UAAP,CACD,CAbD,GADG,CAeH,EAfJ,CAxIO,CAwJP;AACA,CAACvO,mBAAD,EAAe,CAACpC,GAAhB,EAAuB,GAAIsO,kBAAQwC,qBAAZ,EAzJhB,CA0JP,CAAC9Q,GAAD,EACE,GAAIsO,kBAAQiC,YAAZ,CAAyB,CACvBC,cAAc,CAAE,UADO,CAEvBC,aAAa,CAAE,kCAFQ,CAAzB,CA3JK,CA+JPzN,YAAY,EAAIzG,QAAhB,EAA4B,GAAIwU,mCAAJ,EA/JrB,CAgKPxU,QAAQ,EACN,GAAIyU,6BAAJ,CAAwB,CAAEC,UAAU,CAAE/N,gBAAd,CAAgClD,GAAhC,CAAxB,CAjKK,CAkKP,CAACoC,mBAAD,EACElC,MAAM,GAAK,QADb,EAEE3D,QAFF,EAGE,GAAI2U,8BAAJ,CAA+B,CAAE5N,UAAF,CAA/B,CArKK,CAsKP/G,QAAQ,EAAI,GAAI4U,yBAAJ,EAtKL,CAuKP,CAAC5U,QAAD,EACE,GAAI6U,6BAAJ,CAAwB,CACtBtR,OADsB,CAEtBO,QAFsB,CAGtBC,aAHsB,CAAxB,CAxKK,CA6KP,CAACN,GAAD,EACE,CAACzD,QADH,EAEEwD,MAAM,CAAC6B,YAAP,CAAoByP,KAFtB,EAGE,GAAIC,0BAAJ,CAAqB,CACnB5P,OADmB,CAArB,CAhLK,CAmLP,GAAI6P,iCAAJ,EAnLO,CAoLPxR,MAAM,CAACsP,aAAP,EACE,CAACrP,GADH,EAEEzD,QAFF,EAGG,UAAY,CACX,KAAM,CACJiV,6BADI,EAEFjW,OAAO,CAAC,oDAAD,CAFX,CAKA,MAAO,IAAIiW,CAAAA,6BAAJ,CAAkC,CACvCtO,gBADuC,CAAlC,CAAP,CAGD,CATD,EAvLK,CAiMPnD,MAAM,CAAC6B,YAAP,CAAoBgI,WAApB,EACE,CAACxH,mBADH,EAEE,CAACpC,GAFH,EAGE,GAAIyR,kCAAJ,CAA6B,CAC3BC,KAAK,CAAE,CACL,CAACnV,QAAD,EACE+M,iBAAiB,CAACG,4BAAlB,CAA+CD,OADjD,EAEE,GAAIC,uDAAJ,EAHG,CAILH,iBAAiB,CAACC,gCAAlB,CAAmDC,OAAnD,EACE,GAAID,2DAAJ,CAAqC,CACnCoI,cAAc,CACZrI,iBAAiB,CAACC,gCAAlB,CACGqI,cADH,EACqB,EAHY,CAArC,CALG,CAUL,CAACrV,QAAD,EACE+M,iBAAiB,CAACI,kCAAlB,CAAqDF,OADvD,EAEE,GAAIE,6DAAJ,CAAuC,CACrCC,wBAAwB,CACtBL,iBAAiB,CAACI,kCAAlB,CACGC,wBAHgC,CAAvC,CAZG,CAiBL,CAACpN,QAAD,EACE+M,iBAAiB,CAACO,8BAAlB,CAAiDL,OADnD,EAEE,GAAIK,yDAAJ,CACExC,kBAAkB,CAACI,YADrB,CAnBG,EAsBL3E,MAtBK,CAsBE9B,OAtBF,CADoB,CAA7B,CApMK,CA6NP,GAAI6Q,6CAAJ,EA7NO,EA8NP/O,MA9NO,CA8NC9B,OA9ND,CAxOgC,CAA3C,CAycA;AACA,GAAI2D,eAAJ,CAAqB,kDACnB,uBAAAvH,aAAa,CAACF,OAAd,6DAAuByB,OAAvB,sCAAgCiS,IAAhC,CAAqCjM,eAArC,EACD,CAED,GAAI,YAAAL,QAAQ,OAAR,oCAAUG,eAAV,+BAA2BP,KAA3B,EAAoCS,eAAxC,CAAyD,mDACvD,wBAAAvH,aAAa,CAACF,OAAd,8DAAuBqJ,OAAvB,sCAAgCuL,OAAhC,CACE,GAAIC,yCAAJ,CAAwBzN,QAAQ,CAACG,eAAT,CAAyBP,KAAjD,CAAwDS,eAAxD,CADF,EAGD,CAED,GAAIvC,mBAAJ,CAAgB,2BACd;AACA,uBAAOhF,aAAa,CAAC4J,MAArB,mBAAO,uBAAsB6G,gBAA7B,CAEA,GAAItR,QAAQ,EAAIyD,GAAhB,CAAqB,CACnB;AACA;AACA5C,aAAa,CAAC4U,YAAd,CAA6B,CAAC,QAAD,CAA7B,CACD,CACD;AACA,GAAI5U,aAAa,CAACqP,IAAlB,CAAwB,MAAOrP,CAAAA,aAAa,CAACqP,IAAd,CAAmBC,YAA1B,CAExB;AACA;AACA;AACAtP,aAAa,CAAC6U,QAAd,CAAyB,EAAzB,CACA,GAAIxP,OAAO,CAACyP,QAAR,CAAiBC,GAAjB,GAAyB,GAA7B,CAAkC,CAChC,KAAMvH,CAAAA,KAAK,CAAG,0EAA0EwH,IAA1E,CACZ7W,OAAO,CAAC2B,OAAR,CAAgB,aAAhB,CADY,CAAd,CAGA,GAAI0N,KAAJ,CAAW,CACT;AACAxN,aAAa,CAAC6U,QAAd,CAAuBI,YAAvB,CAAsC,CACpC5V,cAAKS,OAAL,CAAa0N,KAAK,CAAC,CAAD,CAAlB,CAAuB,WAAvB,CADoC,CAAtC,CAGD,CACF,CAVD,IAUO,CACL,KAAMA,CAAAA,KAAK,CAAG,+BAA+BwH,IAA/B,CACZ7W,OAAO,CAAC2B,OAAR,CAAgB,aAAhB,CADY,CAAd,CAGA,GAAI0N,KAAJ,CAAW,CACT;AACAxN,aAAa,CAAC6U,QAAd,CAAuBI,YAAvB,CAAsC,CAACzH,KAAK,CAAC,CAAD,CAAN,CAAtC,CACD,CACF,CACD,GAAInI,OAAO,CAACyP,QAAR,CAAiBC,GAAjB,GAAyB,GAA7B,CAAkC,CAChC,KAAMvH,CAAAA,KAAK,CAAG,8EAA8EwH,IAA9E,CACZ7W,OAAO,CAAC2B,OAAR,CAAgB,aAAhB,CADY,CAAd,CAGA,GAAI0N,KAAJ,CAAW,CACT;AACAxN,aAAa,CAAC6U,QAAd,CAAuBK,cAAvB,CAAwC,CAAC1H,KAAK,CAAC,CAAD,CAAN,CAAxC,CACD,CACF,CARD,IAQO,IAAInI,OAAO,CAACyP,QAAR,CAAiBC,GAAjB,GAAyB,GAA7B,CAAkC,CACvC,KAAMvH,CAAAA,KAAK,CAAG,gEAAgEwH,IAAhE,CACZ7W,OAAO,CAAC2B,OAAR,CAAgB,aAAhB,CADY,CAAd,CAGA,GAAI0N,KAAJ,CAAW,CACT;AACAxN,aAAa,CAAC6U,QAAd,CAAuBK,cAAvB,CAAwC,CAAC1H,KAAK,CAAC,CAAD,CAAN,CAAxC,CACD,CACF,CAED,GAAI5K,GAAJ,CAAS,CACP,GAAI,CAAC5C,aAAa,CAACiO,YAAnB,CAAiC,CAC/BjO,aAAa,CAACiO,YAAd,CAA6B,EAA7B,CACD,CACDjO,aAAa,CAACiO,YAAd,CAA2BkH,eAA3B,CAA6C,KAA7C,CACAnV,aAAa,CAACiO,YAAd,CAA2BmH,WAA3B,CAAyC,KAAzC,CACD,CAED,KAAMC,CAAAA,UAAU,CAAG5D,IAAI,CAACC,SAAL,CAAe,CAChC3F,WAAW,CAAEpJ,MAAM,CAACoJ,WADY,CAEhCE,cAAc,CAAEtJ,MAAM,CAACsJ,cAFS,CAGhC2F,aAAa,CAAEjP,MAAM,CAACiP,aAHU,CAIhCE,aAAa,CAAEnP,MAAM,CAACkP,aAAP,CAAqBC,aAJJ,CAKhC3I,OAAO,CAAExG,MAAM,CAAC6B,YAAP,CAAoB2E,OALG,CAMhC4I,eAAe,CAAEpP,MAAM,CAACoP,eANQ,CAOhCuD,SAAS,CAAE3S,MAAM,CAAC6B,YAAP,CAAoB8Q,SAPC,CAQhCrD,aAAa,CAAEtP,MAAM,CAACsP,aARU,CAShCC,cAAc,CAAEvP,MAAM,CAAC6B,YAAP,CAAoB0N,cATJ,CAUhClE,WAAW,CAAErL,MAAM,CAAC6B,YAAP,CAAoBwJ,WAVD,CAWhCoE,iBAAiB,CAAEzP,MAAM,CAAC6B,YAAP,CAAoB4N,iBAXP,CAYhCM,QAAQ,CAAE/P,MAAM,CAAC+P,QAZe,CAahCG,OAAO,CAAElQ,MAAM,CAAC6B,YAAP,CAAoBqO,OAbG,CAchCK,2BAA2B,CAAEvQ,MAAM,CAACsQ,MAAP,CAAcC,2BAdX,CAehCqC,WAAW,CAAE5S,MAAM,CAAC4S,WAfY,CAgBhCC,uBAAuB,CAAE7S,MAAM,CAAC6B,YAAP,CAAoBgR,uBAhBb,CAiBhC1S,MAjBgC,CAkBhCC,wBAlBgC,CAmBhCmO,OAAO,CAAE,CAAC,CAACvO,MAAM,CAACuO,OAnBc,CAoBhC/N,WApBgC,CAAf,CAAnB,CAuBA,KAAM4B,CAAAA,KAAU,CAAG,CACjBiF,IAAI,CAAE,YADW,CAEjB;AACA;AACA;AACAyL,OAAO,CAAG,GAAEpQ,OAAO,CAACC,GAAR,CAAYoQ,cAAe,IAAGL,UAAW,EALpC,CAMjBM,cAAc,CAAEtW,cAAKC,IAAL,CAAUgF,OAAV,CAAmB,OAAnB,CAA4B,SAA5B,CANC,CAAnB,CASA;AACA,GAAI3B,MAAM,CAACuO,OAAP,EAAkBvO,MAAM,CAACmC,UAA7B,CAAyC,CACvCC,KAAK,CAAC6Q,iBAAN,CAA0B,CACxBjT,MAAM,CAAE,CAACA,MAAM,CAACmC,UAAR,CADgB,CAA1B,CAGD,CAED9E,aAAa,CAAC+E,KAAd,CAAsBA,KAAtB,CAEA,GAAIM,OAAO,CAACC,GAAR,CAAYuQ,oBAAhB,CAAsC,CACpC,KAAMC,CAAAA,QAAQ,CAAGzQ,OAAO,CAACC,GAAR,CAAYuQ,oBAAZ,CAAiCE,QAAjC,CACf,gBADe,CAAjB,CAGA,KAAMC,CAAAA,gBAAgB,CAAG3Q,OAAO,CAACC,GAAR,CAAYuQ,oBAAZ,CAAiCE,QAAjC,CACvB,gBADuB,CAAzB,CAGA,KAAME,CAAAA,gBAAgB,CAAG5Q,OAAO,CAACC,GAAR,CAAYuQ,oBAAZ,CAAiCE,QAAjC,CACvB,gBADuB,CAAzB,CAGA,KAAMG,CAAAA,UAAU,CAAG,CAACJ,QAAD,EAAa,CAACE,gBAAd,EAAkC,CAACC,gBAAtD,CAEA,GAAIC,UAAU,EAAIJ,QAAlB,CAA4B,CAC1B;AACA9V,aAAa,CAACmW,qBAAd,CAAsC,CACpCC,KAAK,CAAE,SAD6B,CAEpCC,KAAK,CAAE,gBAF6B,CAAtC,CAID,CAED,GACEH,UAAU,EACTF,gBAAgB,EAAI,CAAC7W,QADtB,EAEC8W,gBAAgB,EAAI9W,QAHvB,CAIE,CACAa,aAAa,CAACmJ,OAAd,CAAuBqK,IAAvB,CAA6B7E,QAAD,EAAgC,CAC1DA,QAAQ,CAAC2H,KAAT,CAAeC,IAAf,CAAoBC,GAApB,CAAwB,sBAAxB,CAAiDvC,KAAD,EAAW,CACzDtW,OAAO,CAAC8Y,GAAR,CACExC,KAAK,CAACyC,QAAN,CAAe,CACbC,MAAM,CAAE,IADK,CAEb;AACAC,OAAO,CAAEV,UAAU,CAAG,KAAH,CAAW,SAHjB,CAAf,CADF,EAOD,CARD,EASD,CAVD,EAWD,CAED,GAAKF,gBAAgB,EAAI,CAAC7W,QAAtB,EAAoC8W,gBAAgB,EAAI9W,QAA5D,CAAuE,CACrEa,aAAa,CAACmJ,OAAd,CAAuBqK,IAAvB,CACE,GAAItC,kBAAQ2F,cAAZ,CAA2B,CACzB;AACAC,OAAO,CAAE,IAFgB,CAA3B,CADF,EAMA9W,aAAa,CAAC8W,OAAd,CAAwB,IAAxB,CACD,CACF,CACF,CAED9W,aAAa,CAAG,KAAM,kBAAmBA,aAAnB,CAAkC,CACtD+W,aAAa,CAAEtU,GADuC,CAEtDuJ,aAFsD,CAGtDgL,aAAa,CAAEpU,GAHuC,CAItDzD,QAJsD,CAKtDoW,WAAW,CAAE5S,MAAM,CAAC4S,WAAP,EAAsB,EALmB,CAMtD0B,WAAW,CAAEtU,MAAM,CAACsU,WANkC,CAOtDC,2BAA2B,CAAEvU,MAAM,CAACuU,2BAPkB,CAQtDjE,MAAM,CAAEtQ,MAAM,CAACsQ,MARuC,CAAlC,CAAtB,CAWA,GAAIkE,CAAAA,eAAe,CAAGnX,aAAa,CAACtC,OAApC,CACA,GAAI,MAAOiF,CAAAA,MAAM,CAACuO,OAAd,GAA0B,UAA9B,CAA0C,CACxClR,aAAa,CAAG2C,MAAM,CAACuO,OAAP,CAAelR,aAAf,CAA8B,CAC5CyC,GAD4C,CAE5CG,GAF4C,CAG5CzD,QAH4C,CAI5CuD,OAJ4C,CAK5CC,MAL4C,CAM5C+B,cAN4C,CAO5CyB,UAP4C,CAQ5C+K,OAAO,CAAPA,gBAR4C,CAA9B,CAAhB,CAWA,GAAI,CAAClR,aAAL,CAAoB,CAClB,KAAM,IAAIf,CAAAA,KAAJ,CACJ,iIACE,8EAFE,CAAN,CAID,CAED,GAAI2D,GAAG,EAAIuU,eAAe,GAAKnX,aAAa,CAACtC,OAA7C,CAAsD,CACpDsC,aAAa,CAACtC,OAAd,CAAwByZ,eAAxB,CACA1Z,oBAAoB,CAAC0Z,eAAD,CAApB,CACD,CAED,GAAI,MAAQnX,CAAAA,aAAD,CAAuB8N,IAA9B,GAAuC,UAA3C,CAAuD,CACrDnQ,OAAO,CAACC,IAAR,CACE,4FADF,EAGD,CACF,CAED;AACA,GAAI,MAAO+E,CAAAA,MAAM,CAACyU,oBAAd,GAAuC,UAA3C,CAAuD,CACrD,KAAMvS,CAAAA,OAAO,CAAGlC,MAAM,CAACyU,oBAAP,CAA4B,CAC1C5H,YAAY,CAAExP,aAAa,CAACwP,YADc,CAA5B,CAAhB,CAGA,GAAI3K,OAAO,CAAC2K,YAAZ,CAA0B,CACxBxP,aAAa,CAACwP,YAAd,CAA6B3K,OAAO,CAAC2K,YAArC,CACD,CACF,CAED,QAAS6H,CAAAA,WAAT,CAAqB7W,IAArB,CAA0E,CACxE,GAAI,CAACA,IAAL,CAAW,CACT,MAAO,MAAP,CACD,CAED,KAAM8W,CAAAA,SAAS,CAAG,CAChB,eADgB,CAEhB,gBAFgB,CAGhB,gBAHgB,CAIhB,gBAJgB,CAKhB,gBALgB,CAAlB,CAQA,GAAI9W,IAAI,WAAY+W,CAAAA,MAAhB,EAA0BD,SAAS,CAACzW,IAAV,CAAgB2W,KAAD,EAAWhX,IAAI,CAACgK,IAAL,CAAUgN,KAAV,CAA1B,CAA9B,CAA2E,CACzE,MAAO,KAAP,CACD,CAED,GAAI,MAAOhX,CAAAA,IAAP,GAAgB,UAApB,CAAgC,CAC9B,GACE8W,SAAS,CAACzW,IAAV,CAAgB2W,KAAD,EAAW,CACxB,GAAI,CACF,GAAIhX,IAAI,CAACgX,KAAD,CAAR,CAAiB,CACf,MAAO,KAAP,CACD,CACF,CAAC,MAAOzQ,CAAP,CAAU,CAAE,CACd,MAAO,MAAP,CACD,CAPD,CADF,CASE,CACA,MAAO,KAAP,CACD,CACF,CAED,GAAIpG,KAAK,CAACC,OAAN,CAAcJ,IAAd,GAAuBA,IAAI,CAACK,IAAL,CAAUwW,WAAV,CAA3B,CAAmD,CACjD,MAAO,KAAP,CACD,CAED,MAAO,MAAP,CACD,CAED,KAAMI,CAAAA,gBAAgB,iDACpBzX,aAAa,CAACK,MADM,eACpB,uBAAsBC,KAAtB,CAA4BO,IAA5B,CACGL,IAAD,EAAU6W,WAAW,CAAC7W,IAAI,CAACgK,IAAN,CAAX,EAA0B6M,WAAW,CAAC7W,IAAI,CAACsQ,OAAN,CADjD,CADoB,+BAGf,KAHP,CAKA,GAAI2G,gBAAJ,CAAsB,+FACpB;AACA,GAAItY,QAAJ,CAAc,CACZxB,OAAO,CAACC,IAAR,CACEC,eAAMC,MAAN,CAAaC,IAAb,CAAkB,WAAlB,EACEF,eAAME,IAAN,CACE,0FADF,CADF,CAIE,kFALJ,EAOD,CAED,2BAAIiC,aAAa,CAACK,MAAlB,SAAI,uBAAsBC,KAAtB,CAA4B+C,MAAhC,CAAwC,CACtC;AACArD,aAAa,CAACK,MAAd,CAAqBC,KAArB,CAA6BN,aAAa,CAACK,MAAd,CAAqBC,KAArB,CAA2BoF,MAA3B,CAC1B5E,CAAD,gCACE,EACE,iBAAOA,CAAC,CAAC4W,KAAT,0BAAO,SAAU,CAAV,CAAP,eAAO,UAAc7S,OAArB,IAAiC,QAAjC,EACA/D,CAAC,CAAC4W,KAAF,CAAQ,CAAR,EAAW7S,OAAX,CAAmB8S,iBAAnB,GAAyC,IAF3C,CADF,EAD2B,CAA7B,CAOD,CACD,0BAAI3X,aAAa,CAACmJ,OAAlB,SAAI,sBAAuB9F,MAA3B,CAAmC,CACjC;AACArD,aAAa,CAACmJ,OAAd,CAAwBnJ,aAAa,CAACmJ,OAAd,CAAsBzD,MAAtB,CACrBC,CAAD,EAAQA,CAAD,CAAWgS,iBAAX,GAAiC,IADlB,CAAxB,CAGD,CACD,0BAAI3X,aAAa,CAACiO,YAAlB,iCAAI,sBAA4BS,SAAhC,SAAI,uBAAuCrL,MAA3C,CAAmD,CACjD;AACArD,aAAa,CAACiO,YAAd,CAA2BS,SAA3B,CAAuC1O,aAAa,CAACiO,YAAd,CAA2BS,SAA3B,CAAqChJ,MAArC,CACpCkS,CAAD,EAAQA,CAAD,CAAWD,iBAAX,GAAiC,IADH,CAAvC,CAGD,CACF,CAlCD,IAkCO,IAAI,CAAChV,MAAM,CAACsQ,MAAP,CAAc4E,0BAAnB,CAA+C,CACpD,KAAM,yDAA2BpV,GAA3B,CAAgC,CAACG,GAAjC,CAAsC5C,aAAtC,CAAN,CACD,CAED;AACA,GAAI0D,eAAJ,CAAqB,CACnB3D,kBAAkB,CAACC,aAAD,CAAgB0E,cAAc,CAACC,KAA/B,CAAlB,CACD,CAED;AACA,GACExF,QAAQ,EACRa,aAAa,CAACK,MADd,EAEAM,KAAK,CAACC,OAAN,CAAcZ,aAAa,CAACK,MAAd,CAAqBC,KAAnC,CAHF,CAIE,CACA,GAAIwX,CAAAA,WAAW,CAAG,KAAlB,CAEA9X,aAAa,CAACK,MAAd,CAAqBC,KAArB,CAA6BN,aAAa,CAACK,MAAd,CAAqBC,KAArB,CAA2BoF,MAA3B,CAC1BlF,IAAD,EAAmB,CACjB,GAAI,EAAEA,IAAI,CAACgK,IAAL,WAAqB+M,CAAAA,MAAvB,CAAJ,CAAoC,MAAO,KAAP,CACpC,GAAI,UAAU/J,KAAV,CAAgBhN,IAAI,CAACgK,IAArB,GAA8B,CAAC,UAAUgD,KAAV,CAAgBhN,IAAI,CAACgK,IAArB,CAAnC,CAA+D,CAC7D;AACAsN,WAAW,CAAGtX,IAAI,CAACE,GAAL,GAAagE,cAAc,CAACC,KAA1C,CACA,MAAO,CAACmT,WAAR,CACD,CACD,MAAO,KAAP,CACD,CAT0B,CAA7B,CAYA,GAAIA,WAAJ,CAAiB,CACfna,OAAO,CAACC,IAAR,CACE,kKADF,EAGD,CACF,CAED;AACA,GAAIoC,aAAa,CAACK,MAAd,EAAwBM,KAAK,CAACC,OAAN,CAAcZ,aAAa,CAACK,MAAd,CAAqBC,KAAnC,CAA5B,CAAuE,CACrE,CAAC,GAAGC,OAAH,CAAWwX,IAAX,CAAgB/X,aAAa,CAACK,MAAd,CAAqBC,KAArC,CAA4C,SAC3CE,IAD2C,CAE3C,CACA,GAAI,EAAEA,IAAI,CAACgK,IAAL,WAAqB+M,CAAAA,MAArB,EAA+B5W,KAAK,CAACC,OAAN,CAAcJ,IAAI,CAACE,GAAnB,CAAjC,CAAJ,CAA+D,CAC7D,OACD,CAED,KAAMsX,CAAAA,MAAM,CACVxX,IAAI,CAACgK,IAAL,CAAUyN,MAAV,GAAqB,UAArB,EAAmCzX,IAAI,CAACgK,IAAL,CAAUyN,MAAV,GAAqB,UAD1D,CAEA,KAAMC,CAAAA,MAAM,CAAG1X,IAAI,CAACgK,IAAL,CAAUyN,MAAV,GAAqB,UAApC,CACA,KAAME,CAAAA,KAAK,CAAG3X,IAAI,CAACgK,IAAL,CAAUyN,MAAV,GAAqB,SAAnC,CACA,KAAMG,CAAAA,QAAQ,CAAG5X,IAAI,CAACgK,IAAL,CAAUyN,MAAV,GAAqB,UAAtC,CAEA;AACA,GAAI,EAAED,MAAM,EAAIE,MAAV,EAAoBC,KAApB,EAA6BC,QAA/B,CAAJ,CAA8C,CAC5C,OACD,CAED,CAAC,GAAG7X,OAAH,CAAWwX,IAAX,CAAgBvX,IAAI,CAACE,GAArB,CAA0B,SAAUA,GAAV,CAAuC,CAChE,GACE,EACEA,GAAG,EACH,MAAOA,CAAAA,GAAP,GAAe,QADf,GAEA;AACCA,GAAG,CAACkE,MAAJ,GAAe,YAAf,EACClE,GAAG,CAACkE,MAAJ,GAAe,mBAJjB,GAKAlE,GAAG,CAACmE,OALJ,EAMA,MAAOnE,CAAAA,GAAG,CAACmE,OAAX,GAAuB,QANvB,GAOA;AACA;AACA;AACA;AACA;AACCnF,MAAM,CAAC2Y,SAAP,CAAiBC,cAAjB,CAAgCP,IAAhC,CAAqCrX,GAAG,CAACmE,OAAzC,CAAkD,UAAlD,GACCnF,MAAM,CAAC2Y,SAAP,CAAiBC,cAAjB,CAAgCP,IAAhC,CACErX,GAAG,CAACmE,OADN,CAEE,kBAFF,CAbF,CADF,CADF,CAoBE,CACA,OACD,CAED;AACA;AACA;AACA;AACA;AACA,GAAI,CACF;AACA;AACA,KAAM0T,CAAAA,cAAc,CAAGpa,OAAO,CAAC2B,OAAR,CAAgB,gBAAhB,CAAkC,CACvDgH,KAAK,CAAE,CACLqR,KAAK,CACD;AACA1V,GAFC,CAGD;AACAtE,OAAO,CAAC2B,OAAR,CACEkY,MAAM,CACF,iBADE,CAEFE,MAAM,CACN,iBADM,CAENE,QAAQ,CACR,mBADQ,CAER,MAPN,CALC,CADgD,CAAlC,CAAvB,CAkBA;AACA,GAAIG,cAAJ,CAAoB,CAClB;AACA;AACA;AACA,KAAMC,CAAAA,gBAAgB,CAAGra,OAAO,CAAC2B,OAAR,CAAgBY,GAAG,CAACkE,MAApB,CAA4B,CACnDkC,KAAK,CAAE,CAACyR,cAAD,CAD4C,CAA5B,CAAzB,CAGA,GAAIC,gBAAJ,CAAsB,CACpB;AACA9X,GAAG,CAACkE,MAAJ,CAAa4T,gBAAb,CACD,CACF,CACF,CAAC,MAAOzR,CAAP,CAAU,CACV;AACD,CACF,CAnEA,EAoEF,CAtFA,EAuFF,CAED;AACA;AACA;AACA;AACA,KAAM0R,CAAAA,aAAkB,CAAGzY,aAAa,CAACuP,KAAzC,CACA,GAAI,MAAOkJ,CAAAA,aAAP,GAAyB,WAA7B,CAA0C,CACxC,KAAMC,CAAAA,YAAY,CAAG,SAAY,CAC/B,KAAMnJ,CAAAA,KAAyB,CAC7B,MAAOkJ,CAAAA,aAAP,GAAyB,UAAzB,CACI,KAAMA,CAAAA,aAAa,EADvB,CAEIA,aAHN,CAIA;AACA,GACEpS,aAAa,EACb1F,KAAK,CAACC,OAAN,CAAc2O,KAAK,CAAC,SAAD,CAAnB,CADA,EAEAA,KAAK,CAAC,SAAD,CAAL,CAAiBlM,MAAjB,CAA0B,CAH5B,CAIE,CACA,KAAMsV,CAAAA,YAAY,CAAGtS,aAAa,CAChCK,4CADgC,CAAlC,CAGA6I,KAAK,CAAC7I,4CAAD,CAAL,CAA0C,CACxC,GAAG6I,KAAK,CAAC,SAAD,CADgC,CAExCoJ,YAFwC,CAA1C,CAID,CACD,MAAOpJ,CAAAA,KAAK,CAAC,SAAD,CAAZ,CAEA,GAAIvK,qBAAc,CAAC7F,QAAnB,CAA6B,CAC3B,IAAK,KAAMsE,CAAAA,IAAX,GAAmB/D,CAAAA,MAAM,CAAC0G,IAAP,CAAYmJ,KAAZ,CAAnB,CAAuC,CACrC,GACE9L,IAAI,GAAK,WAAT,EACAA,IAAI,GAAK,MADT,EAEAA,IAAI,GAAK,KAFT,EAGAA,IAAI,GAAK,eAJX,CAME,SACF,KAAMmV,CAAAA,QAAQ,CACZnV,IAAI,CAACqJ,UAAL,CAAgB,QAAhB,GAA6BrJ,IAAI,GAAK,YAAtC,CACI,YADJ,CAEI,MAHN,CAIA,KAAMoV,CAAAA,GAAG,CAAGtJ,KAAK,CAAC9L,IAAD,CAAjB,CACA,GAAI,MAAOoV,CAAAA,GAAP,GAAe,QAAf,EAA2B,CAAClY,KAAK,CAACC,OAAN,CAAciY,GAAd,CAAhC,CAAoD,CAClDtJ,KAAK,CAAC9L,IAAD,CAAL,CAAc,CACZmV,QADY,CAEZ,GAAGC,GAFS,CAAd,CAID,CALD,IAKO,CACLtJ,KAAK,CAAC9L,IAAD,CAAL,CAAc,CACZqV,MAAM,CAAED,GADI,CAEZD,QAFY,CAAd,CAID,CACF,CACF,CAED,MAAOrJ,CAAAA,KAAP,CACD,CAlDD,CAmDA;AACAvP,aAAa,CAACuP,KAAd,CAAsBmJ,YAAtB,CACD,CAED,GAAI,CAAC9V,GAAL,CAAU,CACR;AACA5C,aAAa,CAACuP,KAAd,CAAsB,KAAOvP,CAAAA,aAAa,CAACuP,KAAf,EAA5B,CACD,CAED,MAAOvP,CAAAA,aAAP,CACD", "sourcesContent": ["import ReactRefreshWebpackPlugin from '@next/react-refresh-utils/ReactRefreshWebpackPlugin'\nimport chalk from 'chalk'\nimport crypto from 'crypto'\nimport { readFileSync } from 'fs'\nimport { codeFrameColumns } from 'next/dist/compiled/babel/code-frame'\nimport semver from 'next/dist/compiled/semver'\nimport { isWebpack5, webpack } from 'next/dist/compiled/webpack/webpack'\nimport path, { join as pathJoin, relative as relativePath } from 'path'\nimport {\n  DOT_NEXT_ALIAS,\n  NEXT_PROJECT_ROOT,\n  NEXT_PROJECT_ROOT_DIST_CLIENT,\n  PAGES_DIR_ALIAS,\n} from '../lib/constants'\nimport { fileExists } from '../lib/file-exists'\nimport { getPackageVersion } from '../lib/get-package-version'\nimport { CustomRoutes } from '../lib/load-custom-routes.js'\nimport { getTypeScriptConfiguration } from '../lib/typescript/getTypeScriptConfiguration'\nimport {\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  CLIENT_STATIC_FILES_RUNTIME_WEBPACK,\n  REACT_LOADABLE_MANIFEST,\n  SERVERLESS_DIRECTORY,\n  SERVER_DIRECTORY,\n} from '../next-server/lib/constants'\nimport { execOnce } from '../next-server/lib/utils'\nimport { NextConfig } from '../next-server/server/config'\nimport { findPageFile } from '../server/lib/find-page-file'\nimport { WebpackEntrypoints } from './entries'\nimport * as Log from './output/log'\nimport { build as buildConfiguration } from './webpack/config'\nimport { __overrideCssConfiguration } from './webpack/config/blocks/css/overrideCssConfiguration'\nimport BuildManifestPlugin from './webpack/plugins/build-manifest-plugin'\nimport BuildStatsPlugin from './webpack/plugins/build-stats-plugin'\nimport ChunkNamesPlugin from './webpack/plugins/chunk-names-plugin'\nimport { JsConfigPathsPlugin } from './webpack/plugins/jsconfig-paths-plugin'\nimport { DropClientPage } from './webpack/plugins/next-drop-client-page-plugin'\nimport NextJsSsrImportPlugin from './webpack/plugins/nextjs-ssr-import'\nimport NextJsSSRModuleCachePlugin from './webpack/plugins/nextjs-ssr-module-cache'\nimport PagesManifestPlugin from './webpack/plugins/pages-manifest-plugin'\nimport { ProfilingPlugin } from './webpack/plugins/profiling-plugin'\nimport { ReactLoadablePlugin } from './webpack/plugins/react-loadable-plugin'\nimport { ServerlessPlugin } from './webpack/plugins/serverless-plugin'\nimport WebpackConformancePlugin, {\n  DuplicatePolyfillsConformanceCheck,\n  GranularChunksConformanceCheck,\n  MinificationConformanceCheck,\n  ReactSyncScriptsConformanceCheck,\n} from './webpack/plugins/webpack-conformance-plugin'\nimport { WellKnownErrorsPlugin } from './webpack/plugins/wellknown-errors-plugin'\n\ntype ExcludesFalse = <T>(x: T | false) => x is T\n\nconst devtoolRevertWarning = execOnce(\n  (devtool: webpack.Configuration['devtool']) => {\n    console.warn(\n      chalk.yellow.bold('Warning: ') +\n        chalk.bold(`Reverting webpack devtool to '${devtool}'.\\n`) +\n        'Changing the webpack devtool in development mode will cause severe performance regressions.\\n' +\n        'Read more: https://nextjs.org/docs/messages/improper-devtool'\n    )\n  }\n)\n\nfunction parseJsonFile(filePath: string) {\n  const JSON5 = require('next/dist/compiled/json5')\n  const contents = readFileSync(filePath, 'utf8')\n\n  // Special case an empty file\n  if (contents.trim() === '') {\n    return {}\n  }\n\n  try {\n    return JSON5.parse(contents)\n  } catch (err) {\n    const codeFrame = codeFrameColumns(\n      String(contents),\n      { start: { line: err.lineNumber, column: err.columnNumber } },\n      { message: err.message, highlightCode: true }\n    )\n    throw new Error(`Failed to parse \"${filePath}\":\\n${codeFrame}`)\n  }\n}\n\nfunction getOptimizedAliases(isServer: boolean): { [pkg: string]: string } {\n  if (isServer) {\n    return {}\n  }\n\n  const stubWindowFetch = path.join(__dirname, 'polyfills', 'fetch', 'index.js')\n  const stubObjectAssign = path.join(__dirname, 'polyfills', 'object-assign.js')\n\n  const shimAssign = path.join(__dirname, 'polyfills', 'object.assign')\n  return Object.assign(\n    {},\n    {\n      unfetch$: stubWindowFetch,\n      'isomorphic-unfetch$': stubWindowFetch,\n      'whatwg-fetch$': path.join(\n        __dirname,\n        'polyfills',\n        'fetch',\n        'whatwg-fetch.js'\n      ),\n    },\n    {\n      'object-assign$': stubObjectAssign,\n\n      // Stub Package: object.assign\n      'object.assign/auto': path.join(shimAssign, 'auto.js'),\n      'object.assign/implementation': path.join(\n        shimAssign,\n        'implementation.js'\n      ),\n      'object.assign$': path.join(shimAssign, 'index.js'),\n      'object.assign/polyfill': path.join(shimAssign, 'polyfill.js'),\n      'object.assign/shim': path.join(shimAssign, 'shim.js'),\n\n      // Replace: full URL polyfill with platform-based polyfill\n      url: require.resolve('native-url'),\n    }\n  )\n}\n\ntype ClientEntries = {\n  [key: string]: string | string[]\n}\n\nexport function attachReactRefresh(\n  webpackConfig: webpack.Configuration,\n  targetLoader: webpack.RuleSetUseItem\n) {\n  let injections = 0\n  const reactRefreshLoaderName = '@next/react-refresh-utils/loader'\n  const reactRefreshLoader = require.resolve(reactRefreshLoaderName)\n  webpackConfig.module?.rules.forEach((rule) => {\n    const curr = rule.use\n    // When the user has configured `defaultLoaders.babel` for a input file:\n    if (curr === targetLoader) {\n      ++injections\n      rule.use = [reactRefreshLoader, curr as webpack.RuleSetUseItem]\n    } else if (\n      Array.isArray(curr) &&\n      curr.some((r) => r === targetLoader) &&\n      // Check if loader already exists:\n      !curr.some(\n        (r) => r === reactRefreshLoader || r === reactRefreshLoaderName\n      )\n    ) {\n      ++injections\n      const idx = curr.findIndex((r) => r === targetLoader)\n      // Clone to not mutate user input\n      rule.use = [...curr]\n\n      // inject / input: [other, babel] output: [other, refresh, babel]:\n      rule.use.splice(idx, 0, reactRefreshLoader)\n    }\n  })\n\n  if (injections) {\n    Log.info(\n      `automatically enabled Fast Refresh for ${injections} custom loader${\n        injections > 1 ? 's' : ''\n      }`\n    )\n  }\n}\n\nconst WEBPACK_RESOLVE_OPTIONS = {\n  // This always uses commonjs resolving, assuming API is identical\n  // between ESM and CJS in a package\n  // Otherwise combined ESM+CJS packages will never be external\n  // as resolving mismatch would lead to opt-out from being external.\n  dependencyType: 'commonjs',\n}\n\nconst NODE_RESOLVE_OPTIONS = {\n  dependencyType: 'commonjs',\n  modules: ['node_modules'],\n  alias: false,\n  fallback: false,\n  exportsFields: ['exports'],\n  importsFields: ['imports'],\n  conditionNames: ['node', 'require', 'module'],\n  descriptionFiles: ['package.json'],\n  extensions: ['.js', '.json', '.node'],\n  enforceExtensions: false,\n  symlinks: true,\n  mainFields: ['main'],\n  mainFiles: ['index'],\n  roots: [],\n  fullySpecified: false,\n  preferRelative: false,\n  preferAbsolute: false,\n  restrictions: [],\n}\n\nexport default async function getBaseWebpackConfig(\n  dir: string,\n  {\n    buildId,\n    config,\n    dev = false,\n    isServer = false,\n    pagesDir,\n    target = 'server',\n    reactProductionProfiling = false,\n    entrypoints,\n    rewrites,\n    isDevFallback = false,\n  }: {\n    buildId: string\n    config: NextConfig\n    dev?: boolean\n    isServer?: boolean\n    pagesDir: string\n    target?: string\n    reactProductionProfiling?: boolean\n    entrypoints: WebpackEntrypoints\n    rewrites: CustomRoutes['rewrites']\n    isDevFallback?: boolean\n  }\n): Promise<webpack.Configuration> {\n  const hasRewrites =\n    rewrites.beforeFiles.length > 0 ||\n    rewrites.afterFiles.length > 0 ||\n    rewrites.fallback.length > 0\n\n  const reactVersion = await getPackageVersion({ cwd: dir, name: 'react' })\n  const hasReactRefresh: boolean = dev && !isServer\n  const hasJsxRuntime: boolean =\n    Boolean(reactVersion) &&\n    // 17.0.0-rc.0 had a breaking change not compatible with Next.js, but was\n    // fixed in rc.1.\n    semver.gte(reactVersion!, '17.0.0-rc.1')\n\n  const babelConfigFile = await [\n    '.babelrc',\n    '.babelrc.json',\n    '.babelrc.js',\n    '.babelrc.mjs',\n    '.babelrc.cjs',\n    'babel.config.js',\n    'babel.config.json',\n    'babel.config.mjs',\n    'babel.config.cjs',\n  ].reduce(async (memo: Promise<string | undefined>, filename) => {\n    const configFilePath = path.join(dir, filename)\n    return (\n      (await memo) ||\n      ((await fileExists(configFilePath)) ? configFilePath : undefined)\n    )\n  }, Promise.resolve(undefined))\n\n  const distDir = path.join(dir, config.distDir)\n\n  const babelLoader = config.experimental.turboMode\n    ? require.resolve('./babel/loader/index')\n    : 'next-babel-loader'\n  const defaultLoaders = {\n    babel: {\n      loader: babelLoader,\n      options: {\n        configFile: babelConfigFile,\n        isServer,\n        distDir,\n        pagesDir,\n        cwd: dir,\n        // Webpack 5 has a built-in loader cache\n        cache: !isWebpack5,\n        development: dev,\n        hasReactRefresh,\n        hasJsxRuntime,\n      },\n    },\n    // Backwards compat\n    hotSelfAccept: {\n      loader: 'noop-loader',\n    },\n  }\n\n  const babelIncludeRegexes: RegExp[] = [\n    /next[\\\\/]dist[\\\\/]next-server[\\\\/]lib/,\n    /next[\\\\/]dist[\\\\/]client/,\n    /next[\\\\/]dist[\\\\/]pages/,\n    /[\\\\/](strip-ansi|ansi-regex)[\\\\/]/,\n  ]\n\n  // Support for NODE_PATH\n  const nodePathList = (process.env.NODE_PATH || '')\n    .split(process.platform === 'win32' ? ';' : ':')\n    .filter((p) => !!p)\n\n  const isServerless = target === 'serverless'\n  const isServerlessTrace = target === 'experimental-serverless-trace'\n  // Intentionally not using isTargetLikeServerless helper\n  const isLikeServerless = isServerless || isServerlessTrace\n\n  const outputDir = isLikeServerless ? SERVERLESS_DIRECTORY : SERVER_DIRECTORY\n  const outputPath = path.join(distDir, isServer ? outputDir : '')\n  const totalPages = Object.keys(entrypoints).length\n  const clientEntries = !isServer\n    ? ({\n        // Backwards compatibility\n        'main.js': [],\n        ...(dev\n          ? {\n              [CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH]: require.resolve(\n                `@next/react-refresh-utils/runtime`\n              ),\n              [CLIENT_STATIC_FILES_RUNTIME_AMP]:\n                `./` +\n                relativePath(\n                  dir,\n                  pathJoin(NEXT_PROJECT_ROOT_DIST_CLIENT, 'dev', 'amp-dev')\n                ).replace(/\\\\/g, '/'),\n            }\n          : {}),\n        [CLIENT_STATIC_FILES_RUNTIME_MAIN]:\n          `./` +\n          path\n            .relative(\n              dir,\n              path.join(\n                NEXT_PROJECT_ROOT_DIST_CLIENT,\n                dev ? `next-dev.js` : 'next.js'\n              )\n            )\n            .replace(/\\\\/g, '/'),\n        [CLIENT_STATIC_FILES_RUNTIME_POLYFILLS]: path.join(\n          NEXT_PROJECT_ROOT_DIST_CLIENT,\n          'polyfills.js'\n        ),\n      } as ClientEntries)\n    : undefined\n\n  let typeScriptPath: string | undefined\n  try {\n    typeScriptPath = require.resolve('typescript', { paths: [dir] })\n  } catch (_) {}\n  const tsConfigPath = path.join(dir, 'tsconfig.json')\n  const useTypeScript = Boolean(\n    typeScriptPath && (await fileExists(tsConfigPath))\n  )\n\n  let jsConfig\n  // jsconfig is a subset of tsconfig\n  if (useTypeScript) {\n    const ts = (await import(typeScriptPath!)) as typeof import('typescript')\n    const tsConfig = await getTypeScriptConfiguration(ts, tsConfigPath, true)\n    jsConfig = { compilerOptions: tsConfig.options }\n  }\n\n  const jsConfigPath = path.join(dir, 'jsconfig.json')\n  if (!useTypeScript && (await fileExists(jsConfigPath))) {\n    jsConfig = parseJsonFile(jsConfigPath)\n  }\n\n  let resolvedBaseUrl\n  if (jsConfig?.compilerOptions?.baseUrl) {\n    resolvedBaseUrl = path.resolve(dir, jsConfig.compilerOptions.baseUrl)\n  }\n\n  function getReactProfilingInProduction() {\n    if (reactProductionProfiling) {\n      return {\n        'react-dom$': 'react-dom/profiling',\n        'scheduler/tracing': 'scheduler/tracing-profiling',\n      }\n    }\n  }\n\n  const clientResolveRewrites = require.resolve(\n    '../next-server/lib/router/utils/resolve-rewrites'\n  )\n  const clientResolveRewritesNoop = require.resolve(\n    '../next-server/lib/router/utils/resolve-rewrites-noop'\n  )\n\n  const resolveConfig = {\n    // Disable .mjs for node_modules bundling\n    extensions: isServer\n      ? [\n          '.js',\n          '.mjs',\n          ...(useTypeScript ? ['.tsx', '.ts'] : []),\n          '.jsx',\n          '.json',\n          '.wasm',\n        ]\n      : [\n          '.mjs',\n          '.js',\n          ...(useTypeScript ? ['.tsx', '.ts'] : []),\n          '.jsx',\n          '.json',\n          '.wasm',\n        ],\n    modules: [\n      'node_modules',\n      ...nodePathList, // Support for NODE_PATH environment variable\n    ],\n    alias: {\n      next: NEXT_PROJECT_ROOT,\n      [PAGES_DIR_ALIAS]: pagesDir,\n      [DOT_NEXT_ALIAS]: distDir,\n      ...getOptimizedAliases(isServer),\n      ...getReactProfilingInProduction(),\n      [clientResolveRewrites]: hasRewrites\n        ? clientResolveRewrites\n        : // With webpack 5 an alias can be pointed to false to noop\n        isWebpack5\n        ? false\n        : clientResolveRewritesNoop,\n    },\n    ...(isWebpack5 && !isServer\n      ? {\n          // Full list of old polyfills is accessible here:\n          // https://github.com/webpack/webpack/blob/2a0536cf510768111a3a6dceeb14cb79b9f59273/lib/ModuleNotFoundError.js#L13-L42\n          fallback: {\n            assert: require.resolve('assert/'),\n            buffer: require.resolve('buffer/'),\n            constants: require.resolve('constants-browserify'),\n            crypto: require.resolve('crypto-browserify'),\n            domain: require.resolve('domain-browser'),\n            http: require.resolve('stream-http'),\n            https: require.resolve('https-browserify'),\n            os: require.resolve('os-browserify/browser'),\n            path: require.resolve('path-browserify'),\n            punycode: require.resolve('punycode'),\n            process: require.resolve('process/browser'),\n            // Handled in separate alias\n            querystring: require.resolve('querystring-es3'),\n            stream: require.resolve('stream-browserify'),\n            string_decoder: require.resolve('string_decoder'),\n            sys: require.resolve('util/'),\n            timers: require.resolve('timers-browserify'),\n            tty: require.resolve('tty-browserify'),\n            // Handled in separate alias\n            // url: require.resolve('url/'),\n            util: require.resolve('util/'),\n            vm: require.resolve('vm-browserify'),\n            zlib: require.resolve('browserify-zlib'),\n          },\n        }\n      : undefined),\n    mainFields: isServer ? ['main', 'module'] : ['browser', 'module', 'main'],\n    plugins: isWebpack5\n      ? // webpack 5+ has the PnP resolver built-in by default:\n        []\n      : [require('pnp-webpack-plugin')],\n  }\n\n  const terserOptions: any = {\n    parse: {\n      ecma: 8,\n    },\n    compress: {\n      ecma: 5,\n      warnings: false,\n      // The following two options are known to break valid JavaScript code\n      comparisons: false,\n      inline: 2, // https://github.com/vercel/next.js/issues/7178#issuecomment-493048965\n    },\n    mangle: { safari10: true },\n    output: {\n      ecma: 5,\n      safari10: true,\n      comments: false,\n      // Fixes usage of Emoji and certain Regex\n      ascii_only: true,\n    },\n  }\n\n  const isModuleCSS = (module: { type: string }): boolean => {\n    return (\n      // mini-css-extract-plugin\n      module.type === `css/mini-extract` ||\n      // extract-css-chunks-webpack-plugin (old)\n      module.type === `css/extract-chunks` ||\n      // extract-css-chunks-webpack-plugin (new)\n      module.type === `css/extract-css-chunks`\n    )\n  }\n\n  // Contains various versions of the Webpack SplitChunksPlugin used in different build types\n  const splitChunksConfigs: {\n    [propName: string]: webpack.Options.SplitChunksOptions\n  } = {\n    dev: {\n      cacheGroups: {\n        default: false,\n        vendors: false,\n      },\n    },\n    prodGranular: {\n      // Keep main and _app chunks unsplitted in webpack 5\n      // as we don't need a separate vendor chunk from that\n      // and all other chunk depend on them so there is no\n      // duplication that need to be pulled out.\n      chunks: isWebpack5\n        ? (chunk) => !/^(polyfills|main|pages\\/_app)$/.test(chunk.name)\n        : 'all',\n      cacheGroups: {\n        framework: {\n          chunks: 'all',\n          name: 'framework',\n          // This regex ignores nested copies of framework libraries so they're\n          // bundled with their issuer.\n          // https://github.com/vercel/next.js/pull/9012\n          test: /(?<!node_modules.*)[\\\\/]node_modules[\\\\/](react|react-dom|scheduler|prop-types|use-subscription)[\\\\/]/,\n          priority: 40,\n          // Don't let webpack eliminate this chunk (prevents this chunk from\n          // becoming a part of the commons chunk)\n          enforce: true,\n        },\n        lib: {\n          test(module: {\n            size: Function\n            nameForCondition: Function\n          }): boolean {\n            return (\n              module.size() > 160000 &&\n              /node_modules[/\\\\]/.test(module.nameForCondition() || '')\n            )\n          },\n          name(module: {\n            type: string\n            libIdent?: Function\n            updateHash: (hash: crypto.Hash) => void\n          }): string {\n            const hash = crypto.createHash('sha1')\n            if (isModuleCSS(module)) {\n              module.updateHash(hash)\n            } else {\n              if (!module.libIdent) {\n                throw new Error(\n                  `Encountered unknown module type: ${module.type}. Please open an issue.`\n                )\n              }\n\n              hash.update(module.libIdent({ context: dir }))\n            }\n\n            return hash.digest('hex').substring(0, 8)\n          },\n          priority: 30,\n          minChunks: 1,\n          reuseExistingChunk: true,\n        },\n        commons: {\n          name: 'commons',\n          minChunks: totalPages,\n          priority: 20,\n        },\n        ...(isWebpack5\n          ? undefined\n          : {\n              default: false,\n              vendors: false,\n              shared: {\n                name(module, chunks) {\n                  return (\n                    crypto\n                      .createHash('sha1')\n                      .update(\n                        chunks.reduce(\n                          (acc: string, chunk: webpack.compilation.Chunk) => {\n                            return acc + chunk.name\n                          },\n                          ''\n                        )\n                      )\n                      .digest('hex') + (isModuleCSS(module) ? '_CSS' : '')\n                  )\n                },\n                priority: 10,\n                minChunks: 2,\n                reuseExistingChunk: true,\n              },\n            }),\n      },\n      maxInitialRequests: 25,\n      minSize: 20000,\n    },\n  }\n\n  // Select appropriate SplitChunksPlugin config for this build\n  let splitChunksConfig: webpack.Options.SplitChunksOptions\n  if (dev) {\n    splitChunksConfig = splitChunksConfigs.dev\n  } else {\n    splitChunksConfig = splitChunksConfigs.prodGranular\n  }\n\n  const crossOrigin = config.crossOrigin\n\n  let customAppFile: string | null = await findPageFile(\n    pagesDir,\n    '/_app',\n    config.pageExtensions\n  )\n  if (customAppFile) {\n    customAppFile = path.resolve(path.join(pagesDir, customAppFile))\n  }\n\n  const conformanceConfig = Object.assign(\n    {\n      ReactSyncScriptsConformanceCheck: {\n        enabled: true,\n      },\n      MinificationConformanceCheck: {\n        enabled: true,\n      },\n      DuplicatePolyfillsConformanceCheck: {\n        enabled: true,\n        BlockedAPIToBePolyfilled: Object.assign(\n          [],\n          ['fetch'],\n          config.conformance?.DuplicatePolyfillsConformanceCheck\n            ?.BlockedAPIToBePolyfilled || []\n        ),\n      },\n      GranularChunksConformanceCheck: {\n        enabled: true,\n      },\n    },\n    config.conformance\n  )\n\n  async function handleExternals(\n    context: string,\n    request: string,\n    getResolve: (\n      options: any\n    ) => (resolveContext: string, resolveRequest: string) => Promise<string>\n  ) {\n    // We need to externalize internal requests for files intended to\n    // not be bundled.\n\n    const isLocal: boolean =\n      request.startsWith('.') ||\n      // Always check for unix-style path, as webpack sometimes\n      // normalizes as posix.\n      path.posix.isAbsolute(request) ||\n      // When on Windows, we also want to check for Windows-specific\n      // absolute paths.\n      (process.platform === 'win32' && path.win32.isAbsolute(request))\n\n    // Relative requires don't need custom resolution, because they\n    // are relative to requests we've already resolved here.\n    // Absolute requires (require('/foo')) are extremely uncommon, but\n    // also have no need for customization as they're already resolved.\n    if (isLocal) {\n      if (!/[/\\\\]next-server[/\\\\]/.test(request)) {\n        return\n      }\n    } else {\n      if (/^(?:next$|react(?:$|\\/))/.test(request)) {\n        return `commonjs ${request}`\n      }\n\n      const notExternalModules = /^(?:private-next-pages\\/|next\\/(?:dist\\/pages\\/|(?:app|document|link|image|constants)$)|string-hash$)/\n      if (notExternalModules.test(request)) {\n        return\n      }\n    }\n\n    const resolve = getResolve(WEBPACK_RESOLVE_OPTIONS)\n\n    // Resolve the import with the webpack provided context, this\n    // ensures we're resolving the correct version when multiple\n    // exist.\n    let res: string\n    try {\n      res = await resolve(context, request)\n    } catch (err) {\n      // If the request cannot be resolved, we need to tell webpack to\n      // \"bundle\" it so that webpack shows an error (that it cannot be\n      // resolved).\n      return\n    }\n\n    // Same as above, if the request cannot be resolved we need to have\n    // webpack \"bundle\" it so it surfaces the not found error.\n    if (!res) {\n      return\n    }\n\n    if (isLocal) {\n      // we need to process next-server/lib/router/router so that\n      // the DefinePlugin can inject process.env values\n      const isNextExternal = /next[/\\\\]dist[/\\\\]next-server[/\\\\](?!lib[/\\\\]router[/\\\\]router)/.test(\n        res\n      )\n\n      if (isNextExternal) {\n        // Generate Next.js external import\n        const externalRequest = path.posix.join(\n          'next',\n          'dist',\n          path\n            .relative(\n              // Root of Next.js package:\n              path.join(__dirname, '..'),\n              res\n            )\n            // Windows path normalization\n            .replace(/\\\\/g, '/')\n        )\n        return `commonjs ${externalRequest}`\n      } else {\n        return\n      }\n    }\n\n    // Bundled Node.js code is relocated without its node_modules tree.\n    // This means we need to make sure its request resolves to the same\n    // package that'll be available at runtime. If it's not identical,\n    // we need to bundle the code (even if it _should_ be external).\n    let baseRes: string | null\n    try {\n      const baseResolve = getResolve(NODE_RESOLVE_OPTIONS)\n      baseRes = await baseResolve(dir, request)\n    } catch (err) {\n      baseRes = null\n    }\n\n    // Same as above: if the package, when required from the root,\n    // would be different from what the real resolution would use, we\n    // cannot externalize it.\n    // if res or baseRes are symlinks they could point to the the same file,\n    // but the resolver will resolve symlinks so this is already handled\n    if (baseRes !== res) {\n      return\n    }\n\n    // Default pages have to be transpiled\n    if (\n      res.match(/[/\\\\]next[/\\\\]dist[/\\\\]/) ||\n      // This is the @babel/plugin-transform-runtime \"helpers: true\" option\n      res.match(/node_modules[/\\\\]@babel[/\\\\]runtime[/\\\\]/)\n    ) {\n      return\n    }\n\n    // Webpack itself has to be compiled because it doesn't always use module relative paths\n    if (\n      res.match(/node_modules[/\\\\]webpack/) ||\n      res.match(/node_modules[/\\\\]css-loader/)\n    ) {\n      return\n    }\n\n    // Anything else that is standard JavaScript within `node_modules`\n    // can be externalized.\n    if (/node_modules[/\\\\].*\\.c?js$/.test(res)) {\n      return `commonjs ${request}`\n    }\n\n    // Default behavior: bundle the code!\n  }\n\n  const emacsLockfilePattern = '**/.#*'\n\n  let webpackConfig: webpack.Configuration = {\n    externals: !isServer\n      ? // make sure importing \"next\" is handled gracefully for client\n        // bundles in case a user imported types and it wasn't removed\n        // TODO: should we warn/error for this instead?\n        ['next']\n      : !isServerless\n      ? [\n          isWebpack5\n            ? ({\n                context,\n                request,\n                getResolve,\n              }: {\n                context: string\n                request: string\n                getResolve: (\n                  options: any\n                ) => (\n                  resolveContext: string,\n                  resolveRequest: string\n                ) => Promise<string>\n              }) => handleExternals(context, request, getResolve)\n            : (\n                context: string,\n                request: string,\n                callback: (err?: Error, result?: string | undefined) => void\n              ) =>\n                handleExternals(\n                  context,\n                  request,\n                  () => (resolveContext: string, requestToResolve: string) =>\n                    new Promise((resolve) =>\n                      resolve(\n                        require.resolve(requestToResolve, {\n                          paths: [resolveContext],\n                        })\n                      )\n                    )\n                ).then((result) => callback(undefined, result), callback),\n        ]\n      : [\n          // When the 'serverless' target is used all node_modules will be compiled into the output bundles\n          // So that the 'serverless' bundles have 0 runtime dependencies\n          'next/dist/compiled/@ampproject/toolbox-optimizer', // except this one\n\n          // Mark this as external if not enabled so it doesn't cause a\n          // webpack error from being missing\n          ...(config.experimental.optimizeCss ? [] : ['critters']),\n        ],\n    optimization: {\n      // Webpack 5 uses a new property for the same functionality\n      ...(isWebpack5 ? { emitOnErrors: !dev } : { noEmitOnErrors: dev }),\n      checkWasmTypes: false,\n      nodeEnv: false,\n      splitChunks: isServer\n        ? isWebpack5\n          ? ({\n              filename: '[name].js',\n              // allow to split entrypoints\n              chunks: 'all',\n              // size of files is not so relevant for server build\n              // we want to prefer deduplication to load less code\n              minSize: 1000,\n            } as any)\n          : false\n        : splitChunksConfig,\n      runtimeChunk: isServer\n        ? isWebpack5 && !isLikeServerless\n          ? { name: 'webpack-runtime' }\n          : undefined\n        : { name: CLIENT_STATIC_FILES_RUNTIME_WEBPACK },\n      minimize: !(dev || isServer),\n      minimizer: [\n        // Minify JavaScript\n        (compiler: webpack.Compiler) => {\n          // @ts-ignore No typings yet\n          const {\n            TerserPlugin,\n          } = require('./webpack/plugins/terser-webpack-plugin/src/index.js')\n          new TerserPlugin({\n            cacheDir: path.join(distDir, 'cache', 'next-minifier'),\n            parallel: config.experimental.cpus,\n            terserOptions,\n          }).apply(compiler)\n        },\n        // Minify CSS\n        (compiler: webpack.Compiler) => {\n          const {\n            CssMinimizerPlugin,\n          } = require('./webpack/plugins/css-minimizer-plugin')\n          new CssMinimizerPlugin({\n            postcssOptions: {\n              map: {\n                // `inline: false` generates the source map in a separate file.\n                // Otherwise, the CSS file is needlessly large.\n                inline: false,\n                // `annotation: false` skips appending the `sourceMappingURL`\n                // to the end of the CSS file. Webpack already handles this.\n                annotation: false,\n              },\n            },\n          }).apply(compiler)\n        },\n      ],\n    },\n    context: dir,\n    node: {\n      setImmediate: false,\n    },\n    // Kept as function to be backwards compatible\n    // @ts-ignore TODO webpack 5 typings needed\n    entry: async () => {\n      return {\n        ...(clientEntries ? clientEntries : {}),\n        ...entrypoints,\n      }\n    },\n    watchOptions: {\n      aggregateTimeout: 5,\n      ignored: [\n        '**/.git/**',\n        '**/node_modules/**',\n        '**/.next/**',\n        // can be removed after https://github.com/paulmillr/chokidar/issues/955 is released\n        emacsLockfilePattern,\n      ],\n    },\n    output: {\n      ...(isWebpack5\n        ? {\n            environment: {\n              arrowFunction: false,\n              bigIntLiteral: false,\n              const: false,\n              destructuring: false,\n              dynamicImport: false,\n              forOf: false,\n              module: false,\n            },\n          }\n        : {}),\n      path:\n        isServer && isWebpack5 && !dev\n          ? path.join(outputPath, 'chunks')\n          : outputPath,\n      // On the server we don't use hashes\n      filename: isServer\n        ? isWebpack5 && !dev\n          ? '../[name].js'\n          : '[name].js'\n        : `static/chunks/${isDevFallback ? 'fallback/' : ''}[name]${\n            dev ? '' : isWebpack5 ? '-[contenthash]' : '-[chunkhash]'\n          }.js`,\n      library: isServer ? undefined : '_N_E',\n      libraryTarget: isServer ? 'commonjs2' : 'assign',\n      hotUpdateChunkFilename: isWebpack5\n        ? 'static/webpack/[id].[fullhash].hot-update.js'\n        : 'static/webpack/[id].[hash].hot-update.js',\n      hotUpdateMainFilename: isWebpack5\n        ? 'static/webpack/[fullhash].hot-update.json'\n        : 'static/webpack/[hash].hot-update.json',\n      // This saves chunks with the name given via `import()`\n      chunkFilename: isServer\n        ? '[name].js'\n        : `static/chunks/${isDevFallback ? 'fallback/' : ''}${\n            dev ? '[name]' : '[name].[contenthash]'\n          }.js`,\n      strictModuleExceptionHandling: true,\n      crossOriginLoading: crossOrigin,\n      futureEmitAssets: !dev,\n      webassemblyModuleFilename: 'static/wasm/[modulehash].wasm',\n    },\n    performance: false,\n    resolve: resolveConfig,\n    resolveLoader: {\n      // The loaders Next.js provides\n      alias: [\n        'emit-file-loader',\n        'error-loader',\n        'next-babel-loader',\n        'next-client-pages-loader',\n        'next-serverless-loader',\n        'noop-loader',\n        'next-style-loader',\n      ].reduce((alias, loader) => {\n        // using multiple aliases to replace `resolveLoader.modules`\n        alias[loader] = path.join(__dirname, 'webpack', 'loaders', loader)\n\n        return alias\n      }, {} as Record<string, string>),\n      modules: [\n        'node_modules',\n        ...nodePathList, // Support for NODE_PATH environment variable\n      ],\n      plugins: isWebpack5 ? [] : [require('pnp-webpack-plugin')],\n    },\n    module: {\n      rules: [\n        ...(isWebpack5\n          ? [\n              // TODO: FIXME: do NOT webpack 5 support with this\n              // x-ref: https://github.com/webpack/webpack/issues/11467\n              {\n                test: /\\.m?js/,\n                resolve: {\n                  fullySpecified: false,\n                },\n              } as any,\n            ]\n          : []),\n        {\n          test: /\\.(tsx|ts|js|mjs|jsx)$/,\n          ...(config.experimental.externalDir\n            ? // Allowing importing TS/TSX files from outside of the root dir.\n              {}\n            : { include: [dir, ...babelIncludeRegexes] }),\n          exclude: (excludePath: string) => {\n            if (babelIncludeRegexes.some((r) => r.test(excludePath))) {\n              return false\n            }\n            return /node_modules/.test(excludePath)\n          },\n          use: hasReactRefresh\n            ? [\n                require.resolve('@next/react-refresh-utils/loader'),\n                defaultLoaders.babel,\n              ]\n            : defaultLoaders.babel,\n        },\n      ].filter(Boolean),\n    },\n    plugins: [\n      hasReactRefresh && new ReactRefreshWebpackPlugin(webpack),\n      // Makes sure `Buffer` and `process` are polyfilled in client-side bundles (same behavior as webpack 4)\n      isWebpack5 &&\n        !isServer &&\n        new webpack.ProvidePlugin({\n          Buffer: [require.resolve('buffer'), 'Buffer'],\n          process: [require.resolve('process')],\n        }),\n      // This plugin makes sure `output.filename` is used for entry chunks\n      !isWebpack5 && new ChunkNamesPlugin(),\n      new webpack.DefinePlugin({\n        ...Object.keys(process.env).reduce(\n          (prev: { [key: string]: string }, key: string) => {\n            if (key.startsWith('NEXT_PUBLIC_')) {\n              prev[`process.env.${key}`] = JSON.stringify(process.env[key]!)\n            }\n            return prev\n          },\n          {}\n        ),\n        ...Object.keys(config.env).reduce((acc, key) => {\n          if (/^(?:NODE_.+)|^(?:__.+)$/i.test(key)) {\n            throw new Error(\n              `The key \"${key}\" under \"env\" in next.config.js is not allowed. https://nextjs.org/docs/messages/env-key-not-allowed`\n            )\n          }\n\n          return {\n            ...acc,\n            [`process.env.${key}`]: JSON.stringify(config.env[key]),\n          }\n        }, {}),\n        // TODO: enforce `NODE_ENV` on `process.env`, and add a test:\n        'process.env.NODE_ENV': JSON.stringify(\n          dev ? 'development' : 'production'\n        ),\n        'process.env.__NEXT_CROSS_ORIGIN': JSON.stringify(crossOrigin),\n        'process.browser': JSON.stringify(!isServer),\n        'process.env.__NEXT_TEST_MODE': JSON.stringify(\n          process.env.__NEXT_TEST_MODE\n        ),\n        // This is used in client/dev-error-overlay/hot-dev-client.js to replace the dist directory\n        ...(dev && !isServer\n          ? {\n              'process.env.__NEXT_DIST_DIR': JSON.stringify(distDir),\n            }\n          : {}),\n        'process.env.__NEXT_TRAILING_SLASH': JSON.stringify(\n          config.trailingSlash\n        ),\n        'process.env.__NEXT_BUILD_INDICATOR': JSON.stringify(\n          config.devIndicators.buildActivity\n        ),\n        'process.env.__NEXT_PLUGINS': JSON.stringify(\n          config.experimental.plugins\n        ),\n        'process.env.__NEXT_STRICT_MODE': JSON.stringify(\n          config.reactStrictMode\n        ),\n        'process.env.__NEXT_REACT_ROOT': JSON.stringify(\n          config.experimental.reactRoot\n        ),\n        'process.env.__NEXT_OPTIMIZE_FONTS': JSON.stringify(\n          config.optimizeFonts && !dev\n        ),\n        'process.env.__NEXT_OPTIMIZE_IMAGES': JSON.stringify(\n          config.experimental.optimizeImages\n        ),\n        'process.env.__NEXT_OPTIMIZE_CSS': JSON.stringify(\n          config.experimental.optimizeCss && !dev\n        ),\n        'process.env.__NEXT_SCRIPT_LOADER': JSON.stringify(\n          !!config.experimental.scriptLoader\n        ),\n        'process.env.__NEXT_SCROLL_RESTORATION': JSON.stringify(\n          config.experimental.scrollRestoration\n        ),\n        'process.env.__NEXT_IMAGE_OPTS': JSON.stringify({\n          deviceSizes: config.images.deviceSizes,\n          imageSizes: config.images.imageSizes,\n          path: config.images.path,\n          loader: config.images.loader,\n          ...(dev\n            ? {\n                // pass domains in development to allow validating on the client\n                domains: config.images.domains,\n              }\n            : {}),\n          enableBlurryPlaceholder: config.experimental.enableBlurryPlaceholder,\n        }),\n        'process.env.__NEXT_ROUTER_BASEPATH': JSON.stringify(config.basePath),\n        'process.env.__NEXT_HAS_REWRITES': JSON.stringify(hasRewrites),\n        'process.env.__NEXT_I18N_SUPPORT': JSON.stringify(!!config.i18n),\n        'process.env.__NEXT_I18N_DOMAINS': JSON.stringify(config.i18n?.domains),\n        'process.env.__NEXT_ANALYTICS_ID': JSON.stringify(config.analyticsId),\n        ...(isServer\n          ? {\n              // Fix bad-actors in the npm ecosystem (e.g. `node-formidable`)\n              // This is typically found in unmaintained modules from the\n              // pre-webpack era (common in server-side code)\n              'global.GENTLY': JSON.stringify(false),\n            }\n          : undefined),\n        // stub process.env with proxy to warn a missing value is\n        // being accessed in development mode\n        ...(config.experimental.pageEnv && dev\n          ? {\n              'process.env': `\n            new Proxy(${isServer ? 'process.env' : '{}'}, {\n              get(target, prop) {\n                if (typeof target[prop] === 'undefined') {\n                  console.warn(\\`An environment variable (\\${prop}) that was not provided in the environment was accessed.\\nSee more info here: https://nextjs.org/docs/messages/missing-env-value\\`)\n                }\n                return target[prop]\n              }\n            })\n          `,\n            }\n          : {}),\n      }),\n      !isServer &&\n        new ReactLoadablePlugin({\n          filename: REACT_LOADABLE_MANIFEST,\n          pagesDir,\n        }),\n      !isServer && new DropClientPage(),\n      // Moment.js is an extremely popular library that bundles large locale files\n      // by default due to how Webpack interprets its code. This is a practical\n      // solution that requires the user to opt into importing specific locales.\n      // https://github.com/jmblog/how-to-optimize-momentjs-with-webpack\n      config.future.excludeDefaultMomentLocales &&\n        new webpack.IgnorePlugin({\n          resourceRegExp: /^\\.\\/locale$/,\n          contextRegExp: /moment$/,\n        }),\n      ...(dev\n        ? (() => {\n            // Even though require.cache is server only we have to clear assets from both compilations\n            // This is because the client compilation generates the build manifest that's used on the server side\n            const {\n              NextJsRequireCacheHotReloader,\n            } = require('./webpack/plugins/nextjs-require-cache-hot-reloader')\n            const devPlugins = [new NextJsRequireCacheHotReloader()]\n\n            if (!isServer) {\n              devPlugins.push(new webpack.HotModuleReplacementPlugin())\n            }\n\n            return devPlugins\n          })()\n        : []),\n      // Webpack 5 no longer requires this plugin in production:\n      !isWebpack5 && !dev && new webpack.HashedModuleIdsPlugin(),\n      !dev &&\n        new webpack.IgnorePlugin({\n          resourceRegExp: /react-is/,\n          contextRegExp: /(next-server|next)[\\\\/]dist[\\\\/]/,\n        }),\n      isServerless && isServer && new ServerlessPlugin(),\n      isServer &&\n        new PagesManifestPlugin({ serverless: isLikeServerless, dev }),\n      !isWebpack5 &&\n        target === 'server' &&\n        isServer &&\n        new NextJsSSRModuleCachePlugin({ outputPath }),\n      isServer && new NextJsSsrImportPlugin(),\n      !isServer &&\n        new BuildManifestPlugin({\n          buildId,\n          rewrites,\n          isDevFallback,\n        }),\n      !dev &&\n        !isServer &&\n        config.experimental.stats &&\n        new BuildStatsPlugin({\n          distDir,\n        }),\n      new ProfilingPlugin(),\n      config.optimizeFonts &&\n        !dev &&\n        isServer &&\n        (function () {\n          const {\n            FontStylesheetGatheringPlugin,\n          } = require('./webpack/plugins/font-stylesheet-gathering-plugin') as {\n            FontStylesheetGatheringPlugin: typeof import('./webpack/plugins/font-stylesheet-gathering-plugin').FontStylesheetGatheringPlugin\n          }\n          return new FontStylesheetGatheringPlugin({\n            isLikeServerless,\n          })\n        })(),\n      config.experimental.conformance &&\n        !isWebpack5 &&\n        !dev &&\n        new WebpackConformancePlugin({\n          tests: [\n            !isServer &&\n              conformanceConfig.MinificationConformanceCheck.enabled &&\n              new MinificationConformanceCheck(),\n            conformanceConfig.ReactSyncScriptsConformanceCheck.enabled &&\n              new ReactSyncScriptsConformanceCheck({\n                AllowedSources:\n                  conformanceConfig.ReactSyncScriptsConformanceCheck\n                    .allowedSources || [],\n              }),\n            !isServer &&\n              conformanceConfig.DuplicatePolyfillsConformanceCheck.enabled &&\n              new DuplicatePolyfillsConformanceCheck({\n                BlockedAPIToBePolyfilled:\n                  conformanceConfig.DuplicatePolyfillsConformanceCheck\n                    .BlockedAPIToBePolyfilled,\n              }),\n            !isServer &&\n              conformanceConfig.GranularChunksConformanceCheck.enabled &&\n              new GranularChunksConformanceCheck(\n                splitChunksConfigs.prodGranular\n              ),\n          ].filter(Boolean),\n        }),\n      new WellKnownErrorsPlugin(),\n    ].filter((Boolean as any) as ExcludesFalse),\n  }\n\n  // Support tsconfig and jsconfig baseUrl\n  if (resolvedBaseUrl) {\n    webpackConfig.resolve?.modules?.push(resolvedBaseUrl)\n  }\n\n  if (jsConfig?.compilerOptions?.paths && resolvedBaseUrl) {\n    webpackConfig.resolve?.plugins?.unshift(\n      new JsConfigPathsPlugin(jsConfig.compilerOptions.paths, resolvedBaseUrl)\n    )\n  }\n\n  if (isWebpack5) {\n    // futureEmitAssets is on by default in webpack 5\n    delete webpackConfig.output?.futureEmitAssets\n\n    if (isServer && dev) {\n      // Enable building of client compilation before server compilation in development\n      // @ts-ignore dependencies exists\n      webpackConfig.dependencies = ['client']\n    }\n    // webpack 5 no longer polyfills Node.js modules:\n    if (webpackConfig.node) delete webpackConfig.node.setImmediate\n\n    // Due to bundling of webpack the default values can't be correctly detected\n    // This restores the webpack defaults\n    // @ts-ignore webpack 5\n    webpackConfig.snapshot = {}\n    if (process.versions.pnp === '3') {\n      const match = /^(.+?)[\\\\/]cache[\\\\/]jest-worker-npm-[^\\\\/]+\\.zip[\\\\/]node_modules[\\\\/]/.exec(\n        require.resolve('jest-worker')\n      )\n      if (match) {\n        // @ts-ignore webpack 5\n        webpackConfig.snapshot.managedPaths = [\n          path.resolve(match[1], 'unplugged'),\n        ]\n      }\n    } else {\n      const match = /^(.+?[\\\\/]node_modules)[\\\\/]/.exec(\n        require.resolve('jest-worker')\n      )\n      if (match) {\n        // @ts-ignore webpack 5\n        webpackConfig.snapshot.managedPaths = [match[1]]\n      }\n    }\n    if (process.versions.pnp === '1') {\n      const match = /^(.+?[\\\\/]v4)[\\\\/]npm-jest-worker-[^\\\\/]+-[\\da-f]{40}[\\\\/]node_modules[\\\\/]/.exec(\n        require.resolve('jest-worker')\n      )\n      if (match) {\n        // @ts-ignore webpack 5\n        webpackConfig.snapshot.immutablePaths = [match[1]]\n      }\n    } else if (process.versions.pnp === '3') {\n      const match = /^(.+?)[\\\\/]jest-worker-npm-[^\\\\/]+\\.zip[\\\\/]node_modules[\\\\/]/.exec(\n        require.resolve('jest-worker')\n      )\n      if (match) {\n        // @ts-ignore webpack 5\n        webpackConfig.snapshot.immutablePaths = [match[1]]\n      }\n    }\n\n    if (dev) {\n      if (!webpackConfig.optimization) {\n        webpackConfig.optimization = {}\n      }\n      webpackConfig.optimization.providedExports = false\n      webpackConfig.optimization.usedExports = false\n    }\n\n    const configVars = JSON.stringify({\n      crossOrigin: config.crossOrigin,\n      pageExtensions: config.pageExtensions,\n      trailingSlash: config.trailingSlash,\n      buildActivity: config.devIndicators.buildActivity,\n      plugins: config.experimental.plugins,\n      reactStrictMode: config.reactStrictMode,\n      reactMode: config.experimental.reactMode,\n      optimizeFonts: config.optimizeFonts,\n      optimizeImages: config.experimental.optimizeImages,\n      optimizeCss: config.experimental.optimizeCss,\n      scrollRestoration: config.experimental.scrollRestoration,\n      basePath: config.basePath,\n      pageEnv: config.experimental.pageEnv,\n      excludeDefaultMomentLocales: config.future.excludeDefaultMomentLocales,\n      assetPrefix: config.assetPrefix,\n      disableOptimizedLoading: config.experimental.disableOptimizedLoading,\n      target,\n      reactProductionProfiling,\n      webpack: !!config.webpack,\n      hasRewrites,\n    })\n\n    const cache: any = {\n      type: 'filesystem',\n      // Includes:\n      //  - Next.js version\n      //  - next.config.js keys that affect compilation\n      version: `${process.env.__NEXT_VERSION}|${configVars}`,\n      cacheDirectory: path.join(distDir, 'cache', 'webpack'),\n    }\n\n    // Adds `next.config.js` as a buildDependency when custom webpack config is provided\n    if (config.webpack && config.configFile) {\n      cache.buildDependencies = {\n        config: [config.configFile],\n      }\n    }\n\n    webpackConfig.cache = cache\n\n    if (process.env.NEXT_WEBPACK_LOGGING) {\n      const logInfra = process.env.NEXT_WEBPACK_LOGGING.includes(\n        'infrastructure'\n      )\n      const logProfileClient = process.env.NEXT_WEBPACK_LOGGING.includes(\n        'profile-client'\n      )\n      const logProfileServer = process.env.NEXT_WEBPACK_LOGGING.includes(\n        'profile-server'\n      )\n      const logDefault = !logInfra && !logProfileClient && !logProfileServer\n\n      if (logDefault || logInfra) {\n        // @ts-ignore TODO: remove ignore when webpack 5 is stable\n        webpackConfig.infrastructureLogging = {\n          level: 'verbose',\n          debug: /FileSystemInfo/,\n        }\n      }\n\n      if (\n        logDefault ||\n        (logProfileClient && !isServer) ||\n        (logProfileServer && isServer)\n      ) {\n        webpackConfig.plugins!.push((compiler: webpack.Compiler) => {\n          compiler.hooks.done.tap('next-webpack-logging', (stats) => {\n            console.log(\n              stats.toString({\n                colors: true,\n                // @ts-ignore TODO: remove ignore when webpack 5 is stable\n                logging: logDefault ? 'log' : 'verbose',\n              })\n            )\n          })\n        })\n      }\n\n      if ((logProfileClient && !isServer) || (logProfileServer && isServer)) {\n        webpackConfig.plugins!.push(\n          new webpack.ProgressPlugin({\n            // @ts-ignore TODO: remove ignore when webpack 5 is stable\n            profile: true,\n          })\n        )\n        webpackConfig.profile = true\n      }\n    }\n  }\n\n  webpackConfig = await buildConfiguration(webpackConfig, {\n    rootDirectory: dir,\n    customAppFile,\n    isDevelopment: dev,\n    isServer,\n    assetPrefix: config.assetPrefix || '',\n    sassOptions: config.sassOptions,\n    productionBrowserSourceMaps: config.productionBrowserSourceMaps,\n    future: config.future,\n  })\n\n  let originalDevtool = webpackConfig.devtool\n  if (typeof config.webpack === 'function') {\n    webpackConfig = config.webpack(webpackConfig, {\n      dir,\n      dev,\n      isServer,\n      buildId,\n      config,\n      defaultLoaders,\n      totalPages,\n      webpack,\n    })\n\n    if (!webpackConfig) {\n      throw new Error(\n        'Webpack config is undefined. You may have forgot to return properly from within the \"webpack\" method of your next.config.js.\\n' +\n          'See more info here https://nextjs.org/docs/messages/undefined-webpack-config'\n      )\n    }\n\n    if (dev && originalDevtool !== webpackConfig.devtool) {\n      webpackConfig.devtool = originalDevtool\n      devtoolRevertWarning(originalDevtool)\n    }\n\n    if (typeof (webpackConfig as any).then === 'function') {\n      console.warn(\n        '> Promise returned in next config. https://nextjs.org/docs/messages/promise-in-next-config'\n      )\n    }\n  }\n\n  // Backwards compat with webpack-dev-middleware options object\n  if (typeof config.webpackDevMiddleware === 'function') {\n    const options = config.webpackDevMiddleware({\n      watchOptions: webpackConfig.watchOptions,\n    })\n    if (options.watchOptions) {\n      webpackConfig.watchOptions = options.watchOptions\n    }\n  }\n\n  function canMatchCss(rule: webpack.RuleSetCondition | undefined): boolean {\n    if (!rule) {\n      return false\n    }\n\n    const fileNames = [\n      '/tmp/test.css',\n      '/tmp/test.scss',\n      '/tmp/test.sass',\n      '/tmp/test.less',\n      '/tmp/test.styl',\n    ]\n\n    if (rule instanceof RegExp && fileNames.some((input) => rule.test(input))) {\n      return true\n    }\n\n    if (typeof rule === 'function') {\n      if (\n        fileNames.some((input) => {\n          try {\n            if (rule(input)) {\n              return true\n            }\n          } catch (_) {}\n          return false\n        })\n      ) {\n        return true\n      }\n    }\n\n    if (Array.isArray(rule) && rule.some(canMatchCss)) {\n      return true\n    }\n\n    return false\n  }\n\n  const hasUserCssConfig =\n    webpackConfig.module?.rules.some(\n      (rule) => canMatchCss(rule.test) || canMatchCss(rule.include)\n    ) ?? false\n\n  if (hasUserCssConfig) {\n    // only show warning for one build\n    if (isServer) {\n      console.warn(\n        chalk.yellow.bold('Warning: ') +\n          chalk.bold(\n            'Built-in CSS support is being disabled due to custom CSS configuration being detected.\\n'\n          ) +\n          'See here for more info: https://nextjs.org/docs/messages/built-in-css-disabled\\n'\n      )\n    }\n\n    if (webpackConfig.module?.rules.length) {\n      // Remove default CSS Loader\n      webpackConfig.module.rules = webpackConfig.module.rules.filter(\n        (r) =>\n          !(\n            typeof r.oneOf?.[0]?.options === 'object' &&\n            r.oneOf[0].options.__next_css_remove === true\n          )\n      )\n    }\n    if (webpackConfig.plugins?.length) {\n      // Disable CSS Extraction Plugin\n      webpackConfig.plugins = webpackConfig.plugins.filter(\n        (p) => (p as any).__next_css_remove !== true\n      )\n    }\n    if (webpackConfig.optimization?.minimizer?.length) {\n      // Disable CSS Minifier\n      webpackConfig.optimization.minimizer = webpackConfig.optimization.minimizer.filter(\n        (e) => (e as any).__next_css_remove !== true\n      )\n    }\n  } else if (!config.future.strictPostcssConfiguration) {\n    await __overrideCssConfiguration(dir, !dev, webpackConfig)\n  }\n\n  // Inject missing React Refresh loaders so that development mode is fast:\n  if (hasReactRefresh) {\n    attachReactRefresh(webpackConfig, defaultLoaders.babel)\n  }\n\n  // check if using @zeit/next-typescript and show warning\n  if (\n    isServer &&\n    webpackConfig.module &&\n    Array.isArray(webpackConfig.module.rules)\n  ) {\n    let foundTsRule = false\n\n    webpackConfig.module.rules = webpackConfig.module.rules.filter(\n      (rule): boolean => {\n        if (!(rule.test instanceof RegExp)) return true\n        if ('noop.ts'.match(rule.test) && !'noop.js'.match(rule.test)) {\n          // remove if it matches @zeit/next-typescript\n          foundTsRule = rule.use === defaultLoaders.babel\n          return !foundTsRule\n        }\n        return true\n      }\n    )\n\n    if (foundTsRule) {\n      console.warn(\n        '\\n@zeit/next-typescript is no longer needed since Next.js has built-in support for TypeScript now. Please remove it from your next.config.js and your .babelrc\\n'\n      )\n    }\n  }\n\n  // Patch `@zeit/next-sass`, `@zeit/next-less`, `@zeit/next-stylus` for compatibility\n  if (webpackConfig.module && Array.isArray(webpackConfig.module.rules)) {\n    ;[].forEach.call(webpackConfig.module.rules, function (\n      rule: webpack.RuleSetRule\n    ) {\n      if (!(rule.test instanceof RegExp && Array.isArray(rule.use))) {\n        return\n      }\n\n      const isSass =\n        rule.test.source === '\\\\.scss$' || rule.test.source === '\\\\.sass$'\n      const isLess = rule.test.source === '\\\\.less$'\n      const isCss = rule.test.source === '\\\\.css$'\n      const isStylus = rule.test.source === '\\\\.styl$'\n\n      // Check if the rule we're iterating over applies to Sass, Less, or CSS\n      if (!(isSass || isLess || isCss || isStylus)) {\n        return\n      }\n\n      ;[].forEach.call(rule.use, function (use: webpack.RuleSetUseItem) {\n        if (\n          !(\n            use &&\n            typeof use === 'object' &&\n            // Identify use statements only pertaining to `css-loader`\n            (use.loader === 'css-loader' ||\n              use.loader === 'css-loader/locals') &&\n            use.options &&\n            typeof use.options === 'object' &&\n            // The `minimize` property is a good heuristic that we need to\n            // perform this hack. The `minimize` property was only valid on\n            // old `css-loader` versions. Custom setups (that aren't next-sass,\n            // next-less or next-stylus) likely have the newer version.\n            // We still handle this gracefully below.\n            (Object.prototype.hasOwnProperty.call(use.options, 'minimize') ||\n              Object.prototype.hasOwnProperty.call(\n                use.options,\n                'exportOnlyLocals'\n              ))\n          )\n        ) {\n          return\n        }\n\n        // Try to monkey patch within a try-catch. We shouldn't fail the build\n        // if we cannot pull this off.\n        // The user may not even be using the `next-sass` or `next-less` or\n        // `next-stylus` plugins.\n        // If it does work, great!\n        try {\n          // Resolve the version of `@zeit/next-css` as depended on by the Sass,\n          // Less or Stylus plugin.\n          const correctNextCss = require.resolve('@zeit/next-css', {\n            paths: [\n              isCss\n                ? // Resolve `@zeit/next-css` from the base directory\n                  dir\n                : // Else, resolve it from the specific plugins\n                  require.resolve(\n                    isSass\n                      ? '@zeit/next-sass'\n                      : isLess\n                      ? '@zeit/next-less'\n                      : isStylus\n                      ? '@zeit/next-stylus'\n                      : 'next'\n                  ),\n            ],\n          })\n\n          // If we found `@zeit/next-css` ...\n          if (correctNextCss) {\n            // ... resolve the version of `css-loader` shipped with that\n            // package instead of whichever was hoisted highest in your\n            // `node_modules` tree.\n            const correctCssLoader = require.resolve(use.loader, {\n              paths: [correctNextCss],\n            })\n            if (correctCssLoader) {\n              // We saved the user from a failed build!\n              use.loader = correctCssLoader\n            }\n          }\n        } catch (_) {\n          // The error is not required to be handled.\n        }\n      })\n    })\n  }\n\n  // Backwards compat for `main.js` entry key\n  // and setup of dependencies between entries\n  // we can't do that in the initial entry for\n  // backward-compat reasons\n  const originalEntry: any = webpackConfig.entry\n  if (typeof originalEntry !== 'undefined') {\n    const updatedEntry = async () => {\n      const entry: WebpackEntrypoints =\n        typeof originalEntry === 'function'\n          ? await originalEntry()\n          : originalEntry\n      // Server compilation doesn't have main.js\n      if (\n        clientEntries &&\n        Array.isArray(entry['main.js']) &&\n        entry['main.js'].length > 0\n      ) {\n        const originalFile = clientEntries[\n          CLIENT_STATIC_FILES_RUNTIME_MAIN\n        ] as string\n        entry[CLIENT_STATIC_FILES_RUNTIME_MAIN] = [\n          ...entry['main.js'],\n          originalFile,\n        ]\n      }\n      delete entry['main.js']\n\n      if (isWebpack5 && !isServer) {\n        for (const name of Object.keys(entry)) {\n          if (\n            name === 'polyfills' ||\n            name === 'main' ||\n            name === 'amp' ||\n            name === 'react-refresh'\n          )\n            continue\n          const dependOn =\n            name.startsWith('pages/') && name !== 'pages/_app'\n              ? 'pages/_app'\n              : 'main'\n          const old = entry[name]\n          if (typeof old === 'object' && !Array.isArray(old)) {\n            entry[name] = {\n              dependOn,\n              ...old,\n            }\n          } else {\n            entry[name] = {\n              import: old,\n              dependOn,\n            }\n          }\n        }\n      }\n\n      return entry\n    }\n    // @ts-ignore webpack 5 typings needed\n    webpackConfig.entry = updatedEntry\n  }\n\n  if (!dev) {\n    // entry is always a function\n    webpackConfig.entry = await (webpackConfig.entry as webpack.EntryFunc)()\n  }\n\n  return webpackConfig\n}\n"]}