{"version": 3, "sources": ["../../../../next-server/lib/router/router.ts"], "names": ["detectDomainLocale", "process", "env", "__NEXT_I18N_SUPPORT", "require", "basePath", "__NEXT_ROUTER_BASEPATH", "buildCancellationError", "Object", "assign", "Error", "cancelled", "addPathPrefix", "path", "prefix", "startsWith", "pathNoQueryHash", "substring", "getDomainLocale", "locale", "locales", "domainLocales", "detectedLocale", "detectedDomain", "undefined", "http", "domain", "defaultLocale", "addLocale", "pathname", "pathLower", "toLowerCase", "localeLower", "delLocale", "length", "substr", "queryIndex", "indexOf", "hashIndex", "has<PERSON>ase<PERSON><PERSON>", "addBasePath", "delBase<PERSON><PERSON>", "slice", "isLocalURL", "url", "locationOrigin", "resolved", "URL", "origin", "_", "interpolateAs", "route", "asPathname", "query", "interpolatedRoute", "dynamicRegex", "dynamicGroups", "groups", "dynamicMatches", "params", "keys", "every", "param", "value", "repeat", "optional", "replaced", "Array", "isArray", "replace", "map", "segment", "encodeURIComponent", "join", "result", "omitParmsFromQuery", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "key", "includes", "resolveHref", "currentPath", "href", "resolveAs", "base", "urlAsString", "finalUrl", "interpolatedAs", "searchParams", "hash", "resolvedHref", "strip<PERSON><PERSON>in", "prepareUrlAs", "router", "as", "resolvedAs", "<PERSON><PERSON><PERSON>", "hrefHadO<PERSON>in", "as<PERSON><PERSON><PERSON><PERSON><PERSON>", "preparedUrl", "preparedAs", "resolveDynamicRoute", "pages", "cleanPathname", "some", "page", "re", "test", "manualScrollRestoration", "__NEXT_SCROLL_RESTORATION", "window", "history", "v", "sessionStorage", "setItem", "removeItem", "n", "SSG_DATA_NOT_FOUND", "Symbol", "fetchRetry", "attempts", "fetch", "credentials", "then", "res", "ok", "status", "json", "data", "notFound", "fetchNextData", "dataHref", "isServerRender", "catch", "err", "Router", "constructor", "initialProps", "page<PERSON><PERSON>der", "App", "wrapApp", "Component", "subscription", "<PERSON><PERSON><PERSON><PERSON>", "isPreview", "components", "sdc", "sdr", "sub", "clc", "_bps", "events", "_wrapApp", "isSsr", "_inFlightRoute", "_shallow", "isReady", "isLocaleDomain", "_idx", "onPopState", "e", "state", "changeState", "__N", "forcedScroll", "options", "idx", "JSON", "stringify", "x", "self", "pageXOffset", "y", "pageYOffset", "getItem", "parse", "change", "shallow", "initial", "props", "__N_SSG", "__N_SSP", "styleSheets", "autoExportDynamic", "__NEXT_DATA__", "autoExport", "gssp", "gip", "location", "search", "__NEXT_HAS_REWRITES", "hostname", "addEventListener", "scrollRestoration", "reload", "back", "push", "method", "shouldResolveHref", "_h", "scroll", "localeChange", "parsedAs", "localePathResult", "didNavigate", "asNoBasePath", "Promise", "ST", "performance", "mark", "routeProps", "abortComponentLoad", "cleanedAs", "onlyAHashChange", "emit", "scrollToHash", "notify", "parsed", "rewrites", "getPageList", "__rewrites", "urlIsNew", "rewritesResult", "p", "matchedPage", "NODE_ENV", "routeRegex", "routeMatch", "shouldInterpolate", "missingParams", "filter", "console", "warn", "routeInfo", "getRouteInfo", "error", "pageProps", "__N_REDIRECT", "destination", "parsedHref", "newUrl", "newAs", "__N_PREVIEW", "notFoundRoute", "fetchComponent", "appComp", "next", "isP<PERSON>endered", "getInitialProps", "origGetInitialProps", "isValidShallowRoute", "statusCode", "set", "document", "documentElement", "lang", "handleRouteInfoError", "loadErrorFail", "gipErr", "routeInfoErr", "existingRouteInfo", "cachedRouteInfo", "mod", "isValidElementType", "getDataHref", "_getData", "_getStaticData", "_getServerData", "resetScroll", "beforePopState", "cb", "oldUrlNoHash", "oldHash", "split", "newUrlNoHash", "newHash", "scrollTo", "idEl", "getElementById", "scrollIntoView", "nameEl", "getElementsByName", "prefetch", "all", "_isSsg", "isSsg", "priority", "cancel", "componentResult", "loadPage", "fn", "cache<PERSON>ey", "resolve", "resourceKey", "ctx", "AppTree"], "mappings": "8VAIA,gFAKA,yDAMA,uEACA,kEACA,qDACA,+BAUA,6CACA,4DACA,gDACA,iFACA,mDACA,+C,mFAjCA;AA+DA,GAAIA,CAAAA,kBAAJ,CAEA,GAAIC,OAAO,CAACC,GAAR,CAAYC,mBAAhB,CAAqC,CACnCH,kBAAkB,CAAGI,OAAO,CAAC,8BAAD,CAAP,CAClBJ,kBADH,CAED,CAED,KAAMK,CAAAA,QAAQ,CAAIJ,OAAO,CAACC,GAAR,CAAYI,sBAAb,EAAkD,EAAnE,CAEA,QAASC,CAAAA,sBAAT,EAAkC,CAChC,MAAOC,CAAAA,MAAM,CAACC,MAAP,CAAc,GAAIC,CAAAA,KAAJ,CAAU,iBAAV,CAAd,CAA4C,CACjDC,SAAS,CAAE,IADsC,CAA5C,CAAP,CAGD,CAED,QAASC,CAAAA,aAAT,CAAuBC,IAAvB,CAAqCC,MAArC,CAAsD,CACpD,MAAOA,CAAAA,MAAM,EAAID,IAAI,CAACE,UAAL,CAAgB,GAAhB,CAAV,CACHF,IAAI,GAAK,GAAT,CACE,uDAA2BC,MAA3B,CADF,CAEG,GAAEA,MAAO,GAAEE,eAAe,CAACH,IAAD,CAAf,GAA0B,GAA1B,CAAgCA,IAAI,CAACI,SAAL,CAAe,CAAf,CAAhC,CAAoDJ,IAAK,EAHpE,CAIHA,IAJJ,CAKD,CAEM,QAASK,CAAAA,eAAT,CACLL,IADK,CAELM,MAFK,CAGLC,OAHK,CAILC,aAJK,CAKL,CACA,GAAIpB,OAAO,CAACC,GAAR,CAAYC,mBAAhB,CAAqC,CACnCgB,MAAM,CAAGA,MAAM,EAAI,6CAAoBN,IAApB,CAA0BO,OAA1B,EAAmCE,cAAtD,CAEA,KAAMC,CAAAA,cAAc,CAAGvB,kBAAkB,CAACqB,aAAD,CAAgBG,SAAhB,CAA2BL,MAA3B,CAAzC,CAEA,GAAII,cAAJ,CAAoB,CAClB,MAAQ,OAAMA,cAAc,CAACE,IAAf,CAAsB,EAAtB,CAA2B,GAAI,MAAKF,cAAc,CAACG,MAAO,GACtErB,QAAQ,EAAI,EACb,GAAEc,MAAM,GAAKI,cAAc,CAACI,aAA1B,CAA0C,EAA1C,CAAgD,IAAGR,MAAO,EAAE,GAAEN,IAAK,EAFtE,CAGD,CACD,MAAO,MAAP,CACD,CAED,MAAO,MAAP,CACD,CAEM,QAASe,CAAAA,SAAT,CACLf,IADK,CAELM,MAFK,CAGLQ,aAHK,CAIL,CACA,GAAI1B,OAAO,CAACC,GAAR,CAAYC,mBAAhB,CAAqC,CACnC,KAAM0B,CAAAA,QAAQ,CAAGb,eAAe,CAACH,IAAD,CAAhC,CACA,KAAMiB,CAAAA,SAAS,CAAGD,QAAQ,CAACE,WAAT,EAAlB,CACA,KAAMC,CAAAA,WAAW,CAAGb,MAAM,EAAIA,MAAM,CAACY,WAAP,EAA9B,CAEA,MAAOZ,CAAAA,MAAM,EACXA,MAAM,GAAKQ,aADN,EAEL,CAACG,SAAS,CAACf,UAAV,CAAqB,IAAMiB,WAAN,CAAoB,GAAzC,CAFI,EAGLF,SAAS,GAAK,IAAME,WAHf,CAIHpB,aAAa,CAACC,IAAD,CAAO,IAAMM,MAAb,CAJV,CAKHN,IALJ,CAMD,CACD,MAAOA,CAAAA,IAAP,CACD,CAEM,QAASoB,CAAAA,SAAT,CAAmBpB,IAAnB,CAAiCM,MAAjC,CAAkD,CACvD,GAAIlB,OAAO,CAACC,GAAR,CAAYC,mBAAhB,CAAqC,CACnC,KAAM0B,CAAAA,QAAQ,CAAGb,eAAe,CAACH,IAAD,CAAhC,CACA,KAAMiB,CAAAA,SAAS,CAAGD,QAAQ,CAACE,WAAT,EAAlB,CACA,KAAMC,CAAAA,WAAW,CAAGb,MAAM,EAAIA,MAAM,CAACY,WAAP,EAA9B,CAEA,MAAOZ,CAAAA,MAAM,GACVW,SAAS,CAACf,UAAV,CAAqB,IAAMiB,WAAN,CAAoB,GAAzC,GACCF,SAAS,GAAK,IAAME,WAFX,CAAN,CAGH,CAACH,QAAQ,CAACK,MAAT,GAAoBf,MAAM,CAACe,MAAP,CAAgB,CAApC,CAAwC,GAAxC,CAA8C,EAA/C,EACErB,IAAI,CAACsB,MAAL,CAAYhB,MAAM,CAACe,MAAP,CAAgB,CAA5B,CAJC,CAKHrB,IALJ,CAMD,CACD,MAAOA,CAAAA,IAAP,CACD,CAED,QAASG,CAAAA,eAAT,CAAyBH,IAAzB,CAAuC,CACrC,KAAMuB,CAAAA,UAAU,CAAGvB,IAAI,CAACwB,OAAL,CAAa,GAAb,CAAnB,CACA,KAAMC,CAAAA,SAAS,CAAGzB,IAAI,CAACwB,OAAL,CAAa,GAAb,CAAlB,CAEA,GAAID,UAAU,CAAG,CAAC,CAAd,EAAmBE,SAAS,CAAG,CAAC,CAApC,CAAuC,CACrCzB,IAAI,CAAGA,IAAI,CAACI,SAAL,CAAe,CAAf,CAAkBmB,UAAU,CAAG,CAAC,CAAd,CAAkBA,UAAlB,CAA+BE,SAAjD,CAAP,CACD,CACD,MAAOzB,CAAAA,IAAP,CACD,CAEM,QAAS0B,CAAAA,WAAT,CAAqB1B,IAArB,CAA4C,CACjDA,IAAI,CAAGG,eAAe,CAACH,IAAD,CAAtB,CACA,MAAOA,CAAAA,IAAI,GAAKR,QAAT,EAAqBQ,IAAI,CAACE,UAAL,CAAgBV,QAAQ,CAAG,GAA3B,CAA5B,CACD,CAEM,QAASmC,CAAAA,WAAT,CAAqB3B,IAArB,CAA2C,CAChD;AACA,MAAOD,CAAAA,aAAa,CAACC,IAAD,CAAOR,QAAP,CAApB,CACD,CAEM,QAASoC,CAAAA,WAAT,CAAqB5B,IAArB,CAA2C,CAChDA,IAAI,CAAGA,IAAI,CAAC6B,KAAL,CAAWrC,QAAQ,CAAC6B,MAApB,CAAP,CACA,GAAI,CAACrB,IAAI,CAACE,UAAL,CAAgB,GAAhB,CAAL,CAA2BF,IAAI,CAAI,IAAGA,IAAK,EAAhB,CAC3B,MAAOA,CAAAA,IAAP,CACD,CAED;AACA;AACA,GACO,QAAS8B,CAAAA,UAAT,CAAoBC,GAApB,CAA0C,CAC/C;AACA,GAAIA,GAAG,CAAC7B,UAAJ,CAAe,GAAf,GAAuB6B,GAAG,CAAC7B,UAAJ,CAAe,GAAf,CAA3B,CAAgD,MAAO,KAAP,CAChD,GAAI,CACF;AACA,KAAM8B,CAAAA,cAAc,CAAG,8BAAvB,CACA,KAAMC,CAAAA,QAAQ,CAAG,GAAIC,CAAAA,GAAJ,CAAQH,GAAR,CAAaC,cAAb,CAAjB,CACA,MAAOC,CAAAA,QAAQ,CAACE,MAAT,GAAoBH,cAApB,EAAsCN,WAAW,CAACO,QAAQ,CAACjB,QAAV,CAAxD,CACD,CAAC,MAAOoB,CAAP,CAAU,CACV,MAAO,MAAP,CACD,CACF,CAIM,QAASC,CAAAA,aAAT,CACLC,KADK,CAELC,UAFK,CAGLC,KAHK,CAIL,CACA,GAAIC,CAAAA,iBAAiB,CAAG,EAAxB,CAEA,KAAMC,CAAAA,YAAY,CAAG,8BAAcJ,KAAd,CAArB,CACA,KAAMK,CAAAA,aAAa,CAAGD,YAAY,CAACE,MAAnC,CACA,KAAMC,CAAAA,cAAc,CAClB;AACA,CAACN,UAAU,GAAKD,KAAf,CAAuB,kCAAgBI,YAAhB,EAA8BH,UAA9B,CAAvB,CAAmE,EAApE,GACA;AACA;AACAC,KALF,CAOAC,iBAAiB,CAAGH,KAApB,CACA,KAAMQ,CAAAA,MAAM,CAAGnD,MAAM,CAACoD,IAAP,CAAYJ,aAAZ,CAAf,CAEA,GACE,CAACG,MAAM,CAACE,KAAP,CAAcC,KAAD,EAAW,CACvB,GAAIC,CAAAA,KAAK,CAAGL,cAAc,CAACI,KAAD,CAAd,EAAyB,EAArC,CACA,KAAM,CAAEE,MAAF,CAAUC,QAAV,EAAuBT,aAAa,CAACM,KAAD,CAA1C,CAEA;AACA;AACA,GAAII,CAAAA,QAAQ,CAAI,IAAGF,MAAM,CAAG,KAAH,CAAW,EAAG,GAAEF,KAAM,GAA/C,CACA,GAAIG,QAAJ,CAAc,CACZC,QAAQ,CAAI,GAAE,CAACH,KAAD,CAAS,GAAT,CAAe,EAAG,IAAGG,QAAS,GAA5C,CACD,CACD,GAAIF,MAAM,EAAI,CAACG,KAAK,CAACC,OAAN,CAAcL,KAAd,CAAf,CAAqCA,KAAK,CAAG,CAACA,KAAD,CAAR,CAErC,MACE,CAACE,QAAQ,EAAIH,KAAK,GAAIJ,CAAAA,cAAtB,IACA;AACCJ,iBAAiB,CAChBA,iBAAiB,CAAEe,OAAnB,CACEH,QADF,CAEEF,MAAM,CACDD,KAAD,CACGO,GADH,CAEI;AACA;AACA;AACA;AACCC,OAAD,EAAaC,kBAAkB,CAACD,OAAD,CANnC,EAQGE,IARH,CAQQ,GARR,CADE,CAUFD,kBAAkB,CAACT,KAAD,CAZxB,GAaK,GAhBP,CADF,CAmBD,CA/BA,CADH,CAiCE,CACAT,iBAAiB,CAAG,EAApB,CAAuB;AAEvB;AACA;AACD,CACD,MAAO,CACLK,MADK,CAELe,MAAM,CAAEpB,iBAFH,CAAP,CAID,CAED,QAASqB,CAAAA,kBAAT,CAA4BtB,KAA5B,CAAmDM,MAAnD,CAAqE,CACnE,KAAMiB,CAAAA,aAA6B,CAAG,EAAtC,CAEApE,MAAM,CAACoD,IAAP,CAAYP,KAAZ,EAAmBwB,OAAnB,CAA4BC,GAAD,EAAS,CAClC,GAAI,CAACnB,MAAM,CAACoB,QAAP,CAAgBD,GAAhB,CAAL,CAA2B,CACzBF,aAAa,CAACE,GAAD,CAAb,CAAqBzB,KAAK,CAACyB,GAAD,CAA1B,CACD,CACF,CAJD,EAKA,MAAOF,CAAAA,aAAP,CACD,CAED;AACA;AACA;AACA,GACO,QAASI,CAAAA,WAAT,CACLC,WADK,CAELC,IAFK,CAGLC,SAHK,CAIG,CACR;AACA,GAAIC,CAAAA,IAAJ,CAEA,GAAI,CACFA,IAAI,CAAG,GAAIrC,CAAAA,GAAJ,CAAQkC,WAAR,CAAqB,UAArB,CAAP,CACD,CAAC,MAAOhC,CAAP,CAAU,CACV;AACAmC,IAAI,CAAG,GAAIrC,CAAAA,GAAJ,CAAQ,GAAR,CAAa,UAAb,CAAP,CACD,CACD,KAAMsC,CAAAA,WAAW,CACf,MAAOH,CAAAA,IAAP,GAAgB,QAAhB,CAA2BA,IAA3B,CAAkC,gCAAqBA,IAArB,CADpC,CAEA;AACA,GAAI,CAACvC,UAAU,CAAC0C,WAAD,CAAf,CAA8B,CAC5B,MAAQF,CAAAA,SAAS,CAAG,CAACE,WAAD,CAAH,CAAmBA,WAApC,CACD,CACD,GAAI,CACF,KAAMC,CAAAA,QAAQ,CAAG,GAAIvC,CAAAA,GAAJ,CAAQsC,WAAR,CAAqBD,IAArB,CAAjB,CACAE,QAAQ,CAACzD,QAAT,CAAoB,uDAA2ByD,QAAQ,CAACzD,QAApC,CAApB,CACA,GAAI0D,CAAAA,cAAc,CAAG,EAArB,CAEA,GACE,8BAAeD,QAAQ,CAACzD,QAAxB,GACAyD,QAAQ,CAACE,YADT,EAEAL,SAHF,CAIE,CACA,KAAM9B,CAAAA,KAAK,CAAG,wCAAuBiC,QAAQ,CAACE,YAAhC,CAAd,CAEA,KAAM,CAAEd,MAAF,CAAUf,MAAV,EAAqBT,aAAa,CACtCoC,QAAQ,CAACzD,QAD6B,CAEtCyD,QAAQ,CAACzD,QAF6B,CAGtCwB,KAHsC,CAAxC,CAMA,GAAIqB,MAAJ,CAAY,CACVa,cAAc,CAAG,gCAAqB,CACpC1D,QAAQ,CAAE6C,MAD0B,CAEpCe,IAAI,CAAEH,QAAQ,CAACG,IAFqB,CAGpCpC,KAAK,CAAEsB,kBAAkB,CAACtB,KAAD,CAAQM,MAAR,CAHW,CAArB,CAAjB,CAKD,CACF,CAED;AACA,KAAM+B,CAAAA,YAAY,CAChBJ,QAAQ,CAACtC,MAAT,GAAoBoC,IAAI,CAACpC,MAAzB,CACIsC,QAAQ,CAACJ,IAAT,CAAcxC,KAAd,CAAoB4C,QAAQ,CAACtC,MAAT,CAAgBd,MAApC,CADJ,CAEIoD,QAAQ,CAACJ,IAHf,CAKA,MAAQC,CAAAA,SAAS,CACb,CAACO,YAAD,CAAeH,cAAc,EAAIG,YAAjC,CADa,CAEbA,YAFJ,CAGD,CAAC,MAAOzC,CAAP,CAAU,CACV,MAAQkC,CAAAA,SAAS,CAAG,CAACE,WAAD,CAAH,CAAmBA,WAApC,CACD,CACF,CAED,QAASM,CAAAA,WAAT,CAAqB/C,GAArB,CAAkC,CAChC,KAAMI,CAAAA,MAAM,CAAG,8BAAf,CAEA,MAAOJ,CAAAA,GAAG,CAAC7B,UAAJ,CAAeiC,MAAf,EAAyBJ,GAAG,CAAC3B,SAAJ,CAAc+B,MAAM,CAACd,MAArB,CAAzB,CAAwDU,GAA/D,CACD,CAED,QAASgD,CAAAA,YAAT,CAAsBC,MAAtB,CAA0CjD,GAA1C,CAAoDkD,EAApD,CAA8D,CAC5D;AACA;AACA,GAAI,CAACJ,YAAD,CAAeK,UAAf,EAA6Bf,WAAW,CAACa,MAAM,CAACG,MAAR,CAAgBpD,GAAhB,CAAqB,IAArB,CAA5C,CACA,KAAMI,CAAAA,MAAM,CAAG,8BAAf,CACA,KAAMiD,CAAAA,aAAa,CAAGP,YAAY,CAAC3E,UAAb,CAAwBiC,MAAxB,CAAtB,CACA,KAAMkD,CAAAA,WAAW,CAAGH,UAAU,EAAIA,UAAU,CAAChF,UAAX,CAAsBiC,MAAtB,CAAlC,CAEA0C,YAAY,CAAGC,WAAW,CAACD,YAAD,CAA1B,CACAK,UAAU,CAAGA,UAAU,CAAGJ,WAAW,CAACI,UAAD,CAAd,CAA6BA,UAApD,CAEA,KAAMI,CAAAA,WAAW,CAAGF,aAAa,CAAGP,YAAH,CAAkBlD,WAAW,CAACkD,YAAD,CAA9D,CACA,KAAMU,CAAAA,UAAU,CAAGN,EAAE,CACjBH,WAAW,CAACX,WAAW,CAACa,MAAM,CAACG,MAAR,CAAgBF,EAAhB,CAAZ,CADM,CAEjBC,UAAU,EAAIL,YAFlB,CAIA,MAAO,CACL9C,GAAG,CAAEuD,WADA,CAELL,EAAE,CAAEI,WAAW,CAAGE,UAAH,CAAgB5D,WAAW,CAAC4D,UAAD,CAFrC,CAAP,CAID,CAED,QAASC,CAAAA,mBAAT,CAA6BxE,QAA7B,CAA+CyE,KAA/C,CAAgE,CAC9D,KAAMC,CAAAA,aAAa,CAAG,oDAAwB,6CAAoB1E,QAApB,CAAxB,CAAtB,CAEA,GAAI0E,aAAa,GAAK,MAAlB,EAA4BA,aAAa,GAAK,SAAlD,CAA6D,CAC3D,MAAO1E,CAAAA,QAAP,CACD,CAED;AACA,GAAI,CAACyE,KAAK,CAACvB,QAAN,CAAewB,aAAf,CAAL,CAAqC,CACnC;AACAD,KAAK,CAACE,IAAN,CAAYC,IAAD,EAAU,CACnB,GAAI,8BAAeA,IAAf,GAAwB,8BAAcA,IAAd,EAAoBC,EAApB,CAAuBC,IAAvB,CAA4BJ,aAA5B,CAA5B,CAAyE,CACvE1E,QAAQ,CAAG4E,IAAX,CACA,MAAO,KAAP,CACD,CACF,CALD,EAMD,CACD,MAAO,oDAAwB5E,QAAxB,CAAP,CACD,CAkED,KAAM+E,CAAAA,uBAAuB,CAC3B3G,OAAO,CAACC,GAAR,CAAY2G,yBAAZ,EACA,MAAOC,CAAAA,MAAP,GAAkB,WADlB,EAEA,qBAAuBA,CAAAA,MAAM,CAACC,OAF9B,EAGA,CAAC,CAAE,UAAY,CACb,GAAI,CACF,GAAIC,CAAAA,CAAC,CAAG,QAAR,CACA;AACA,MAAOC,CAAAA,cAAc,CAACC,OAAf,CAAuBF,CAAvB,CAA0BA,CAA1B,EAA8BC,cAAc,CAACE,UAAf,CAA0BH,CAA1B,CAA9B,CAA4D,IAAnE,CACD,CAAC,MAAOI,CAAP,CAAU,CAAE,CACf,CANC,EAJJ,CAYA,KAAMC,CAAAA,kBAAkB,CAAGC,MAAM,CAAC,oBAAD,CAAjC,CAEA,QAASC,CAAAA,UAAT,CAAoB3E,GAApB,CAAiC4E,QAAjC,CAAiE,CAC/D,MAAOC,CAAAA,KAAK,CAAC7E,GAAD,CAAM,CAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA8E,WAAW,CAAE,aAZG,CAAN,CAAL,CAaJC,IAbI,CAaEC,GAAD,EAAS,CACf,GAAI,CAACA,GAAG,CAACC,EAAT,CAAa,CACX,GAAIL,QAAQ,CAAG,CAAX,EAAgBI,GAAG,CAACE,MAAJ,EAAc,GAAlC,CAAuC,CACrC,MAAOP,CAAAA,UAAU,CAAC3E,GAAD,CAAM4E,QAAQ,CAAG,CAAjB,CAAjB,CACD,CACD,GAAII,GAAG,CAACE,MAAJ,GAAe,GAAnB,CAAwB,CACtB,MAAOF,CAAAA,GAAG,CAACG,IAAJ,GAAWJ,IAAX,CAAiBK,IAAD,EAAU,CAC/B,GAAIA,IAAI,CAACC,QAAT,CAAmB,CACjB,MAAO,CAAEA,QAAQ,CAAEZ,kBAAZ,CAAP,CACD,CACD,KAAM,IAAI3G,CAAAA,KAAJ,CAAW,6BAAX,CAAN,CACD,CALM,CAAP,CAMD,CACD,KAAM,IAAIA,CAAAA,KAAJ,CAAW,6BAAX,CAAN,CACD,CACD,MAAOkH,CAAAA,GAAG,CAACG,IAAJ,EAAP,CACD,CA7BM,CAAP,CA8BD,CAED,QAASG,CAAAA,aAAT,CAAuBC,QAAvB,CAAyCC,cAAzC,CAAkE,CAChE,MAAOb,CAAAA,UAAU,CAACY,QAAD,CAAWC,cAAc,CAAG,CAAH,CAAO,CAAhC,CAAV,CAA6CC,KAA7C,CAAoDC,GAAD,EAAgB,CACxE;AACA;AACA;AAEA,GAAI,CAACF,cAAL,CAAqB,CACnB,gCAAeE,GAAf,EACD,CACD,KAAMA,CAAAA,GAAN,CACD,CATM,CAAP,CAUD,CAEc,KAAMC,CAAAA,MAA6B,CAOhD;AACF;AACA,KATkD,CAWhD;AAEA;AAyBAC,WAAW,CACT3G,SADS,CAETwB,MAFS,CAGTyC,GAHS,CAIT,CACE2C,YADF,CAEEC,UAFF,CAGEC,GAHF,CAIEC,OAJF,CAKEC,SALF,CAMEP,GANF,CAOEQ,YAPF,CAQEC,UARF,CASE5H,MATF,CAUEC,OAVF,CAWEO,aAXF,CAYEN,aAZF,CAaE2H,SAbF,CAJS,CAiCT,MAtEF7F,KAsEE,aArEFtB,QAqEE,aApEFwB,KAoEE,aAnEF2C,MAmEE,aAlEF3F,QAkEE,aA7DF4I,UA6DE,aA3DFC,GA2DE,CA3DkC,EA2DlC,MAzDFC,GAyDE,CAzD2C,EAyD3C,MAvDFC,GAuDE,aAtDFC,GAsDE,aArDFX,UAqDE,aApDFY,IAoDE,aAnDFC,MAmDE,aAlDFC,QAkDE,aAjDFC,KAiDE,aAhDFV,UAgDE,aA/CFW,cA+CE,aA9CFC,QA8CE,aA7CFxI,MA6CE,aA5CFC,OA4CE,aA3CFO,aA2CE,aA1CFN,aA0CE,aAzCFuI,OAyCE,aAxCFZ,SAwCE,aAvCFa,cAuCE,aArCMC,IAqCN,CArCqB,CAqCrB,MAiGFC,UAjGE,CAiGYC,CAAD,EAA4B,CACvC,KAAMC,CAAAA,KAAK,CAAGD,CAAC,CAACC,KAAhB,CAEA,GAAI,CAACA,KAAL,CAAY,CACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAM,CAAEpI,QAAF,CAAYwB,KAAZ,EAAsB,IAA5B,CACA,KAAK6G,WAAL,CACE,cADF,CAEE,gCAAqB,CAAErI,QAAQ,CAAEW,WAAW,CAACX,QAAD,CAAvB,CAAmCwB,KAAnC,CAArB,CAFF,CAGE,mBAHF,EAKA,OACD,CAED,GAAI,CAAC4G,KAAK,CAACE,GAAX,CAAgB,CACd,OACD,CAED,GAAIC,CAAAA,YAAJ,CACA,KAAM,CAAExH,GAAF,CAAOkD,EAAP,CAAWuE,OAAX,CAAoBC,GAApB,EAA4BL,KAAlC,CACA,GAAIhK,OAAO,CAACC,GAAR,CAAY2G,yBAAhB,CAA2C,CACzC,GAAID,uBAAJ,CAA6B,CAC3B,GAAI,KAAKkD,IAAL,GAAcQ,GAAlB,CAAuB,CACrB;AACA,GAAI,CACFrD,cAAc,CAACC,OAAf,CACE,iBAAmB,KAAK4C,IAD1B,CAEES,IAAI,CAACC,SAAL,CAAe,CAAEC,CAAC,CAAEC,IAAI,CAACC,WAAV,CAAuBC,CAAC,CAAEF,IAAI,CAACG,WAA/B,CAAf,CAFF,EAID,CAAC,cAAM,CAAE,CAEV;AACA,GAAI,CACF,KAAM7D,CAAAA,CAAC,CAAGC,cAAc,CAAC6D,OAAf,CAAuB,iBAAmBR,GAA1C,CAAV,CACAF,YAAY,CAAGG,IAAI,CAACQ,KAAL,CAAW/D,CAAX,CAAf,CACD,CAAC,eAAM,CACNoD,YAAY,CAAG,CAAEK,CAAC,CAAE,CAAL,CAAQG,CAAC,CAAE,CAAX,CAAf,CACD,CACF,CACF,CACF,CACD,KAAKd,IAAL,CAAYQ,GAAZ,CAEA,KAAM,CAAEzI,QAAF,EAAe,uCAAiBe,GAAjB,CAArB,CAEA;AACA;AACA,GAAI,KAAK6G,KAAL,EAAc3D,EAAE,GAAK,KAAKE,MAA1B,EAAoCnE,QAAQ,GAAK,KAAKA,QAA1D,CAAoE,CAClE,OACD,CAED;AACA;AACA,GAAI,KAAKyH,IAAL,EAAa,CAAC,KAAKA,IAAL,CAAUW,KAAV,CAAlB,CAAoC,CAClC,OACD,CAED,KAAKe,MAAL,CACE,cADF,CAEEpI,GAFF,CAGEkD,EAHF,CAIEtF,MAAM,CAACC,MAAP,CAAwD,EAAxD,CAA4D4J,OAA5D,CAAqE,CACnEY,OAAO,CAAEZ,OAAO,CAACY,OAAR,EAAmB,KAAKtB,QADkC,CAEnExI,MAAM,CAAEkJ,OAAO,CAAClJ,MAAR,EAAkB,KAAKQ,aAFoC,CAArE,CAJF,CAQEyI,YARF,EAUD,CA5KC,CACA;AACA,KAAKjH,KAAL,CAAa,oDAAwBtB,SAAxB,CAAb,CAEA;AACA,KAAKoH,UAAL,CAAkB,EAAlB,CACA;AACA;AACA;AACA,GAAIpH,SAAQ,GAAK,SAAjB,CAA4B,CAC1B,KAAKoH,UAAL,CAAgB,KAAK9F,KAArB,EAA8B,CAC5B0F,SAD4B,CAE5BqC,OAAO,CAAE,IAFmB,CAG5BC,KAAK,CAAE1C,YAHqB,CAI5BH,GAJ4B,CAK5B8C,OAAO,CAAE3C,YAAY,EAAIA,YAAY,CAAC2C,OALV,CAM5BC,OAAO,CAAE5C,YAAY,EAAIA,YAAY,CAAC4C,OANV,CAA9B,CAQD,CAED,KAAKpC,UAAL,CAAgB,OAAhB,EAA2B,CACzBJ,SAAS,CAAEF,GADc,CAEzB2C,WAAW,CAAE,CACX,iDADW,CAFY,CAA3B,CAOA;AACA;AACA,KAAK/B,MAAL,CAAchB,MAAM,CAACgB,MAArB,CAEA,KAAKb,UAAL,CAAkBA,UAAlB,CACA,KAAK7G,QAAL,CAAgBA,SAAhB,CACA,KAAKwB,KAAL,CAAaA,MAAb,CACA;AACA;AACA,KAAMkI,CAAAA,iBAAiB,CACrB,8BAAe1J,SAAf,GAA4B6I,IAAI,CAACc,aAAL,CAAmBC,UADjD,CAGA,KAAKzF,MAAL,CAAcuF,iBAAiB,CAAG1J,SAAH,CAAciE,GAA7C,CACA,KAAKzF,QAAL,CAAgBA,QAAhB,CACA,KAAK+I,GAAL,CAAWN,YAAX,CACA,KAAKO,GAAL,CAAW,IAAX,CACA,KAAKG,QAAL,CAAgBZ,OAAhB,CACA;AACA;AACA,KAAKa,KAAL,CAAa,IAAb,CAEA,KAAKV,UAAL,CAAkBA,UAAlB,CAEA,KAAKa,OAAL,CAAe,CAAC,EACdc,IAAI,CAACc,aAAL,CAAmBE,IAAnB,EACAhB,IAAI,CAACc,aAAL,CAAmBG,GADnB,EAEC,CAACJ,iBAAD,EACC,CAACb,IAAI,CAACkB,QAAL,CAAcC,MADhB,EAEC,CAAC5L,OAAO,CAACC,GAAR,CAAY4L,mBALD,CAAhB,CAOA,KAAK9C,SAAL,CAAiB,CAAC,CAACA,SAAnB,CACA,KAAKa,cAAL,CAAsB,KAAtB,CAEA,GAAI5J,OAAO,CAACC,GAAR,CAAYC,mBAAhB,CAAqC,CACnC,KAAKgB,MAAL,CAAcA,MAAd,CACA,KAAKC,OAAL,CAAeA,OAAf,CACA,KAAKO,aAAL,CAAqBA,aAArB,CACA,KAAKN,aAAL,CAAqBA,aAArB,CACA,KAAKwI,cAAL,CAAsB,CAAC,CAAC7J,kBAAkB,CACxCqB,aADwC,CAExCqJ,IAAI,CAACkB,QAAL,CAAcG,QAF0B,CAA1C,CAID,CAED,GAAI,MAAOjF,CAAAA,MAAP,GAAkB,WAAtB,CAAmC,CACjC;AACA;AACA,GAAIhB,GAAE,CAAC3D,MAAH,CAAU,CAAV,CAAa,CAAb,IAAoB,IAAxB,CAA8B,CAC5B;AACA;AACA,KAAK+H,WAAL,CACE,cADF,CAEE,gCAAqB,CAAErI,QAAQ,CAAEW,WAAW,CAACX,SAAD,CAAvB,CAAmCwB,KAAK,CAALA,MAAnC,CAArB,CAFF,CAGE,mBAHF,CAIE,CAAElC,MAAF,CAJF,EAMD,CAED2F,MAAM,CAACkF,gBAAP,CAAwB,UAAxB,CAAoC,KAAKjC,UAAzC,EAEA;AACA;AACA,GAAI9J,OAAO,CAACC,GAAR,CAAY2G,yBAAhB,CAA2C,CACzC,GAAID,uBAAJ,CAA6B,CAC3BE,MAAM,CAACC,OAAP,CAAekF,iBAAf,CAAmC,QAAnC,CACD,CACF,CACF,CACF,CA+EDC,MAAM,EAAS,CACbpF,MAAM,CAAC8E,QAAP,CAAgBM,MAAhB,GACD,CAED;AACF;AACA,KACEC,IAAI,EAAG,CACLrF,MAAM,CAACC,OAAP,CAAeoF,IAAf,GACD,CAED;AACF;AACA;AACA;AACA;AACA,KACEC,IAAI,CAACxJ,GAAD,CAAWkD,EAAX,CAAqBuE,OAA0B,CAAG,EAAlD,CAAsD,CACxD,GAAIpK,OAAO,CAACC,GAAR,CAAY2G,yBAAhB,CAA2C,CACzC;AACA;AACA,GAAID,uBAAJ,CAA6B,CAC3B,GAAI,CACF;AACAK,cAAc,CAACC,OAAf,CACE,iBAAmB,KAAK4C,IAD1B,CAEES,IAAI,CAACC,SAAL,CAAe,CAAEC,CAAC,CAAEC,IAAI,CAACC,WAAV,CAAuBC,CAAC,CAAEF,IAAI,CAACG,WAA/B,CAAf,CAFF,EAID,CAAC,eAAM,CAAE,CACX,CACF,CACD,CAAC,CAAC,CAAEjI,GAAF,CAAOkD,EAAP,EAAcF,YAAY,CAAC,IAAD,CAAOhD,GAAP,CAAYkD,EAAZ,CAA3B,EACD,MAAO,MAAKkF,MAAL,CAAY,WAAZ,CAAyBpI,GAAzB,CAA8BkD,EAA9B,CAAkCuE,OAAlC,CAAP,CACD,CAED;AACF;AACA;AACA;AACA;AACA,KACEhG,OAAO,CAACzB,GAAD,CAAWkD,EAAX,CAAqBuE,OAA0B,CAAG,EAAlD,CAAsD,CAC3D,CAAC,CAAC,CAAEzH,GAAF,CAAOkD,EAAP,EAAcF,YAAY,CAAC,IAAD,CAAOhD,GAAP,CAAYkD,EAAZ,CAA3B,EACD,MAAO,MAAKkF,MAAL,CAAY,cAAZ,CAA4BpI,GAA5B,CAAiCkD,EAAjC,CAAqCuE,OAArC,CAAP,CACD,CAED,KAAcW,CAAAA,MAAd,CACEqB,MADF,CAEEzJ,GAFF,CAGEkD,EAHF,CAIEuE,OAJF,CAKED,YALF,CAMoB,qBAClB,GAAI,CAACzH,UAAU,CAACC,GAAD,CAAf,CAAsB,CACpBkE,MAAM,CAAC8E,QAAP,CAAgB1G,IAAhB,CAAuBtC,GAAvB,CACA,MAAO,MAAP,CACD,CACD,KAAM0J,CAAAA,iBAAiB,CAAG1J,GAAG,GAAKkD,EAAR,EAAeuE,OAAD,CAAiBkC,EAAzD,CAEA;AACA;AACA,GAAKlC,OAAD,CAAiBkC,EAArB,CAAyB,CACvB,KAAK3C,OAAL,CAAe,IAAf,CACD,CAED;AACA;AACA;AACAS,OAAO,CAACmC,MAAR,CAAiB,CAAC,mBAAEnC,OAAO,CAACmC,MAAV,wBAAoB,IAApB,CAAlB,CAEA,GAAIC,CAAAA,YAAY,CAAGpC,OAAO,CAAClJ,MAAR,GAAmB,KAAKA,MAA3C,CAEA,GAAIlB,OAAO,CAACC,GAAR,CAAYC,mBAAhB,CAAqC,CACnC,KAAKgB,MAAL,CACEkJ,OAAO,CAAClJ,MAAR,GAAmB,KAAnB,CACI,KAAKQ,aADT,CAEI0I,OAAO,CAAClJ,MAAR,EAAkB,KAAKA,MAH7B,CAKA,GAAI,MAAOkJ,CAAAA,OAAO,CAAClJ,MAAf,GAA0B,WAA9B,CAA2C,CACzCkJ,OAAO,CAAClJ,MAAR,CAAiB,KAAKA,MAAtB,CACD,CAED,KAAMuL,CAAAA,QAAQ,CAAG,uCAAiBnK,WAAW,CAACuD,EAAD,CAAX,CAAkBrD,WAAW,CAACqD,EAAD,CAA7B,CAAoCA,EAArD,CAAjB,CACA,KAAM6G,CAAAA,gBAAgB,CAAG,6CACvBD,QAAQ,CAAC7K,QADc,CAEvB,KAAKT,OAFkB,CAAzB,CAKA,GAAIuL,gBAAgB,CAACrL,cAArB,CAAqC,CACnC,KAAKH,MAAL,CAAcwL,gBAAgB,CAACrL,cAA/B,CACAoL,QAAQ,CAAC7K,QAAT,CAAoBW,WAAW,CAACkK,QAAQ,CAAC7K,QAAV,CAA/B,CACAiE,EAAE,CAAG,gCAAqB4G,QAArB,CAAL,CACA9J,GAAG,CAAGJ,WAAW,CACf,6CACED,WAAW,CAACK,GAAD,CAAX,CAAmBH,WAAW,CAACG,GAAD,CAA9B,CAAsCA,GADxC,CAEE,KAAKxB,OAFP,EAGES,QAJa,CAAjB,CAMD,CACD,GAAI+K,CAAAA,WAAW,CAAG,KAAlB,CAEA;AACA;AACA,GAAI3M,OAAO,CAACC,GAAR,CAAYC,mBAAhB,CAAqC,mBACnC;AACA,GAAI,iBAAC,KAAKiB,OAAN,SAAC,cAAc2D,QAAd,CAAuB,KAAK5D,MAA5B,CAAD,CAAJ,CAA2C,CACzCuL,QAAQ,CAAC7K,QAAT,CAAoBD,SAAS,CAAC8K,QAAQ,CAAC7K,QAAV,CAAoB,KAAKV,MAAzB,CAA7B,CACA2F,MAAM,CAAC8E,QAAP,CAAgB1G,IAAhB,CAAuB,gCAAqBwH,QAArB,CAAvB,CACA;AACA;AACAE,WAAW,CAAG,IAAd,CACD,CACF,CAED,KAAMrL,CAAAA,cAAc,CAAGvB,kBAAkB,CACvC,KAAKqB,aADkC,CAEvCG,SAFuC,CAGvC,KAAKL,MAHkC,CAAzC,CAMA;AACA;AACA,GAAIlB,OAAO,CAACC,GAAR,CAAYC,mBAAhB,CAAqC,CACnC;AACA;AACA,GACE,CAACyM,WAAD,EACArL,cADA,EAEA,KAAKsI,cAFL,EAGAa,IAAI,CAACkB,QAAL,CAAcG,QAAd,GAA2BxK,cAAc,CAACG,MAJ5C,CAKE,CACA,KAAMmL,CAAAA,YAAY,CAAGpK,WAAW,CAACqD,EAAD,CAAhC,CACAgB,MAAM,CAAC8E,QAAP,CAAgB1G,IAAhB,CAAwB,OAAM3D,cAAc,CAACE,IAAf,CAAsB,EAAtB,CAA2B,GAAI,MAC3DF,cAAc,CAACG,MAChB,GAAEc,WAAW,CACX,GACC,KAAKrB,MAAL,GAAgBI,cAAc,CAACI,aAA/B,CACI,EADJ,CAEK,IAAG,KAAKR,MAAO,EACrB,GAAE0L,YAAY,GAAK,GAAjB,CAAuB,EAAvB,CAA4BA,YAAa,EAJ5C,EAIiD,GALrC,CAMZ,EARF,CASA;AACA;AACAD,WAAW,CAAG,IAAd,CACD,CACF,CAED,GAAIA,WAAJ,CAAiB,CACf,MAAO,IAAIE,CAAAA,OAAJ,CAAY,IAAM,CAAE,CAApB,CAAP,CACD,CACF,CAED,GAAI,CAAEzC,OAAD,CAAiBkC,EAAtB,CAA0B,CACxB,KAAK9C,KAAL,CAAa,KAAb,CACD,CACD;AACA,GAAIsD,SAAJ,CAAQ,CACNC,WAAW,CAACC,IAAZ,CAAiB,aAAjB,EACD,CAED,KAAM,CAAEhC,OAAO,CAAG,KAAZ,EAAsBZ,OAA5B,CACA,KAAM6C,CAAAA,UAAU,CAAG,CAAEjC,OAAF,CAAnB,CAEA,GAAI,KAAKvB,cAAT,CAAyB,CACvB,KAAKyD,kBAAL,CAAwB,KAAKzD,cAA7B,CAA6CwD,UAA7C,EACD,CAEDpH,EAAE,CAAGtD,WAAW,CACdZ,SAAS,CACPW,WAAW,CAACuD,EAAD,CAAX,CAAkBrD,WAAW,CAACqD,EAAD,CAA7B,CAAoCA,EAD7B,CAEPuE,OAAO,CAAClJ,MAFD,CAGP,KAAKQ,aAHE,CADK,CAAhB,CAOA,KAAMyL,CAAAA,SAAS,CAAGnL,SAAS,CACzBM,WAAW,CAACuD,EAAD,CAAX,CAAkBrD,WAAW,CAACqD,EAAD,CAA7B,CAAoCA,EADX,CAEzB,KAAK3E,MAFoB,CAA3B,CAIA,KAAKuI,cAAL,CAAsB5D,EAAtB,CAEA;AACA;AAEA;AACA;AACA;AACA,GAAI,CAAEuE,OAAD,CAAiBkC,EAAlB,EAAwB,KAAKc,eAAL,CAAqBD,SAArB,CAA5B,CAA6D,CAC3D,KAAKpH,MAAL,CAAcoH,SAAd,CACA7E,MAAM,CAACgB,MAAP,CAAc+D,IAAd,CAAmB,iBAAnB,CAAsCxH,EAAtC,CAA0CoH,UAA1C,EACA;AACA,KAAKhD,WAAL,CAAiBmC,MAAjB,CAAyBzJ,GAAzB,CAA8BkD,EAA9B,CAAkCuE,OAAlC,EACA,KAAKkD,YAAL,CAAkBH,SAAlB,EACA,KAAKI,MAAL,CAAY,KAAKvE,UAAL,CAAgB,KAAK9F,KAArB,CAAZ,CAAyC,IAAzC,EACAoF,MAAM,CAACgB,MAAP,CAAc+D,IAAd,CAAmB,oBAAnB,CAAyCxH,EAAzC,CAA6CoH,UAA7C,EACA,MAAO,KAAP,CACD,CAED,GAAIO,CAAAA,MAAM,CAAG,uCAAiB7K,GAAjB,CAAb,CACA,GAAI,CAAEf,QAAF,CAAYwB,KAAZ,EAAsBoK,MAA1B,CAEA;AACA;AACA;AACA,GAAInH,CAAAA,KAAJ,CAAgBoH,QAAhB,CACA,GAAI,CACFpH,KAAK,CAAG,KAAM,MAAKoC,UAAL,CAAgBiF,WAAhB,EAAd,CACC,CAAC,CAAEC,UAAU,CAAEF,QAAd,EAA2B,KAAM,yCAAlC,EACF,CAAC,MAAOpF,GAAP,CAAY,CACZ;AACA;AACAxB,MAAM,CAAC8E,QAAP,CAAgB1G,IAAhB,CAAuBY,EAAvB,CACA,MAAO,MAAP,CACD,CAED;AACA;AACA;AACA;AACA;AACA,GAAI,CAAC,KAAK+H,QAAL,CAAcT,SAAd,CAAD,EAA6B,CAACX,YAAlC,CAAgD,CAC9CJ,MAAM,CAAG,cAAT,CACD,CAED;AACA;AACA,GAAItG,CAAAA,UAAU,CAAGD,EAAjB,CAEA;AACA;AACA;AACAjE,QAAQ,CAAGA,QAAQ,CACf,oDAAwBY,WAAW,CAACZ,QAAD,CAAnC,CADe,CAEfA,QAFJ,CAIA,GAAIyK,iBAAiB,EAAIzK,QAAQ,GAAK,SAAtC,CAAiD,CAC/C,GAAI5B,OAAO,CAACC,GAAR,CAAY4L,mBAAZ,EAAmChG,EAAE,CAAC/E,UAAH,CAAc,GAAd,CAAvC,CAA2D,CACzD,KAAM+M,CAAAA,cAAc,CAAG,6BACrBtL,WAAW,CAACZ,SAAS,CAACwL,SAAD,CAAY,KAAKjM,MAAjB,CAAV,CADU,CAErBmF,KAFqB,CAGrBoH,QAHqB,CAIrBrK,KAJqB,CAKpB0K,CAAD,EAAe1H,mBAAmB,CAAC0H,CAAD,CAAIzH,KAAJ,CALb,CAMrB,KAAKlF,OANgB,CAAvB,CAQA2E,UAAU,CAAG+H,cAAc,CAAC9H,MAA5B,CAEA,GAAI8H,cAAc,CAACE,WAAf,EAA8BF,cAAc,CAACpI,YAAjD,CAA+D,CAC7D;AACA;AACA7D,QAAQ,CAAGiM,cAAc,CAACpI,YAA1B,CACA+H,MAAM,CAAC5L,QAAP,CAAkBA,QAAlB,CACAe,GAAG,CAAG,gCAAqB6K,MAArB,CAAN,CACD,CACF,CAlBD,IAkBO,CACLA,MAAM,CAAC5L,QAAP,CAAkBwE,mBAAmB,CAACxE,QAAD,CAAWyE,KAAX,CAArC,CAEA,GAAImH,MAAM,CAAC5L,QAAP,GAAoBA,QAAxB,CAAkC,CAChCA,QAAQ,CAAG4L,MAAM,CAAC5L,QAAlB,CACAe,GAAG,CAAG,gCAAqB6K,MAArB,CAAN,CACD,CACF,CACF,CAED,KAAMtK,CAAAA,KAAK,CAAG,oDAAwBtB,QAAxB,CAAd,CAEA,GAAI,CAACc,UAAU,CAACmD,EAAD,CAAf,CAAqB,CACnB,GAAI7F,OAAO,CAACC,GAAR,CAAY+N,QAAZ,GAAyB,YAA7B,CAA2C,CACzC,KAAM,IAAIvN,CAAAA,KAAJ,CACH,kBAAiBkC,GAAI,cAAakD,EAAG,2CAAtC,CACG,oFAFC,CAAN,CAID,CAEDgB,MAAM,CAAC8E,QAAP,CAAgB1G,IAAhB,CAAuBY,EAAvB,CACA,MAAO,MAAP,CACD,CAEDC,UAAU,CAAG9D,SAAS,CAACQ,WAAW,CAACsD,UAAD,CAAZ,CAA0B,KAAK5E,MAA/B,CAAtB,CAEA,GAAI,8BAAegC,KAAf,CAAJ,CAA2B,CACzB,KAAMuJ,CAAAA,QAAQ,CAAG,uCAAiB3G,UAAjB,CAAjB,CACA,KAAM3C,CAAAA,UAAU,CAAGsJ,QAAQ,CAAC7K,QAA5B,CAEA,KAAMqM,CAAAA,UAAU,CAAG,8BAAc/K,KAAd,CAAnB,CACA,KAAMgL,CAAAA,UAAU,CAAG,kCAAgBD,UAAhB,EAA4B9K,UAA5B,CAAnB,CACA,KAAMgL,CAAAA,iBAAiB,CAAGjL,KAAK,GAAKC,UAApC,CACA,KAAMmC,CAAAA,cAAc,CAAG6I,iBAAiB,CACpClL,aAAa,CAACC,KAAD,CAAQC,UAAR,CAAoBC,KAApB,CADuB,CAEnC,EAFL,CAIA,GAAI,CAAC8K,UAAD,EAAgBC,iBAAiB,EAAI,CAAC7I,cAAc,CAACb,MAAzD,CAAkE,CAChE,KAAM2J,CAAAA,aAAa,CAAG7N,MAAM,CAACoD,IAAP,CAAYsK,UAAU,CAACzK,MAAvB,EAA+B6K,MAA/B,CACnBxK,KAAD,EAAW,CAACT,KAAK,CAACS,KAAD,CADG,CAAtB,CAIA,GAAIuK,aAAa,CAACnM,MAAd,CAAuB,CAA3B,CAA8B,CAC5B,GAAIjC,OAAO,CAACC,GAAR,CAAY+N,QAAZ,GAAyB,YAA7B,CAA2C,CACzCM,OAAO,CAACC,IAAR,CACG,GACCJ,iBAAiB,CACZ,oBADY,CAEZ,iCACN,8BAJD,CAKG,eAAcC,aAAa,CAAC5J,IAAd,CACb,IADa,CAEb,8BARN,EAUD,CAED,KAAM,IAAI/D,CAAAA,KAAJ,CACJ,CAAC0N,iBAAiB,CACb,0BAAyBxL,GAAI,oCAAmCyL,aAAa,CAAC5J,IAAd,CAC/D,IAD+D,CAE/D,iCAHY,CAIb,8BAA6BrB,UAAW,8CAA6CD,KAAM,KAJhG,EAKG,+CACCiL,iBAAiB,CACb,2BADa,CAEb,sBACL,EAVC,CAAN,CAYD,CACF,CAhCD,IAgCO,IAAIA,iBAAJ,CAAuB,CAC5BtI,EAAE,CAAG,gCACHtF,MAAM,CAACC,MAAP,CAAc,EAAd,CAAkBiM,QAAlB,CAA4B,CAC1B7K,QAAQ,CAAE0D,cAAc,CAACb,MADC,CAE1BrB,KAAK,CAAEsB,kBAAkB,CAACtB,KAAD,CAAQkC,cAAc,CAAC5B,MAAvB,CAFC,CAA5B,CADG,CAAL,CAMD,CAPM,IAOA,CACL;AACAnD,MAAM,CAACC,MAAP,CAAc4C,KAAd,CAAqB8K,UAArB,EACD,CACF,CAED5F,MAAM,CAACgB,MAAP,CAAc+D,IAAd,CAAmB,kBAAnB,CAAuCxH,EAAvC,CAA2CoH,UAA3C,EAEA,GAAI,kDACF,GAAIuB,CAAAA,SAAS,CAAG,KAAM,MAAKC,YAAL,CACpBvL,KADoB,CAEpBtB,QAFoB,CAGpBwB,KAHoB,CAIpByC,EAJoB,CAKpBC,UALoB,CAMpBmH,UANoB,CAAtB,CAQA,GAAI,CAAEyB,KAAF,CAASxD,KAAT,CAAgBC,OAAhB,CAAyBC,OAAzB,EAAqCoD,SAAzC,CAEA;AACA,GAAI,CAACrD,OAAO,EAAIC,OAAZ,GAAwBF,KAA5B,CAAmC,CACjC,GAAKA,KAAD,CAAeyD,SAAf,EAA6BzD,KAAD,CAAeyD,SAAf,CAAyBC,YAAzD,CAAuE,CACrE,KAAMC,CAAAA,WAAW,CAAI3D,KAAD,CAAeyD,SAAf,CAAyBC,YAA7C,CAEA;AACA;AACA;AACA,GAAIC,WAAW,CAAC/N,UAAZ,CAAuB,GAAvB,CAAJ,CAAiC,CAC/B,KAAMgO,CAAAA,UAAU,CAAG,uCAAiBD,WAAjB,CAAnB,CACAC,UAAU,CAAClN,QAAX,CAAsBwE,mBAAmB,CACvC0I,UAAU,CAAClN,QAD4B,CAEvCyE,KAFuC,CAAzC,CAKA,GAAIA,KAAK,CAACvB,QAAN,CAAegK,UAAU,CAAClN,QAA1B,CAAJ,CAAyC,CACvC,KAAM,CAAEe,GAAG,CAAEoM,MAAP,CAAelJ,EAAE,CAAEmJ,KAAnB,EAA6BrJ,YAAY,CAC7C,IAD6C,CAE7CkJ,WAF6C,CAG7CA,WAH6C,CAA/C,CAKA,MAAO,MAAK9D,MAAL,CAAYqB,MAAZ,CAAoB2C,MAApB,CAA4BC,KAA5B,CAAmC5E,OAAnC,CAAP,CACD,CACF,CAEDvD,MAAM,CAAC8E,QAAP,CAAgB1G,IAAhB,CAAuB4J,WAAvB,CACA,MAAO,IAAIhC,CAAAA,OAAJ,CAAY,IAAM,CAAE,CAApB,CAAP,CACD,CAED,KAAK9D,SAAL,CAAiB,CAAC,CAACmC,KAAK,CAAC+D,WAAzB,CAEA;AACA,GAAI/D,KAAK,CAAClD,QAAN,GAAmBZ,kBAAvB,CAA2C,CACzC,GAAI8H,CAAAA,aAAJ,CAEA,GAAI,CACF,KAAM,MAAKC,cAAL,CAAoB,MAApB,CAAN,CACAD,aAAa,CAAG,MAAhB,CACD,CAAC,MAAOlM,CAAP,CAAU,CACVkM,aAAa,CAAG,SAAhB,CACD,CAEDV,SAAS,CAAG,KAAM,MAAKC,YAAL,CAChBS,aADgB,CAEhBA,aAFgB,CAGhB9L,KAHgB,CAIhByC,EAJgB,CAKhBC,UALgB,CAMhB,CAAEkF,OAAO,CAAE,KAAX,CANgB,CAAlB,CAQD,CACF,CAED1C,MAAM,CAACgB,MAAP,CAAc+D,IAAd,CAAmB,qBAAnB,CAA0CxH,EAA1C,CAA8CoH,UAA9C,EACA,KAAKhD,WAAL,CAAiBmC,MAAjB,CAAyBzJ,GAAzB,CAA8BkD,EAA9B,CAAkCuE,OAAlC,EAEA,GAAIpK,OAAO,CAACC,GAAR,CAAY+N,QAAZ,GAAyB,YAA7B,CAA2C,CACzC,KAAMoB,CAAAA,OAAY,CAAG,KAAKpG,UAAL,CAAgB,OAAhB,EAAyBJ,SAA9C,CACE/B,MAAD,CAAgBwI,IAAhB,CAAqBC,aAArB,CACCF,OAAO,CAACG,eAAR,GAA4BH,OAAO,CAACI,mBAApC,EACA,CAAEhB,SAAS,CAAC5F,SAAX,CAA6B2G,eAF/B,CAGF,CAED;AACA,KAAME,CAAAA,mBAAmB,CAAGrF,OAAO,CAACY,OAAR,EAAmB,KAAK9H,KAAL,GAAeA,KAA9D,CAEA,GACGkH,OAAD,CAAiBkC,EAAjB,EACA1K,QAAQ,GAAK,SADb,EAEA,wBAAA6I,IAAI,CAACc,aAAL,CAAmBL,KAAnB,6DAA0ByD,SAA1B,sCAAqCe,UAArC,IAAoD,GAFpD,EAGAxE,KAHA,QAGAA,KAAK,CAAEyD,SAJT,CAKE,CACA;AACA;AACAzD,KAAK,CAACyD,SAAN,CAAgBe,UAAhB,CAA6B,GAA7B,CACD,CAED,KAAM,MAAKC,GAAL,CACJzM,KADI,CAEJtB,QAFI,CAGJwB,KAHI,CAIJ+J,SAJI,CAKJqB,SALI,CAMJrE,YAAY,GACTsF,mBAAmB,EAAI,CAACrF,OAAO,CAACmC,MAAhC,CAAyC,IAAzC,CAAgD,CAAE/B,CAAC,CAAE,CAAL,CAAQG,CAAC,CAAE,CAAX,CADvC,CANR,EAQJvC,KARI,CAQG2B,CAAD,EAAO,CACb,GAAIA,CAAC,CAACrJ,SAAN,CAAiBgO,KAAK,CAAGA,KAAK,EAAI3E,CAAjB,CAAjB,IACK,MAAMA,CAAAA,CAAN,CACN,CAXK,CAAN,CAaA,GAAI2E,KAAJ,CAAW,CACTpG,MAAM,CAACgB,MAAP,CAAc+D,IAAd,CAAmB,kBAAnB,CAAuCqB,KAAvC,CAA8CvB,SAA9C,CAAyDF,UAAzD,EACA,KAAMyB,CAAAA,KAAN,CACD,CAED,GAAI1O,OAAO,CAACC,GAAR,CAAYC,mBAAhB,CAAqC,CACnC,GAAI,KAAKgB,MAAT,CAAiB,CACf0O,QAAQ,CAACC,eAAT,CAAyBC,IAAzB,CAAgC,KAAK5O,MAArC,CACD,CACF,CACDoH,MAAM,CAACgB,MAAP,CAAc+D,IAAd,CAAmB,qBAAnB,CAA0CxH,EAA1C,CAA8CoH,UAA9C,EAEA,MAAO,KAAP,CACD,CAAC,MAAO5E,GAAP,CAAY,CACZ,GAAIA,GAAG,CAAC3H,SAAR,CAAmB,CACjB,MAAO,MAAP,CACD,CACD,KAAM2H,CAAAA,GAAN,CACD,CACF,CAED4B,WAAW,CACTmC,MADS,CAETzJ,GAFS,CAGTkD,EAHS,CAITuE,OAA0B,CAAG,EAJpB,CAKH,CACN,GAAIpK,OAAO,CAACC,GAAR,CAAY+N,QAAZ,GAAyB,YAA7B,CAA2C,CACzC,GAAI,MAAOnH,CAAAA,MAAM,CAACC,OAAd,GAA0B,WAA9B,CAA2C,CACzCwH,OAAO,CAACI,KAAR,CAAe,2CAAf,EACA,OACD,CAED,GAAI,MAAO7H,CAAAA,MAAM,CAACC,OAAP,CAAesF,MAAf,CAAP,GAAkC,WAAtC,CAAmD,CACjDkC,OAAO,CAACI,KAAR,CAAe,2BAA0BtC,MAAO,mBAAhD,EACA,OACD,CACF,CAED,GAAIA,MAAM,GAAK,WAAX,EAA0B,sBAAavG,EAA3C,CAA+C,CAC7C,KAAK6D,QAAL,CAAgBU,OAAO,CAACY,OAAxB,CACAnE,MAAM,CAACC,OAAP,CAAesF,MAAf,EACE,CACEzJ,GADF,CAEEkD,EAFF,CAGEuE,OAHF,CAIEF,GAAG,CAAE,IAJP,CAKEG,GAAG,CAAE,KAAKR,IAAL,CAAYuC,MAAM,GAAK,WAAX,CAAyB,KAAKvC,IAA9B,CAAqC,KAAKA,IAAL,CAAY,CALpE,CADF,CAQE;AACA;AACA;AACA,EAXF,CAYEhE,EAZF,EAcD,CACF,CAED,KAAMkK,CAAAA,oBAAN,CACE1H,GADF,CAEEzG,QAFF,CAGEwB,KAHF,CAIEyC,EAJF,CAKEoH,UALF,CAME+C,aANF,CAOqC,CACnC,GAAI3H,GAAG,CAAC3H,SAAR,CAAmB,CACjB;AACA,KAAM2H,CAAAA,GAAN,CACD,CAED,GAAI,8BAAaA,GAAb,GAAqB2H,aAAzB,CAAwC,CACtC1H,MAAM,CAACgB,MAAP,CAAc+D,IAAd,CAAmB,kBAAnB,CAAuChF,GAAvC,CAA4CxC,EAA5C,CAAgDoH,UAAhD,EAEA;AACA;AACA;AACA;AAEA;AACApG,MAAM,CAAC8E,QAAP,CAAgB1G,IAAhB,CAAuBY,EAAvB,CAEA;AACA;AACA,KAAMvF,CAAAA,sBAAsB,EAA5B,CACD,CAED,GAAI,CACF,GAAIsI,CAAAA,SAAJ,CACA,GAAIyC,CAAAA,WAAJ,CACA,GAAIH,CAAAA,KAAJ,CAEA,GACE,MAAOtC,CAAAA,SAAP,GAAsB,WAAtB,EACA,MAAOyC,CAAAA,WAAP,GAAwB,WAF1B,CAGE,CACA,CAAC,CAAC,CAAE7E,IAAI,CAAEoC,SAAR,CAAmByC,WAAnB,EAAmC,KAAM,MAAK8D,cAAL,CACzC,SADyC,CAA1C,EAGF,CAED,KAAMX,CAAAA,SAAmC,CAAG,CAC1CtD,KAD0C,CAE1CtC,SAF0C,CAG1CyC,WAH0C,CAI1ChD,GAJ0C,CAK1CqG,KAAK,CAAErG,GALmC,CAA5C,CAQA,GAAI,CAACmG,SAAS,CAACtD,KAAf,CAAsB,CACpB,GAAI,CACFsD,SAAS,CAACtD,KAAV,CAAkB,KAAM,MAAKqE,eAAL,CAAqB3G,SAArB,CAAgC,CACtDP,GADsD,CAEtDzG,QAFsD,CAGtDwB,KAHsD,CAAhC,CAAxB,CAKD,CAAC,MAAO6M,MAAP,CAAe,CACf3B,OAAO,CAACI,KAAR,CAAc,yCAAd,CAAyDuB,MAAzD,EACAzB,SAAS,CAACtD,KAAV,CAAkB,EAAlB,CACD,CACF,CAED,MAAOsD,CAAAA,SAAP,CACD,CAAC,MAAO0B,YAAP,CAAqB,CACrB,MAAO,MAAKH,oBAAL,CACLG,YADK,CAELtO,QAFK,CAGLwB,KAHK,CAILyC,EAJK,CAKLoH,UALK,CAML,IANK,CAAP,CAQD,CACF,CAED,KAAMwB,CAAAA,YAAN,CACEvL,KADF,CAEEtB,QAFF,CAGEwB,KAHF,CAIEyC,EAJF,CAKEC,UALF,CAMEmH,UANF,CAO6B,CAC3B,GAAI,CACF,KAAMkD,CAAAA,iBAA+C,CAAG,KAAKnH,UAAL,CACtD9F,KADsD,CAAxD,CAGA,GAAI+J,UAAU,CAACjC,OAAX,EAAsBmF,iBAAtB,EAA2C,KAAKjN,KAAL,GAAeA,KAA9D,CAAqE,CACnE,MAAOiN,CAAAA,iBAAP,CACD,CAED,KAAMC,CAAAA,eAAqD,CACzDD,iBAAiB,EAAI,WAAaA,CAAAA,iBAAlC,CACI5O,SADJ,CAEI4O,iBAHN,CAIA,KAAM3B,CAAAA,SAAmC,CAAG4B,eAAe,CACvDA,eADuD,CAEvD,KAAM,MAAKjB,cAAL,CAAoBjM,KAApB,EAA2BwE,IAA3B,CAAiCC,GAAD,GAAU,CAC9CiB,SAAS,CAAEjB,GAAG,CAACnB,IAD+B,CAE9C6E,WAAW,CAAE1D,GAAG,CAAC0D,WAF6B,CAG9CF,OAAO,CAAExD,GAAG,CAAC0I,GAAJ,CAAQlF,OAH6B,CAI9CC,OAAO,CAAEzD,GAAG,CAAC0I,GAAJ,CAAQjF,OAJ6B,CAAV,CAAhC,CAFV,CASA,KAAM,CAAExC,SAAF,CAAauC,OAAb,CAAsBC,OAAtB,EAAkCoD,SAAxC,CAEA,GAAIxO,OAAO,CAACC,GAAR,CAAY+N,QAAZ,GAAyB,YAA7B,CAA2C,CACzC,KAAM,CAAEsC,kBAAF,EAAyBnQ,OAAO,CAAC,UAAD,CAAtC,CACA,GAAI,CAACmQ,kBAAkB,CAAC1H,SAAD,CAAvB,CAAoC,CAClC,KAAM,IAAInI,CAAAA,KAAJ,CACH,yDAAwDmB,QAAS,GAD9D,CAAN,CAGD,CACF,CAED,GAAIsG,CAAAA,QAAJ,CAEA,GAAIiD,OAAO,EAAIC,OAAf,CAAwB,CACtBlD,QAAQ,CAAG,KAAKO,UAAL,CAAgB8H,WAAhB,CACT,gCAAqB,CAAE3O,QAAF,CAAYwB,KAAZ,CAArB,CADS,CAET0C,UAFS,CAGTqF,OAHS,CAIT,KAAKjK,MAJI,CAAX,CAMD,CAED,KAAMgK,CAAAA,KAAK,CAAG,KAAM,MAAKsF,QAAL,CAAwC,IAC1DrF,OAAO,CACH,KAAKsF,cAAL,CAAoBvI,QAApB,CADG,CAEHkD,OAAO,CACP,KAAKsF,cAAL,CAAoBxI,QAApB,CADO,CAEP,KAAKqH,eAAL,CACE3G,SADF,CAEE;AACA,CACEhH,QADF,CAEEwB,KAFF,CAGE2C,MAAM,CAAEF,EAHV,CAIE3E,MAAM,CAAE,KAAKA,MAJf,CAKEC,OAAO,CAAE,KAAKA,OALhB,CAMEO,aAAa,CAAE,KAAKA,aANtB,CAHF,CALc,CAApB,CAmBA8M,SAAS,CAACtD,KAAV,CAAkBA,KAAlB,CACA,KAAKlC,UAAL,CAAgB9F,KAAhB,EAAyBsL,SAAzB,CACA,MAAOA,CAAAA,SAAP,CACD,CAAC,MAAOnG,GAAP,CAAY,CACZ,MAAO,MAAK0H,oBAAL,CAA0B1H,GAA1B,CAA+BzG,QAA/B,CAAyCwB,KAAzC,CAAgDyC,EAAhD,CAAoDoH,UAApD,CAAP,CACD,CACF,CAED0C,GAAG,CACDzM,KADC,CAEDtB,QAFC,CAGDwB,KAHC,CAIDyC,EAJC,CAKDkC,IALC,CAMD4I,WANC,CAOc,CACf,KAAK7H,UAAL,CAAkB,KAAlB,CAEA,KAAK5F,KAAL,CAAaA,KAAb,CACA,KAAKtB,QAAL,CAAgBA,QAAhB,CACA,KAAKwB,KAAL,CAAaA,KAAb,CACA,KAAK2C,MAAL,CAAcF,EAAd,CACA,MAAO,MAAK0H,MAAL,CAAYxF,IAAZ,CAAkB4I,WAAlB,CAAP,CACD,CAED;AACF;AACA;AACA,KACEC,cAAc,CAACC,EAAD,CAA6B,CACzC,KAAKxH,IAAL,CAAYwH,EAAZ,CACD,CAEDzD,eAAe,CAACvH,EAAD,CAAsB,CACnC,GAAI,CAAC,KAAKE,MAAV,CAAkB,MAAO,MAAP,CAClB,KAAM,CAAC+K,YAAD,CAAeC,OAAf,EAA0B,KAAKhL,MAAL,CAAYiL,KAAZ,CAAkB,GAAlB,CAAhC,CACA,KAAM,CAACC,YAAD,CAAeC,OAAf,EAA0BrL,EAAE,CAACmL,KAAH,CAAS,GAAT,CAAhC,CAEA;AACA,GAAIE,OAAO,EAAIJ,YAAY,GAAKG,YAA5B,EAA4CF,OAAO,GAAKG,OAA5D,CAAqE,CACnE,MAAO,KAAP,CACD,CAED;AACA,GAAIJ,YAAY,GAAKG,YAArB,CAAmC,CACjC,MAAO,MAAP,CACD,CAED;AACA;AACA;AACA;AACA,MAAOF,CAAAA,OAAO,GAAKG,OAAnB,CACD,CAED5D,YAAY,CAACzH,EAAD,CAAmB,CAC7B,KAAM,EAAGL,IAAH,EAAWK,EAAE,CAACmL,KAAH,CAAS,GAAT,CAAjB,CACA;AACA;AACA,GAAIxL,IAAI,GAAK,EAAT,EAAeA,IAAI,GAAK,KAA5B,CAAmC,CACjCqB,MAAM,CAACsK,QAAP,CAAgB,CAAhB,CAAmB,CAAnB,EACA,OACD,CAED;AACA,KAAMC,CAAAA,IAAI,CAAGxB,QAAQ,CAACyB,cAAT,CAAwB7L,IAAxB,CAAb,CACA,GAAI4L,IAAJ,CAAU,CACRA,IAAI,CAACE,cAAL,GACA,OACD,CACD;AACA;AACA,KAAMC,CAAAA,MAAM,CAAG3B,QAAQ,CAAC4B,iBAAT,CAA2BhM,IAA3B,EAAiC,CAAjC,CAAf,CACA,GAAI+L,MAAJ,CAAY,CACVA,MAAM,CAACD,cAAP,GACD,CACF,CAED1D,QAAQ,CAAC7H,MAAD,CAA0B,CAChC,MAAO,MAAKA,MAAL,GAAgBA,MAAvB,CACD,CAED;AACF;AACA;AACA;AACA;AACA,KACE,KAAM0L,CAAAA,QAAN,CACE9O,GADF,CAEEoD,MAAc,CAAGpD,GAFnB,CAGEyH,OAAwB,CAAG,EAH7B,CAIiB,CACf,GAAIoD,CAAAA,MAAM,CAAG,uCAAiB7K,GAAjB,CAAb,CAEA,GAAI,CAAEf,QAAF,EAAe4L,MAAnB,CAEA,GAAIxN,OAAO,CAACC,GAAR,CAAYC,mBAAhB,CAAqC,CACnC,GAAIkK,OAAO,CAAClJ,MAAR,GAAmB,KAAvB,CAA8B,CAC5BU,QAAQ,CAAG,6CAAqBA,QAArB,CAA+B,KAAKT,OAApC,EAA6CS,QAAxD,CACA4L,MAAM,CAAC5L,QAAP,CAAkBA,QAAlB,CACAe,GAAG,CAAG,gCAAqB6K,MAArB,CAAN,CAEA,GAAIf,CAAAA,QAAQ,CAAG,uCAAiB1G,MAAjB,CAAf,CACA,KAAM2G,CAAAA,gBAAgB,CAAG,6CACvBD,QAAQ,CAAC7K,QADc,CAEvB,KAAKT,OAFkB,CAAzB,CAIAsL,QAAQ,CAAC7K,QAAT,CAAoB8K,gBAAgB,CAAC9K,QAArC,CACAwI,OAAO,CAAClJ,MAAR,CAAiBwL,gBAAgB,CAACrL,cAAjB,EAAmC,KAAKK,aAAzD,CACAqE,MAAM,CAAG,gCAAqB0G,QAArB,CAAT,CACD,CACF,CAED,KAAMpG,CAAAA,KAAK,CAAG,KAAM,MAAKoC,UAAL,CAAgBiF,WAAhB,EAApB,CACA,GAAI5H,CAAAA,UAAU,CAAGC,MAAjB,CAEA,GAAI/F,OAAO,CAACC,GAAR,CAAY4L,mBAAZ,EAAmC9F,MAAM,CAACjF,UAAP,CAAkB,GAAlB,CAAvC,CAA+D,CAC7D,GAAI2M,CAAAA,QAAJ,CACC,CAAC,CAAEE,UAAU,CAAEF,QAAd,EAA2B,KAAM,yCAAlC,EAED,KAAMI,CAAAA,cAAc,CAAG,6BACrBtL,WAAW,CAACZ,SAAS,CAACoE,MAAD,CAAS,KAAK7E,MAAd,CAAV,CADU,CAErBmF,KAFqB,CAGrBoH,QAHqB,CAIrBD,MAAM,CAACpK,KAJc,CAKpB0K,CAAD,EAAe1H,mBAAmB,CAAC0H,CAAD,CAAIzH,KAAJ,CALb,CAMrB,KAAKlF,OANgB,CAAvB,CAQA2E,UAAU,CAAG9D,SAAS,CAACQ,WAAW,CAACqL,cAAc,CAAC9H,MAAhB,CAAZ,CAAqC,KAAK7E,MAA1C,CAAtB,CAEA,GAAI2M,cAAc,CAACE,WAAf,EAA8BF,cAAc,CAACpI,YAAjD,CAA+D,CAC7D;AACA;AACA7D,QAAQ,CAAGiM,cAAc,CAACpI,YAA1B,CACA+H,MAAM,CAAC5L,QAAP,CAAkBA,QAAlB,CACAe,GAAG,CAAG,gCAAqB6K,MAArB,CAAN,CACD,CACF,CArBD,IAqBO,CACLA,MAAM,CAAC5L,QAAP,CAAkBwE,mBAAmB,CAACoH,MAAM,CAAC5L,QAAR,CAAkByE,KAAlB,CAArC,CAEA,GAAImH,MAAM,CAAC5L,QAAP,GAAoBA,QAAxB,CAAkC,CAChCA,QAAQ,CAAG4L,MAAM,CAAC5L,QAAlB,CACAe,GAAG,CAAG,gCAAqB6K,MAArB,CAAN,CACD,CACF,CACD,KAAMtK,CAAAA,KAAK,CAAG,oDAAwBtB,QAAxB,CAAd,CAEA;AACA,GAAI5B,OAAO,CAACC,GAAR,CAAY+N,QAAZ,GAAyB,YAA7B,CAA2C,CACzC,OACD,CAED,KAAMnB,CAAAA,OAAO,CAAC6E,GAAR,CAAY,CAChB,KAAKjJ,UAAL,CAAgBkJ,MAAhB,CAAuBzO,KAAvB,EAA8BwE,IAA9B,CAAoCkK,KAAD,EAAoB,CACrD,MAAOA,CAAAA,KAAK,CACR,KAAKnB,cAAL,CACE,KAAKhI,UAAL,CAAgB8H,WAAhB,CACE5N,GADF,CAEEmD,UAFF,CAGE,IAHF,CAIE,MAAOsE,CAAAA,OAAO,CAAClJ,MAAf,GAA0B,WAA1B,CACIkJ,OAAO,CAAClJ,MADZ,CAEI,KAAKA,MANX,CADF,CADQ,CAWR,KAXJ,CAYD,CAbD,CADgB,CAehB,KAAKuH,UAAL,CAAgB2B,OAAO,CAACyH,QAAR,CAAmB,UAAnB,CAAgC,UAAhD,EAA4D3O,KAA5D,CAfgB,CAAZ,CAAN,CAiBD,CAED,KAAMiM,CAAAA,cAAN,CAAqBjM,KAArB,CAA4D,CAC1D,GAAIxC,CAAAA,SAAS,CAAG,KAAhB,CACA,KAAMoR,CAAAA,MAAM,CAAI,KAAK1I,GAAL,CAAW,IAAM,CAC/B1I,SAAS,CAAG,IAAZ,CACD,CAFD,CAIA,KAAMqR,CAAAA,eAAe,CAAG,KAAM,MAAKtJ,UAAL,CAAgBuJ,QAAhB,CAAyB9O,KAAzB,CAA9B,CAEA,GAAIxC,SAAJ,CAAe,CACb,KAAMgO,CAAAA,KAAU,CAAG,GAAIjO,CAAAA,KAAJ,CAChB,wCAAuCyC,KAAM,GAD7B,CAAnB,CAGAwL,KAAK,CAAChO,SAAN,CAAkB,IAAlB,CACA,KAAMgO,CAAAA,KAAN,CACD,CAED,GAAIoD,MAAM,GAAK,KAAK1I,GAApB,CAAyB,CACvB,KAAKA,GAAL,CAAW,IAAX,CACD,CAED,MAAO2I,CAAAA,eAAP,CACD,CAEDvB,QAAQ,CAAIyB,EAAJ,CAAsC,CAC5C,GAAIvR,CAAAA,SAAS,CAAG,KAAhB,CACA,KAAMoR,CAAAA,MAAM,CAAG,IAAM,CACnBpR,SAAS,CAAG,IAAZ,CACD,CAFD,CAGA,KAAK0I,GAAL,CAAW0I,MAAX,CACA,MAAOG,CAAAA,EAAE,GAAGvK,IAAL,CAAWK,IAAD,EAAU,CACzB,GAAI+J,MAAM,GAAK,KAAK1I,GAApB,CAAyB,CACvB,KAAKA,GAAL,CAAW,IAAX,CACD,CAED,GAAI1I,SAAJ,CAAe,CACb,KAAM2H,CAAAA,GAAQ,CAAG,GAAI5H,CAAAA,KAAJ,CAAU,iCAAV,CAAjB,CACA4H,GAAG,CAAC3H,SAAJ,CAAgB,IAAhB,CACA,KAAM2H,CAAAA,GAAN,CACD,CAED,MAAON,CAAAA,IAAP,CACD,CAZM,CAAP,CAaD,CAED0I,cAAc,CAACvI,QAAD,CAAoC,CAChD,KAAM,CAAEjD,IAAI,CAAEiN,QAAR,EAAqB,GAAIpP,CAAAA,GAAJ,CAAQoF,QAAR,CAAkBrB,MAAM,CAAC8E,QAAP,CAAgB1G,IAAlC,CAA3B,CACA,GACEjF,OAAO,CAACC,GAAR,CAAY+N,QAAZ,GAAyB,YAAzB,EACA,CAAC,KAAKjF,SADN,EAEA,KAAKE,GAAL,CAASiJ,QAAT,CAHF,CAIE,CACA,MAAOrF,CAAAA,OAAO,CAACsF,OAAR,CAAgB,KAAKlJ,GAAL,CAASiJ,QAAT,CAAhB,CAAP,CACD,CACD,MAAOjK,CAAAA,aAAa,CAACC,QAAD,CAAW,KAAKsB,KAAhB,CAAb,CAAoC9B,IAApC,CAA0CK,IAAD,EAAU,CACxD,KAAKkB,GAAL,CAASiJ,QAAT,EAAqBnK,IAArB,CACA,MAAOA,CAAAA,IAAP,CACD,CAHM,CAAP,CAID,CAED2I,cAAc,CAACxI,QAAD,CAAoC,CAChD,KAAM,CAAEjD,IAAI,CAAEmN,WAAR,EAAwB,GAAItP,CAAAA,GAAJ,CAAQoF,QAAR,CAAkBrB,MAAM,CAAC8E,QAAP,CAAgB1G,IAAlC,CAA9B,CACA,GAAI,KAAKiE,GAAL,CAASkJ,WAAT,CAAJ,CAA2B,CACzB,MAAO,MAAKlJ,GAAL,CAASkJ,WAAT,CAAP,CACD,CACD,MAAQ,MAAKlJ,GAAL,CAASkJ,WAAT,EAAwBnK,aAAa,CAACC,QAAD,CAAW,KAAKsB,KAAhB,CAAb,CAC7B9B,IAD6B,CACvBK,IAAD,EAAU,CACd,MAAO,MAAKmB,GAAL,CAASkJ,WAAT,CAAP,CACA,MAAOrK,CAAAA,IAAP,CACD,CAJ6B,EAK7BK,KAL6B,CAKtBC,GAAD,EAAS,CACd,MAAO,MAAKa,GAAL,CAASkJ,WAAT,CAAP,CACA,KAAM/J,CAAAA,GAAN,CACD,CAR6B,CAAhC,CASD,CAEDkH,eAAe,CACb3G,SADa,CAEbyJ,GAFa,CAGC,CACd,KAAM,CAAEzJ,SAAS,CAAEF,GAAb,EAAqB,KAAKM,UAAL,CAAgB,OAAhB,CAA3B,CACA,KAAMsJ,CAAAA,OAAO,CAAG,KAAK/I,QAAL,CAAcb,GAAd,CAAhB,CACA2J,GAAG,CAACC,OAAJ,CAAcA,OAAd,CACA,MAAO,+BAA4C5J,GAA5C,CAAiD,CACtD4J,OADsD,CAEtD1J,SAFsD,CAGtDhD,MAAM,CAAE,IAH8C,CAItDyM,GAJsD,CAAjD,CAAP,CAMD,CAEDnF,kBAAkB,CAACrH,EAAD,CAAaoH,UAAb,CAAgD,CAChE,GAAI,KAAK7D,GAAT,CAAc,CACZd,MAAM,CAACgB,MAAP,CAAc+D,IAAd,CACE,kBADF,CAEE/M,sBAAsB,EAFxB,CAGEuF,EAHF,CAIEoH,UAJF,EAMA,KAAK7D,GAAL,GACA,KAAKA,GAAL,CAAW,IAAX,CACD,CACF,CAEDmE,MAAM,CACJxF,IADI,CAEJ4I,WAFI,CAGW,CACf,MAAO,MAAKxH,GAAL,CACLpB,IADK,CAEL,KAAKiB,UAAL,CAAgB,OAAhB,EAAyBJ,SAFpB,CAGL+H,WAHK,CAAP,CAKD,CAppC+C,C,uBAA7BrI,M,CAoCZgB,M,CAAsB,mB", "sourcesContent": ["// tslint:disable:no-console\nimport { ParsedUrlQuery } from 'querystring'\nimport { ComponentType } from 'react'\nimport { UrlObject } from 'url'\nimport {\n  normalizePathTrailingSlash,\n  removePathTrailingSlash,\n} from '../../../client/normalize-trailing-slash'\nimport { GoodPageCache, StyleSheetTuple } from '../../../client/page-loader'\nimport {\n  getClientBuildManifest,\n  isAssetError,\n  markAssetError,\n} from '../../../client/route-loader'\nimport { DomainLocales } from '../../server/config'\nimport { denormalizePagePath } from '../../server/denormalize-page-path'\nimport { normalizeLocalePath } from '../i18n/normalize-locale-path'\nimport mitt, { MittEmitter } from '../mitt'\nimport {\n  AppContextType,\n  formatWithValidation,\n  getLocationOrigin,\n  getURL,\n  loadGetInitialProps,\n  NextPageContext,\n  ST,\n  NEXT_DATA,\n} from '../utils'\nimport { isDynamicRoute } from './utils/is-dynamic'\nimport { parseRelativeUrl } from './utils/parse-relative-url'\nimport { searchParamsToUrlQuery } from './utils/querystring'\nimport resolveRewrites from './utils/resolve-rewrites'\nimport { getRouteMatcher } from './utils/route-matcher'\nimport { getRouteRegex } from './utils/route-regex'\n\ndeclare global {\n  interface Window {\n    /* prod */\n    __NEXT_DATA__: NEXT_DATA\n  }\n}\n\ninterface RouteProperties {\n  shallow: boolean\n}\n\ninterface TransitionOptions {\n  shallow?: boolean\n  locale?: string | false\n  scroll?: boolean\n}\n\ninterface NextHistoryState {\n  url: string\n  as: string\n  options: TransitionOptions\n}\n\ntype HistoryState =\n  | null\n  | { __N: false }\n  | ({ __N: true; idx: number } & NextHistoryState)\n\nlet detectDomainLocale: typeof import('../i18n/detect-domain-locale').detectDomainLocale\n\nif (process.env.__NEXT_I18N_SUPPORT) {\n  detectDomainLocale = require('../i18n/detect-domain-locale')\n    .detectDomainLocale\n}\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nfunction buildCancellationError() {\n  return Object.assign(new Error('Route Cancelled'), {\n    cancelled: true,\n  })\n}\n\nfunction addPathPrefix(path: string, prefix?: string) {\n  return prefix && path.startsWith('/')\n    ? path === '/'\n      ? normalizePathTrailingSlash(prefix)\n      : `${prefix}${pathNoQueryHash(path) === '/' ? path.substring(1) : path}`\n    : path\n}\n\nexport function getDomainLocale(\n  path: string,\n  locale?: string | false,\n  locales?: string[],\n  domainLocales?: DomainLocales\n) {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    locale = locale || normalizeLocalePath(path, locales).detectedLocale\n\n    const detectedDomain = detectDomainLocale(domainLocales, undefined, locale)\n\n    if (detectedDomain) {\n      return `http${detectedDomain.http ? '' : 's'}://${detectedDomain.domain}${\n        basePath || ''\n      }${locale === detectedDomain.defaultLocale ? '' : `/${locale}`}${path}`\n    }\n    return false\n  }\n\n  return false\n}\n\nexport function addLocale(\n  path: string,\n  locale?: string | false,\n  defaultLocale?: string\n) {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    const pathname = pathNoQueryHash(path)\n    const pathLower = pathname.toLowerCase()\n    const localeLower = locale && locale.toLowerCase()\n\n    return locale &&\n      locale !== defaultLocale &&\n      !pathLower.startsWith('/' + localeLower + '/') &&\n      pathLower !== '/' + localeLower\n      ? addPathPrefix(path, '/' + locale)\n      : path\n  }\n  return path\n}\n\nexport function delLocale(path: string, locale?: string) {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    const pathname = pathNoQueryHash(path)\n    const pathLower = pathname.toLowerCase()\n    const localeLower = locale && locale.toLowerCase()\n\n    return locale &&\n      (pathLower.startsWith('/' + localeLower + '/') ||\n        pathLower === '/' + localeLower)\n      ? (pathname.length === locale.length + 1 ? '/' : '') +\n          path.substr(locale.length + 1)\n      : path\n  }\n  return path\n}\n\nfunction pathNoQueryHash(path: string) {\n  const queryIndex = path.indexOf('?')\n  const hashIndex = path.indexOf('#')\n\n  if (queryIndex > -1 || hashIndex > -1) {\n    path = path.substring(0, queryIndex > -1 ? queryIndex : hashIndex)\n  }\n  return path\n}\n\nexport function hasBasePath(path: string): boolean {\n  path = pathNoQueryHash(path)\n  return path === basePath || path.startsWith(basePath + '/')\n}\n\nexport function addBasePath(path: string): string {\n  // we only add the basepath on relative urls\n  return addPathPrefix(path, basePath)\n}\n\nexport function delBasePath(path: string): string {\n  path = path.slice(basePath.length)\n  if (!path.startsWith('/')) path = `/${path}`\n  return path\n}\n\n/**\n * Detects whether a given url is routable by the Next.js router (browser only).\n */\nexport function isLocalURL(url: string): boolean {\n  // prevent a hydration mismatch on href for url with anchor refs\n  if (url.startsWith('/') || url.startsWith('#')) return true\n  try {\n    // absolute urls can be local if they are on the same origin\n    const locationOrigin = getLocationOrigin()\n    const resolved = new URL(url, locationOrigin)\n    return resolved.origin === locationOrigin && hasBasePath(resolved.pathname)\n  } catch (_) {\n    return false\n  }\n}\n\ntype Url = UrlObject | string\n\nexport function interpolateAs(\n  route: string,\n  asPathname: string,\n  query: ParsedUrlQuery\n) {\n  let interpolatedRoute = ''\n\n  const dynamicRegex = getRouteRegex(route)\n  const dynamicGroups = dynamicRegex.groups\n  const dynamicMatches =\n    // Try to match the dynamic route against the asPath\n    (asPathname !== route ? getRouteMatcher(dynamicRegex)(asPathname) : '') ||\n    // Fall back to reading the values from the href\n    // TODO: should this take priority; also need to change in the router.\n    query\n\n  interpolatedRoute = route\n  const params = Object.keys(dynamicGroups)\n\n  if (\n    !params.every((param) => {\n      let value = dynamicMatches[param] || ''\n      const { repeat, optional } = dynamicGroups[param]\n\n      // support single-level catch-all\n      // TODO: more robust handling for user-error (passing `/`)\n      let replaced = `[${repeat ? '...' : ''}${param}]`\n      if (optional) {\n        replaced = `${!value ? '/' : ''}[${replaced}]`\n      }\n      if (repeat && !Array.isArray(value)) value = [value]\n\n      return (\n        (optional || param in dynamicMatches) &&\n        // Interpolate group into data URL if present\n        (interpolatedRoute =\n          interpolatedRoute!.replace(\n            replaced,\n            repeat\n              ? (value as string[])\n                  .map(\n                    // these values should be fully encoded instead of just\n                    // path delimiter escaped since they are being inserted\n                    // into the URL and we expect URL encoded segments\n                    // when parsing dynamic route params\n                    (segment) => encodeURIComponent(segment)\n                  )\n                  .join('/')\n              : encodeURIComponent(value as string)\n          ) || '/')\n      )\n    })\n  ) {\n    interpolatedRoute = '' // did not satisfy all requirements\n\n    // n.b. We ignore this error because we handle warning for this case in\n    // development in the `<Link>` component directly.\n  }\n  return {\n    params,\n    result: interpolatedRoute,\n  }\n}\n\nfunction omitParmsFromQuery(query: ParsedUrlQuery, params: string[]) {\n  const filteredQuery: ParsedUrlQuery = {}\n\n  Object.keys(query).forEach((key) => {\n    if (!params.includes(key)) {\n      filteredQuery[key] = query[key]\n    }\n  })\n  return filteredQuery\n}\n\n/**\n * Resolves a given hyperlink with a certain router state (basePath not included).\n * Preserves absolute urls.\n */\nexport function resolveHref(\n  currentPath: string,\n  href: Url,\n  resolveAs?: boolean\n): string {\n  // we use a dummy base url for relative urls\n  let base: URL\n\n  try {\n    base = new URL(currentPath, 'http://n')\n  } catch (_) {\n    // fallback to / for invalid asPath values e.g. //\n    base = new URL('/', 'http://n')\n  }\n  const urlAsString =\n    typeof href === 'string' ? href : formatWithValidation(href)\n  // Return because it cannot be routed by the Next.js router\n  if (!isLocalURL(urlAsString)) {\n    return (resolveAs ? [urlAsString] : urlAsString) as string\n  }\n  try {\n    const finalUrl = new URL(urlAsString, base)\n    finalUrl.pathname = normalizePathTrailingSlash(finalUrl.pathname)\n    let interpolatedAs = ''\n\n    if (\n      isDynamicRoute(finalUrl.pathname) &&\n      finalUrl.searchParams &&\n      resolveAs\n    ) {\n      const query = searchParamsToUrlQuery(finalUrl.searchParams)\n\n      const { result, params } = interpolateAs(\n        finalUrl.pathname,\n        finalUrl.pathname,\n        query\n      )\n\n      if (result) {\n        interpolatedAs = formatWithValidation({\n          pathname: result,\n          hash: finalUrl.hash,\n          query: omitParmsFromQuery(query, params),\n        })\n      }\n    }\n\n    // if the origin didn't change, it means we received a relative href\n    const resolvedHref =\n      finalUrl.origin === base.origin\n        ? finalUrl.href.slice(finalUrl.origin.length)\n        : finalUrl.href\n\n    return (resolveAs\n      ? [resolvedHref, interpolatedAs || resolvedHref]\n      : resolvedHref) as string\n  } catch (_) {\n    return (resolveAs ? [urlAsString] : urlAsString) as string\n  }\n}\n\nfunction stripOrigin(url: string) {\n  const origin = getLocationOrigin()\n\n  return url.startsWith(origin) ? url.substring(origin.length) : url\n}\n\nfunction prepareUrlAs(router: NextRouter, url: Url, as?: Url) {\n  // If url and as provided as an object representation,\n  // we'll format them into the string version here.\n  let [resolvedHref, resolvedAs] = resolveHref(router.asPath, url, true)\n  const origin = getLocationOrigin()\n  const hrefHadOrigin = resolvedHref.startsWith(origin)\n  const asHadOrigin = resolvedAs && resolvedAs.startsWith(origin)\n\n  resolvedHref = stripOrigin(resolvedHref)\n  resolvedAs = resolvedAs ? stripOrigin(resolvedAs) : resolvedAs\n\n  const preparedUrl = hrefHadOrigin ? resolvedHref : addBasePath(resolvedHref)\n  const preparedAs = as\n    ? stripOrigin(resolveHref(router.asPath, as))\n    : resolvedAs || resolvedHref\n\n  return {\n    url: preparedUrl,\n    as: asHadOrigin ? preparedAs : addBasePath(preparedAs),\n  }\n}\n\nfunction resolveDynamicRoute(pathname: string, pages: string[]) {\n  const cleanPathname = removePathTrailingSlash(denormalizePagePath(pathname!))\n\n  if (cleanPathname === '/404' || cleanPathname === '/_error') {\n    return pathname\n  }\n\n  // handle resolving href for dynamic routes\n  if (!pages.includes(cleanPathname!)) {\n    // eslint-disable-next-line array-callback-return\n    pages.some((page) => {\n      if (isDynamicRoute(page) && getRouteRegex(page).re.test(cleanPathname!)) {\n        pathname = page\n        return true\n      }\n    })\n  }\n  return removePathTrailingSlash(pathname)\n}\n\nexport type BaseRouter = {\n  route: string\n  pathname: string\n  query: ParsedUrlQuery\n  asPath: string\n  basePath: string\n  locale?: string\n  locales?: string[]\n  defaultLocale?: string\n  domainLocales?: DomainLocales\n  isLocaleDomain: boolean\n}\n\nexport type NextRouter = BaseRouter &\n  Pick<\n    Router,\n    | 'push'\n    | 'replace'\n    | 'reload'\n    | 'back'\n    | 'prefetch'\n    | 'beforePopState'\n    | 'events'\n    | 'isFallback'\n    | 'isReady'\n    | 'isPreview'\n  >\n\nexport type PrefetchOptions = {\n  priority?: boolean\n  locale?: string | false\n}\n\nexport type PrivateRouteInfo =\n  | (Omit<CompletePrivateRouteInfo, 'styleSheets'> & { initial: true })\n  | CompletePrivateRouteInfo\n\nexport type CompletePrivateRouteInfo = {\n  Component: ComponentType\n  styleSheets: StyleSheetTuple[]\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n  props?: Record<string, any>\n  err?: Error\n  error?: any\n}\n\nexport type AppProps = Pick<CompletePrivateRouteInfo, 'Component' | 'err'> & {\n  router: Router\n} & Record<string, any>\nexport type AppComponent = ComponentType<AppProps>\n\ntype Subscription = (\n  data: PrivateRouteInfo,\n  App: AppComponent,\n  resetScroll: { x: number; y: number } | null\n) => Promise<void>\n\ntype BeforePopStateCallback = (state: NextHistoryState) => boolean\n\ntype ComponentLoadCancel = (() => void) | null\n\ntype HistoryMethod = 'replaceState' | 'pushState'\n\nconst manualScrollRestoration =\n  process.env.__NEXT_SCROLL_RESTORATION &&\n  typeof window !== 'undefined' &&\n  'scrollRestoration' in window.history &&\n  !!(function () {\n    try {\n      let v = '__next'\n      // eslint-disable-next-line no-sequences\n      return sessionStorage.setItem(v, v), sessionStorage.removeItem(v), true\n    } catch (n) {}\n  })()\n\nconst SSG_DATA_NOT_FOUND = Symbol('SSG_DATA_NOT_FOUND')\n\nfunction fetchRetry(url: string, attempts: number): Promise<any> {\n  return fetch(url, {\n    // Cookies are required to be present for Next.js' SSG \"Preview Mode\".\n    // Cookies may also be required for `getServerSideProps`.\n    //\n    // > `fetch` won’t send cookies, unless you set the credentials init\n    // > option.\n    // https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API/Using_Fetch\n    //\n    // > For maximum browser compatibility when it comes to sending &\n    // > receiving cookies, always supply the `credentials: 'same-origin'`\n    // > option instead of relying on the default.\n    // https://github.com/github/fetch#caveats\n    credentials: 'same-origin',\n  }).then((res) => {\n    if (!res.ok) {\n      if (attempts > 1 && res.status >= 500) {\n        return fetchRetry(url, attempts - 1)\n      }\n      if (res.status === 404) {\n        return res.json().then((data) => {\n          if (data.notFound) {\n            return { notFound: SSG_DATA_NOT_FOUND }\n          }\n          throw new Error(`Failed to load static props`)\n        })\n      }\n      throw new Error(`Failed to load static props`)\n    }\n    return res.json()\n  })\n}\n\nfunction fetchNextData(dataHref: string, isServerRender: boolean) {\n  return fetchRetry(dataHref, isServerRender ? 3 : 1).catch((err: Error) => {\n    // We should only trigger a server-side transition if this was caused\n    // on a client-side transition. Otherwise, we'd get into an infinite\n    // loop.\n\n    if (!isServerRender) {\n      markAssetError(err)\n    }\n    throw err\n  })\n}\n\nexport default class Router implements BaseRouter {\n  route: string\n  pathname: string\n  query: ParsedUrlQuery\n  asPath: string\n  basePath: string\n\n  /**\n   * Map of all components loaded in `Router`\n   */\n  components: { [pathname: string]: PrivateRouteInfo }\n  // Static Data Cache\n  sdc: { [asPath: string]: object } = {}\n  // In-flight Server Data Requests, for deduping\n  sdr: { [asPath: string]: Promise<object> } = {}\n\n  sub: Subscription\n  clc: ComponentLoadCancel\n  pageLoader: any\n  _bps: BeforePopStateCallback | undefined\n  events: MittEmitter\n  _wrapApp: (App: AppComponent) => any\n  isSsr: boolean\n  isFallback: boolean\n  _inFlightRoute?: string\n  _shallow?: boolean\n  locale?: string\n  locales?: string[]\n  defaultLocale?: string\n  domainLocales?: DomainLocales\n  isReady: boolean\n  isPreview: boolean\n  isLocaleDomain: boolean\n\n  private _idx: number = 0\n\n  static events: MittEmitter = mitt()\n\n  constructor(\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    {\n      initialProps,\n      pageLoader,\n      App,\n      wrapApp,\n      Component,\n      err,\n      subscription,\n      isFallback,\n      locale,\n      locales,\n      defaultLocale,\n      domainLocales,\n      isPreview,\n    }: {\n      subscription: Subscription\n      initialProps: any\n      pageLoader: any\n      Component: ComponentType\n      App: AppComponent\n      wrapApp: (WrapAppComponent: AppComponent) => any\n      err?: Error\n      isFallback: boolean\n      locale?: string\n      locales?: string[]\n      defaultLocale?: string\n      domainLocales?: DomainLocales\n      isPreview?: boolean\n    }\n  ) {\n    // represents the current component key\n    this.route = removePathTrailingSlash(pathname)\n\n    // set up the component cache (by route keys)\n    this.components = {}\n    // We should not keep the cache, if there's an error\n    // Otherwise, this cause issues when when going back and\n    // come again to the errored page.\n    if (pathname !== '/_error') {\n      this.components[this.route] = {\n        Component,\n        initial: true,\n        props: initialProps,\n        err,\n        __N_SSG: initialProps && initialProps.__N_SSG,\n        __N_SSP: initialProps && initialProps.__N_SSP,\n      }\n    }\n\n    this.components['/_app'] = {\n      Component: App as ComponentType,\n      styleSheets: [\n        /* /_app does not need its stylesheets managed */\n      ],\n    }\n\n    // Backwards compat for Router.router.events\n    // TODO: Should be remove the following major version as it was never documented\n    this.events = Router.events\n\n    this.pageLoader = pageLoader\n    this.pathname = pathname\n    this.query = query\n    // if auto prerendered and dynamic route wait to update asPath\n    // until after mount to prevent hydration mismatch\n    const autoExportDynamic =\n      isDynamicRoute(pathname) && self.__NEXT_DATA__.autoExport\n\n    this.asPath = autoExportDynamic ? pathname : as\n    this.basePath = basePath\n    this.sub = subscription\n    this.clc = null\n    this._wrapApp = wrapApp\n    // make sure to ignore extra popState in safari on navigating\n    // back from external site\n    this.isSsr = true\n\n    this.isFallback = isFallback\n\n    this.isReady = !!(\n      self.__NEXT_DATA__.gssp ||\n      self.__NEXT_DATA__.gip ||\n      (!autoExportDynamic &&\n        !self.location.search &&\n        !process.env.__NEXT_HAS_REWRITES)\n    )\n    this.isPreview = !!isPreview\n    this.isLocaleDomain = false\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      this.locale = locale\n      this.locales = locales\n      this.defaultLocale = defaultLocale\n      this.domainLocales = domainLocales\n      this.isLocaleDomain = !!detectDomainLocale(\n        domainLocales,\n        self.location.hostname\n      )\n    }\n\n    if (typeof window !== 'undefined') {\n      // make sure \"as\" doesn't start with double slashes or else it can\n      // throw an error as it's considered invalid\n      if (as.substr(0, 2) !== '//') {\n        // in order for `e.state` to work on the `onpopstate` event\n        // we have to register the initial route upon initialization\n        this.changeState(\n          'replaceState',\n          formatWithValidation({ pathname: addBasePath(pathname), query }),\n          getURL(),\n          { locale }\n        )\n      }\n\n      window.addEventListener('popstate', this.onPopState)\n\n      // enable custom scroll restoration handling when available\n      // otherwise fallback to browser's default handling\n      if (process.env.__NEXT_SCROLL_RESTORATION) {\n        if (manualScrollRestoration) {\n          window.history.scrollRestoration = 'manual'\n        }\n      }\n    }\n  }\n\n  onPopState = (e: PopStateEvent): void => {\n    const state = e.state as HistoryState\n\n    if (!state) {\n      // We get state as undefined for two reasons.\n      //  1. With older safari (< 8) and older chrome (< 34)\n      //  2. When the URL changed with #\n      //\n      // In the both cases, we don't need to proceed and change the route.\n      // (as it's already changed)\n      // But we can simply replace the state with the new changes.\n      // Actually, for (1) we don't need to nothing. But it's hard to detect that event.\n      // So, doing the following for (1) does no harm.\n      const { pathname, query } = this\n      this.changeState(\n        'replaceState',\n        formatWithValidation({ pathname: addBasePath(pathname), query }),\n        getURL()\n      )\n      return\n    }\n\n    if (!state.__N) {\n      return\n    }\n\n    let forcedScroll: { x: number; y: number } | undefined\n    const { url, as, options, idx } = state\n    if (process.env.__NEXT_SCROLL_RESTORATION) {\n      if (manualScrollRestoration) {\n        if (this._idx !== idx) {\n          // Snapshot current scroll position:\n          try {\n            sessionStorage.setItem(\n              '__next_scroll_' + this._idx,\n              JSON.stringify({ x: self.pageXOffset, y: self.pageYOffset })\n            )\n          } catch {}\n\n          // Restore old scroll position:\n          try {\n            const v = sessionStorage.getItem('__next_scroll_' + idx)\n            forcedScroll = JSON.parse(v!)\n          } catch {\n            forcedScroll = { x: 0, y: 0 }\n          }\n        }\n      }\n    }\n    this._idx = idx\n\n    const { pathname } = parseRelativeUrl(url)\n\n    // Make sure we don't re-render on initial load,\n    // can be caused by navigating back from an external site\n    if (this.isSsr && as === this.asPath && pathname === this.pathname) {\n      return\n    }\n\n    // If the downstream application returns falsy, return.\n    // They will then be responsible for handling the event.\n    if (this._bps && !this._bps(state)) {\n      return\n    }\n\n    this.change(\n      'replaceState',\n      url,\n      as,\n      Object.assign<{}, TransitionOptions, TransitionOptions>({}, options, {\n        shallow: options.shallow && this._shallow,\n        locale: options.locale || this.defaultLocale,\n      }),\n      forcedScroll\n    )\n  }\n\n  reload(): void {\n    window.location.reload()\n  }\n\n  /**\n   * Go back in history\n   */\n  back() {\n    window.history.back()\n  }\n\n  /**\n   * Performs a `pushState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */\n  push(url: Url, as?: Url, options: TransitionOptions = {}) {\n    if (process.env.__NEXT_SCROLL_RESTORATION) {\n      // TODO: remove in the future when we update history before route change\n      // is complete, as the popstate event should handle this capture.\n      if (manualScrollRestoration) {\n        try {\n          // Snapshot scroll position right before navigating to a new page:\n          sessionStorage.setItem(\n            '__next_scroll_' + this._idx,\n            JSON.stringify({ x: self.pageXOffset, y: self.pageYOffset })\n          )\n        } catch {}\n      }\n    }\n    ;({ url, as } = prepareUrlAs(this, url, as))\n    return this.change('pushState', url, as, options)\n  }\n\n  /**\n   * Performs a `replaceState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */\n  replace(url: Url, as?: Url, options: TransitionOptions = {}) {\n    ;({ url, as } = prepareUrlAs(this, url, as))\n    return this.change('replaceState', url, as, options)\n  }\n\n  private async change(\n    method: HistoryMethod,\n    url: string,\n    as: string,\n    options: TransitionOptions,\n    forcedScroll?: { x: number; y: number }\n  ): Promise<boolean> {\n    if (!isLocalURL(url)) {\n      window.location.href = url\n      return false\n    }\n    const shouldResolveHref = url === as || (options as any)._h\n\n    // for static pages with query params in the URL we delay\n    // marking the router ready until after the query is updated\n    if ((options as any)._h) {\n      this.isReady = true\n    }\n\n    // Default to scroll reset behavior unless explicitly specified to be\n    // `false`! This makes the behavior between using `Router#push` and a\n    // `<Link />` consistent.\n    options.scroll = !!(options.scroll ?? true)\n\n    let localeChange = options.locale !== this.locale\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      this.locale =\n        options.locale === false\n          ? this.defaultLocale\n          : options.locale || this.locale\n\n      if (typeof options.locale === 'undefined') {\n        options.locale = this.locale\n      }\n\n      const parsedAs = parseRelativeUrl(hasBasePath(as) ? delBasePath(as) : as)\n      const localePathResult = normalizeLocalePath(\n        parsedAs.pathname,\n        this.locales\n      )\n\n      if (localePathResult.detectedLocale) {\n        this.locale = localePathResult.detectedLocale\n        parsedAs.pathname = addBasePath(parsedAs.pathname)\n        as = formatWithValidation(parsedAs)\n        url = addBasePath(\n          normalizeLocalePath(\n            hasBasePath(url) ? delBasePath(url) : url,\n            this.locales\n          ).pathname\n        )\n      }\n      let didNavigate = false\n\n      // we need to wrap this in the env check again since regenerator runtime\n      // moves this on its own due to the return\n      if (process.env.__NEXT_I18N_SUPPORT) {\n        // if the locale isn't configured hard navigate to show 404 page\n        if (!this.locales?.includes(this.locale!)) {\n          parsedAs.pathname = addLocale(parsedAs.pathname, this.locale)\n          window.location.href = formatWithValidation(parsedAs)\n          // this was previously a return but was removed in favor\n          // of better dead code elimination with regenerator runtime\n          didNavigate = true\n        }\n      }\n\n      const detectedDomain = detectDomainLocale(\n        this.domainLocales,\n        undefined,\n        this.locale\n      )\n\n      // we need to wrap this in the env check again since regenerator runtime\n      // moves this on its own due to the return\n      if (process.env.__NEXT_I18N_SUPPORT) {\n        // if we are navigating to a domain locale ensure we redirect to the\n        // correct domain\n        if (\n          !didNavigate &&\n          detectedDomain &&\n          this.isLocaleDomain &&\n          self.location.hostname !== detectedDomain.domain\n        ) {\n          const asNoBasePath = delBasePath(as)\n          window.location.href = `http${detectedDomain.http ? '' : 's'}://${\n            detectedDomain.domain\n          }${addBasePath(\n            `${\n              this.locale === detectedDomain.defaultLocale\n                ? ''\n                : `/${this.locale}`\n            }${asNoBasePath === '/' ? '' : asNoBasePath}` || '/'\n          )}`\n          // this was previously a return but was removed in favor\n          // of better dead code elimination with regenerator runtime\n          didNavigate = true\n        }\n      }\n\n      if (didNavigate) {\n        return new Promise(() => {})\n      }\n    }\n\n    if (!(options as any)._h) {\n      this.isSsr = false\n    }\n    // marking route changes as a navigation start entry\n    if (ST) {\n      performance.mark('routeChange')\n    }\n\n    const { shallow = false } = options\n    const routeProps = { shallow }\n\n    if (this._inFlightRoute) {\n      this.abortComponentLoad(this._inFlightRoute, routeProps)\n    }\n\n    as = addBasePath(\n      addLocale(\n        hasBasePath(as) ? delBasePath(as) : as,\n        options.locale,\n        this.defaultLocale\n      )\n    )\n    const cleanedAs = delLocale(\n      hasBasePath(as) ? delBasePath(as) : as,\n      this.locale\n    )\n    this._inFlightRoute = as\n\n    // If the url change is only related to a hash change\n    // We should not proceed. We should only change the state.\n\n    // WARNING: `_h` is an internal option for handing Next.js client-side\n    // hydration. Your app should _never_ use this property. It may change at\n    // any time without notice.\n    if (!(options as any)._h && this.onlyAHashChange(cleanedAs)) {\n      this.asPath = cleanedAs\n      Router.events.emit('hashChangeStart', as, routeProps)\n      // TODO: do we need the resolved href when only a hash change?\n      this.changeState(method, url, as, options)\n      this.scrollToHash(cleanedAs)\n      this.notify(this.components[this.route], null)\n      Router.events.emit('hashChangeComplete', as, routeProps)\n      return true\n    }\n\n    let parsed = parseRelativeUrl(url)\n    let { pathname, query } = parsed\n\n    // The build manifest needs to be loaded before auto-static dynamic pages\n    // get their query parameters to allow ensuring they can be parsed properly\n    // when rewritten to\n    let pages: any, rewrites: any\n    try {\n      pages = await this.pageLoader.getPageList()\n      ;({ __rewrites: rewrites } = await getClientBuildManifest())\n    } catch (err) {\n      // If we fail to resolve the page list or client-build manifest, we must\n      // do a server-side transition:\n      window.location.href = as\n      return false\n    }\n\n    // If asked to change the current URL we should reload the current page\n    // (not location.reload() but reload getInitialProps and other Next.js stuffs)\n    // We also need to set the method = replaceState always\n    // as this should not go into the history (That's how browsers work)\n    // We should compare the new asPath to the current asPath, not the url\n    if (!this.urlIsNew(cleanedAs) && !localeChange) {\n      method = 'replaceState'\n    }\n\n    // we need to resolve the as value using rewrites for dynamic SSG\n    // pages to allow building the data URL correctly\n    let resolvedAs = as\n\n    // url and as should always be prefixed with basePath by this\n    // point by either next/link or router.push/replace so strip the\n    // basePath from the pathname to match the pages dir 1-to-1\n    pathname = pathname\n      ? removePathTrailingSlash(delBasePath(pathname))\n      : pathname\n\n    if (shouldResolveHref && pathname !== '/_error') {\n      if (process.env.__NEXT_HAS_REWRITES && as.startsWith('/')) {\n        const rewritesResult = resolveRewrites(\n          addBasePath(addLocale(cleanedAs, this.locale)),\n          pages,\n          rewrites,\n          query,\n          (p: string) => resolveDynamicRoute(p, pages),\n          this.locales\n        )\n        resolvedAs = rewritesResult.asPath\n\n        if (rewritesResult.matchedPage && rewritesResult.resolvedHref) {\n          // if this directly matches a page we need to update the href to\n          // allow the correct page chunk to be loaded\n          pathname = rewritesResult.resolvedHref\n          parsed.pathname = pathname\n          url = formatWithValidation(parsed)\n        }\n      } else {\n        parsed.pathname = resolveDynamicRoute(pathname, pages)\n\n        if (parsed.pathname !== pathname) {\n          pathname = parsed.pathname\n          url = formatWithValidation(parsed)\n        }\n      }\n    }\n\n    const route = removePathTrailingSlash(pathname)\n\n    if (!isLocalURL(as)) {\n      if (process.env.NODE_ENV !== 'production') {\n        throw new Error(\n          `Invalid href: \"${url}\" and as: \"${as}\", received relative href and external as` +\n            `\\nSee more info: https://nextjs.org/docs/messages/invalid-relative-url-external-as`\n        )\n      }\n\n      window.location.href = as\n      return false\n    }\n\n    resolvedAs = delLocale(delBasePath(resolvedAs), this.locale)\n\n    if (isDynamicRoute(route)) {\n      const parsedAs = parseRelativeUrl(resolvedAs)\n      const asPathname = parsedAs.pathname\n\n      const routeRegex = getRouteRegex(route)\n      const routeMatch = getRouteMatcher(routeRegex)(asPathname)\n      const shouldInterpolate = route === asPathname\n      const interpolatedAs = shouldInterpolate\n        ? interpolateAs(route, asPathname, query)\n        : ({} as { result: undefined; params: undefined })\n\n      if (!routeMatch || (shouldInterpolate && !interpolatedAs.result)) {\n        const missingParams = Object.keys(routeRegex.groups).filter(\n          (param) => !query[param]\n        )\n\n        if (missingParams.length > 0) {\n          if (process.env.NODE_ENV !== 'production') {\n            console.warn(\n              `${\n                shouldInterpolate\n                  ? `Interpolating href`\n                  : `Mismatching \\`as\\` and \\`href\\``\n              } failed to manually provide ` +\n                `the params: ${missingParams.join(\n                  ', '\n                )} in the \\`href\\`'s \\`query\\``\n            )\n          }\n\n          throw new Error(\n            (shouldInterpolate\n              ? `The provided \\`href\\` (${url}) value is missing query values (${missingParams.join(\n                  ', '\n                )}) to be interpolated properly. `\n              : `The provided \\`as\\` value (${asPathname}) is incompatible with the \\`href\\` value (${route}). `) +\n              `Read more: https://nextjs.org/docs/messages/${\n                shouldInterpolate\n                  ? 'href-interpolation-failed'\n                  : 'incompatible-href-as'\n              }`\n          )\n        }\n      } else if (shouldInterpolate) {\n        as = formatWithValidation(\n          Object.assign({}, parsedAs, {\n            pathname: interpolatedAs.result,\n            query: omitParmsFromQuery(query, interpolatedAs.params!),\n          })\n        )\n      } else {\n        // Merge params into `query`, overwriting any specified in search\n        Object.assign(query, routeMatch)\n      }\n    }\n\n    Router.events.emit('routeChangeStart', as, routeProps)\n\n    try {\n      let routeInfo = await this.getRouteInfo(\n        route,\n        pathname,\n        query,\n        as,\n        resolvedAs,\n        routeProps\n      )\n      let { error, props, __N_SSG, __N_SSP } = routeInfo\n\n      // handle redirect on client-transition\n      if ((__N_SSG || __N_SSP) && props) {\n        if ((props as any).pageProps && (props as any).pageProps.__N_REDIRECT) {\n          const destination = (props as any).pageProps.__N_REDIRECT\n\n          // check if destination is internal (resolves to a page) and attempt\n          // client-navigation if it is falling back to hard navigation if\n          // it's not\n          if (destination.startsWith('/')) {\n            const parsedHref = parseRelativeUrl(destination)\n            parsedHref.pathname = resolveDynamicRoute(\n              parsedHref.pathname,\n              pages\n            )\n\n            if (pages.includes(parsedHref.pathname)) {\n              const { url: newUrl, as: newAs } = prepareUrlAs(\n                this,\n                destination,\n                destination\n              )\n              return this.change(method, newUrl, newAs, options)\n            }\n          }\n\n          window.location.href = destination\n          return new Promise(() => {})\n        }\n\n        this.isPreview = !!props.__N_PREVIEW\n\n        // handle SSG data 404\n        if (props.notFound === SSG_DATA_NOT_FOUND) {\n          let notFoundRoute\n\n          try {\n            await this.fetchComponent('/404')\n            notFoundRoute = '/404'\n          } catch (_) {\n            notFoundRoute = '/_error'\n          }\n\n          routeInfo = await this.getRouteInfo(\n            notFoundRoute,\n            notFoundRoute,\n            query,\n            as,\n            resolvedAs,\n            { shallow: false }\n          )\n        }\n      }\n\n      Router.events.emit('beforeHistoryChange', as, routeProps)\n      this.changeState(method, url, as, options)\n\n      if (process.env.NODE_ENV !== 'production') {\n        const appComp: any = this.components['/_app'].Component\n        ;(window as any).next.isPrerendered =\n          appComp.getInitialProps === appComp.origGetInitialProps &&\n          !(routeInfo.Component as any).getInitialProps\n      }\n\n      // shallow routing is only allowed for same page URL changes.\n      const isValidShallowRoute = options.shallow && this.route === route\n\n      if (\n        (options as any)._h &&\n        pathname === '/_error' &&\n        self.__NEXT_DATA__.props?.pageProps?.statusCode === 500 &&\n        props?.pageProps\n      ) {\n        // ensure statusCode is still correct for static 500 page\n        // when updating query information\n        props.pageProps.statusCode = 500\n      }\n\n      await this.set(\n        route,\n        pathname!,\n        query,\n        cleanedAs,\n        routeInfo,\n        forcedScroll ||\n          (isValidShallowRoute || !options.scroll ? null : { x: 0, y: 0 })\n      ).catch((e) => {\n        if (e.cancelled) error = error || e\n        else throw e\n      })\n\n      if (error) {\n        Router.events.emit('routeChangeError', error, cleanedAs, routeProps)\n        throw error\n      }\n\n      if (process.env.__NEXT_I18N_SUPPORT) {\n        if (this.locale) {\n          document.documentElement.lang = this.locale\n        }\n      }\n      Router.events.emit('routeChangeComplete', as, routeProps)\n\n      return true\n    } catch (err) {\n      if (err.cancelled) {\n        return false\n      }\n      throw err\n    }\n  }\n\n  changeState(\n    method: HistoryMethod,\n    url: string,\n    as: string,\n    options: TransitionOptions = {}\n  ): void {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof window.history === 'undefined') {\n        console.error(`Warning: window.history is not available.`)\n        return\n      }\n\n      if (typeof window.history[method] === 'undefined') {\n        console.error(`Warning: window.history.${method} is not available`)\n        return\n      }\n    }\n\n    if (method !== 'pushState' || getURL() !== as) {\n      this._shallow = options.shallow\n      window.history[method](\n        {\n          url,\n          as,\n          options,\n          __N: true,\n          idx: this._idx = method !== 'pushState' ? this._idx : this._idx + 1,\n        } as HistoryState,\n        // Most browsers currently ignores this parameter, although they may use it in the future.\n        // Passing the empty string here should be safe against future changes to the method.\n        // https://developer.mozilla.org/en-US/docs/Web/API/History/replaceState\n        '',\n        as\n      )\n    }\n  }\n\n  async handleRouteInfoError(\n    err: Error & { code: any; cancelled: boolean },\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    routeProps: RouteProperties,\n    loadErrorFail?: boolean\n  ): Promise<CompletePrivateRouteInfo> {\n    if (err.cancelled) {\n      // bubble up cancellation errors\n      throw err\n    }\n\n    if (isAssetError(err) || loadErrorFail) {\n      Router.events.emit('routeChangeError', err, as, routeProps)\n\n      // If we can't load the page it could be one of following reasons\n      //  1. Page doesn't exists\n      //  2. Page does exist in a different zone\n      //  3. Internal error while loading the page\n\n      // So, doing a hard reload is the proper way to deal with this.\n      window.location.href = as\n\n      // Changing the URL doesn't block executing the current code path.\n      // So let's throw a cancellation error stop the routing logic.\n      throw buildCancellationError()\n    }\n\n    try {\n      let Component: ComponentType\n      let styleSheets: StyleSheetTuple[]\n      let props: Record<string, any> | undefined\n\n      if (\n        typeof Component! === 'undefined' ||\n        typeof styleSheets! === 'undefined'\n      ) {\n        ;({ page: Component, styleSheets } = await this.fetchComponent(\n          '/_error'\n        ))\n      }\n\n      const routeInfo: CompletePrivateRouteInfo = {\n        props,\n        Component,\n        styleSheets,\n        err,\n        error: err,\n      }\n\n      if (!routeInfo.props) {\n        try {\n          routeInfo.props = await this.getInitialProps(Component, {\n            err,\n            pathname,\n            query,\n          } as any)\n        } catch (gipErr) {\n          console.error('Error in error page `getInitialProps`: ', gipErr)\n          routeInfo.props = {}\n        }\n      }\n\n      return routeInfo\n    } catch (routeInfoErr) {\n      return this.handleRouteInfoError(\n        routeInfoErr,\n        pathname,\n        query,\n        as,\n        routeProps,\n        true\n      )\n    }\n  }\n\n  async getRouteInfo(\n    route: string,\n    pathname: string,\n    query: any,\n    as: string,\n    resolvedAs: string,\n    routeProps: RouteProperties\n  ): Promise<PrivateRouteInfo> {\n    try {\n      const existingRouteInfo: PrivateRouteInfo | undefined = this.components[\n        route\n      ]\n      if (routeProps.shallow && existingRouteInfo && this.route === route) {\n        return existingRouteInfo\n      }\n\n      const cachedRouteInfo: CompletePrivateRouteInfo | undefined =\n        existingRouteInfo && 'initial' in existingRouteInfo\n          ? undefined\n          : existingRouteInfo\n      const routeInfo: CompletePrivateRouteInfo = cachedRouteInfo\n        ? cachedRouteInfo\n        : await this.fetchComponent(route).then((res) => ({\n            Component: res.page,\n            styleSheets: res.styleSheets,\n            __N_SSG: res.mod.__N_SSG,\n            __N_SSP: res.mod.__N_SSP,\n          }))\n\n      const { Component, __N_SSG, __N_SSP } = routeInfo\n\n      if (process.env.NODE_ENV !== 'production') {\n        const { isValidElementType } = require('react-is')\n        if (!isValidElementType(Component)) {\n          throw new Error(\n            `The default export is not a React Component in page: \"${pathname}\"`\n          )\n        }\n      }\n\n      let dataHref: string | undefined\n\n      if (__N_SSG || __N_SSP) {\n        dataHref = this.pageLoader.getDataHref(\n          formatWithValidation({ pathname, query }),\n          resolvedAs,\n          __N_SSG,\n          this.locale\n        )\n      }\n\n      const props = await this._getData<CompletePrivateRouteInfo>(() =>\n        __N_SSG\n          ? this._getStaticData(dataHref!)\n          : __N_SSP\n          ? this._getServerData(dataHref!)\n          : this.getInitialProps(\n              Component,\n              // we provide AppTree later so this needs to be `any`\n              {\n                pathname,\n                query,\n                asPath: as,\n                locale: this.locale,\n                locales: this.locales,\n                defaultLocale: this.defaultLocale,\n              } as any\n            )\n      )\n\n      routeInfo.props = props\n      this.components[route] = routeInfo\n      return routeInfo\n    } catch (err) {\n      return this.handleRouteInfoError(err, pathname, query, as, routeProps)\n    }\n  }\n\n  set(\n    route: string,\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    data: PrivateRouteInfo,\n    resetScroll: { x: number; y: number } | null\n  ): Promise<void> {\n    this.isFallback = false\n\n    this.route = route\n    this.pathname = pathname\n    this.query = query\n    this.asPath = as\n    return this.notify(data, resetScroll)\n  }\n\n  /**\n   * Callback to execute before replacing router state\n   * @param cb callback to be executed\n   */\n  beforePopState(cb: BeforePopStateCallback) {\n    this._bps = cb\n  }\n\n  onlyAHashChange(as: string): boolean {\n    if (!this.asPath) return false\n    const [oldUrlNoHash, oldHash] = this.asPath.split('#')\n    const [newUrlNoHash, newHash] = as.split('#')\n\n    // Makes sure we scroll to the provided hash if the url/hash are the same\n    if (newHash && oldUrlNoHash === newUrlNoHash && oldHash === newHash) {\n      return true\n    }\n\n    // If the urls are change, there's more than a hash change\n    if (oldUrlNoHash !== newUrlNoHash) {\n      return false\n    }\n\n    // If the hash has changed, then it's a hash only change.\n    // This check is necessary to handle both the enter and\n    // leave hash === '' cases. The identity case falls through\n    // and is treated as a next reload.\n    return oldHash !== newHash\n  }\n\n  scrollToHash(as: string): void {\n    const [, hash] = as.split('#')\n    // Scroll to top if the hash is just `#` with no value or `#top`\n    // To mirror browsers\n    if (hash === '' || hash === 'top') {\n      window.scrollTo(0, 0)\n      return\n    }\n\n    // First we check if the element by id is found\n    const idEl = document.getElementById(hash)\n    if (idEl) {\n      idEl.scrollIntoView()\n      return\n    }\n    // If there's no element with the id, we check the `name` property\n    // To mirror browsers\n    const nameEl = document.getElementsByName(hash)[0]\n    if (nameEl) {\n      nameEl.scrollIntoView()\n    }\n  }\n\n  urlIsNew(asPath: string): boolean {\n    return this.asPath !== asPath\n  }\n\n  /**\n   * Prefetch page code, you may wait for the data during page rendering.\n   * This feature only works in production!\n   * @param url the href of prefetched page\n   * @param asPath the as path of the prefetched page\n   */\n  async prefetch(\n    url: string,\n    asPath: string = url,\n    options: PrefetchOptions = {}\n  ): Promise<void> {\n    let parsed = parseRelativeUrl(url)\n\n    let { pathname } = parsed\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      if (options.locale === false) {\n        pathname = normalizeLocalePath!(pathname, this.locales).pathname\n        parsed.pathname = pathname\n        url = formatWithValidation(parsed)\n\n        let parsedAs = parseRelativeUrl(asPath)\n        const localePathResult = normalizeLocalePath!(\n          parsedAs.pathname,\n          this.locales\n        )\n        parsedAs.pathname = localePathResult.pathname\n        options.locale = localePathResult.detectedLocale || this.defaultLocale\n        asPath = formatWithValidation(parsedAs)\n      }\n    }\n\n    const pages = await this.pageLoader.getPageList()\n    let resolvedAs = asPath\n\n    if (process.env.__NEXT_HAS_REWRITES && asPath.startsWith('/')) {\n      let rewrites: any\n      ;({ __rewrites: rewrites } = await getClientBuildManifest())\n\n      const rewritesResult = resolveRewrites(\n        addBasePath(addLocale(asPath, this.locale)),\n        pages,\n        rewrites,\n        parsed.query,\n        (p: string) => resolveDynamicRoute(p, pages),\n        this.locales\n      )\n      resolvedAs = delLocale(delBasePath(rewritesResult.asPath), this.locale)\n\n      if (rewritesResult.matchedPage && rewritesResult.resolvedHref) {\n        // if this directly matches a page we need to update the href to\n        // allow the correct page chunk to be loaded\n        pathname = rewritesResult.resolvedHref\n        parsed.pathname = pathname\n        url = formatWithValidation(parsed)\n      }\n    } else {\n      parsed.pathname = resolveDynamicRoute(parsed.pathname, pages)\n\n      if (parsed.pathname !== pathname) {\n        pathname = parsed.pathname\n        url = formatWithValidation(parsed)\n      }\n    }\n    const route = removePathTrailingSlash(pathname)\n\n    // Prefetch is not supported in development mode because it would trigger on-demand-entries\n    if (process.env.NODE_ENV !== 'production') {\n      return\n    }\n\n    await Promise.all([\n      this.pageLoader._isSsg(route).then((isSsg: boolean) => {\n        return isSsg\n          ? this._getStaticData(\n              this.pageLoader.getDataHref(\n                url,\n                resolvedAs,\n                true,\n                typeof options.locale !== 'undefined'\n                  ? options.locale\n                  : this.locale\n              )\n            )\n          : false\n      }),\n      this.pageLoader[options.priority ? 'loadPage' : 'prefetch'](route),\n    ])\n  }\n\n  async fetchComponent(route: string): Promise<GoodPageCache> {\n    let cancelled = false\n    const cancel = (this.clc = () => {\n      cancelled = true\n    })\n\n    const componentResult = await this.pageLoader.loadPage(route)\n\n    if (cancelled) {\n      const error: any = new Error(\n        `Abort fetching component for route: \"${route}\"`\n      )\n      error.cancelled = true\n      throw error\n    }\n\n    if (cancel === this.clc) {\n      this.clc = null\n    }\n\n    return componentResult\n  }\n\n  _getData<T>(fn: () => Promise<T>): Promise<T> {\n    let cancelled = false\n    const cancel = () => {\n      cancelled = true\n    }\n    this.clc = cancel\n    return fn().then((data) => {\n      if (cancel === this.clc) {\n        this.clc = null\n      }\n\n      if (cancelled) {\n        const err: any = new Error('Loading initial props cancelled')\n        err.cancelled = true\n        throw err\n      }\n\n      return data\n    })\n  }\n\n  _getStaticData(dataHref: string): Promise<object> {\n    const { href: cacheKey } = new URL(dataHref, window.location.href)\n    if (\n      process.env.NODE_ENV === 'production' &&\n      !this.isPreview &&\n      this.sdc[cacheKey]\n    ) {\n      return Promise.resolve(this.sdc[cacheKey])\n    }\n    return fetchNextData(dataHref, this.isSsr).then((data) => {\n      this.sdc[cacheKey] = data\n      return data\n    })\n  }\n\n  _getServerData(dataHref: string): Promise<object> {\n    const { href: resourceKey } = new URL(dataHref, window.location.href)\n    if (this.sdr[resourceKey]) {\n      return this.sdr[resourceKey]\n    }\n    return (this.sdr[resourceKey] = fetchNextData(dataHref, this.isSsr)\n      .then((data) => {\n        delete this.sdr[resourceKey]\n        return data\n      })\n      .catch((err) => {\n        delete this.sdr[resourceKey]\n        throw err\n      }))\n  }\n\n  getInitialProps(\n    Component: ComponentType,\n    ctx: NextPageContext\n  ): Promise<any> {\n    const { Component: App } = this.components['/_app']\n    const AppTree = this._wrapApp(App as AppComponent)\n    ctx.AppTree = AppTree\n    return loadGetInitialProps<AppContextType<Router>>(App, {\n      AppTree,\n      Component,\n      router: this,\n      ctx,\n    })\n  }\n\n  abortComponentLoad(as: string, routeProps: RouteProperties): void {\n    if (this.clc) {\n      Router.events.emit(\n        'routeChangeError',\n        buildCancellationError(),\n        as,\n        routeProps\n      )\n      this.clc()\n      this.clc = null\n    }\n  }\n\n  notify(\n    data: PrivateRouteInfo,\n    resetScroll: { x: number; y: number } | null\n  ): Promise<void> {\n    return this.sub(\n      data,\n      this.components['/_app'].Component as AppComponent,\n      resetScroll\n    )\n  }\n}\n"]}