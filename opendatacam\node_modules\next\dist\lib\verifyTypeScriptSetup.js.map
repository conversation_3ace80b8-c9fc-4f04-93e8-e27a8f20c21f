{"version": 3, "sources": ["../../lib/verifyTypeScriptSetup.ts"], "names": ["verifyTypeScriptSetup", "dir", "pagesDir", "typeCheckPreflight", "cacheDir", "tsConfigPath", "path", "join", "intent", "version", "firstTimeSetup", "deps", "ts", "resolved", "result", "runTypeCheck", "require", "err", "CompileError", "console", "error", "chalk", "red", "message", "process", "exit", "FatalE<PERSON>r"], "mappings": "yFAAA,oDACA,kDACA,sEAIA,6CACA,yCAEA,qEAEA,+EACA,mF,w4BAEO,cAAeA,CAAAA,qBAAf,CACLC,GADK,CAELC,QAFK,CAGLC,kBAHK,CAILC,QAJK,CAK0D,CAC/D,KAAMC,CAAAA,YAAY,CAAGC,cAAKC,IAAL,CAAUN,GAAV,CAAe,eAAf,CAArB,CAEA,GAAI,CACF;AACA,KAAMO,CAAAA,MAAM,CAAG,KAAM,6CAAoBP,GAApB,CAAyBC,QAAzB,CAArB,CACA,GAAI,CAACM,MAAL,CAAa,CACX,MAAO,CAAEC,OAAO,CAAE,IAAX,CAAP,CACD,CACD,KAAMC,CAAAA,cAAc,CAAGF,MAAM,CAACE,cAA9B,CAEA;AACA,KAAMC,CAAAA,IAA2B,CAAG,KAAM,uDACxCV,GADwC,CAExC,CAAC,CAACO,MAFsC,CAGxC,KAHwC,CAA1C,CAMA;AACA,KAAMI,CAAAA,EAAE,CAAI,yBAAaD,IAAI,CAACE,QAAlB,gDAAZ,CAEA;AACA,KAAM,2DAA2BD,EAA3B,CAA+BP,YAA/B,CAA6CK,cAA7C,CAAN,CACA;AACA;AACA,KAAM,uDAAyBT,GAAzB,CAAN,CAEA,GAAIa,CAAAA,MAAJ,CACA,GAAIX,kBAAJ,CAAwB,CACtB,KAAM,CAAEY,YAAF,EAAmBC,OAAO,CAAC,2BAAD,CAAhC,CAEA;AACAF,MAAM,CAAG,KAAMC,CAAAA,YAAY,CAACH,EAAD,CAAKX,GAAL,CAAUI,YAAV,CAAwBD,QAAxB,CAA3B,CACD,CACD,MAAO,CAAEU,MAAF,CAAUL,OAAO,CAAEG,EAAE,CAACH,OAAtB,CAAP,CACD,CAAC,MAAOQ,GAAP,CAAY,CACZ;AACA,GAAIA,GAAG,WAAYC,2BAAnB,CAAiC,CAC/BC,OAAO,CAACC,KAAR,CAAcC,eAAMC,GAAN,CAAU,sBAAV,CAAd,EACAH,OAAO,CAACC,KAAR,CAAcH,GAAG,CAACM,OAAlB,EACAC,OAAO,CAACC,IAAR,CAAa,CAAb,EACD,CAJD,IAIO,IAAIR,GAAG,WAAYS,uBAAnB,CAA+B,CACpCP,OAAO,CAACC,KAAR,CAAcH,GAAG,CAACM,OAAlB,EACAC,OAAO,CAACC,IAAR,CAAa,CAAb,EACD,CACD,KAAMR,CAAAA,GAAN,CACD,CACF", "sourcesContent": ["import chalk from 'chalk'\nimport path from 'path'\nimport {\n  hasNecessaryDependencies,\n  NecessaryDependencies,\n} from './has-necessary-dependencies'\nimport { CompileError } from './compile-error'\nimport { FatalError } from './fatal-error'\n\nimport { getTypeScriptIntent } from './typescript/getTypeScriptIntent'\nimport { TypeCheckResult } from './typescript/runTypeCheck'\nimport { writeAppTypeDeclarations } from './typescript/writeAppTypeDeclarations'\nimport { writeConfigurationDefaults } from './typescript/writeConfigurationDefaults'\n\nexport async function verifyTypeScriptSetup(\n  dir: string,\n  pagesDir: string,\n  typeCheckPreflight: boolean,\n  cacheDir?: string\n): Promise<{ result?: TypeCheckResult; version: string | null }> {\n  const tsConfigPath = path.join(dir, 'tsconfig.json')\n\n  try {\n    // Check if the project uses TypeScript:\n    const intent = await getTypeScriptIntent(dir, pagesDir)\n    if (!intent) {\n      return { version: null }\n    }\n    const firstTimeSetup = intent.firstTimeSetup\n\n    // Ensure TypeScript and necessary `@types/*` are installed:\n    const deps: NecessaryDependencies = await hasNecessaryDependencies(\n      dir,\n      !!intent,\n      false\n    )\n\n    // Load TypeScript after we're sure it exists:\n    const ts = (await import(deps.resolved)) as typeof import('typescript')\n\n    // Reconfigure (or create) the user's `tsconfig.json` for them:\n    await writeConfigurationDefaults(ts, tsConfigPath, firstTimeSetup)\n    // Write out the necessary `next-env.d.ts` file to correctly register\n    // Next.js' types:\n    await writeAppTypeDeclarations(dir)\n\n    let result\n    if (typeCheckPreflight) {\n      const { runTypeCheck } = require('./typescript/runTypeCheck')\n\n      // Verify the project passes type-checking before we go to webpack phase:\n      result = await runTypeCheck(ts, dir, tsConfigPath, cacheDir)\n    }\n    return { result, version: ts.version }\n  } catch (err) {\n    // These are special errors that should not show a stack trace:\n    if (err instanceof CompileError) {\n      console.error(chalk.red('Failed to compile.\\n'))\n      console.error(err.message)\n      process.exit(1)\n    } else if (err instanceof FatalError) {\n      console.error(err.message)\n      process.exit(1)\n    }\n    throw err\n  }\n}\n"]}