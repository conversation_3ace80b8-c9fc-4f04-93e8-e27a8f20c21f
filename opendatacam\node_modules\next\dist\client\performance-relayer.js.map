{"version": 3, "sources": ["../../client/performance-relayer.ts"], "names": ["initialHref", "location", "href", "isRegistered", "userReportHandler", "onReport", "metric", "process", "env", "NODE_ENV", "__NEXT_ANALYTICS_ID", "body", "dsn", "id", "page", "window", "__NEXT_DATA__", "event_name", "name", "value", "toString", "speed", "navigator", "blob", "Blob", "URLSearchParams", "type", "vitalsUrl", "sendBeacon", "fetch", "method", "credentials", "keepalive", "onPerfEntry"], "mappings": "4DAAA,wDAUA,KAAMA,CAAAA,WAAW,CAAGC,QAAQ,CAACC,IAA7B,CACA,GAAIC,CAAAA,YAAY,CAAG,KAAnB,CACA,GAAIC,CAAAA,iBAAJ,CAEA,QAASC,CAAAA,QAAT,CAAkBC,MAAlB,CAAwC,CACtC,GAAIF,iBAAJ,CAAuB,CACrBA,iBAAiB,CAACE,MAAD,CAAjB,CACD,CAED;AACA;AACA;AACA;AACA;AACA,GACEC,OAAO,CAACC,GAAR,CAAYC,QAAZ,GAAyB,YAAzB,EACA;AACAF,OAAO,CAACC,GAAR,CAAYE,mBAHd,CAIE,CACA,KAAMC,CAAAA,IAA4B,CAAG,CACnCC,GAAG,CAAEL,OAAO,CAACC,GAAR,CAAYE,mBADkB,CAEnCG,EAAE,CAAEP,MAAM,CAACO,EAFwB,CAGnCC,IAAI,CAAEC,MAAM,CAACC,aAAP,CAAqBF,IAHQ,CAInCZ,IAAI,CAAEF,WAJ6B,CAKnCiB,UAAU,CAAEX,MAAM,CAACY,IALgB,CAMnCC,KAAK,CAAEb,MAAM,CAACa,KAAP,CAAaC,QAAb,EAN4B,CAOnCC,KAAK,CACH,cAAgBC,CAAAA,SAAhB,EACAA,SAAS,CAAC,YAAD,CADT,EAEA,iBAAmBA,CAAAA,SAAS,CAAC,YAAD,CAF5B,CAGKA,SAAS,CAAC,YAAD,CAAT,CAAwB,eAAxB,CAHL,CAII,EAZ6B,CAArC,CAeA,KAAMC,CAAAA,IAAI,CAAG,GAAIC,CAAAA,IAAJ,CAAS,CAAC,GAAIC,CAAAA,eAAJ,CAAoBd,IAApB,EAA0BS,QAA1B,EAAD,CAAT,CAAiD,CAC5D;AACAM,IAAI,CAAE,mCAFsD,CAAjD,CAAb,CAIA,KAAMC,CAAAA,SAAS,CAAG,8CAAlB,CACEL,SAAS,CAACM,UAAV,EAAwBN,SAAS,CAACM,UAAV,CAAqBD,SAArB,CAAgCJ,IAAhC,CAAzB,EACCM,KAAK,CAACF,SAAD,CAAY,CACfhB,IAAI,CAAEY,IADS,CAEfO,MAAM,CAAE,MAFO,CAGfC,WAAW,CAAE,MAHE,CAIfC,SAAS,CAAE,IAJI,CAAZ,CADN,CAOF,CACF,C,aAEeC,WAAD,EAAuC,CACpD;AACA7B,iBAAiB,CAAG6B,WAApB,CAEA;AACA,GAAI9B,YAAJ,CAAkB,CAChB,OACD,CACDA,YAAY,CAAG,IAAf,CAEA,sBAAOE,QAAP,EACA,sBAAOA,QAAP,EACA,sBAAOA,QAAP,EACA,sBAAOA,QAAP,EACA,uBAAQA,QAAR,EACD,C", "sourcesContent": ["import {\n  getCLS,\n  getFCP,\n  getFID,\n  getLCP,\n  getTTFB,\n  Metric,\n  ReportHandler,\n} from 'next/dist/compiled/web-vitals'\n\nconst initialHref = location.href\nlet isRegistered = false\nlet userReportHandler: ReportHandler | undefined\n\nfunction onReport(metric: Metric): void {\n  if (userReportHandler) {\n    userReportHandler(metric)\n  }\n\n  // This code is not shipped, executed, or present in the client-side\n  // JavaScript bundle unless explicitly enabled in your application.\n  //\n  // When this feature is enabled, we'll make it very clear by printing a\n  // message during the build (`next build`).\n  if (\n    process.env.NODE_ENV === 'production' &&\n    // This field is empty unless you explicitly configure it:\n    process.env.__NEXT_ANALYTICS_ID\n  ) {\n    const body: Record<string, string> = {\n      dsn: process.env.__NEXT_ANALYTICS_ID,\n      id: metric.id,\n      page: window.__NEXT_DATA__.page,\n      href: initialHref,\n      event_name: metric.name,\n      value: metric.value.toString(),\n      speed:\n        'connection' in navigator &&\n        navigator['connection'] &&\n        'effectiveType' in navigator['connection']\n          ? (navigator['connection']['effectiveType'] as string)\n          : '',\n    }\n\n    const blob = new Blob([new URLSearchParams(body).toString()], {\n      // This content type is necessary for `sendBeacon`:\n      type: 'application/x-www-form-urlencoded',\n    })\n    const vitalsUrl = 'https://vitals.vercel-insights.com/v1/vitals'\n    ;(navigator.sendBeacon && navigator.sendBeacon(vitalsUrl, blob)) ||\n      fetch(vitalsUrl, {\n        body: blob,\n        method: 'POST',\n        credentials: 'omit',\n        keepalive: true,\n      })\n  }\n}\n\nexport default (onPerfEntry?: ReportHandler): void => {\n  // Update function if it changes:\n  userReportHandler = onPerfEntry\n\n  // Only register listeners once:\n  if (isRegistered) {\n    return\n  }\n  isRegistered = true\n\n  getCLS(onReport)\n  getFID(onReport)\n  getFCP(onReport)\n  getLCP(onReport)\n  getTTFB(onReport)\n}\n"]}