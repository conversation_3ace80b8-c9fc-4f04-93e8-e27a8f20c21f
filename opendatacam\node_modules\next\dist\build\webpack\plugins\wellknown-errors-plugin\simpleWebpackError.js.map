{"version": 3, "sources": ["../../../../../build/webpack/plugins/wellknown-errors-plugin/simpleWebpackError.ts"], "names": ["SimpleWebpackError", "Error", "constructor", "file", "message"], "mappings": "uEAAA;AACA;AACA;AACO,KAAMA,CAAAA,kBAAN,QAAiCC,CAAAA,KAAM,CAG5CC,WAAW,CAACC,IAAD,CAAeC,OAAf,CAAgC,CACzC,MAAMA,OAAN,EADyC,KAF3CD,IAE2C,QAEzC,KAAKA,IAAL,CAAYA,IAAZ,CACD,CAN2C,C", "sourcesContent": ["// This class creates a simplified webpack error that formats nicely based on\n// webpack's build in serializer.\n// https://github.com/webpack/webpack/blob/c9d4ff7b054fc581c96ce0e53432d44f9dd8ca72/lib/Stats.js#L294-L356\nexport class SimpleWebpackError extends Error {\n  file: string\n\n  constructor(file: string, message: string) {\n    super(message)\n    this.file = file\n  }\n}\n"]}