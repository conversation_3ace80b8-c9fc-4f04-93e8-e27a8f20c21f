{"version": 3, "sources": ["../../../../../build/webpack/loaders/next-serverless-loader/page-handler.ts"], "names": ["getPageHandler", "ctx", "page", "pageComponent", "pageConfig", "pageGetStaticProps", "pageGetStaticPaths", "pageGetServerSideProps", "appModule", "documentModule", "errorModule", "notFoundModule", "encodedPreviewProps", "pageIsDynamic", "generateEtags", "poweredByHeader", "runtimeConfig", "buildManifest", "reactLoadableManifest", "i18n", "buildId", "basePath", "assetPrefix", "canonicalBase", "escapedBuildId", "handleLocale", "handleRewrites", "handleBasePath", "defaultRouteRegex", "dynamicRouteMatcher", "interpolateDynamicPath", "getParamsFromRouteMatches", "normalizeDynamicRouteParams", "normalizeVercelUrl", "renderReqToHTML", "req", "res", "renderMode", "_renderOpts", "_params", "Component", "App", "config", "Document", "Error", "notFoundMod", "getStaticProps", "getStaticPaths", "getServerSideProps", "default", "Promise", "all", "fromExport", "nextStartMode", "hasValidParams", "options", "publicRuntimeConfig", "previewProps", "env", "process", "_nextData", "defaultLocale", "detectedLocale", "parsedUrl", "trustQuery", "headers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url", "routeNoAssetPath", "pathname", "replace", "RegExp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "assign", "query", "amp", "queryNoAmp", "search", "undefined", "match", "localeResult", "nextInternalLocale", "renderOpts", "nextExport", "isDataReq", "locales", "locale", "domainLocales", "domains", "statusCode", "params", "result", "nowParams", "_parsedUrl", "param", "keys", "groups", "resolvedUrl", "resolvedAsPath", "<PERSON><PERSON><PERSON><PERSON>", "__<PERSON><PERSON><PERSON><PERSON>", "previewData", "isPreviewMode", "__NEXT_OPTIMIZE_FONTS", "optimizeFonts", "fontManifest", "__webpack_require__", "__NEXT_FONT_MANIFEST__", "isNotFound", "end", "NotFoundComponent", "err<PERSON><PERSON><PERSON>", "result2", "err", "private", "stateful", "revalidate", "isRedirect", "redirect", "destination", "pageData", "pageProps", "__N_REDIRECT", "__N_REDIRECT_STATUS", "__N_REDIRECT_BASE_PATH", "startsWith", "PERMANENT_REDIRECT_STATUS", "<PERSON><PERSON><PERSON><PERSON>", "JSON", "stringify", "html", "code", "console", "error", "underErrorErr", "render"], "mappings": "2EACA,wBACA,wDACA,wEACA,+BAEA,6DACA,kEACA,yFAKA,oEACA,iIACA,gE,mFAEO,QAASA,CAAAA,cAAT,CAAwBC,GAAxB,CAAmD,CACxD,KAAM,CACJC,IADI,CAGJC,aAHI,CAIJC,UAJI,CAKJC,kBALI,CAMJC,kBANI,CAOJC,sBAPI,CASJC,SATI,CAUJC,cAVI,CAWJC,WAXI,CAYJC,cAZI,CAcJC,mBAdI,CAeJC,aAfI,CAgBJC,aAhBI,CAiBJC,eAjBI,CAmBJC,aAnBI,CAoBJC,aApBI,CAqBJC,qBArBI,CAuBJC,IAvBI,CAwBJC,OAxBI,CAyBJC,QAzBI,CA0BJC,WA1BI,CA2BJC,aA3BI,CA4BJC,cA5BI,EA6BFvB,GA7BJ,CA8BA,KAAM,CACJwB,YADI,CAEJC,cAFI,CAGJC,cAHI,CAIJC,iBAJI,CAKJC,mBALI,CAMJC,sBANI,CAOJC,yBAPI,CAQJC,2BARI,CASJC,kBATI,EAUF,qBAAShC,GAAT,CAVJ,CAYA,cAAeiC,CAAAA,eAAf,CACEC,GADF,CAEEC,GAFF,CAGEC,UAHF,CAIEC,WAJF,CAKEC,OALF,CAME,CACA,GAAIC,CAAAA,SAAJ,CACA,GAAIC,CAAAA,GAAJ,CACA,GAAIC,CAAAA,MAAJ,CACA,GAAIC,CAAAA,QAAJ,CACA,GAAIC,CAAAA,KAAJ,CACA,GAAIC,CAAAA,WAAJ,CACA,GAAIC,CAAAA,cAAJ,CACA,GAAIC,CAAAA,cAAJ,CACA,GAAIC,CAAAA,kBAAJ,CACC,CACCF,cADD,CAECE,kBAFD,CAGCD,cAHD,CAICP,SAJD,CAKCC,GALD,CAMCC,MAND,CAOC,CAAEO,OAAO,CAAEN,QAAX,CAPD,CAQC,CAAEM,OAAO,CAAEL,KAAX,CARD,CASCC,WATD,EAUG,KAAMK,CAAAA,OAAO,CAACC,GAAR,CAAY,CACpB9C,kBADoB,CAEpBE,sBAFoB,CAGpBD,kBAHoB,CAIpBH,aAJoB,CAKpBK,SALoB,CAMpBJ,UANoB,CAOpBK,cAPoB,CAQpBC,WARoB,CASpBC,cAToB,CAAZ,CAVT,CAsBD,KAAMyC,CAAAA,UAAU,CAAGf,UAAU,GAAK,QAAf,EAA2BA,UAAU,GAAK,IAA7D,CACA,KAAMgB,CAAAA,aAAa,CAAGhB,UAAU,GAAK,aAArC,CAEA,GAAIiB,CAAAA,cAAc,CAAG,IAArB,CAEA,0BAAY,CAAEnB,GAAG,CAAEA,GAAP,CAAZ,CAAiC,SAAjC,CAA4C,8BAAgBA,GAAhB,CAA5C,EAEA,KAAMoB,CAAAA,OAAO,CAAG,CACdd,GADc,CAEdE,QAFc,CAGd1B,aAHc,CAId6B,cAJc,CAKdE,kBALc,CAMdD,cANc,CAOd7B,qBAPc,CAQdK,aARc,CASdH,OATc,CAUdE,WAVc,CAWdN,aAAa,CAAE,CAACA,aAAa,EAAI,EAAlB,EAAsBwC,mBAAtB,EAA6C,EAX9C,CAYdC,YAAY,CAAE7C,mBAZA,CAad8C,GAAG,CAAEC,OAAO,CAACD,GAbC,CAcdrC,QAdc,CAed,GAAGiB,WAfW,CAAhB,CAiBA,GAAIsB,CAAAA,SAAS,CAAG,KAAhB,CACA,GAAIC,CAAAA,aAAa,CAAG1C,IAAH,cAAGA,IAAI,CAAE0C,aAA1B,CACA,GAAIC,CAAAA,cAAc,CAAG3C,IAAH,cAAGA,IAAI,CAAE0C,aAA3B,CACA,GAAIE,CAAAA,SAAJ,CAEA,GAAI,kBACF;AACA;AACA,KAAMC,CAAAA,UAAU,CAAG,CAAClB,cAAD,EAAmBX,GAAG,CAAC8B,OAAJ,CAAYC,oBAAZ,CAAtC,CACAH,SAAS,CAAG,eAAS5B,GAAG,CAACgC,GAAb,CAAmB,IAAnB,CAAZ,CACA,GAAIC,CAAAA,gBAAgB,CAAGL,SAAS,CAACM,QAAjC,CAEA,GAAIhD,QAAJ,CAAc,CACZ+C,gBAAgB,CACdA,gBAAgB,CAACE,OAAjB,CAAyB,GAAIC,CAAAA,MAAJ,CAAY,IAAGlD,QAAS,EAAxB,CAAzB,CAAqD,EAArD,GAA4D,GAD9D,CAED,CACD,KAAMmD,CAAAA,SAAS,CAAGC,MAAM,CAACC,MAAP,CAAc,EAAd,CAAkBX,SAAS,CAACY,KAA5B,CAAlB,CAEAZ,SAAS,CAAGrC,cAAc,CAACS,GAAD,CAAM4B,SAAN,CAA1B,CACApC,cAAc,CAACQ,GAAD,CAAM4B,SAAN,CAAd,CAEA;AACA,GAAIX,UAAU,EAAIW,SAAS,CAACY,KAAV,CAAgBC,GAAlC,CAAuC,CACrC,KAAMC,CAAAA,UAAU,CAAGJ,MAAM,CAACC,MAAP,CAAc,EAAd,CAAkBF,SAAlB,CAAnB,CACA,MAAOK,CAAAA,UAAU,CAACD,GAAlB,CAEAzC,GAAG,CAACgC,GAAJ,CAAU,gBAAU,CAClB,GAAGJ,SADe,CAElBe,MAAM,CAAEC,SAFU,CAGlBJ,KAAK,CAAEE,UAHW,CAAV,CAAV,CAKD,CAED,GAAId,SAAS,CAACM,QAAV,CAAoBW,KAApB,CAA0B,aAA1B,CAAJ,CAA8C,CAC5CpB,SAAS,CAAG1D,IAAI,GAAK,SAArB,CACA6D,SAAS,CAACM,QAAV,CAAqB,mCACnBN,SAAS,CAACM,QAAV,CAAoBC,OAApB,CACE,GAAIC,CAAAA,MAAJ,CAAY,eAAc/C,cAAe,GAAzC,CADF,CAEE,GAFF,CADmB,CAKnB,OALmB,CAArB,CAOA4C,gBAAgB,CAAGL,SAAS,CAACM,QAA7B,CACD,CAED,KAAMY,CAAAA,YAAY,CAAGxD,YAAY,CAC/BU,GAD+B,CAE/BC,GAF+B,CAG/B2B,SAH+B,CAI/BK,gBAJ+B,CAK/BhB,UAAU,EAAIC,aALiB,CAAjC,CAOAQ,aAAa,CAAG,CAAAoB,YAAY,MAAZ,QAAAA,YAAY,CAAEpB,aAAd,GAA+BA,aAA/C,CACAC,cAAc,CAAG,CAAAmB,YAAY,MAAZ,QAAAA,YAAY,CAAEnB,cAAd,GAAgCA,cAAjD,CACAM,gBAAgB,CAAG,CAAAa,YAAY,MAAZ,QAAAA,YAAY,CAAEb,gBAAd,GAAkCA,gBAArD,CAEA,GAAIL,SAAS,CAACY,KAAV,CAAgBO,kBAApB,CAAwC,CACtCpB,cAAc,CAAGC,SAAS,CAACY,KAAV,CAAgBO,kBAAjC,CACA,MAAOnB,CAAAA,SAAS,CAACY,KAAV,CAAgBO,kBAAvB,CACD,CAED,KAAMC,CAAAA,UAAU,CAAGV,MAAM,CAACC,MAAP,CACjB,CACElC,SADF,CAEEpC,UAAU,CAAEsC,MAFd,CAGE0C,UAAU,CAAEhC,UAHd,CAIEiC,SAAS,CAAEzB,SAJb,CAKE0B,OAAO,CAAEnE,IAAF,cAAEA,IAAI,CAAEmE,OALjB,CAMEC,MAAM,CAAEzB,cANV,CAOED,aAPF,CAQE2B,aAAa,CAAErE,IAAF,cAAEA,IAAI,CAAEsE,OARvB,CADiB,CAWjBlC,OAXiB,CAAnB,CAcA,GAAIrD,IAAI,GAAK,SAAT,EAAsB,CAACkC,GAAG,CAACsD,UAA/B,CAA2C,CACzCtD,GAAG,CAACsD,UAAJ,CAAiB,GAAjB,CACD,CAED,GAAIC,CAAAA,MAAM,CAAG,EAAb,CAEA,GAAI,CAACvC,UAAD,EAAevC,aAAnB,CAAkC,CAChC,KAAM+E,CAAAA,MAAM,CAAG5D,2BAA2B,CACxCgC,UAAU,CACND,SAAS,CAACY,KADJ,CAEL9C,mBAAmB,CAAEkC,SAAS,CAACM,QAAZ,CAHgB,CAA1C,CASAf,cAAc,CAAGsC,MAAM,CAACtC,cAAxB,CACAqC,MAAM,CAAGC,MAAM,CAACD,MAAhB,CACD,CAED,GAAIE,CAAAA,SAAS,CAAG,IAAhB,CAEA,GACEhF,aAAa,EACb,CAACyC,cADD,gBAEAnB,GAAG,CAAC8B,OAFJ,SAEA,aAAc,qBAAd,CAHF,CAIE,CACA4B,SAAS,CAAG9D,yBAAyB,CAACI,GAAD,CAAMgD,UAAN,CAAkBrB,cAAlB,CAArC,CACD,CAED;AACA;AACAqB,UAAU,CAACQ,MAAX,CAAoBpD,OAAO,EAAIoD,MAA/B,CAEA1D,kBAAkB,CAACE,GAAD,CAAM,CAAC,CAAC6B,UAAR,CAAlB,CAEA;AACA;AACA,GAAInD,aAAa,EAAIgF,SAAjB,EAA8BjE,iBAAlC,CAAqD,CACnD,KAAMkE,CAAAA,UAAU,CAAG,eAAS3D,GAAG,CAACgC,GAAb,CAAnB,CAEA2B,UAAU,CAACzB,QAAX,CAAsBvC,sBAAsB,CAC1CgE,UAAU,CAACzB,QAD+B,CAE1CwB,SAF0C,CAA5C,CAIA9B,SAAS,CAACM,QAAV,CAAqByB,UAAU,CAACzB,QAAhC,CACAlC,GAAG,CAACgC,GAAJ,CAAU,gBAAU2B,UAAV,CAAV,CACD,CAED;AACA;AACA,GAAI,CAAC1C,UAAD,GAAgBN,cAAc,EAAIE,kBAAlC,CAAJ,CAA2D,CACzD;AACA;AACA,GAAInC,aAAa,EAAImD,UAAjB,EAA+BpC,iBAAnC,CAAsD,CACpD,MAAQmC,CAAAA,SAAD,CAAmBe,MAA1B,CAEA,IAAK,KAAMiB,CAAAA,KAAX,GAAoBtB,CAAAA,MAAM,CAACuB,IAAP,CAAYpE,iBAAiB,CAACqE,MAA9B,CAApB,CAA2D,CACzD,MAAOzB,CAAAA,SAAS,CAACuB,KAAD,CAAhB,CACD,CACF,CAEDhC,SAAS,CAACM,QAAV,CAAqB,6CAAoBN,SAAS,CAACM,QAA9B,CAArB,CACAc,UAAU,CAACe,WAAX,CAAyB,gBAAU,CACjC,GAAGnC,SAD8B,CAEjCY,KAAK,CAAEH,SAF0B,CAAV,CAAzB,CAKA;AACA;AACAW,UAAU,CAACgB,cAAX,CAA4BnD,kBAAkB,CAC1C,gBAAU,CACR,GAAGe,SADK,CAERM,QAAQ,CAAED,gBAFF,CAGRO,KAAK,CAAEH,SAHC,CAAV,CAD0C,CAM1CW,UAAU,CAACe,WANf,CAOD,CAED,KAAME,CAAAA,UAAU,CAAGrC,SAAS,CAACY,KAAV,CAAgB0B,cAAnC,CAEA,KAAMC,CAAAA,WAAW,CAAG,gCAAkBnE,GAAlB,CAAuBC,GAAvB,CAA4BmB,OAAO,CAACE,YAApC,CAApB,CACA,KAAM8C,CAAAA,aAAa,CAAGD,WAAW,GAAK,KAAtC,CAEA,GAAI3C,OAAO,CAACD,GAAR,CAAY8C,qBAAhB,CAAuC,CACrCrB,UAAU,CAACsB,aAAX,CAA2B,IAA3B,CACA;AACR;AACA;AACA,WAL6C,CAMrC;AACAtB,UAAU,CAACuB,YAAX,CAA0BC,mBAAmB,CAACC,sBAA9C,CACD,CAED,GAAIhB,CAAAA,MAAM,CAAG,KAAM,yBACjBzD,GADiB,CAEjBC,GAFiB,CAGjBlC,IAHiB,CAIjBuE,MAAM,CAACC,MAAP,CACE,EADF,CAEE5B,cAAc,CACV,CAAE,IAAIiB,SAAS,CAACY,KAAV,CAAgBC,GAAhB,CAAsB,CAAEA,GAAG,CAAE,GAAP,CAAtB,CAAqC,EAAzC,CAAF,CADU,CAEVb,SAAS,CAACY,KAJhB,CAKEkB,SAAS,CAAGA,SAAH,CAAeF,MAL1B,CAMEpD,OANF,CAOE6D,UAAU,CAAG,CAAEC,cAAc,CAAE,MAAlB,CAAH,CAAgC,EAP5C,CAJiB,CAajBlB,UAbiB,CAAnB,CAgBA,GAAI,CAAC9C,UAAL,CAAiB,CACf,GAAIuB,SAAS,EAAId,cAAb,EAA+BE,kBAAnC,CAAuD,CACrD,GAAImC,UAAU,CAAC0B,UAAf,CAA2B,CACzBzE,GAAG,CAACsD,UAAJ,CAAiB,GAAjB,CAEA,GAAI9B,SAAJ,CAAe,CACbxB,GAAG,CAAC0E,GAAJ,CAAQ,mBAAR,EACA,MAAO,KAAP,CACD,CAED,KAAMC,CAAAA,iBAAiB,CAAGlE,WAAW,CAAGA,WAAW,CAACI,OAAf,CAAyBL,KAA9D,CACA,KAAMoE,CAAAA,WAAW,CAAGnE,WAAW,CAAG,MAAH,CAAY,SAA3C,CAEA,KAAMoE,CAAAA,OAAO,CAAG,KAAM,yBACpB9E,GADoB,CAEpBC,GAFoB,CAGpB4E,WAHoB,CAIpBjD,SAAS,CAACY,KAJU,CAKpBF,MAAM,CAACC,MAAP,CAAc,EAAd,CAAkBnB,OAAlB,CAA2B,CACzBT,cAAc,CAAED,WAAW,CACvBA,WAAW,CAACC,cADW,CAEvBiC,SAHqB,CAIzBhC,cAAc,CAAEgC,SAJS,CAKzB/B,kBAAkB,CAAE+B,SALK,CAMzBvC,SAAS,CAAEuE,iBANc,CAOzBG,GAAG,CAAEnC,SAPoB,CAQzBQ,MAAM,CAAEzB,cARiB,CASzBwB,OAAO,CAAEnE,IAAF,cAAEA,IAAI,CAAEmE,OATU,CAUzBzB,aAAa,CAAE1C,IAAF,cAAEA,IAAI,CAAE0C,aAVI,CAA3B,CALoB,CAAtB,CAmBA,6BACE1B,GADF,CAEEC,GAFF,CAGE6E,OAHF,CAIE,MAJF,CAKE,CACEnG,aADF,CAEEC,eAFF,CALF,CASE,CACEoG,OAAO,CAAEZ,aADX,CAEEa,QAAQ,CAAE,CAAC,CAACpE,kBAFd,CAGEqE,UAAU,CAAElC,UAAU,CAACkC,UAHzB,CATF,EAeA,MAAO,KAAP,CACD,CA9CD,IA8CO,IAAIlC,UAAU,CAACmC,UAAX,EAAyB,CAAC1D,SAA9B,CAAyC,CAC9C,KAAM2D,CAAAA,QAAQ,CAAG,CACfC,WAAW,CAAErC,UAAU,CAACsC,QAAX,CAAoBC,SAApB,CAA8BC,YAD5B,CAEfjC,UAAU,CAAEP,UAAU,CAACsC,QAAX,CAAoBC,SAApB,CAA8BE,mBAF3B,CAGfvG,QAAQ,CAAE8D,UAAU,CAACsC,QAAX,CAAoBC,SAApB,CAA8BG,sBAHzB,CAAjB,CAKA,KAAMnC,CAAAA,UAAU,CAAG,wCAAkB6B,QAAlB,CAAnB,CAEA,GACElG,QAAQ,EACRkG,QAAQ,CAAClG,QAAT,GAAsB,KADtB,EAEAkG,QAAQ,CAACC,WAAT,CAAqBM,UAArB,CAAgC,GAAhC,CAHF,CAIE,CACAP,QAAQ,CAACC,WAAT,CAAwB,GAAEnG,QAAS,GAAEkG,QAAQ,CAACC,WAAY,EAA1D,CACD,CAED,GAAI9B,UAAU,GAAKqC,oCAAnB,CAA8C,CAC5C3F,GAAG,CAAC4F,SAAJ,CAAc,SAAd,CAA0B,SAAQT,QAAQ,CAACC,WAAY,EAAvD,EACD,CAEDpF,GAAG,CAACsD,UAAJ,CAAiBA,UAAjB,CACAtD,GAAG,CAAC4F,SAAJ,CAAc,UAAd,CAA0BT,QAAQ,CAACC,WAAnC,EACApF,GAAG,CAAC0E,GAAJ,GACA,MAAO,KAAP,CACD,CAxBM,IAwBA,CACL,6BACE3E,GADF,CAEEC,GAFF,CAGEwB,SAAS,CAAGqE,IAAI,CAACC,SAAL,CAAe/C,UAAU,CAACsC,QAA1B,CAAH,CAAyC7B,MAHpD,CAIEhC,SAAS,CAAG,MAAH,CAAY,MAJvB,CAKE,CACE9C,aADF,CAEEC,eAFF,CALF,CASE,CACEoG,OAAO,CAAEZ,aADX,CAEEa,QAAQ,CAAE,CAAC,CAACpE,kBAFd,CAGEqE,UAAU,CAAElC,UAAU,CAACkC,UAHzB,CATF,EAeA,MAAO,KAAP,CACD,CACF,CACF,CA3FD,IA2FO,IAAId,aAAJ,CAAmB,CACxBnE,GAAG,CAAC4F,SAAJ,CACE,eADF,CAEE,yDAFF,EAID,CAED,GAAI3F,UAAJ,CAAgB,MAAO,CAAE8F,IAAI,CAAEvC,MAAR,CAAgBT,UAAhB,CAAP,CAChB,MAAOS,CAAAA,MAAP,CACD,CAAC,MAAOsB,GAAP,CAAY,CACZ,GAAI,CAACnD,SAAL,CAAiB,CACfA,SAAS,CAAG,eAAS5B,GAAG,CAACgC,GAAb,CAAmB,IAAnB,CAAZ,CACD,CAED,GAAI+C,GAAG,CAACkB,IAAJ,GAAa,QAAjB,CAA2B,CACzBhG,GAAG,CAACsD,UAAJ,CAAiB,GAAjB,CACD,CAFD,IAEO,IAAIwB,GAAG,CAACkB,IAAJ,GAAa,eAAb,EAAgClB,GAAG,CAACkB,IAAJ,GAAa,cAAjD,CAAiE,CACtE;AACAhG,GAAG,CAACsD,UAAJ,CAAiB,GAAjB,CACD,CAHM,IAGA,CACL2C,OAAO,CAACC,KAAR,CAAc,iCAAd,CAAiDpB,GAAjD,EAEA;AACA,GAAI,CACF,KAAM,yBACJ/E,GADI,CAEJC,GAFI,CAGJ,SAHI,CAIJ2B,SAAS,CAAEY,KAJP,CAKJF,MAAM,CAACC,MAAP,CAAc,EAAd,CAAkBnB,OAAlB,CAA2B,CACzBT,cAAc,CAAEiC,SADS,CAEzBhC,cAAc,CAAEgC,SAFS,CAGzB/B,kBAAkB,CAAE+B,SAHK,CAIzBvC,SAAS,CAAEI,KAJc,CAKzBsE,GAAG,CAAEA,GALoB,CAMzB;AACA7B,SAAS,CAAE,IAPc,CAA3B,CALI,CAAN,CAeD,CAAC,MAAOkD,aAAP,CAAsB,CACtBF,OAAO,CAACC,KAAR,CACE,+DADF,CAEEC,aAFF,EAID,CAED;AACA,GAAI,qBAAUnG,GAAV,CAAJ,CAAoB,CAClBiG,OAAO,CAACC,KAAR,CAAc,iBAAd,EACAD,OAAO,CAACC,KAAR,CACE,0FACE,0DAFJ,EAIAD,OAAO,CAACC,KAAR,CAAc,iBAAd,EACD,CACD,KAAMpB,CAAAA,GAAN,CACD,CAED,KAAMD,CAAAA,OAAO,CAAG,KAAM,yBACpB9E,GADoB,CAEpBC,GAFoB,CAGpB,SAHoB,CAIpB2B,SAAS,CAAEY,KAJS,CAKpBF,MAAM,CAACC,MAAP,CAAc,EAAd,CAAkBnB,OAAlB,CAA2B,CACzBT,cAAc,CAAEiC,SADS,CAEzBhC,cAAc,CAAEgC,SAFS,CAGzB/B,kBAAkB,CAAE+B,SAHK,CAIzBvC,SAAS,CAAEI,KAJc,CAKzBsE,GAAG,CAAE9E,GAAG,CAACsD,UAAJ,GAAmB,GAAnB,CAAyBX,SAAzB,CAAqCmC,GALjB,CAA3B,CALoB,CAAtB,CAaA,MAAOD,CAAAA,OAAP,CACD,CACF,CAED,MAAO,CACL/E,eADK,CAELsG,MAAM,CAAE,cAAeA,CAAAA,MAAf,CAAsBrG,GAAtB,CAA4CC,GAA5C,CAAiE,CACvE,GAAI,CACF,KAAM+F,CAAAA,IAAI,CAAG,KAAMjG,CAAAA,eAAe,CAACC,GAAD,CAAMC,GAAN,CAAlC,CACA,GAAI+F,IAAJ,CAAU,CACR,6BAAYhG,GAAZ,CAAiBC,GAAjB,CAAsB+F,IAAtB,CAA4B,MAA5B,CAAoC,CAClCrH,aADkC,CAElCC,eAFkC,CAApC,EAID,CACF,CAAC,MAAOmG,GAAP,CAAY,CACZmB,OAAO,CAACC,KAAR,CAAcpB,GAAd,EACA;AACA,KAAMA,CAAAA,GAAN,CACD,CACF,CAhBI,CAAP,CAkBD", "sourcesContent": ["import { IncomingMessage, ServerResponse } from 'http'\nimport { parse as parseUrl, format as formatUrl, UrlWithParsedQuery } from 'url'\nimport { isResSent } from '../../../../next-server/lib/utils'\nimport { sendPayload } from '../../../../next-server/server/send-payload'\nimport { getUtils, vercelHeader, ServerlessHandlerCtx } from './utils'\n\nimport { renderToHTML } from '../../../../next-server/server/render'\nimport { tryGetPreviewData } from '../../../../next-server/server/api-utils'\nimport { denormalizePagePath } from '../../../../next-server/server/denormalize-page-path'\nimport {\n  setLazyProp,\n  getCookieParser,\n} from '../../../../next-server/server/api-utils'\nimport { getRedirectStatus } from '../../../../lib/load-custom-routes'\nimport getRouteNoAssetPath from '../../../../next-server/lib/router/utils/get-route-from-asset-path'\nimport { PERMANENT_REDIRECT_STATUS } from '../../../../next-server/lib/constants'\n\nexport function getPageHandler(ctx: ServerlessHandlerCtx) {\n  const {\n    page,\n\n    pageComponent,\n    pageConfig,\n    pageGetStaticProps,\n    pageGetStaticPaths,\n    pageGetServerSideProps,\n\n    appModule,\n    documentModule,\n    errorModule,\n    notFoundModule,\n\n    encodedPreviewProps,\n    pageIsDynamic,\n    generateEtags,\n    poweredByHeader,\n\n    runtimeConfig,\n    buildManifest,\n    reactLoadableManifest,\n\n    i18n,\n    buildId,\n    basePath,\n    assetPrefix,\n    canonicalBase,\n    escapedBuildId,\n  } = ctx\n  const {\n    handleLocale,\n    handleRewrites,\n    handleBasePath,\n    defaultRouteRegex,\n    dynamicRouteMatcher,\n    interpolateDynamicPath,\n    getParamsFromRouteMatches,\n    normalizeDynamicRouteParams,\n    normalizeVercelUrl,\n  } = getUtils(ctx)\n\n  async function renderReqToHTML(\n    req: IncomingMessage,\n    res: ServerResponse,\n    renderMode?: 'export' | 'passthrough' | true,\n    _renderOpts?: any,\n    _params?: any\n  ) {\n    let Component\n    let App\n    let config\n    let Document\n    let Error\n    let notFoundMod\n    let getStaticProps\n    let getStaticPaths\n    let getServerSideProps\n    ;[\n      getStaticProps,\n      getServerSideProps,\n      getStaticPaths,\n      Component,\n      App,\n      config,\n      { default: Document },\n      { default: Error },\n      notFoundMod,\n    ] = await Promise.all([\n      pageGetStaticProps,\n      pageGetServerSideProps,\n      pageGetStaticPaths,\n      pageComponent,\n      appModule,\n      pageConfig,\n      documentModule,\n      errorModule,\n      notFoundModule,\n    ])\n\n    const fromExport = renderMode === 'export' || renderMode === true\n    const nextStartMode = renderMode === 'passthrough'\n\n    let hasValidParams = true\n\n    setLazyProp({ req: req as any }, 'cookies', getCookieParser(req))\n\n    const options = {\n      App,\n      Document,\n      buildManifest,\n      getStaticProps,\n      getServerSideProps,\n      getStaticPaths,\n      reactLoadableManifest,\n      canonicalBase,\n      buildId,\n      assetPrefix,\n      runtimeConfig: (runtimeConfig || {}).publicRuntimeConfig || {},\n      previewProps: encodedPreviewProps,\n      env: process.env,\n      basePath,\n      ..._renderOpts,\n    }\n    let _nextData = false\n    let defaultLocale = i18n?.defaultLocale\n    let detectedLocale = i18n?.defaultLocale\n    let parsedUrl: UrlWithParsedQuery\n\n    try {\n      // We need to trust the dynamic route params from the proxy\n      // to ensure we are using the correct values\n      const trustQuery = !getStaticProps && req.headers[vercelHeader]\n      parsedUrl = parseUrl(req.url!, true)\n      let routeNoAssetPath = parsedUrl.pathname!\n\n      if (basePath) {\n        routeNoAssetPath =\n          routeNoAssetPath.replace(new RegExp(`^${basePath}`), '') || '/'\n      }\n      const origQuery = Object.assign({}, parsedUrl.query)\n\n      parsedUrl = handleRewrites(req, parsedUrl)\n      handleBasePath(req, parsedUrl)\n\n      // remove ?amp=1 from request URL if rendering for export\n      if (fromExport && parsedUrl.query.amp) {\n        const queryNoAmp = Object.assign({}, origQuery)\n        delete queryNoAmp.amp\n\n        req.url = formatUrl({\n          ...parsedUrl,\n          search: undefined,\n          query: queryNoAmp,\n        })\n      }\n\n      if (parsedUrl.pathname!.match(/_next\\/data/)) {\n        _nextData = page !== '/_error'\n        parsedUrl.pathname = getRouteNoAssetPath(\n          parsedUrl.pathname!.replace(\n            new RegExp(`/_next/data/${escapedBuildId}/`),\n            '/'\n          ),\n          '.json'\n        )\n        routeNoAssetPath = parsedUrl.pathname\n      }\n\n      const localeResult = handleLocale(\n        req,\n        res,\n        parsedUrl,\n        routeNoAssetPath,\n        fromExport || nextStartMode\n      )\n      defaultLocale = localeResult?.defaultLocale || defaultLocale\n      detectedLocale = localeResult?.detectedLocale || detectedLocale\n      routeNoAssetPath = localeResult?.routeNoAssetPath || routeNoAssetPath\n\n      if (parsedUrl.query.nextInternalLocale) {\n        detectedLocale = parsedUrl.query.nextInternalLocale as string\n        delete parsedUrl.query.nextInternalLocale\n      }\n\n      const renderOpts = Object.assign(\n        {\n          Component,\n          pageConfig: config,\n          nextExport: fromExport,\n          isDataReq: _nextData,\n          locales: i18n?.locales,\n          locale: detectedLocale,\n          defaultLocale,\n          domainLocales: i18n?.domains,\n        },\n        options\n      )\n\n      if (page === '/_error' && !res.statusCode) {\n        res.statusCode = 404\n      }\n\n      let params = {}\n\n      if (!fromExport && pageIsDynamic) {\n        const result = normalizeDynamicRouteParams(\n          trustQuery\n            ? parsedUrl.query\n            : (dynamicRouteMatcher!(parsedUrl.pathname) as Record<\n                string,\n                string | string[]\n              >)\n        )\n\n        hasValidParams = result.hasValidParams\n        params = result.params\n      }\n\n      let nowParams = null\n\n      if (\n        pageIsDynamic &&\n        !hasValidParams &&\n        req.headers?.['x-now-route-matches']\n      ) {\n        nowParams = getParamsFromRouteMatches(req, renderOpts, detectedLocale)\n      }\n\n      // make sure to set renderOpts to the correct params e.g. _params\n      // if provided from worker or params if we're parsing them here\n      renderOpts.params = _params || params\n\n      normalizeVercelUrl(req, !!trustQuery)\n\n      // normalize request URL/asPath for fallback/revalidate pages since the\n      // proxy sets the request URL to the output's path for fallback pages\n      if (pageIsDynamic && nowParams && defaultRouteRegex) {\n        const _parsedUrl = parseUrl(req.url!)\n\n        _parsedUrl.pathname = interpolateDynamicPath(\n          _parsedUrl.pathname!,\n          nowParams\n        )\n        parsedUrl.pathname = _parsedUrl.pathname\n        req.url = formatUrl(_parsedUrl)\n      }\n\n      // make sure to normalize asPath for revalidate and _next/data requests\n      // since the asPath should match what is shown on the client\n      if (!fromExport && (getStaticProps || getServerSideProps)) {\n        // don't include dynamic route params in query while normalizing\n        // asPath\n        if (pageIsDynamic && trustQuery && defaultRouteRegex) {\n          delete (parsedUrl as any).search\n\n          for (const param of Object.keys(defaultRouteRegex.groups)) {\n            delete origQuery[param]\n          }\n        }\n\n        parsedUrl.pathname = denormalizePagePath(parsedUrl.pathname!)\n        renderOpts.resolvedUrl = formatUrl({\n          ...parsedUrl,\n          query: origQuery,\n        })\n\n        // For getServerSideProps we need to ensure we use the original URL\n        // and not the resolved URL to prevent a hydration mismatch on asPath\n        renderOpts.resolvedAsPath = getServerSideProps\n          ? formatUrl({\n              ...parsedUrl,\n              pathname: routeNoAssetPath,\n              query: origQuery,\n            })\n          : renderOpts.resolvedUrl\n      }\n\n      const isFallback = parsedUrl.query.__nextFallback\n\n      const previewData = tryGetPreviewData(req, res, options.previewProps)\n      const isPreviewMode = previewData !== false\n\n      if (process.env.__NEXT_OPTIMIZE_FONTS) {\n        renderOpts.optimizeFonts = true\n        /**\n         * __webpack_require__.__NEXT_FONT_MANIFEST__ is added by\n         * font-stylesheet-gathering-plugin\n         */\n        // @ts-ignore\n        renderOpts.fontManifest = __webpack_require__.__NEXT_FONT_MANIFEST__\n      }\n\n      let result = await renderToHTML(\n        req,\n        res,\n        page,\n        Object.assign(\n          {},\n          getStaticProps\n            ? { ...(parsedUrl.query.amp ? { amp: '1' } : {}) }\n            : parsedUrl.query,\n          nowParams ? nowParams : params,\n          _params,\n          isFallback ? { __nextFallback: 'true' } : {}\n        ),\n        renderOpts\n      )\n\n      if (!renderMode) {\n        if (_nextData || getStaticProps || getServerSideProps) {\n          if (renderOpts.isNotFound) {\n            res.statusCode = 404\n\n            if (_nextData) {\n              res.end('{\"notFound\":true}')\n              return null\n            }\n\n            const NotFoundComponent = notFoundMod ? notFoundMod.default : Error\n            const errPathname = notFoundMod ? '/404' : '/_error'\n\n            const result2 = await renderToHTML(\n              req,\n              res,\n              errPathname,\n              parsedUrl.query,\n              Object.assign({}, options, {\n                getStaticProps: notFoundMod\n                  ? notFoundMod.getStaticProps\n                  : undefined,\n                getStaticPaths: undefined,\n                getServerSideProps: undefined,\n                Component: NotFoundComponent,\n                err: undefined,\n                locale: detectedLocale,\n                locales: i18n?.locales,\n                defaultLocale: i18n?.defaultLocale,\n              })\n            )\n\n            sendPayload(\n              req,\n              res,\n              result2,\n              'html',\n              {\n                generateEtags,\n                poweredByHeader,\n              },\n              {\n                private: isPreviewMode,\n                stateful: !!getServerSideProps,\n                revalidate: renderOpts.revalidate,\n              }\n            )\n            return null\n          } else if (renderOpts.isRedirect && !_nextData) {\n            const redirect = {\n              destination: renderOpts.pageData.pageProps.__N_REDIRECT,\n              statusCode: renderOpts.pageData.pageProps.__N_REDIRECT_STATUS,\n              basePath: renderOpts.pageData.pageProps.__N_REDIRECT_BASE_PATH,\n            }\n            const statusCode = getRedirectStatus(redirect)\n\n            if (\n              basePath &&\n              redirect.basePath !== false &&\n              redirect.destination.startsWith('/')\n            ) {\n              redirect.destination = `${basePath}${redirect.destination}`\n            }\n\n            if (statusCode === PERMANENT_REDIRECT_STATUS) {\n              res.setHeader('Refresh', `0;url=${redirect.destination}`)\n            }\n\n            res.statusCode = statusCode\n            res.setHeader('Location', redirect.destination)\n            res.end()\n            return null\n          } else {\n            sendPayload(\n              req,\n              res,\n              _nextData ? JSON.stringify(renderOpts.pageData) : result,\n              _nextData ? 'json' : 'html',\n              {\n                generateEtags,\n                poweredByHeader,\n              },\n              {\n                private: isPreviewMode,\n                stateful: !!getServerSideProps,\n                revalidate: renderOpts.revalidate,\n              }\n            )\n            return null\n          }\n        }\n      } else if (isPreviewMode) {\n        res.setHeader(\n          'Cache-Control',\n          'private, no-cache, no-store, max-age=0, must-revalidate'\n        )\n      }\n\n      if (renderMode) return { html: result, renderOpts }\n      return result\n    } catch (err) {\n      if (!parsedUrl!) {\n        parsedUrl = parseUrl(req.url!, true)\n      }\n\n      if (err.code === 'ENOENT') {\n        res.statusCode = 404\n      } else if (err.code === 'DECODE_FAILED' || err.code === 'ENAMETOOLONG') {\n        // TODO: better error?\n        res.statusCode = 400\n      } else {\n        console.error('Unhandled error during request:', err)\n\n        // Backwards compat (call getInitialProps in custom error):\n        try {\n          await renderToHTML(\n            req,\n            res,\n            '/_error',\n            parsedUrl!.query,\n            Object.assign({}, options, {\n              getStaticProps: undefined,\n              getStaticPaths: undefined,\n              getServerSideProps: undefined,\n              Component: Error,\n              err: err,\n              // Short-circuit rendering:\n              isDataReq: true,\n            })\n          )\n        } catch (underErrorErr) {\n          console.error(\n            'Failed call /_error subroutine, continuing to crash function:',\n            underErrorErr\n          )\n        }\n\n        // Throw the error to crash the serverless function\n        if (isResSent(res)) {\n          console.error('!!! WARNING !!!')\n          console.error(\n            'Your function crashed, but closed the response before allowing the function to exit.\\\\n' +\n              'This may cause unexpected behavior for the next request.'\n          )\n          console.error('!!! WARNING !!!')\n        }\n        throw err\n      }\n\n      const result2 = await renderToHTML(\n        req,\n        res,\n        '/_error',\n        parsedUrl!.query,\n        Object.assign({}, options, {\n          getStaticProps: undefined,\n          getStaticPaths: undefined,\n          getServerSideProps: undefined,\n          Component: Error,\n          err: res.statusCode === 404 ? undefined : err,\n        })\n      )\n      return result2\n    }\n  }\n\n  return {\n    renderReqToHTML,\n    render: async function render(req: IncomingMessage, res: ServerResponse) {\n      try {\n        const html = await renderReqToHTML(req, res)\n        if (html) {\n          sendPayload(req, res, html, 'html', {\n            generateEtags,\n            poweredByHeader,\n          })\n        }\n      } catch (err) {\n        console.error(err)\n        // Throw the error to crash the serverless function\n        throw err\n      }\n    },\n  }\n}\n"]}