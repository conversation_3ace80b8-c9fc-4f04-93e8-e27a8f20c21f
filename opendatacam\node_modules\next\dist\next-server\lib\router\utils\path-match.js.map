{"version": 3, "sources": ["../../../../../next-server/lib/router/utils/path-match.ts"], "names": ["matcherOptions", "sensitive", "delimiter", "customRouteMatcherOptions", "strict", "customRoute", "path", "keys", "matcherRegex", "pathToRegexp", "matcher", "regexpToFunction", "pathname", "params", "res", "key", "name"], "mappings": "0IAAA,uF,u1BAIO,KAAMA,CAAAA,cACc,CAAG,CAC5BC,SAAS,CAAE,KADiB,CAE5BC,SAAS,CAAE,GAFiB,CADvB,C,sCAMA,KAAMC,CAAAA,yBACc,CAAG,CAC5B,GAAGH,cADyB,CAE5BI,MAAM,CAAE,IAFoB,CADvB,C,yEAMQ,CAACC,WAAW,CAAG,KAAf,GAAyB,CACtC,MAAQC,CAAAA,IAAD,EAAkB,CACvB,KAAMC,CAAAA,IAAwB,CAAG,EAAjC,CACA,KAAMC,CAAAA,YAAY,CAAGC,YAAY,CAACA,YAAb,CACnBH,IADmB,CAEnBC,IAFmB,CAGnBF,WAAW,CAAGF,yBAAH,CAA+BH,cAHvB,CAArB,CAKA,KAAMU,CAAAA,OAAO,CAAGD,YAAY,CAACE,gBAAb,CAA8BH,YAA9B,CAA4CD,IAA5C,CAAhB,CAEA,MAAO,CAACK,QAAD,CAAsCC,MAAtC,GAAuD,CAC5D,KAAMC,CAAAA,GAAG,CAAGF,QAAQ,EAAI,IAAZ,CAAmB,KAAnB,CAA2BF,OAAO,CAACE,QAAD,CAA9C,CACA,GAAI,CAACE,GAAL,CAAU,CACR,MAAO,MAAP,CACD,CAED,GAAIT,WAAJ,CAAiB,CACf,IAAK,KAAMU,CAAAA,GAAX,GAAkBR,CAAAA,IAAlB,CAAwB,CACtB;AACA;AACA,GAAI,MAAOQ,CAAAA,GAAG,CAACC,IAAX,GAAoB,QAAxB,CAAkC,CAChC,MAAQF,CAAAA,GAAG,CAACD,MAAL,CAAoBE,GAAG,CAACC,IAAxB,CAAP,CACD,CACF,CACF,CAED,MAAO,CAAE,GAAGH,MAAL,CAAa,GAAGC,GAAG,CAACD,MAApB,CAAP,CACD,CAjBD,CAkBD,CA3BD,CA4BD,C", "sourcesContent": ["import * as pathToRegexp from 'next/dist/compiled/path-to-regexp'\n\nexport { pathToRegexp }\n\nexport const matcherOptions: pathToRegexp.TokensToRegexpOptions &\n  pathToRegexp.ParseOptions = {\n  sensitive: false,\n  delimiter: '/',\n}\n\nexport const customRouteMatcherOptions: pathToRegexp.TokensToRegexpOptions &\n  pathToRegexp.ParseOptions = {\n  ...matcherOptions,\n  strict: true,\n}\n\nexport default (customRoute = false) => {\n  return (path: string) => {\n    const keys: pathToRegexp.Key[] = []\n    const matcherRegex = pathToRegexp.pathToRegexp(\n      path,\n      keys,\n      customRoute ? customRouteMatcherOptions : matcherOptions\n    )\n    const matcher = pathToRegexp.regexpToFunction(matcherRegex, keys)\n\n    return (pathname: string | null | undefined, params?: any) => {\n      const res = pathname == null ? false : matcher(pathname)\n      if (!res) {\n        return false\n      }\n\n      if (customRoute) {\n        for (const key of keys) {\n          // unnamed params should be removed as they\n          // are not allowed to be used in the destination\n          if (typeof key.name === 'number') {\n            delete (res.params as any)[key.name]\n          }\n        }\n      }\n\n      return { ...params, ...res.params }\n    }\n  }\n}\n"]}