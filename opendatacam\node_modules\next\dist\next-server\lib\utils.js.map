{"version": 3, "sources": ["../../../next-server/lib/utils.ts"], "names": ["execOnce", "fn", "used", "result", "args", "getLocationOrigin", "protocol", "hostname", "port", "window", "location", "getURL", "href", "origin", "substring", "length", "getDisplayName", "Component", "displayName", "name", "isResSent", "res", "finished", "headersSent", "loadGetInitialProps", "App", "ctx", "process", "env", "NODE_ENV", "prototype", "getInitialProps", "message", "Error", "pageProps", "props", "Object", "keys", "console", "warn", "urlObjectKeys", "formatWithValidation", "url", "for<PERSON>ach", "key", "indexOf", "SP", "performance", "ST", "mark", "measure"], "mappings": "wVAIA,oDAyRA;AACA;AACA,GACO,QAASA,CAAAA,QAAT,CACLC,EADK,CAEF,CACH,GAAIC,CAAAA,IAAI,CAAG,KAAX,CACA,GAAIC,CAAAA,MAAJ,CAEA,MAAQ,CAAC,GAAGC,IAAJ,GAAoB,CAC1B,GAAI,CAACF,IAAL,CAAW,CACTA,IAAI,CAAG,IAAP,CACAC,MAAM,CAAGF,EAAE,CAAC,GAAGG,IAAJ,CAAX,CACD,CACD,MAAOD,CAAAA,MAAP,CACD,CAND,CAOD,CAEM,QAASE,CAAAA,iBAAT,EAA6B,CAClC,KAAM,CAAEC,QAAF,CAAYC,QAAZ,CAAsBC,IAAtB,EAA+BC,MAAM,CAACC,QAA5C,CACA,MAAQ,GAAEJ,QAAS,KAAIC,QAAS,GAAEC,IAAI,CAAG,IAAMA,IAAT,CAAgB,EAAG,EAAzD,CACD,CAEM,QAASG,CAAAA,MAAT,EAAkB,CACvB,KAAM,CAAEC,IAAF,EAAWH,MAAM,CAACC,QAAxB,CACA,KAAMG,CAAAA,MAAM,CAAGR,iBAAiB,EAAhC,CACA,MAAOO,CAAAA,IAAI,CAACE,SAAL,CAAeD,MAAM,CAACE,MAAtB,CAAP,CACD,CAEM,QAASC,CAAAA,cAAT,CAA2BC,SAA3B,CAAwD,CAC7D,MAAO,OAAOA,CAAAA,SAAP,GAAqB,QAArB,CACHA,SADG,CAEHA,SAAS,CAACC,WAAV,EAAyBD,SAAS,CAACE,IAAnC,EAA2C,SAF/C,CAGD,CAEM,QAASC,CAAAA,SAAT,CAAmBC,GAAnB,CAAwC,CAC7C,MAAOA,CAAAA,GAAG,CAACC,QAAJ,EAAgBD,GAAG,CAACE,WAA3B,CACD,CAEM,cAAeC,CAAAA,mBAAf,CAILC,GAJK,CAI6BC,GAJ7B,CAIkD,CACvD,GAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,GAAyB,YAA7B,CAA2C,oBACzC,mBAAIJ,GAAG,CAACK,SAAR,SAAI,eAAeC,eAAnB,CAAoC,CAClC,KAAMC,CAAAA,OAAO,CAAI,IAAGhB,cAAc,CAChCS,GADgC,CAEhC,6JAFF,CAGA,KAAM,IAAIQ,CAAAA,KAAJ,CAAUD,OAAV,CAAN,CACD,CACF,CACD;AACA,KAAMX,CAAAA,GAAG,CAAGK,GAAG,CAACL,GAAJ,EAAYK,GAAG,CAACA,GAAJ,EAAWA,GAAG,CAACA,GAAJ,CAAQL,GAA3C,CAEA,GAAI,CAACI,GAAG,CAACM,eAAT,CAA0B,CACxB,GAAIL,GAAG,CAACA,GAAJ,EAAWA,GAAG,CAACT,SAAnB,CAA8B,CAC5B;AACA,MAAO,CACLiB,SAAS,CAAE,KAAMV,CAAAA,mBAAmB,CAACE,GAAG,CAACT,SAAL,CAAgBS,GAAG,CAACA,GAApB,CAD/B,CAAP,CAGD,CACD,MAAO,EAAP,CACD,CAED,KAAMS,CAAAA,KAAK,CAAG,KAAMV,CAAAA,GAAG,CAACM,eAAJ,CAAoBL,GAApB,CAApB,CAEA,GAAIL,GAAG,EAAID,SAAS,CAACC,GAAD,CAApB,CAA2B,CACzB,MAAOc,CAAAA,KAAP,CACD,CAED,GAAI,CAACA,KAAL,CAAY,CACV,KAAMH,CAAAA,OAAO,CAAI,IAAGhB,cAAc,CAChCS,GADgC,CAEhC,+DAA8DU,KAAM,YAFtE,CAGA,KAAM,IAAIF,CAAAA,KAAJ,CAAUD,OAAV,CAAN,CACD,CAED,GAAIL,OAAO,CAACC,GAAR,CAAYC,QAAZ,GAAyB,YAA7B,CAA2C,CACzC,GAAIO,MAAM,CAACC,IAAP,CAAYF,KAAZ,EAAmBpB,MAAnB,GAA8B,CAA9B,EAAmC,CAACW,GAAG,CAACA,GAA5C,CAAiD,CAC/CY,OAAO,CAACC,IAAR,CACG,GAAEvB,cAAc,CACfS,GADe,CAEf,iLAHJ,EAKD,CACF,CAED,MAAOU,CAAAA,KAAP,CACD,CAEM,KAAMK,CAAAA,aAAa,CAAG,CAC3B,MAD2B,CAE3B,MAF2B,CAG3B,MAH2B,CAI3B,UAJ2B,CAK3B,MAL2B,CAM3B,MAN2B,CAO3B,UAP2B,CAQ3B,MAR2B,CAS3B,UAT2B,CAU3B,OAV2B,CAW3B,QAX2B,CAY3B,SAZ2B,CAAtB,C,oCAeA,QAASC,CAAAA,oBAAT,CAA8BC,GAA9B,CAAsD,CAC3D,GAAIf,OAAO,CAACC,GAAR,CAAYC,QAAZ,GAAyB,aAA7B,CAA4C,CAC1C,GAAIa,GAAG,GAAK,IAAR,EAAgB,MAAOA,CAAAA,GAAP,GAAe,QAAnC,CAA6C,CAC3CN,MAAM,CAACC,IAAP,CAAYK,GAAZ,EAAiBC,OAAjB,CAA0BC,GAAD,EAAS,CAChC,GAAIJ,aAAa,CAACK,OAAd,CAAsBD,GAAtB,IAA+B,CAAC,CAApC,CAAuC,CACrCN,OAAO,CAACC,IAAR,CACG,qDAAoDK,GAAI,EAD3D,EAGD,CACF,CAND,EAOD,CACF,CAED,MAAO,yBAAUF,GAAV,CAAP,CACD,CAEM,KAAMI,CAAAA,EAAE,CAAG,MAAOC,CAAAA,WAAP,GAAuB,WAAlC,C,cACA,KAAMC,CAAAA,EAAE,CACbF,EAAE,EACF,MAAOC,CAAAA,WAAW,CAACE,IAAnB,GAA4B,UAD5B,EAEA,MAAOF,CAAAA,WAAW,CAACG,OAAnB,GAA+B,UAH1B,C", "sourcesContent": ["import { IncomingMessage, ServerResponse } from 'http'\nimport { ParsedUrlQuery } from 'querystring'\nimport { ComponentType } from 'react'\nimport { UrlObject } from 'url'\nimport { formatUrl } from './router/utils/format-url'\nimport { NextRouter } from './router/router'\nimport { Env } from '@next/env'\nimport { BuildManifest } from '../server/get-page-files'\nimport { DomainLocales } from '../server/config'\nimport { PreviewData } from 'next/types'\n\n/**\n * Types used by both next and next-server\n */\n\nexport type NextComponentType<\n  C extends BaseContext = NextPageContext,\n  IP = {},\n  P = {}\n> = ComponentType<P> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param ctx Context of `page`\n   */\n  getInitialProps?(context: C): IP | Promise<IP>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n> & {\n  renderDocument(\n    Document: DocumentType,\n    props: DocumentProps\n  ): React.ReactElement\n}\n\nexport type AppType = NextComponentType<\n  AppContextType,\n  AppInitialProps,\n  AppPropsType\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport type NextWebVitalsMetric = {\n  id: string\n  label: string\n  name: string\n  startTime: number\n  value: number\n}\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => RenderPageResult | Promise<RenderPageResult>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & { statusCode?: number }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: string[]\n  defaultLocale?: string\n  domainLocales?: DomainLocales\n  scriptLoader?: any[]\n  isPreview?: boolean\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<R extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: R\n}\n\nexport type AppInitialProps = {\n  pageProps: any\n}\n\nexport type AppPropsType<\n  R extends NextRouter = NextRouter,\n  P = {}\n> = AppInitialProps & {\n  Component: NextComponentType<NextPageContext, any, P>\n  router: R\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | React.ReactFragment\n}\n\nexport type DocumentProps = DocumentInitialProps & {\n  __NEXT_DATA__: NEXT_DATA\n  dangerousAsPath: string\n  docComponentsRendered: {\n    Html?: boolean\n    Main?: boolean\n    Head?: boolean\n    NextScript?: boolean\n  }\n  buildManifest: BuildManifest\n  ampPath: string\n  inAmpMode: boolean\n  hybridAmp: boolean\n  isDevelopment: boolean\n  dynamicImports: string[]\n  assetPrefix?: string\n  canonicalBase: string\n  headTags: any[]\n  unstable_runtimeJS?: false\n  unstable_JsPreload?: false\n  devOnlyCacheBusterQueryString: string\n  scriptLoader: { afterInteractive?: string[]; beforeInteractive?: any[] }\n  locale?: string\n  disableOptimizedLoading?: boolean\n}\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: {\n    [key: string]: string | string[]\n  }\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: {\n    [key: string]: string\n  }\n\n  body: any\n\n  env: Env\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<T = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<T>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<T>\n  status: (statusCode: number) => NextApiResponse<T>\n  redirect(url: string): NextApiResponse<T>\n  redirect(status: number, url: string): NextApiResponse<T>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n    }\n  ) => NextApiResponse<T>\n  clearPreviewData: () => NextApiResponse<T>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => void | Promise<void>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {}\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (urlObjectKeys.indexOf(key) === -1) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  typeof performance.mark === 'function' &&\n  typeof performance.measure === 'function'\n"]}