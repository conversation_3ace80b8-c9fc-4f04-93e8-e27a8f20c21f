{"version": 3, "sources": ["../../../../next-server/server/lib/recursive-readdir-sync.ts"], "names": ["recursiveReadDirSync", "dir", "arr", "rootDir", "result", "fs", "readdirSync", "for<PERSON>ach", "part", "absolutePath", "pathStat", "statSync", "isDirectory", "push", "replace"], "mappings": "uFAAA,8CACA,0B,mFAEA;AACA;AACA;AACA;AACA;AACA,GACO,QAASA,CAAAA,oBAAT,CACLC,GADK,CAELC,GAAa,CAAG,EAFX,CAGLC,OAAO,CAAGF,GAHL,CAIK,CACV,KAAMG,CAAAA,MAAM,CAAGC,YAAGC,WAAH,CAAeL,GAAf,CAAf,CAEAG,MAAM,CAACG,OAAP,CAAgBC,IAAD,EAAkB,CAC/B,KAAMC,CAAAA,YAAY,CAAG,eAAKR,GAAL,CAAUO,IAAV,CAArB,CACA,KAAME,CAAAA,QAAQ,CAAGL,YAAGM,QAAH,CAAYF,YAAZ,CAAjB,CAEA,GAAIC,QAAQ,CAACE,WAAT,EAAJ,CAA4B,CAC1BZ,oBAAoB,CAACS,YAAD,CAAeP,GAAf,CAAoBC,OAApB,CAApB,CACA,OACD,CACDD,GAAG,CAACW,IAAJ,CAASJ,YAAY,CAACK,OAAb,CAAqBX,OAArB,CAA8B,EAA9B,CAAT,EACD,CATD,EAWA,MAAOD,CAAAA,GAAP,CACD", "sourcesContent": ["import fs from 'fs'\nimport { join } from 'path'\n\n/**\n * Recursively read directory\n * @param  {string[]=[]} arr This doesn't have to be provided, it's used for the recursion\n * @param  {string=dir`} rootDir Used to replace the initial path, only the relative path is left, it's faster than path.relative.\n * @returns Array holding all relative paths\n */\nexport function recursiveReadDirSync(\n  dir: string,\n  arr: string[] = [],\n  rootDir = dir\n): string[] {\n  const result = fs.readdirSync(dir)\n\n  result.forEach((part: string) => {\n    const absolutePath = join(dir, part)\n    const pathStat = fs.statSync(absolutePath)\n\n    if (pathStat.isDirectory()) {\n      recursiveReadDirSync(absolutePath, arr, rootDir)\n      return\n    }\n    arr.push(absolutePath.replace(rootDir, ''))\n  })\n\n  return arr\n}\n"]}