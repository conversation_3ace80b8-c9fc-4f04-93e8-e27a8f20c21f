{"version": 3, "sources": ["../../../../../build/webpack/loaders/next-serverless-loader/utils.ts"], "names": ["getCustomRouteMatcher", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getUtils", "page", "i18n", "basePath", "rewrites", "pageIsDynamic", "defaultRouteRegex", "dynamicRouteMatcher", "defaultRouteMatches", "handleRewrites", "req", "parsedUrl", "rewrite", "matcher", "source", "params", "pathname", "has", "hasParams", "query", "Object", "assign", "parsedDestination", "destination", "fsPathname", "replace", "RegExp", "destLocalePathResult", "locales", "nextInternalLocale", "detectedLocale", "dynamicParams", "handleBasePath", "url", "getParamsFromRouteMatches", "renderOpts", "groups", "routeKeys", "re", "exec", "str", "obj", "routeKeyNames", "keys", "filterLocaleItem", "val", "isCatchAll", "Array", "isArray", "_val", "some", "item", "toLowerCase", "locale", "splice", "length", "every", "name", "reduce", "prev", "keyName", "paramName", "pos", "key", "headers", "interpolateDynamicPath", "param", "optional", "repeat", "builtParam", "paramIdx", "indexOf", "paramValue", "join", "substr", "encodeURI", "normalizeVercelUrl", "trustQuery", "_parsedUrl", "search", "normalizeDynamicRouteParams", "hasValidParams", "value", "defaultValue", "isDefaultValue", "defaultVal", "includes", "undefined", "split", "handleLocale", "res", "routeNoAssetPath", "shouldNotRedirect", "defaultLocale", "acceptPreferredLocale", "localeDetection", "accept", "language", "host", "hostname", "detectedDomain", "domains", "__nextIsLocaleDomain", "localeDomainRedirect", "localePathResult", "__nextStrippedLocale", "localeToCheck", "matchedDomain", "domain", "http", "denormalizedPagePath", "detectedDefaultLocale", "shouldStripDefaultLocale", "shouldAddLocalePrefix", "previous", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "cookie", "serialize", "httpOnly", "path", "statusCode", "TEMPORARY_REDIRECT_STATUS", "end"], "mappings": "2FACA,wBACA,wCAEA,2FACA,sGACA,gFACA,oFACA,yHAUA,4DACA,yFACA,yFACA,yFACA,yEACA,gE,w4BAGA,KAAMA,CAAAA,qBAAqB,CAAG,uBAAU,IAAV,CAA9B,CAEO,KAAMC,CAAAA,YAAY,CAAG,aAArB,C,kCAkCA,QAASC,CAAAA,QAAT,CAAkB,CACvBC,IADuB,CAEvBC,IAFuB,CAGvBC,QAHuB,CAIvBC,QAJuB,CAKvBC,aALuB,CAAlB,CAYJ,CACD,GAAIC,CAAAA,iBAAJ,CACA,GAAIC,CAAAA,mBAAJ,CACA,GAAIC,CAAAA,mBAAJ,CAEA,GAAIH,aAAJ,CAAmB,CACjBC,iBAAiB,CAAG,8BAAcL,IAAd,CAApB,CACAM,mBAAmB,CAAG,kCAAgBD,iBAAhB,CAAtB,CACAE,mBAAmB,CAAGD,mBAAmB,CAACN,IAAD,CAAzC,CACD,CAED,QAASQ,CAAAA,cAAT,CAAwBC,GAAxB,CAA8CC,SAA9C,CAA6E,CAC3E,IAAK,KAAMC,CAAAA,OAAX,GAAsBR,CAAAA,QAAtB,CAAgC,CAC9B,KAAMS,CAAAA,OAAO,CAAGf,qBAAqB,CAACc,OAAO,CAACE,MAAT,CAArC,CACA,GAAIC,CAAAA,MAAM,CAAGF,OAAO,CAACF,SAAS,CAACK,QAAX,CAApB,CAEA,GAAIJ,OAAO,CAACK,GAAR,EAAeF,MAAnB,CAA2B,CACzB,KAAMG,CAAAA,SAAS,CAAG,iCAASR,GAAT,CAAcE,OAAO,CAACK,GAAtB,CAA2BN,SAAS,CAACQ,KAArC,CAAlB,CAEA,GAAID,SAAJ,CAAe,CACbE,MAAM,CAACC,MAAP,CAAcN,MAAd,CAAsBG,SAAtB,EACD,CAFD,IAEO,CACLH,MAAM,CAAG,KAAT,CACD,CACF,CAED,GAAIA,MAAJ,CAAY,CACV,KAAM,CAAEO,iBAAF,EAAwB,gCAC5BV,OAAO,CAACW,WADoB,CAE5BR,MAF4B,CAG5BJ,SAAS,CAACQ,KAHkB,CAI5B,IAJ4B,CAA9B,CAOAC,MAAM,CAACC,MAAP,CAAcV,SAAS,CAACQ,KAAxB,CAA+BG,iBAAiB,CAACH,KAAjD,EACA,MAAQG,CAAAA,iBAAD,CAA2BH,KAAlC,CAEAC,MAAM,CAACC,MAAP,CAAcV,SAAd,CAAyBW,iBAAzB,EAEA,GAAIE,CAAAA,UAAU,CAAGb,SAAS,CAACK,QAA3B,CAEA,GAAIb,QAAJ,CAAc,CACZqB,UAAU,CACRA,UAAU,CAAEC,OAAZ,CAAoB,GAAIC,CAAAA,MAAJ,CAAY,IAAGvB,QAAS,EAAxB,CAApB,CAAgD,EAAhD,GAAuD,GADzD,CAED,CAED,GAAID,IAAJ,CAAU,CACR,KAAMyB,CAAAA,oBAAoB,CAAG,6CAC3BH,UAD2B,CAE3BtB,IAAI,CAAC0B,OAFsB,CAA7B,CAIAJ,UAAU,CAAGG,oBAAoB,CAACX,QAAlC,CACAL,SAAS,CAACQ,KAAV,CAAgBU,kBAAhB,CACEF,oBAAoB,CAACG,cAArB,EAAuCf,MAAM,CAACc,kBADhD,CAED,CAED,GAAIL,UAAU,GAAKvB,IAAnB,CAAyB,CACvB,MACD,CAED,GAAII,aAAa,EAAIE,mBAArB,CAA0C,CACxC,KAAMwB,CAAAA,aAAa,CAAGxB,mBAAmB,CAACiB,UAAD,CAAzC,CACA,GAAIO,aAAJ,CAAmB,CACjBpB,SAAS,CAACQ,KAAV,CAAkB,CAChB,GAAGR,SAAS,CAACQ,KADG,CAEhB,GAAGY,aAFa,CAAlB,CAIA,MACD,CACF,CACF,CACF,CAED,MAAOpB,CAAAA,SAAP,CACD,CAED,QAASqB,CAAAA,cAAT,CAAwBtB,GAAxB,CAA8CC,SAA9C,CAA6E,CAC3E;AACAD,GAAG,CAACuB,GAAJ,CAAUvB,GAAG,CAACuB,GAAJ,CAASR,OAAT,CAAiB,GAAIC,CAAAA,MAAJ,CAAY,IAAGvB,QAAS,EAAxB,CAAjB,CAA6C,EAA7C,GAAoD,GAA9D,CACAQ,SAAS,CAACK,QAAV,CACEL,SAAS,CAACK,QAAV,CAAoBS,OAApB,CAA4B,GAAIC,CAAAA,MAAJ,CAAY,IAAGvB,QAAS,EAAxB,CAA5B,CAAwD,EAAxD,GAA+D,GADjE,CAED,CAED,QAAS+B,CAAAA,yBAAT,CACExB,GADF,CAEEyB,UAFF,CAGEL,cAHF,CAIE,CACA,MAAO,kCACJ,UAAY,CACX,KAAM,CAAEM,MAAF,CAAUC,SAAV,EAAwB/B,iBAA9B,CAEA,MAAO,CACLgC,EAAE,CAAE,CACF;AACAC,IAAI,CAAGC,GAAD,EAAiB,CACrB,KAAMC,CAAAA,GAAG,CAAG,uBAAQD,GAAR,CAAZ,CAEA;AACA,KAAME,CAAAA,aAAa,CAAGtB,MAAM,CAACuB,IAAP,CAAYN,SAAS,EAAI,EAAzB,CAAtB,CAEA,KAAMO,CAAAA,gBAAgB,CAAIC,GAAD,EAA4B,CACnD,GAAI3C,IAAJ,CAAU,CACR;AACA;AACA;AACA,KAAM4C,CAAAA,UAAU,CAAGC,KAAK,CAACC,OAAN,CAAcH,GAAd,CAAnB,CACA,KAAMI,CAAAA,IAAI,CAAGH,UAAU,CAAGD,GAAG,CAAC,CAAD,CAAN,CAAYA,GAAnC,CAEA,GACE,MAAOI,CAAAA,IAAP,GAAgB,QAAhB,EACA/C,IAAI,CAAC0B,OAAL,CAAasB,IAAb,CAAmBC,IAAD,EAAU,CAC1B,GAAIA,IAAI,CAACC,WAAL,KAAuBH,IAAI,CAACG,WAAL,EAA3B,CAA+C,CAC7CtB,cAAc,CAAGqB,IAAjB,CACAhB,UAAU,CAACkB,MAAX,CAAoBvB,cAApB,CACA,MAAO,KAAP,CACD,CACD,MAAO,MAAP,CACD,CAPD,CAFF,CAUE,CACA;AACA,GAAIgB,UAAJ,CAAgB,CACd,CAAED,GAAD,CAAkBS,MAAlB,CAAyB,CAAzB,CAA4B,CAA5B,EACF,CAED;AACA;AACA,MAAOR,CAAAA,UAAU,CAAGD,GAAG,CAACU,MAAJ,GAAe,CAAlB,CAAsB,IAAvC,CACD,CACF,CACD,MAAO,MAAP,CACD,CA9BD,CAgCA,GAAIb,aAAa,CAACc,KAAd,CAAqBC,IAAD,EAAUhB,GAAG,CAACgB,IAAD,CAAjC,CAAJ,CAA8C,CAC5C,MAAOf,CAAAA,aAAa,CAACgB,MAAd,CAAqB,CAACC,IAAD,CAAOC,OAAP,GAAmB,CAC7C,KAAMC,CAAAA,SAAS,CAAGxB,SAAH,cAAGA,SAAS,CAAGuB,OAAH,CAA3B,CAEA,GAAIC,SAAS,EAAI,CAACjB,gBAAgB,CAACH,GAAG,CAACmB,OAAD,CAAJ,CAAlC,CAAkD,CAChDD,IAAI,CAACvB,MAAM,CAACyB,SAAD,CAAN,CAAkBC,GAAnB,CAAJ,CAA8BrB,GAAG,CAACmB,OAAD,CAAjC,CACD,CACD,MAAOD,CAAAA,IAAP,CACD,CAPM,CAOJ,EAPI,CAAP,CAQD,CAED,MAAOvC,CAAAA,MAAM,CAACuB,IAAP,CAAYF,GAAZ,EAAiBiB,MAAjB,CAAwB,CAACC,IAAD,CAAOI,GAAP,GAAe,CAC5C,GAAI,CAACnB,gBAAgB,CAACH,GAAG,CAACsB,GAAD,CAAJ,CAArB,CAAiC,CAC/B,MAAO3C,CAAAA,MAAM,CAACC,MAAP,CAAcsC,IAAd,CAAoB,CACzB,CAACI,GAAD,EAAOtB,GAAG,CAACsB,GAAD,CADe,CAApB,CAAP,CAGD,CACD,MAAOJ,CAAAA,IAAP,CACD,CAPM,CAOJ,EAPI,CAAP,CAQD,CA3DC,CADC,CA8DLvB,MA9DK,CAAP,CAgED,CAnED,EADK,EAqEL1B,GAAG,CAACsD,OAAJ,CAAY,qBAAZ,CArEK,CAAP,CAsED,CAED,QAASC,CAAAA,sBAAT,CAAgCjD,QAAhC,CAAkDD,MAAlD,CAA0E,CACxE,GAAI,CAACT,iBAAL,CAAwB,MAAOU,CAAAA,QAAP,CAExB,IAAK,KAAMkD,CAAAA,KAAX,GAAoB9C,CAAAA,MAAM,CAACuB,IAAP,CAAYrC,iBAAiB,CAAC8B,MAA9B,CAApB,CAA2D,CACzD,KAAM,CAAE+B,QAAF,CAAYC,MAAZ,EAAuB9D,iBAAiB,CAAC8B,MAAlB,CAAyB8B,KAAzB,CAA7B,CACA,GAAIG,CAAAA,UAAU,CAAI,IAAGD,MAAM,CAAG,KAAH,CAAW,EAAG,GAAEF,KAAM,GAAjD,CAEA,GAAIC,QAAJ,CAAc,CACZE,UAAU,CAAI,IAAGA,UAAW,GAA5B,CACD,CAED,KAAMC,CAAAA,QAAQ,CAAGtD,QAAQ,CAAEuD,OAAV,CAAkBF,UAAlB,CAAjB,CAEA,GAAIC,QAAQ,CAAG,CAAC,CAAhB,CAAmB,CACjB,GAAIE,CAAAA,UAAJ,CAEA,GAAIzB,KAAK,CAACC,OAAN,CAAcjC,MAAM,CAACmD,KAAD,CAApB,CAAJ,CAAkC,CAChCM,UAAU,CAAIzD,MAAM,CAACmD,KAAD,CAAP,CAA4BO,IAA5B,CAAiC,GAAjC,CAAb,CACD,CAFD,IAEO,CACLD,UAAU,CAAGzD,MAAM,CAACmD,KAAD,CAAnB,CACD,CAEDlD,QAAQ,CACNA,QAAQ,CAAC0D,MAAT,CAAgB,CAAhB,CAAmBJ,QAAnB,EACAK,SAAS,CAACH,UAAU,EAAI,EAAf,CADT,CAEAxD,QAAQ,CAAC0D,MAAT,CAAgBJ,QAAQ,CAAGD,UAAU,CAACd,MAAtC,CAHF,CAID,CACF,CAED,MAAOvC,CAAAA,QAAP,CACD,CAED,QAAS4D,CAAAA,kBAAT,CAA4BlE,GAA5B,CAAkDmE,UAAlD,CAAuE,CACrE;AACA;AACA,GAAIxE,aAAa,EAAIwE,UAAjB,EAA+BvE,iBAAnC,CAAsD,CACpD,KAAMwE,CAAAA,UAAU,CAAG,eAASpE,GAAG,CAACuB,GAAb,CAAmB,IAAnB,CAAnB,CACA,MAAQ6C,CAAAA,UAAD,CAAoBC,MAA3B,CAEA,IAAK,KAAMb,CAAAA,KAAX,GAAoB9C,CAAAA,MAAM,CAACuB,IAAP,CAAYrC,iBAAiB,CAAC8B,MAA9B,CAApB,CAA2D,CACzD,MAAO0C,CAAAA,UAAU,CAAC3D,KAAX,CAAiB+C,KAAjB,CAAP,CACD,CACDxD,GAAG,CAACuB,GAAJ,CAAU,gBAAU6C,UAAV,CAAV,CACD,CACF,CAED,QAASE,CAAAA,2BAAT,CAAqCjE,MAArC,CAA6D,CAC3D,GAAIkE,CAAAA,cAAc,CAAG,IAArB,CACA,GAAI,CAAC3E,iBAAL,CAAwB,MAAO,CAAES,MAAF,CAAUkE,cAAc,CAAE,KAA1B,CAAP,CAExBlE,MAAM,CAAGK,MAAM,CAACuB,IAAP,CAAYrC,iBAAiB,CAAC8B,MAA9B,EAAsCsB,MAAtC,CAA6C,CAACC,IAAD,CAAOI,GAAP,GAAe,aACnE,GAAImB,CAAAA,KAAoC,CAAGnE,MAAM,CAACgD,GAAD,CAAjD,CAEA;AACA;AACA;AACA,KAAMoB,CAAAA,YAAY,CAAG3E,mBAAmB,CAAEuD,GAAF,CAAxC,CAEA,KAAMqB,CAAAA,cAAc,CAAGrC,KAAK,CAACC,OAAN,CAAcmC,YAAd,EACnBA,YAAY,CAACjC,IAAb,CAAmBmC,UAAD,EAAgB,YAChC,MAAOtC,CAAAA,KAAK,CAACC,OAAN,CAAckC,KAAd,EACHA,KAAK,CAAChC,IAAN,CAAYL,GAAD,EAASA,GAAG,CAACyC,QAAJ,CAAaD,UAAb,CAApB,CADG,SAEHH,KAFG,eAEH,OAAOI,QAAP,CAAgBD,UAAhB,CAFJ,CAGD,CAJD,CADmB,UAMnBH,KANmB,eAMnB,QAAOI,QAAP,CAAgBH,YAAhB,CANJ,CAQA,GAAIC,cAAc,EAAI,MAAOF,CAAAA,KAAP,GAAiB,WAAvC,CAAoD,CAClDD,cAAc,CAAG,KAAjB,CACD,CAED;AACA;AACA,GACE3E,iBAAiB,CAAE8B,MAAnB,CAA0B2B,GAA1B,EAA+BI,QAA/B,GACC,CAACe,KAAD,EACEnC,KAAK,CAACC,OAAN,CAAckC,KAAd,GACCA,KAAK,CAAC3B,MAAN,GAAiB,CADlB,GAEC;AACA;AACC2B,KAAK,CAAC,CAAD,CAAL,GAAa,OAAb,EAAwBA,KAAK,CAAC,CAAD,CAAL,GAAc,QAAOnB,GAAI,IAJnD,CAFH,CADF,CAQE,CACAmB,KAAK,CAAGK,SAAR,CACA,MAAOxE,CAAAA,MAAM,CAACgD,GAAD,CAAb,CACD,CAED;AACA;AACA,GACEmB,KAAK,EACL,MAAOA,CAAAA,KAAP,GAAiB,QADjB,EAEA5E,iBAAiB,CAAE8B,MAAnB,CAA0B2B,GAA1B,EAA+BK,MAHjC,CAIE,CACAc,KAAK,CAAGA,KAAK,CAACM,KAAN,CAAY,GAAZ,CAAR,CACD,CAED,GAAIN,KAAJ,CAAW,CACTvB,IAAI,CAACI,GAAD,CAAJ,CAAYmB,KAAZ,CACD,CACD,MAAOvB,CAAAA,IAAP,CACD,CAjDQ,CAiDN,EAjDM,CAAT,CAmDA,MAAO,CACL5C,MADK,CAELkE,cAFK,CAAP,CAID,CAED,QAASQ,CAAAA,YAAT,CACE/E,GADF,CAEEgF,GAFF,CAGE/E,SAHF,CAIEgF,gBAJF,CAKEC,iBALF,CAME,CACA,GAAI,CAAC1F,IAAL,CAAW,OACX,KAAMc,CAAAA,QAAQ,CAAGL,SAAS,CAACK,QAAV,EAAsB,GAAvC,CAEA,GAAI6E,CAAAA,aAAa,CAAG3F,IAAI,CAAC2F,aAAzB,CACA,GAAI/D,CAAAA,cAAc,CAAG,2CAAmBpB,GAAnB,CAAwBR,IAAI,CAAC0B,OAA7B,CAArB,CACA,GAAIkE,CAAAA,qBAAqB,CACvB5F,IAAI,CAAC6F,eAAL,GAAyB,KAAzB,CACIC,gBAAOC,QAAP,CAAgBvF,GAAG,CAACsD,OAAJ,CAAY,iBAAZ,CAAhB,CAAgD9D,IAAI,CAAC0B,OAArD,CADJ,CAEIE,cAHN,CAKA,KAAM,CAAEoE,IAAF,EAAWxF,GAAG,CAACsD,OAAJ,EAAe,EAAhC,CACA;AACA,KAAMmC,CAAAA,QAAQ,CAAGD,IAAI,EAAIA,IAAI,CAACV,KAAL,CAAW,GAAX,EAAgB,CAAhB,EAAmBpC,WAAnB,EAAzB,CAEA,KAAMgD,CAAAA,cAAc,CAAG,2CAAmBlG,IAAI,CAACmG,OAAxB,CAAiCF,QAAjC,CAAvB,CACA,GAAIC,cAAJ,CAAoB,CAClBP,aAAa,CAAGO,cAAc,CAACP,aAA/B,CACA/D,cAAc,CAAG+D,aAAjB,CACEnF,GAAD,CAAa4F,oBAAb,CAAoC,IAApC,CACF,CAED;AACAxE,cAAc,CAAGA,cAAc,EAAIgE,qBAAnC,CAEA,GAAIS,CAAAA,oBAAJ,CACA,KAAMC,CAAAA,gBAAgB,CAAG,6CAAoBxF,QAApB,CAA8Bd,IAAI,CAAC0B,OAAnC,CAAzB,CAEA+D,gBAAgB,CAAG,6CAAoBA,gBAApB,CAAsCzF,IAAI,CAAC0B,OAA3C,EAChBZ,QADH,CAGA,GAAIwF,gBAAgB,CAAC1E,cAArB,CAAqC,CACnCA,cAAc,CAAG0E,gBAAgB,CAAC1E,cAAlC,CACApB,GAAG,CAACuB,GAAJ,CAAU,gBAAU,CAClB,GAAGtB,SADe,CAElBK,QAAQ,CAAEwF,gBAAgB,CAACxF,QAFT,CAAV,CAAV,CAIEN,GAAD,CAAa+F,oBAAb,CAAoC,IAApC,CACD9F,SAAS,CAACK,QAAV,CAAqBwF,gBAAgB,CAACxF,QAAtC,CACD,CAED;AACA;AACA;AACA,GAAIoF,cAAJ,CAAoB,CAClB,KAAMM,CAAAA,aAAa,CAAGF,gBAAgB,CAAC1E,cAAjB,CAClBA,cADkB,CAElBgE,qBAFJ,CAIA,KAAMa,CAAAA,aAAa,CAAG,2CACpBzG,IAAI,CAACmG,OADe,CAEpBd,SAFoB,CAGpBmB,aAHoB,CAAtB,CAMA,GAAIC,aAAa,EAAIA,aAAa,CAACC,MAAd,GAAyBR,cAAc,CAACQ,MAA7D,CAAqE,CACnEL,oBAAoB,CAAI,OAAMI,aAAa,CAACE,IAAd,CAAqB,EAArB,CAA0B,GAAI,MAC1DF,aAAa,CAACC,MACf,IAAGF,aAAa,GAAKC,aAAa,CAACd,aAAhC,CAAgD,EAAhD,CAAqDa,aAAc,EAFvE,CAGD,CACF,CAED,KAAMI,CAAAA,oBAAoB,CAAG,6CAAoB9F,QAApB,CAA7B,CACA,KAAM+F,CAAAA,qBAAqB,CACzB,CAACjF,cAAD,EACAA,cAAc,CAACsB,WAAf,KAAiCyC,aAAa,CAACzC,WAAd,EAFnC,CAGA,KAAM4D,CAAAA,wBAAwB,CAAG,KAAjC,CACA;AACA;AAEA,KAAMC,CAAAA,qBAAqB,CACzB,CAACF,qBAAD,EAA0BD,oBAAoB,GAAK,GADrD,CAGAhF,cAAc,CAAGA,cAAc,EAAI5B,IAAI,CAAC2F,aAAxC,CAEA,GACE,CAACD,iBAAD,EACA,CAAClF,GAAG,CAACsD,OAAJ,CAAYjE,YAAZ,CADD,EAEAG,IAAI,CAAC6F,eAAL,GAAyB,KAFzB,GAGCQ,oBAAoB,EACnBU,qBADD,EAECD,wBALF,CADF,CAOE,CACA;AACA;AACA;AACA,GAAIA,wBAAwB,EAAIlB,qBAAqB,GAAKD,aAA1D,CAAyE,CACvE,KAAMqB,CAAAA,QAAQ,CAAGxB,GAAG,CAACyB,SAAJ,CAAc,YAAd,CAAjB,CAEAzB,GAAG,CAAC0B,SAAJ,CAAc,YAAd,CAA4B,CAC1B,IAAI,MAAOF,CAAAA,QAAP,GAAoB,QAApB,CACA,CAACA,QAAD,CADA,CAEAnE,KAAK,CAACC,OAAN,CAAckE,QAAd,EACAA,QADA,CAEA,EAJJ,CAD0B,CAM1BG,gBAAOC,SAAP,CAAiB,aAAjB,CAAgCzB,aAAhC,CAA+C,CAC7C0B,QAAQ,CAAE,IADmC,CAE7CC,IAAI,CAAE,GAFuC,CAA/C,CAN0B,CAA5B,EAWD,CAED9B,GAAG,CAAC0B,SAAJ,CACE,UADF,CAEE,gBAAU,CACR;AACA,GAAGzG,SAFK,CAGRK,QAAQ,CAAEuF,oBAAoB,CAC1BA,oBAD0B,CAE1BS,wBAAwB,CACxB7G,QAAQ,EAAI,GADY,CAEvB,GAAEA,QAAS,IAAG2B,cAAe,EAP1B,CAAV,CAFF,EAYA4D,GAAG,CAAC+B,UAAJ,CAAiBC,oCAAjB,CACAhC,GAAG,CAACiC,GAAJ,GACA,OACD,CAED7F,cAAc,CACZ0E,gBAAgB,CAAC1E,cAAjB,EACCsE,cAAc,EAAIA,cAAc,CAACP,aADlC,EAEAA,aAHF,CAKA,MAAO,CACLA,aADK,CAEL/D,cAFK,CAGL6D,gBAHK,CAAP,CAKD,CAED,MAAO,CACLF,YADK,CAELhF,cAFK,CAGLuB,cAHK,CAIL1B,iBAJK,CAKLsE,kBALK,CAMLrE,mBANK,CAOLC,mBAPK,CAQLyD,sBARK,CASL/B,yBATK,CAUL8C,2BAVK,CAAP,CAYD", "sourcesContent": ["import { IncomingMessage, ServerResponse } from 'http'\nimport { format as formatUrl, UrlWithParsedQuery, parse as parseUrl } from 'url'\nimport { parse as parseQs, ParsedUrlQuery } from 'querystring'\nimport { Rewrite } from '../../../../lib/load-custom-routes'\nimport { normalizeLocalePath } from '../../../../next-server/lib/i18n/normalize-locale-path'\nimport pathMatch from '../../../../next-server/lib/router/utils/path-match'\nimport { getRouteRegex } from '../../../../next-server/lib/router/utils/route-regex'\nimport { getRouteMatcher } from '../../../../next-server/lib/router/utils/route-matcher'\nimport prepareDestination, {\n  matchHas,\n} from '../../../../next-server/lib/router/utils/prepare-destination'\nimport { __ApiPreviewProps } from '../../../../next-server/server/api-utils'\nimport { BuildManifest } from '../../../../next-server/server/get-page-files'\nimport {\n  GetServerSideProps,\n  GetStaticPaths,\n  GetStaticProps,\n} from '../../../../types'\nimport accept from '@hapi/accept'\nimport { detectLocaleCookie } from '../../../../next-server/lib/i18n/detect-locale-cookie'\nimport { detectDomainLocale } from '../../../../next-server/lib/i18n/detect-domain-locale'\nimport { denormalizePagePath } from '../../../../next-server/server/denormalize-page-path'\nimport cookie from 'next/dist/compiled/cookie'\nimport { TEMPORARY_REDIRECT_STATUS } from '../../../../next-server/lib/constants'\nimport { NextConfig } from '../../../../next-server/server/config'\n\nconst getCustomRouteMatcher = pathMatch(true)\n\nexport const vercelHeader = 'x-vercel-id'\n\nexport type ServerlessHandlerCtx = {\n  page: string\n\n  pageModule: any\n  pageComponent?: any\n  pageConfig?: any\n  pageGetStaticProps?: GetStaticProps\n  pageGetStaticPaths?: GetStaticPaths\n  pageGetServerSideProps?: GetServerSideProps\n\n  appModule?: any\n  errorModule?: any\n  documentModule?: any\n  notFoundModule?: any\n\n  runtimeConfig: any\n  buildManifest?: BuildManifest\n  reactLoadableManifest?: any\n  basePath: string\n  rewrites: Rewrite[]\n  pageIsDynamic: boolean\n  generateEtags: boolean\n  distDir: string\n  buildId: string\n  escapedBuildId: string\n  assetPrefix: string\n  poweredByHeader: boolean\n  canonicalBase: string\n  encodedPreviewProps: __ApiPreviewProps\n  i18n?: NextConfig['i18n']\n}\n\nexport function getUtils({\n  page,\n  i18n,\n  basePath,\n  rewrites,\n  pageIsDynamic,\n}: {\n  page: ServerlessHandlerCtx['page']\n  i18n?: ServerlessHandlerCtx['i18n']\n  basePath: ServerlessHandlerCtx['basePath']\n  rewrites: ServerlessHandlerCtx['rewrites']\n  pageIsDynamic: ServerlessHandlerCtx['pageIsDynamic']\n}) {\n  let defaultRouteRegex: ReturnType<typeof getRouteRegex> | undefined\n  let dynamicRouteMatcher: ReturnType<typeof getRouteMatcher> | undefined\n  let defaultRouteMatches: ParsedUrlQuery | undefined\n\n  if (pageIsDynamic) {\n    defaultRouteRegex = getRouteRegex(page)\n    dynamicRouteMatcher = getRouteMatcher(defaultRouteRegex)\n    defaultRouteMatches = dynamicRouteMatcher(page) as ParsedUrlQuery\n  }\n\n  function handleRewrites(req: IncomingMessage, parsedUrl: UrlWithParsedQuery) {\n    for (const rewrite of rewrites) {\n      const matcher = getCustomRouteMatcher(rewrite.source)\n      let params = matcher(parsedUrl.pathname)\n\n      if (rewrite.has && params) {\n        const hasParams = matchHas(req, rewrite.has, parsedUrl.query)\n\n        if (hasParams) {\n          Object.assign(params, hasParams)\n        } else {\n          params = false\n        }\n      }\n\n      if (params) {\n        const { parsedDestination } = prepareDestination(\n          rewrite.destination,\n          params,\n          parsedUrl.query,\n          true\n        )\n\n        Object.assign(parsedUrl.query, parsedDestination.query)\n        delete (parsedDestination as any).query\n\n        Object.assign(parsedUrl, parsedDestination)\n\n        let fsPathname = parsedUrl.pathname\n\n        if (basePath) {\n          fsPathname =\n            fsPathname!.replace(new RegExp(`^${basePath}`), '') || '/'\n        }\n\n        if (i18n) {\n          const destLocalePathResult = normalizeLocalePath(\n            fsPathname!,\n            i18n.locales\n          )\n          fsPathname = destLocalePathResult.pathname\n          parsedUrl.query.nextInternalLocale =\n            destLocalePathResult.detectedLocale || params.nextInternalLocale\n        }\n\n        if (fsPathname === page) {\n          break\n        }\n\n        if (pageIsDynamic && dynamicRouteMatcher) {\n          const dynamicParams = dynamicRouteMatcher(fsPathname)\n          if (dynamicParams) {\n            parsedUrl.query = {\n              ...parsedUrl.query,\n              ...dynamicParams,\n            }\n            break\n          }\n        }\n      }\n    }\n\n    return parsedUrl\n  }\n\n  function handleBasePath(req: IncomingMessage, parsedUrl: UrlWithParsedQuery) {\n    // always strip the basePath if configured since it is required\n    req.url = req.url!.replace(new RegExp(`^${basePath}`), '') || '/'\n    parsedUrl.pathname =\n      parsedUrl.pathname!.replace(new RegExp(`^${basePath}`), '') || '/'\n  }\n\n  function getParamsFromRouteMatches(\n    req: IncomingMessage,\n    renderOpts?: any,\n    detectedLocale?: string\n  ) {\n    return getRouteMatcher(\n      (function () {\n        const { groups, routeKeys } = defaultRouteRegex!\n\n        return {\n          re: {\n            // Simulate a RegExp match from the \\`req.url\\` input\n            exec: (str: string) => {\n              const obj = parseQs(str)\n\n              // favor named matches if available\n              const routeKeyNames = Object.keys(routeKeys || {})\n\n              const filterLocaleItem = (val: string | string[]) => {\n                if (i18n) {\n                  // locale items can be included in route-matches\n                  // for fallback SSG pages so ensure they are\n                  // filtered\n                  const isCatchAll = Array.isArray(val)\n                  const _val = isCatchAll ? val[0] : val\n\n                  if (\n                    typeof _val === 'string' &&\n                    i18n.locales.some((item) => {\n                      if (item.toLowerCase() === _val.toLowerCase()) {\n                        detectedLocale = item\n                        renderOpts.locale = detectedLocale\n                        return true\n                      }\n                      return false\n                    })\n                  ) {\n                    // remove the locale item from the match\n                    if (isCatchAll) {\n                      ;(val as string[]).splice(0, 1)\n                    }\n\n                    // the value is only a locale item and\n                    // shouldn't be added\n                    return isCatchAll ? val.length === 0 : true\n                  }\n                }\n                return false\n              }\n\n              if (routeKeyNames.every((name) => obj[name])) {\n                return routeKeyNames.reduce((prev, keyName) => {\n                  const paramName = routeKeys?.[keyName]\n\n                  if (paramName && !filterLocaleItem(obj[keyName])) {\n                    prev[groups[paramName].pos] = obj[keyName]\n                  }\n                  return prev\n                }, {} as any)\n              }\n\n              return Object.keys(obj).reduce((prev, key) => {\n                if (!filterLocaleItem(obj[key])) {\n                  return Object.assign(prev, {\n                    [key]: obj[key],\n                  })\n                }\n                return prev\n              }, {})\n            },\n          },\n          groups,\n        }\n      })() as any\n    )(req.headers['x-now-route-matches'] as string) as ParsedUrlQuery\n  }\n\n  function interpolateDynamicPath(pathname: string, params: ParsedUrlQuery) {\n    if (!defaultRouteRegex) return pathname\n\n    for (const param of Object.keys(defaultRouteRegex.groups)) {\n      const { optional, repeat } = defaultRouteRegex.groups[param]\n      let builtParam = `[${repeat ? '...' : ''}${param}]`\n\n      if (optional) {\n        builtParam = `[${builtParam}]`\n      }\n\n      const paramIdx = pathname!.indexOf(builtParam)\n\n      if (paramIdx > -1) {\n        let paramValue: string\n\n        if (Array.isArray(params[param])) {\n          paramValue = (params[param] as string[]).join('/')\n        } else {\n          paramValue = params[param] as string\n        }\n\n        pathname =\n          pathname.substr(0, paramIdx) +\n          encodeURI(paramValue || '') +\n          pathname.substr(paramIdx + builtParam.length)\n      }\n    }\n\n    return pathname\n  }\n\n  function normalizeVercelUrl(req: IncomingMessage, trustQuery: boolean) {\n    // make sure to normalize req.url on Vercel to strip dynamic params\n    // from the query which are added during routing\n    if (pageIsDynamic && trustQuery && defaultRouteRegex) {\n      const _parsedUrl = parseUrl(req.url!, true)\n      delete (_parsedUrl as any).search\n\n      for (const param of Object.keys(defaultRouteRegex.groups)) {\n        delete _parsedUrl.query[param]\n      }\n      req.url = formatUrl(_parsedUrl)\n    }\n  }\n\n  function normalizeDynamicRouteParams(params: ParsedUrlQuery) {\n    let hasValidParams = true\n    if (!defaultRouteRegex) return { params, hasValidParams: false }\n\n    params = Object.keys(defaultRouteRegex.groups).reduce((prev, key) => {\n      let value: string | string[] | undefined = params[key]\n\n      // if the value matches the default value we can't rely\n      // on the parsed params, this is used to signal if we need\n      // to parse x-now-route-matches or not\n      const defaultValue = defaultRouteMatches![key]\n\n      const isDefaultValue = Array.isArray(defaultValue)\n        ? defaultValue.some((defaultVal) => {\n            return Array.isArray(value)\n              ? value.some((val) => val.includes(defaultVal))\n              : value?.includes(defaultVal)\n          })\n        : value?.includes(defaultValue as string)\n\n      if (isDefaultValue || typeof value === 'undefined') {\n        hasValidParams = false\n      }\n\n      // non-provided optional values should be undefined so normalize\n      // them to undefined\n      if (\n        defaultRouteRegex!.groups[key].optional &&\n        (!value ||\n          (Array.isArray(value) &&\n            value.length === 1 &&\n            // fallback optional catch-all SSG pages have\n            // [[...paramName]] for the root path on Vercel\n            (value[0] === 'index' || value[0] === `[[...${key}]]`)))\n      ) {\n        value = undefined\n        delete params[key]\n      }\n\n      // query values from the proxy aren't already split into arrays\n      // so make sure to normalize catch-all values\n      if (\n        value &&\n        typeof value === 'string' &&\n        defaultRouteRegex!.groups[key].repeat\n      ) {\n        value = value.split('/')\n      }\n\n      if (value) {\n        prev[key] = value\n      }\n      return prev\n    }, {} as ParsedUrlQuery)\n\n    return {\n      params,\n      hasValidParams,\n    }\n  }\n\n  function handleLocale(\n    req: IncomingMessage,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery,\n    routeNoAssetPath: string,\n    shouldNotRedirect: boolean\n  ) {\n    if (!i18n) return\n    const pathname = parsedUrl.pathname || '/'\n\n    let defaultLocale = i18n.defaultLocale\n    let detectedLocale = detectLocaleCookie(req, i18n.locales)\n    let acceptPreferredLocale =\n      i18n.localeDetection !== false\n        ? accept.language(req.headers['accept-language'], i18n.locales)\n        : detectedLocale\n\n    const { host } = req.headers || {}\n    // remove port from host and remove port if present\n    const hostname = host && host.split(':')[0].toLowerCase()\n\n    const detectedDomain = detectDomainLocale(i18n.domains, hostname)\n    if (detectedDomain) {\n      defaultLocale = detectedDomain.defaultLocale\n      detectedLocale = defaultLocale\n      ;(req as any).__nextIsLocaleDomain = true\n    }\n\n    // if not domain specific locale use accept-language preferred\n    detectedLocale = detectedLocale || acceptPreferredLocale\n\n    let localeDomainRedirect\n    const localePathResult = normalizeLocalePath(pathname, i18n.locales)\n\n    routeNoAssetPath = normalizeLocalePath(routeNoAssetPath, i18n.locales)\n      .pathname\n\n    if (localePathResult.detectedLocale) {\n      detectedLocale = localePathResult.detectedLocale\n      req.url = formatUrl({\n        ...parsedUrl,\n        pathname: localePathResult.pathname,\n      })\n      ;(req as any).__nextStrippedLocale = true\n      parsedUrl.pathname = localePathResult.pathname\n    }\n\n    // If a detected locale is a domain specific locale and we aren't already\n    // on that domain and path prefix redirect to it to prevent duplicate\n    // content from multiple domains\n    if (detectedDomain) {\n      const localeToCheck = localePathResult.detectedLocale\n        ? detectedLocale\n        : acceptPreferredLocale\n\n      const matchedDomain = detectDomainLocale(\n        i18n.domains,\n        undefined,\n        localeToCheck\n      )\n\n      if (matchedDomain && matchedDomain.domain !== detectedDomain.domain) {\n        localeDomainRedirect = `http${matchedDomain.http ? '' : 's'}://${\n          matchedDomain.domain\n        }/${localeToCheck === matchedDomain.defaultLocale ? '' : localeToCheck}`\n      }\n    }\n\n    const denormalizedPagePath = denormalizePagePath(pathname)\n    const detectedDefaultLocale =\n      !detectedLocale ||\n      detectedLocale.toLowerCase() === defaultLocale.toLowerCase()\n    const shouldStripDefaultLocale = false\n    // detectedDefaultLocale &&\n    // denormalizedPagePath.toLowerCase() === \\`/\\${i18n.defaultLocale.toLowerCase()}\\`\n\n    const shouldAddLocalePrefix =\n      !detectedDefaultLocale && denormalizedPagePath === '/'\n\n    detectedLocale = detectedLocale || i18n.defaultLocale\n\n    if (\n      !shouldNotRedirect &&\n      !req.headers[vercelHeader] &&\n      i18n.localeDetection !== false &&\n      (localeDomainRedirect ||\n        shouldAddLocalePrefix ||\n        shouldStripDefaultLocale)\n    ) {\n      // set the NEXT_LOCALE cookie when a user visits the default locale\n      // with the locale prefix so that they aren't redirected back to\n      // their accept-language preferred locale\n      if (shouldStripDefaultLocale && acceptPreferredLocale !== defaultLocale) {\n        const previous = res.getHeader('set-cookie')\n\n        res.setHeader('set-cookie', [\n          ...(typeof previous === 'string'\n            ? [previous]\n            : Array.isArray(previous)\n            ? previous\n            : []),\n          cookie.serialize('NEXT_LOCALE', defaultLocale, {\n            httpOnly: true,\n            path: '/',\n          }),\n        ])\n      }\n\n      res.setHeader(\n        'Location',\n        formatUrl({\n          // make sure to include any query values when redirecting\n          ...parsedUrl,\n          pathname: localeDomainRedirect\n            ? localeDomainRedirect\n            : shouldStripDefaultLocale\n            ? basePath || '/'\n            : `${basePath}/${detectedLocale}`,\n        })\n      )\n      res.statusCode = TEMPORARY_REDIRECT_STATUS\n      res.end()\n      return\n    }\n\n    detectedLocale =\n      localePathResult.detectedLocale ||\n      (detectedDomain && detectedDomain.defaultLocale) ||\n      defaultLocale\n\n    return {\n      defaultLocale,\n      detectedLocale,\n      routeNoAssetPath,\n    }\n  }\n\n  return {\n    handleLocale,\n    handleRewrites,\n    handleBasePath,\n    defaultRouteRegex,\n    normalizeVercelUrl,\n    dynamicRouteMatcher,\n    defaultRouteMatches,\n    interpolateDynamicPath,\n    getParamsFromRouteMatches,\n    normalizeDynamicRouteParams,\n  }\n}\n"]}