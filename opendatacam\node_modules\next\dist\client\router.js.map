{"version": 3, "sources": ["../../client/router.ts"], "names": ["singletonRouter", "router", "readyCallbacks", "ready", "cb", "window", "push", "url<PERSON><PERSON><PERSON><PERSON>ields", "routerEvents", "core<PERSON><PERSON><PERSON><PERSON><PERSON>s", "Object", "defineProperty", "get", "Router", "events", "for<PERSON>ach", "field", "getRouter", "args", "event", "on", "eventField", "char<PERSON>t", "toUpperCase", "substring", "_singletonRouter", "err", "console", "error", "message", "stack", "Error", "useRouter", "React", "useContext", "RouterContext", "createRouter", "makePublicRouterInstance", "_router", "instance", "property", "assign", "Array", "isArray"], "mappings": "kWACA,oDACA,kF,uEACA,gEAyHA,iE,uCA5HA,mBAmBA,KAAMA,CAAAA,eAAoC,CAAG,CAC3CC,MAAM,CAAE,IADmC,CAC7B;AACdC,cAAc,CAAE,EAF2B,CAG3CC,KAAK,CAACC,EAAD,CAAiB,CACpB,GAAI,KAAKH,MAAT,CAAiB,MAAOG,CAAAA,EAAE,EAAT,CACjB,GAAI,MAAOC,CAAAA,MAAP,GAAkB,WAAtB,CAAmC,CACjC,KAAKH,cAAL,CAAoBI,IAApB,CAAyBF,EAAzB,EACD,CACF,CAR0C,CAA7C,CAWA;AACA,KAAMG,CAAAA,iBAAiB,CAAG,CACxB,UADwB,CAExB,OAFwB,CAGxB,OAHwB,CAIxB,QAJwB,CAKxB,YALwB,CAMxB,YANwB,CAOxB,UAPwB,CAQxB,QARwB,CASxB,SATwB,CAUxB,eAVwB,CAWxB,SAXwB,CAYxB,WAZwB,CAaxB,gBAbwB,CAA1B,CAeA,KAAMC,CAAAA,YAAY,CAAG,CACnB,kBADmB,CAEnB,qBAFmB,CAGnB,qBAHmB,CAInB,kBAJmB,CAKnB,iBALmB,CAMnB,oBANmB,CAArB,CAQA,KAAMC,CAAAA,gBAAgB,CAAG,CACvB,MADuB,CAEvB,SAFuB,CAGvB,QAHuB,CAIvB,MAJuB,CAKvB,UALuB,CAMvB,gBANuB,CAAzB,CASA;AACAC,MAAM,CAACC,cAAP,CAAsBX,eAAtB,CAAuC,QAAvC,CAAiD,CAC/CY,GAAG,EAAG,CACJ,MAAOC,kBAAOC,MAAd,CACD,CAH8C,CAAjD,EAMAP,iBAAiB,CAACQ,OAAlB,CAA2BC,KAAD,EAAmB,CAC3C;AACA;AACA;AACA;AACAN,MAAM,CAACC,cAAP,CAAsBX,eAAtB,CAAuCgB,KAAvC,CAA8C,CAC5CJ,GAAG,EAAG,CACJ,KAAMX,CAAAA,MAAM,CAAGgB,SAAS,EAAxB,CACA,MAAOhB,CAAAA,MAAM,CAACe,KAAD,CAAb,CACD,CAJ2C,CAA9C,EAMD,CAXD,EAaAP,gBAAgB,CAACM,OAAjB,CAA0BC,KAAD,EAAmB,CAC1C;AACA,CAAEhB,eAAD,CAAyBgB,KAAzB,EAAkC,CAAC,GAAGE,IAAJ,GAAoB,CACrD,KAAMjB,CAAAA,MAAM,CAAGgB,SAAS,EAAxB,CACA,MAAOhB,CAAAA,MAAM,CAACe,KAAD,CAAN,CAAc,GAAGE,IAAjB,CAAP,CACD,CAHA,CAIF,CAND,EAQAV,YAAY,CAACO,OAAb,CAAsBI,KAAD,EAAmB,CACtCnB,eAAe,CAACG,KAAhB,CAAsB,IAAM,CAC1BU,iBAAOC,MAAP,CAAcM,EAAd,CAAiBD,KAAjB,CAAwB,CAAC,GAAGD,IAAJ,GAAa,CACnC,KAAMG,CAAAA,UAAU,CAAI,KAAIF,KAAK,CAACG,MAAN,CAAa,CAAb,EAAgBC,WAAhB,EAA8B,GAAEJ,KAAK,CAACK,SAAN,CACtD,CADsD,CAEtD,EAFF,CAGA,KAAMC,CAAAA,gBAAgB,CAAGzB,eAAzB,CACA,GAAIyB,gBAAgB,CAACJ,UAAD,CAApB,CAAkC,CAChC,GAAI,CACFI,gBAAgB,CAACJ,UAAD,CAAhB,CAA6B,GAAGH,IAAhC,EACD,CAAC,MAAOQ,GAAP,CAAY,CACZC,OAAO,CAACC,KAAR,CAAe,wCAAuCP,UAAW,EAAjE,EACAM,OAAO,CAACC,KAAR,CAAe,GAAEF,GAAG,CAACG,OAAQ,KAAIH,GAAG,CAACI,KAAM,EAA3C,EACD,CACF,CACF,CAbD,EAcD,CAfD,EAgBD,CAjBD,EAmBA,QAASb,CAAAA,SAAT,EAA6B,CAC3B,GAAI,CAACjB,eAAe,CAACC,MAArB,CAA6B,CAC3B,KAAM4B,CAAAA,OAAO,CACX,8BACA,yEAFF,CAGA,KAAM,IAAIE,CAAAA,KAAJ,CAAUF,OAAV,CAAN,CACD,CACD,MAAO7B,CAAAA,eAAe,CAACC,MAAvB,CACD,CAED;aACeD,e,CAEf;yBAGO,QAASgC,CAAAA,SAAT,EAAiC,CACtC,MAAOC,gBAAMC,UAAN,CAAiBC,4BAAjB,CAAP,CACD,CAED;AACA;AACA;AAEA;AACA;AACA;AACO,KAAMC,CAAAA,YAAY,CAAG,CAAC,GAAGlB,IAAJ,GAAiC,CAC3DlB,eAAe,CAACC,MAAhB,CAAyB,GAAIY,iBAAJ,CAAW,GAAGK,IAAd,CAAzB,CACAlB,eAAe,CAACE,cAAhB,CAA+Ba,OAA/B,CAAwCX,EAAD,EAAQA,EAAE,EAAjD,EACAJ,eAAe,CAACE,cAAhB,CAAiC,EAAjC,CAEA,MAAOF,CAAAA,eAAe,CAACC,MAAvB,CACD,CANM,CAQP;kCACO,QAASoC,CAAAA,wBAAT,CAAkCpC,MAAlC,CAA8D,CACnE,KAAMqC,CAAAA,OAAO,CAAGrC,MAAhB,CACA,KAAMsC,CAAAA,QAAQ,CAAG,EAAjB,CAEA,IAAK,KAAMC,CAAAA,QAAX,GAAuBjC,CAAAA,iBAAvB,CAA0C,CACxC,GAAI,MAAO+B,CAAAA,OAAO,CAACE,QAAD,CAAd,GAA6B,QAAjC,CAA2C,CACzCD,QAAQ,CAACC,QAAD,CAAR,CAAqB9B,MAAM,CAAC+B,MAAP,CACnBC,KAAK,CAACC,OAAN,CAAcL,OAAO,CAACE,QAAD,CAArB,EAAmC,EAAnC,CAAwC,EADrB,CAEnBF,OAAO,CAACE,QAAD,CAFY,CAArB,CAGE;AACF,SACD,CAEDD,QAAQ,CAACC,QAAD,CAAR,CAAqBF,OAAO,CAACE,QAAD,CAA5B,CACD,CAED;AACAD,QAAQ,CAACzB,MAAT,CAAkBD,iBAAOC,MAAzB,CAEAL,gBAAgB,CAACM,OAAjB,CAA0BC,KAAD,EAAW,CAClCuB,QAAQ,CAACvB,KAAD,CAAR,CAAkB,CAAC,GAAGE,IAAJ,GAAoB,CACpC,MAAOoB,CAAAA,OAAO,CAACtB,KAAD,CAAP,CAAe,GAAGE,IAAlB,CAAP,CACD,CAFD,CAGD,CAJD,EAMA,MAAOqB,CAAAA,QAAP,CACD", "sourcesContent": ["/* global window */\nimport React from 'react'\nimport Router, { NextRouter } from '../next-server/lib/router/router'\nimport { RouterContext } from '../next-server/lib/router-context'\n\ntype ClassArguments<T> = T extends new (...args: infer U) => any ? U : any\n\ntype RouterArgs = ClassArguments<typeof Router>\n\ntype SingletonRouterBase = {\n  router: Router | null\n  readyCallbacks: Array<() => any>\n  ready(cb: () => any): void\n}\n\nexport { Router, NextRouter }\n\nexport type SingletonRouter = SingletonRouterBase & NextRouter\n\nconst singletonRouter: SingletonRouterBase = {\n  router: null, // holds the actual router instance\n  readyCallbacks: [],\n  ready(cb: () => void) {\n    if (this.router) return cb()\n    if (typeof window !== 'undefined') {\n      this.readyCallbacks.push(cb)\n    }\n  },\n}\n\n// Create public properties and methods of the router in the singletonRouter\nconst urlPropertyFields = [\n  'pathname',\n  'route',\n  'query',\n  'asPath',\n  'components',\n  'isFallback',\n  'basePath',\n  'locale',\n  'locales',\n  'defaultLocale',\n  'isReady',\n  'isPreview',\n  'isLocaleDomain',\n]\nconst routerEvents = [\n  'routeChangeStart',\n  'beforeHistoryChange',\n  'routeChangeComplete',\n  'routeChangeError',\n  'hashChangeStart',\n  'hashChangeComplete',\n]\nconst coreMethodFields = [\n  'push',\n  'replace',\n  'reload',\n  'back',\n  'prefetch',\n  'beforePopState',\n]\n\n// Events is a static property on the router, the router doesn't have to be initialized to use it\nObject.defineProperty(singletonRouter, 'events', {\n  get() {\n    return Router.events\n  },\n})\n\nurlPropertyFields.forEach((field: string) => {\n  // Here we need to use Object.defineProperty because, we need to return\n  // the property assigned to the actual router\n  // The value might get changed as we change routes and this is the\n  // proper way to access it\n  Object.defineProperty(singletonRouter, field, {\n    get() {\n      const router = getRouter() as any\n      return router[field] as string\n    },\n  })\n})\n\ncoreMethodFields.forEach((field: string) => {\n  // We don't really know the types here, so we add them later instead\n  ;(singletonRouter as any)[field] = (...args: any[]) => {\n    const router = getRouter() as any\n    return router[field](...args)\n  }\n})\n\nrouterEvents.forEach((event: string) => {\n  singletonRouter.ready(() => {\n    Router.events.on(event, (...args) => {\n      const eventField = `on${event.charAt(0).toUpperCase()}${event.substring(\n        1\n      )}`\n      const _singletonRouter = singletonRouter as any\n      if (_singletonRouter[eventField]) {\n        try {\n          _singletonRouter[eventField](...args)\n        } catch (err) {\n          console.error(`Error when running the Router event: ${eventField}`)\n          console.error(`${err.message}\\n${err.stack}`)\n        }\n      }\n    })\n  })\n})\n\nfunction getRouter(): Router {\n  if (!singletonRouter.router) {\n    const message =\n      'No router instance found.\\n' +\n      'You should only use \"next/router\" inside the client side of your app.\\n'\n    throw new Error(message)\n  }\n  return singletonRouter.router\n}\n\n// Export the singletonRouter and this is the public API.\nexport default singletonRouter as SingletonRouter\n\n// Reexport the withRoute HOC\nexport { default as withRouter } from './with-router'\n\nexport function useRouter(): NextRouter {\n  return React.useContext(RouterContext)\n}\n\n// INTERNAL APIS\n// -------------\n// (do not use following exports inside the app)\n\n// Create a router and assign it as the singleton instance.\n// This is used in client side when we are initilizing the app.\n// This should **not** use inside the server.\nexport const createRouter = (...args: RouterArgs): Router => {\n  singletonRouter.router = new Router(...args)\n  singletonRouter.readyCallbacks.forEach((cb) => cb())\n  singletonRouter.readyCallbacks = []\n\n  return singletonRouter.router\n}\n\n// This function is used to create the `withRouter` router instance\nexport function makePublicRouterInstance(router: Router): NextRouter {\n  const _router = router as any\n  const instance = {} as any\n\n  for (const property of urlPropertyFields) {\n    if (typeof _router[property] === 'object') {\n      instance[property] = Object.assign(\n        Array.isArray(_router[property]) ? [] : {},\n        _router[property]\n      ) // makes sure query is not stateful\n      continue\n    }\n\n    instance[property] = _router[property]\n  }\n\n  // Events is a static property on the router, the router doesn't have to be initialized to use it\n  instance.events = Router.events\n\n  coreMethodFields.forEach((field) => {\n    instance[field] = (...args: any[]) => {\n      return _router[field](...args)\n    }\n  })\n\n  return instance\n}\n"]}