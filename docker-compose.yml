services:
  opendatacam:
    restart: always
    image: opendatacam/opendatacam:v3.0.2-desktop
    volumes:
      - './config.json:/var/local/opendatacam/config.json'
    ports:
      - "8080:8080"
      - "8070:8070"
      - "8090:8090"
    depends_on:
      - mongo
  mongo:
    image: mongo:latest
    restart: always
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data_container:/data/db
volumes:
  mongodb_data_container: