{"version": 3, "sources": ["../../../../next-server/lib/i18n/normalize-locale-path.ts"], "names": ["normalizeLocalePath", "pathname", "locales", "detectedLocale", "pathnameParts", "split", "some", "locale", "toLowerCase", "splice", "join"], "mappings": "qFAAO,QAASA,CAAAA,mBAAT,CACLC,QADK,CAELC,OAFK,CAML,CACA,GAAIC,CAAAA,cAAJ,CACA;AACA,KAAMC,CAAAA,aAAa,CAAGH,QAAQ,CAACI,KAAT,CAAe,GAAf,CAAtB,CAEC,CAACH,OAAO,EAAI,EAAZ,EAAgBI,IAAhB,CAAsBC,MAAD,EAAY,CAChC,GAAIH,aAAa,CAAC,CAAD,CAAb,CAAiBI,WAAjB,KAAmCD,MAAM,CAACC,WAAP,EAAvC,CAA6D,CAC3DL,cAAc,CAAGI,MAAjB,CACAH,aAAa,CAACK,MAAd,CAAqB,CAArB,CAAwB,CAAxB,EACAR,QAAQ,CAAGG,aAAa,CAACM,IAAd,CAAmB,GAAnB,GAA2B,GAAtC,CACA,MAAO,KAAP,CACD,CACD,MAAO,MAAP,CACD,CARA,EAUD,MAAO,CACLT,QADK,CAELE,cAFK,CAAP,CAID", "sourcesContent": ["export function normalizeLocalePath(\n  pathname: string,\n  locales?: string[]\n): {\n  detectedLocale?: string\n  pathname: string\n} {\n  let detectedLocale: string | undefined\n  // first item will be empty string from splitting at first char\n  const pathnameParts = pathname.split('/')\n\n  ;(locales || []).some((locale) => {\n    if (pathnameParts[1].toLowerCase() === locale.toLowerCase()) {\n      detectedLocale = locale\n      pathnameParts.splice(1, 1)\n      pathname = pathnameParts.join('/') || '/'\n      return true\n    }\n    return false\n  })\n\n  return {\n    pathname,\n    detectedLocale,\n  }\n}\n"]}