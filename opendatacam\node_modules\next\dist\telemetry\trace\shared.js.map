{"version": 3, "sources": ["../../../telemetry/trace/shared.ts"], "names": ["TARGET", "traceGlobals", "Map", "setGlobal", "key", "val", "set", "debugLog", "process", "env", "TRACE_DEBUG", "console", "info", "noop"], "mappings": "mHAAA;AACA,8B,GACYA,CAAAA,M,iCAAAA,M,EAAAA,M,sBAAAA,M,oBAAAA,M,6BAAAA,M,kBAAAA,M,MAQL,KAAMC,CAAAA,YAA2B,CAAG,GAAIC,CAAAA,GAAJ,EAApC,C,kCACA,KAAMC,CAAAA,SAAS,CAAG,CAACC,GAAD,CAAWC,GAAX,GAAwB,CAC/CJ,YAAY,CAACK,GAAb,CAAiBF,GAAjB,CAAsBC,GAAtB,EACD,CAFM,C,4BAIA,KAAME,CAAAA,QAAQ,CAAG,CAAC,CAACC,OAAO,CAACC,GAAR,CAAYC,WAAd,CACpBC,OAAO,CAACC,IADY,CAEpB,QAASC,CAAAA,IAAT,EAAgB,CAAE,CAFf,C", "sourcesContent": ["// eslint typescript has a bug with TS enums\n/* eslint-disable no-shadow */\nexport enum TARGET {\n  CONSOLE = 'CONSOLE',\n  ZIPKIN = 'ZIPKIN',\n  TELEMETRY = 'TELEMETRY',\n}\n\nexport type SpanId = string\n\nexport const traceGlobals: Map<any, any> = new Map()\nexport const setGlobal = (key: any, val: any) => {\n  traceGlobals.set(key, val)\n}\n\nexport const debugLog = !!process.env.TRACE_DEBUG\n  ? console.info\n  : function noop() {}\n"]}