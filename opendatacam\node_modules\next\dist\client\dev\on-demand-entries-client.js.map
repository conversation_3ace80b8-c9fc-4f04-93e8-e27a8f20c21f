{"version": 3, "sources": ["../../../client/dev/on-demand-entries-client.js"], "names": ["assetPrefix", "Router", "ready", "events", "on", "setupPing", "bind", "pathname", "currentPage", "process", "env", "__NEXT_TEST_MODE", "document", "addEventListener", "_event", "state", "visibilityState", "window"], "mappings": "+IAAA,2DACA,+D,aAEe,MAAO,CAAEA,WAAF,CAAP,GAA2B,CACxCC,gBAAOC,KAAP,CAAa,IAAM,CACjBD,gBAAOE,MAAP,CAAcC,EAAd,CACE,qBADF,CAEEC,gCAAUC,IAAV,QAAqBN,WAArB,CAAkC,IAAMC,gBAAOM,QAA/C,CAFF,EAID,CALD,EAOA,oCAAUP,WAAV,CAAuB,IAAMC,gBAAOM,QAApC,CAA8CC,iCAA9C,EAEA;AACA,GAAI,CAACC,OAAO,CAACC,GAAR,CAAYC,gBAAjB,CAAmC,CACjCC,QAAQ,CAACC,gBAAT,CAA0B,kBAA1B,CAA+CC,MAAD,EAAY,CACxD,KAAMC,CAAAA,KAAK,CAAGH,QAAQ,CAACI,eAAvB,CACA,GAAID,KAAK,GAAK,SAAd,CAAyB,CACvB,oCAAUf,WAAV,CAAuB,IAAMC,gBAAOM,QAApC,CAA8C,IAA9C,EACD,CAFD,IAEO,CACL,sCACD,CACF,CAPD,EASAU,MAAM,CAACJ,gBAAP,CAAwB,cAAxB,CAAwC,IAAM,CAC5C,sCACD,CAFD,EAGD,CACF,C", "sourcesContent": ["import Router from 'next/router'\nimport { setupPing, currentPage, closePing } from './on-demand-entries-utils'\n\nexport default async ({ assetPrefix }) => {\n  Router.ready(() => {\n    Router.events.on(\n      'routeChangeComplete',\n      setupPing.bind(this, assetPrefix, () => Router.pathname)\n    )\n  })\n\n  setupPing(assetPrefix, () => Router.pathname, currentPage)\n\n  // prevent HMR connection from being closed when running tests\n  if (!process.env.__NEXT_TEST_MODE) {\n    document.addEventListener('visibilitychange', (_event) => {\n      const state = document.visibilityState\n      if (state === 'visible') {\n        setupPing(assetPrefix, () => Router.pathname, true)\n      } else {\n        closePing()\n      }\n    })\n\n    window.addEventListener('beforeunload', () => {\n      closePing()\n    })\n  }\n}\n"]}