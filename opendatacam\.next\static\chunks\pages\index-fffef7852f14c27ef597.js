(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[405],{6110:function(t){function e(t,n){if(!(this instanceof e))return new e(t,n);this._ctx=t,this._canvasResolution=n,this._width=n.w,this._height=n.h,this._max=1,this._data=[]}t.exports=e,e.prototype={defaultRadius:25,defaultGradient:{.4:"blue",.6:"cyan",.7:"lime",.8:"yellow",1:"red"},data:function(t){return this._data=t,this},max:function(t){return this._max=t,this},add:function(t){return this._data.push(t),this},clear:function(){return this._data=[],this},radius:function(t,e){e=void 0===e?15:e;var n=this._circle=this._createCanvas(),r=n.getContext("2d"),i=this._r=t+e;return n.width=n.height=2*i,r.shadowOffsetX=r.shadowOffsetY=2*i,r.shadowBlur=e,r.shadowColor="black",r.beginPath(),r.arc(-i,-i,t,0,2*Math.PI,!0),r.closePath(),r.fill(),this},resize:function(){this._width=this._canvasResolution.w,this._height=this._canvasResolution.h},gradient:function(t){var e=this._createCanvas(),n=e.getContext("2d"),r=n.createLinearGradient(0,0,0,256);for(var i in e.width=1,e.height=256,t)r.addColorStop(+i,t[i]);return n.fillStyle=r,n.fillRect(0,0,1,256),this._grad=n.getImageData(0,0,1,256).data,this},draw:function(t){this._circle||this.radius(this.defaultRadius),this._grad||this.gradient(this.defaultGradient);var e=this._ctx;e.clearRect(0,0,this._width,this._height);for(var n,r=0,i=this._data.length;r<i;r++)n=this._data[r],e.globalAlpha=Math.min(Math.max(n[2]/this._max,void 0===t?.05:t),1),e.drawImage(this._circle,n[0]-this._r,n[1]-this._r);var o=e.getImageData(0,0,this._width,this._height);return this._colorize(o.data,this._grad),e.putImageData(o,0,0),this},_colorize:function(t,e){for(var n,r=0,i=t.length;r<i;r+=4)(n=4*t[r+3])&&(t[r]=e[n],t[r+1]=e[n+1],t[r+2]=e[n+2])},_createCanvas:function(){return"undefined"!==typeof document?document.createElement("canvas"):new this._canvas.constructor}}},5077:function(t,e,n){"use strict";n.r(e),n.d(e,{default:function(){return We}});var r=n(7757),i=n.n(r),o=n(2137),a=n(6610),s=n(5991),c=n(5255),l=n(6089),u=n(7608),p=n(7294),d=n(9008),f=p.createElement;function h(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var m=function(t){(0,c.Z)(n,t);var e=h(n);function n(){return(0,a.Z)(this,n),e.apply(this,arguments)}return(0,s.Z)(n,[{key:"render",value:function(){return f("div",null,f(d.default,null,f("title",null,"OpenDataCam"),f("meta",{charSet:"utf-8"}),f("meta",{name:"viewport",content:"width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1,user-scalable=0,initial-scale=1"}),f("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/apple-touch-icon.png"}),f("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/favicon-32x32.png"}),f("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/favicon-16x16.png"}),f("link",{rel:"manifest",href:"/site.webmanifest"}),f("script",{type:"text/javascript",src:"/static/js/fabric-3.6.1.min.js"})),this.props.children)}}]),n}(p.Component),g=n(5988),v=n(1512),x=n(9669),y=n.n(x),b=p.createElement;function w(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var k=function(t){(0,c.Z)(n,t);var e=w(n);function n(){return(0,a.Z)(this,n),e.apply(this,arguments)}return(0,s.Z)(n,[{key:"render",value:function(){return b("div",{className:"jsx-808101764 turnDevice"},b("div",{className:"jsx-808101764 icon"},"\ud83d\udcf1"),b("h1",{className:"jsx-808101764 text-white text-2xl"},"Please use your device in Landscape"),b(g.default,{id:"808101764"},[".turnDevice.jsx-808101764{position:fixed;width:100%;height:100%;background-color:black;padding:2rem;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;text-align:center;z-index:10000000000000;}",".turnDevice.jsx-808101764 h1.jsx-808101764{margin-top:1rem;}",".icon.jsx-808101764{font-size:5rem;-webkit-animation-name:spin-jsx-808101764;animation-name:spin-jsx-808101764;-webkit-animation-duration:2s;animation-duration:2s;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;-webkit-animation-timing-function:linear;animation-timing-function:linear;}","@-webkit-keyframes spin-jsx-808101764{0%{-webkit-transform:rotate(0deg);-ms-transform:rotate(0deg);transform:rotate(0deg);}30%{-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg);}100%{-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg);}}","@keyframes spin-jsx-808101764{0%{-webkit-transform:rotate(0deg);-ms-transform:rotate(0deg);transform:rotate(0deg);}30%{-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg);}100%{-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg);}}"]))}}]),n}(p.Component),S=p.createElement;function R(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var C=function(t){(0,c.Z)(n,t);var e=R(n);function n(t){var r;return(0,a.Z)(this,n),(r=e.call(this,t)).state={url:"/webcam/stream"},r}return(0,s.Z)(n,[{key:"componentDidMount",value:function(){}},{key:"render",value:function(){return S(p.Fragment,null,S("img",{width:this.props.resolution.get("w"),height:this.props.resolution.get("h"),src:this.state.url,className:"jsx-1451342145"}),S(g.default,{id:"1451342145"},["img.jsx-1451342145{height:inherit;}"]))}}]),n}(p.PureComponent),j=(0,v.$j)((function(t){return{resolution:t.viewport.get("canvasResolution")}}))(C),D=n(4198),N=n(4855),E=n(3349),Z=n(4087),I=n.n(Z),A=n(2579),_=n(1284),P=n(1799),T=n(7263),O=n.n(T)().theme.extend.colors,L=new(function(){function t(){(0,a.Z)(this,t)}return(0,s.Z)(t,[{key:"drawTrackerData",value:function(t,e,n,r){t.globalAlpha=1,t.lineWidth=2,e.map((function(e){t.globalAlpha=Math.max(Math.min(e.opacity,1),0);var i=(0,_.n)(e,n,r),o=i.x-i.w/2,a=i.y-i.h/2;t.setLineDash([10,10]),t.strokeStyle=(0,P.at)(O.default),t.strokeRect(o+5,a+5,i.w-10,i.h-10),t.setLineDash([]),t.fillStyle=(0,P.at)(O.default),t.fillRect(o+4,a-10,i.w-8,17),t.font="10px",t.fillStyle=(0,P.at)(O.inverse);var s=o+10+t.measureText("".concat(i.name)).width,c=o+i.w-30;s<c&&t.fillText("".concat(Math.round(100*i.confidence),"%"),c,a),t.fillStyle=(0,P.at)(O.default),t.fillRect(o+10,a-10,t.measureText("".concat(i.name)).width,17),t.fillStyle=(0,P.at)(O.inverse),t.fillText("".concat(i.name),o+10,a)}))}},{key:"drawTrackerDataCounterEditor",value:function(t,e,n,r,i){var o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:(new Date).getTime();t.globalAlpha=1,t.lineWidth=2,e.map((function(e){var a=(0,_.n)(e,r,i),s=a.x-a.w/2,c=a.y-a.h/2,l=null,u=e.counted&&e.counted[e.counted.length-1];u&&"polygon"!==n.getIn([u.areaKey,"type"])&&o-u.timeMs<1e3&&(l=!0),u&&"polygon"===n.getIn([u.areaKey,"type"])&&e.areas.indexOf(u.areaKey)>-1&&(l=!0),l?(t.strokeStyle=(0,P.SD)(n.getIn([u.areaKey,"color"])),t.fillStyle=(0,P.SD)(n.getIn([u.areaKey,"color"])),t.strokeRect(s+5,c+5,a.w-10,a.h-10),t.globalAlpha=.1,t.fillRect(s+5,c+5,a.w-10,a.h-10),t.globalAlpha=1):(t.setLineDash([10,10]),t.strokeStyle=(0,P.at)(O.default),t.strokeRect(s+5,c+5,a.w-10,a.h-10),t.setLineDash([]))}))}},{key:"drawCountingAreas",value:function(t,e,n){e.map((function(e,r){if(null!==e.get("location")){var i=e.get("location").toJS(),o=e.get("color");t.strokeStyle=(0,P.SD)(o),t.fillStyle=(0,P.SD)(o),t.lineWidth=5;for(var a=i.points.map((function(t){return(0,_.q)(t,n,i.refResolution)})),s=0;s<a.length;s++){var c=a[s];t.beginPath(),t.arc(c.x,c.y,5,0,2*Math.PI,!1),t.fill(),s>0&&(t.beginPath(),t.moveTo(a[s-1].x,a[s-1].y),t.lineTo(a[s].x,a[s].y),t.stroke())}a.length>2&&(t.globalAlpha=.3,t.beginPath(),t.moveTo(a[0].x,a[0].y),a.map((function(e){t.lineTo(e.x,e.y),t.lineTo(e.x,e.y)})),t.fill(),t.globalAlpha=1)}}))}}]),t}()),F=function(){function t(){(0,a.Z)(this,t),this.lastFrameData=[],this.pathsColors=(0,P.E5)()}return(0,s.Z)(t,[{key:"drawLine",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"green";t.strokeStyle=n,t.lineWidth=5,t.lineCap="round",t.beginPath(),t.moveTo(e.pointA.x,e.pointA.y),t.lineTo(e.pointB.x,e.pointB.y),t.stroke()}},{key:"drawPaths",value:function(t,e,n,r){var i=this;this.lastFrameData=e.map((function(e){var o=(0,_.n)(e,n,r),a=i.lastFrameData.find((function(t){return o.id===t.id}));if(a){var s=a.color?a.color:i.pathsColors[a.id%i.pathsColors.length];o.color=s,i.drawLine(t,{pointA:{x:a.x,y:a.y},pointB:{x:o.x,y:o.y}},s)}return o}))}},{key:"resetLastFrameData",value:function(){this.lastFrameData=[]}}]),t}(),M=n(6110),W=n.n(M),q=n(6544),Q=function(){function t(){(0,a.Z)(this,t),this.lastFrameData=[],this.pathsColors=(0,P.E5)(),this.simpleheat=null,this.heatmapData=[]}return(0,s.Z)(t,[{key:"drawAccuracyHeatmap",value:function(t,e,n,r){this.simpleheat||(this.simpleheat=W()(t,n),this.simpleheat.radius(n.w*(0,q._d)().radius/100,n.w*(0,q._d)().blur/100),this.simpleheat.gradient((0,q._d)().gradient)),this.heatmapData.length>(0,q.QJ)()&&this.heatmapData.shift(),this.heatmapData.push(e.filter((function(t){return!0===t.isZombie})).map((function(t){var e=(0,_.n)(t,n,r);return[e.x,e.y,(0,q._d)().step]}))),this.simpleheat.data(this.heatmapData.flat()).draw()}}]),t}(),B=p.createElement;function z(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var V=function(t){(0,c.Z)(n,t);var e=z(n);function n(t){var r;return(0,a.Z)(this,n),(r=e.call(this,t)).lastFrameDrawn=-1,r.loopUpdateCanvas=r.loopUpdateCanvas.bind((0,E.Z)(r)),r.clearCanvas=r.clearCanvas.bind((0,E.Z)(r)),r.rafHandle=null,r.PathViewEngine=new F,r.TrackerAccuracyEngine=new Q,r}return(0,s.Z)(n,[{key:"componentDidMount",value:function(){this.loopUpdateCanvas()}},{key:"componentDidUpdate",value:function(t){this.props.canvasResolution!==t.canvasResolution&&(this.PathViewEngine.resetLastFrameData(),this.clearCanvas())}},{key:"clearCanvas",value:function(){this.canvasContext.clearRect(0,0,this.props.fixedResolution&&this.props.fixedResolution.w||this.props.canvasResolution.get("w"),this.props.fixedResolution&&this.props.fixedResolution.h||this.props.canvasResolution.get("h")),this.props.mode===A.qI.PATHVIEW&&this.PathViewEngine.resetLastFrameData()}},{key:"loopUpdateCanvas",value:function(){this.lastFrameDrawn!==this.props.trackerData.frameIndex&&(this.props.mode!==A.qI.PATHVIEW&&this.clearCanvas(),this.props.mode===A.qI.LIVEVIEW&&L.drawTrackerData(this.canvasContext,this.props.trackerData.data,this.props.canvasResolution.toJS(),this.props.originalResolution),this.props.mode===A.qI.COUNTERVIEW&&L.drawTrackerDataCounterEditor(this.canvasContext,this.props.trackerData.data,this.props.countingAreas,this.props.canvasResolution.toJS(),this.props.originalResolution),this.props.mode===A.qI.COUNTERVIEW_RECORDING&&(L.drawCountingAreas(this.canvasContext,this.props.countingAreas,this.props.canvasResolution.toJS()),L.drawTrackerDataCounterEditor(this.canvasContext,this.props.trackerData.data,this.props.countingAreas,this.props.canvasResolution.toJS(),this.props.originalResolution)),this.props.mode===A.qI.COUNTING_AREAS&&L.drawCountingAreas(this.canvasContext,this.props.countingAreas,this.props.canvasResolution.toJS()),this.props.mode===A.qI.PATHVIEW&&this.PathViewEngine.drawPaths(this.canvasContext,this.props.trackerData.data,this.props.fixedResolution||this.props.canvasResolution.toJS(),this.props.originalResolution),this.props.mode===A.qI.TRACKER_ACCURACY&&this.TrackerAccuracyEngine.drawAccuracyHeatmap(this.canvasContext,this.props.trackerData.data,this.props.fixedResolution||this.props.canvasResolution.toJS(),this.props.originalResolution),this.lastFrameDrawn=this.props.trackerData.frameIndex),this.rafHandle=I()(this.loopUpdateCanvas.bind(this))}},{key:"componentWillUnmount",value:function(){this.rafHandle&&I().cancel(this.rafHandle)}},{key:"render",value:function(){var t=this;return B("div",{className:g.default.dynamic([["382447905",[this.props.hidden?"hidden":"visible",this.props.userSettings.get("dimmerOpacity")]]])+" canvas-container"},B("canvas",{ref:function(e){t.canvasEl=e,t.canvasEl&&(t.canvasContext=e.getContext("2d"),t.props.onDomReady&&t.props.onDomReady(t.canvasEl),t.props.registerClearCanvas&&t.props.registerClearCanvas(t.clearCanvas))},width:this.props.fixedResolution&&this.props.fixedResolution.w||this.props.canvasResolution.get("w"),height:this.props.fixedResolution&&this.props.fixedResolution.h||this.props.canvasResolution.get("h"),className:g.default.dynamic([["382447905",[this.props.hidden?"hidden":"visible",this.props.userSettings.get("dimmerOpacity")]]])+" canvas"}),B(g.default,{id:"382447905",dynamic:[this.props.hidden?"hidden":"visible",this.props.userSettings.get("dimmerOpacity")]},[".canvas-container.__jsx-style-dynamic-selector{width:100%;height:100%;position:absolute;top:0;left:0;pointer-events:none;visibility:".concat(this.props.hidden?"hidden":"visible",";}"),".canvas.__jsx-style-dynamic-selector{display:block;position:absolute;top:0;left:0;z-index:1;width:100%;height:100%;background-color:rgba(0,0,0,".concat(this.props.userSettings.get("dimmerOpacity"),");}")]))}}]),n}(p.PureComponent),U=(0,v.$j)((function(t){return{trackerData:t.tracker.get("trackerData").toJS(),originalResolution:t.viewport.get("originalResolution").toJS(),canvasResolution:t.viewport.get("canvasResolution"),countingAreas:t.counter.get("countingAreas"),userSettings:t.usersettings}}))(V),Y=p.createElement;function H(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var K=function(t){(0,c.Z)(n,t);var e=H(n);function n(){return(0,a.Z)(this,n),e.apply(this,arguments)}return(0,s.Z)(n,[{key:"handleClick",value:function(){this.props.recordingStatus.isRecording?this.props.dispatch((0,N.kJ)()):this.props.dispatch((0,N.Sy)())}},{key:"render",value:function(){var t=this;return Y("div",{className:"jsx-3341985503 btn-record-container"},Y("div",{onClick:function(){return t.handleClick()},className:"jsx-3341985503 btn-record"},this.props.recordingStatus.isRecording&&Y(p.Fragment,null,Y("img",{src:"/static/icons/ui/stop-recording.svg",className:"jsx-3341985503 inline"}),Y("h3",{className:"jsx-3341985503 btn-record-label text-default text-xl font-bold"},"Stop recording")),!this.props.recordingStatus.isRecording&&Y(p.Fragment,null,Y("img",{src:"/static/icons/ui/start-recording.svg",className:"jsx-3341985503 inline"}),Y("h3",{className:"jsx-3341985503 btn-record-label text-default text-xl font-bold"},"Start recording"))),Y(g.default,{id:"3341985503"},[".btn-record-container.jsx-3341985503{position:fixed;bottom:0.62rem;left:50%;-webkit-transform:translateX(-50%);-ms-transform:translateX(-50%);transform:translateX(-50%);z-index:5;}",".btn-record.jsx-3341985503{position:relative;text-align:center;z-index:2;cursor:pointer;}",".btn-record.jsx-3341985503 img.jsx-3341985503{width:3.12rem;}",".btn-record-label.jsx-3341985503{margin-top:0.5rem;text-shadow:0px 2px 4px rgba(73,73,73,0.5);}"]))}}]),n}(p.Component),J=(0,v.$j)((function(t){return{recordingStatus:t.app.get("recordingStatus").toJS(),mode:t.app.get("mode")}}))(K),X=p.createElement;function G(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var $=function(t){(0,c.Z)(n,t);var e=G(n);function n(t){return(0,a.Z)(this,n),e.call(this,t)}return(0,s.Z)(n,[{key:"componentDidMount",value:function(){}},{key:"componentWillUnmount",value:function(){}},{key:"render",value:function(){return X(p.Fragment,null,X(U,{mode:A.qI.LIVEVIEW}),X(J,null))}}]),n}(p.PureComponent),tt=n(3391),et=n(1208),nt=n.n(et),rt=n(2069),it=p.createElement;function ot(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var at=function(t){(0,c.Z)(n,t);var e=ot(n);function n(){return(0,a.Z)(this,n),e.apply(this,arguments)}return(0,s.Z)(n,[{key:"handleDelete",value:function(){this.props.countingAreas.size>1?this.props.dispatch((0,rt.PM)(rt.Q4.DELETE)):this.props.dispatch((0,rt.XO)(this.props.countingAreas.keySeq().first()))}},{key:"loadFile",value:function(){var t,e,n,r=this;console.log("loadFile"),"function"===typeof window.FileReader?(t=document.getElementById("upload"))?t.files?t.files[0]?(e=t.files[0],(n=new FileReader).onload=function(t){var e=t.target.result,n=JSON.parse(e);r.props.dispatch((0,rt.GA)(n))},n.readAsText(e)):alert("Please select a file before clicking 'Load'"):alert("This browser doesn't seem to support the `files` property of file inputs."):alert("Um, couldn't find the fileinput element."):alert("The file API isn't supported on this browser yet.")}},{key:"render",value:function(){var t=this;return it("div",{className:"jsx-1890780646 menu-active-areas flex fixed bottom-0 left-0 mb-2 ml-2"},this.props.mode!==rt.Q4.DELETE&&it(p.Fragment,null,it("button",{onClick:function(){return t.handleDelete()},className:"jsx-1890780646 btn btn-default p-0 rounded-l shadow"},it(nt(),{className:"w-10 h-10 svg-icon flex items-center",cacheRequests:!0,src:"/static/icons/ui/delete.svg","aria-label":"icon delete"})),it("button",{onClick:function(){return t.props.dispatch((0,rt.PM)(rt.Q4.EDIT_LINE))},className:"jsx-1890780646 "+"btn btn-default p-0 shadow ".concat(this.props.mode===rt.Q4.EDIT_LINE?"btn-default--active":"")},it(nt(),{className:"w-10 h-10 svg-icon flex items-center",cacheRequests:!0,src:"/static/icons/ui/addline.svg","aria-label":"icon addline"})),it("button",{onClick:function(){return t.props.dispatch((0,rt.PM)(rt.Q4.EDIT_POLYGON))},className:"jsx-1890780646 "+"btn btn-default p-0 shadow rounded-r ".concat(this.props.mode===rt.Q4.EDIT_POLYGON?"btn-default--active":"")},it(nt(),{className:"w-10 h-10 svg-icon flex items-center",cacheRequests:!0,src:"/static/icons/ui/addpolygon.svg","aria-label":"icon addpolygon"})),it("a",{href:"/counter/areas",target:"_blank",download:!0,className:"jsx-1890780646 btn btn-default p-0 ml-4 rounded-l shadow"},it(nt(),{className:"w-10 h-10 svg-icon flex items-center",cacheRequests:!0,src:"/static/icons/ui/download.svg","aria-label":"icon download"})),it("label",{htmlFor:"upload",className:"jsx-1890780646 btn btn-default p-0 rounded-r shadow cursor-pointer\t"},it(nt(),{className:"w-10 h-10 svg-icon flex items-center",cacheRequests:!0,src:"/static/icons/ui/upload.svg","aria-label":"icon upload"}),it("input",{type:"file",id:"upload",onChange:function(){return t.loadFile()},style:{display:"none"},className:"jsx-1890780646"}))),this.props.mode===rt.Q4.DELETE&&it("button",{onClick:function(){return t.props.dispatch((0,rt.PM)(t.props.lastEditingMode))},className:"jsx-1890780646 btn btn-default p-0 rounded shadow"},it(nt(),{className:"w-10 h-10 svg-icon flex items-center",cacheRequests:!0,src:"/static/icons/ui/close.svg","aria-label":"icon edit"})),it(g.default,{id:"1890780646"},[".menu-active-areas.jsx-1890780646{z-index:8;}"]))}}]),n}(p.Component),st=(0,v.$j)((function(t){return{countingAreas:t.counter.get("countingAreas"),selectedCountingArea:t.counter.get("selectedCountingArea"),mode:t.counter.get("mode"),lastEditingMode:t.counter.get("lastEditingMode")}}))(at),ct=p.createElement;function lt(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var ut=function(t){(0,c.Z)(n,t);var e=lt(n);function n(t){var r;return(0,a.Z)(this,n),(r=e.call(this,t)).state={name:t.name||""},r.handleChange=r.handleChange.bind((0,E.Z)(r)),r.escFunction=r.escFunction.bind((0,E.Z)(r)),r}return(0,s.Z)(n,[{key:"handleChange",value:function(t){this.setState({name:t.target.value})}},{key:"escFunction",value:function(t){27===t.keyCode&&this.props.cancel()}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.escFunction,!1)}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.escFunction,!1)}},{key:"render",value:function(){var t=this;return ct("div",{className:"jsx-74240079 overlay"},ct("form",{onSubmit:function(e){e.preventDefault(),""!==t.state.name&&t.props.save(t.state.name)},className:"jsx-74240079 ask-name flex"},ct("input",{type:"text",value:this.state.name,onChange:this.handleChange,placeholder:"Counter name",autoFocus:!0,className:"jsx-74240079 appearance-none rounded-l py-2 px-3"}),ct("input",{type:"submit",value:"OK",className:"jsx-74240079 btn btn-default cursor-pointer"}),ct("button",{onClick:function(){return t.props.cancel()},className:"jsx-74240079 btn btn-default p-0 rounded-r"},ct(nt(),{className:"w-10 h-10 svg-icon flex items-center",cacheRequests:!0,src:"/static/icons/ui/close.svg","aria-label":"icon close"}))),ct(U,{mode:A.qI.COUNTING_AREAS}),ct(g.default,{id:"74240079"},[".overlay.jsx-74240079{position:fixed;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,0.8);z-index:8;}",".ask-name.jsx-74240079{text-align:center;position:fixed;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);transform:translate(-50%,-50%);z-index:6;}"]))}}]),n}(p.Component),pt=p.createElement;function dt(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var ft=function(t){(0,c.Z)(n,t);var e=dt(n);function n(t){var r;return(0,a.Z)(this,n),(r=e.call(this,t)).escFunction=r.escFunction.bind((0,E.Z)(r)),r}return(0,s.Z)(n,[{key:"escFunction",value:function(t){t.stopPropagation(),27===t.keyCode&&this.props.cancel()}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.escFunction,!1)}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.escFunction,!1)}},{key:"render",value:function(){var t=this;return pt("div",{className:g.default.dynamic([["135469577",[A.SL,A.SL,A.SL,A.SL]]])+" overlay"},this.props.countingAreasWithCenters.entrySeq().map((function(e){var n=(0,tt.Z)(e,2),r=n[0],i=n[1];return pt("div",{key:r,onClick:function(){return t.props.delete(r)},style:{top:i.getIn(["location","center","y"])-A.SL/2,left:i.getIn(["location","center","x"])-A.SL/2,backgroundColor:(0,P.SD)(i.get("color"))},className:g.default.dynamic([["135469577",[A.SL,A.SL,A.SL,A.SL]]])+" circle"},pt(nt(),{className:"w-8 h-8 svg-icon flex items-center",cacheRequests:!0,src:"/static/icons/ui/delete.svg","aria-label":"icon close"}))})),pt(U,{mode:A.qI.COUNTING_AREAS}),pt(g.default,{id:"135469577",dynamic:[A.SL,A.SL,A.SL,A.SL]},[".overlay.__jsx-style-dynamic-selector{position:fixed;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,0.8);z-index:8;}",".circle.__jsx-style-dynamic-selector{position:absolute;border-radius:".concat(A.SL,"px;z-index:2;min-width:").concat(A.SL,"px;height:").concat(A.SL,"px;line-height:").concat(A.SL,"px;font-size:16px;font-weight:bold;color:black;text-align:center;cursor:pointer;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;}")]))}}]),n}(p.Component),ht=p.createElement;function mt(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var gt=function(t){(0,c.Z)(n,t);var e=mt(n);function n(){return(0,a.Z)(this,n),e.apply(this,arguments)}return(0,s.Z)(n,[{key:"render",value:function(){var t=this;return ht("div",{onClick:function(){return t.props.close()},className:"jsx-3225436558 overlay"},ht("div",{className:"jsx-3225436558 modal rounded p-10 shadow text-inverse bg-default border-inverse"},ht("h3",{className:"jsx-3225436558 text-center text-xl font-bold"},"Draw to define the counting zones"),ht("div",{className:"jsx-3225436558 text-center mt-2"},"(crossing vehicles increase counter by 1)"),ht("button",{onClick:function(){return t.props.close()},className:"jsx-3225436558 btn btn-primary btn-rounded min-w-100 mt-5 pl-10 pr-10"},"OK")),ht(g.default,{id:"3225436558"},[".overlay.jsx-3225436558{position:fixed;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,0.8);z-index:8;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;}",".modal.jsx-3225436558{border:1px solid black;width:300px;height:auto;padding:1rem;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;}"]))}}]),n}(p.Component),vt=p.createElement;function xt(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var yt=function(t){(0,c.Z)(n,t);var e=xt(n);function n(){return(0,a.Z)(this,n),e.apply(this,arguments)}return(0,s.Z)(n,[{key:"render",value:function(){var t=this;return vt(p.Fragment,null,vt("div",{onClick:function(){return t.props.toggleDirection()},style:{top:this.props.area.location.center.y-A.S7/2,left:this.props.area.location.center.x-A.S7/2,backgroundColor:(0,P.SD)(this.props.area.color)},className:g.default.dynamic([["729206982",[A.S7,A.S7,A.S7,A.S7,A.a6,A.a6,this.props.area.computed.lineBearings[0]+90]]])+" circle"},this.props.area.type===A.Og.BIDIRECTIONAL&&vt("img",{src:"/static/icons/ui/arrow-double.svg",className:g.default.dynamic([["729206982",[A.S7,A.S7,A.S7,A.S7,A.a6,A.a6,this.props.area.computed.lineBearings[0]+90]]])+" icon-direction"}),this.props.area.type===A.Og.LEFTRIGHT_TOPBOTTOM&&vt("img",{src:"/static/icons/ui/arrow-up.svg",className:g.default.dynamic([["729206982",[A.S7,A.S7,A.S7,A.S7,A.a6,A.a6,this.props.area.computed.lineBearings[0]+90]]])+" icon-direction"}),this.props.area.type===A.Og.RIGHTLEFT_BOTTOMTOP&&vt("img",{src:"/static/icons/ui/arrow-down.svg",className:g.default.dynamic([["729206982",[A.S7,A.S7,A.S7,A.S7,A.a6,A.a6,this.props.area.computed.lineBearings[0]+90]]])+" icon-direction"})),vt(g.default,{id:"729206982",dynamic:[A.S7,A.S7,A.S7,A.S7,A.a6,A.a6,this.props.area.computed.lineBearings[0]+90]},[".circle.__jsx-style-dynamic-selector{position:absolute;border-radius:".concat(A.S7,"px;z-index:5;min-width:").concat(A.S7,"px;height:").concat(A.S7,"px;line-height:").concat(A.S7,"px;font-size:16px;font-weight:bold;padding-left:5px;padding-right:5px;color:black;text-align:center;cursor:pointer;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;}"),".icon-direction.__jsx-style-dynamic-selector{width:".concat(A.a6,"px;height:").concat(A.a6,"px;-webkit-transform:rotate(").concat(this.props.area.computed.lineBearings[0]+90,"deg);-ms-transform:rotate(").concat(this.props.area.computed.lineBearings[0]+90,"deg);transform:rotate(").concat(this.props.area.computed.lineBearings[0]+90,"deg);}")]))}}]),n}(p.Component),bt=p.createElement;function wt(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var kt=function(t){(0,c.Z)(n,t);var e=wt(n);function n(t){var r;return(0,a.Z)(this,n),(r=e.call(this,t)).state={editorInitialized:!1},r.escFunction=r.escFunction.bind((0,E.Z)(r)),r.currentLine=null,r.currentPolygon=null,r.isDrawing=!1,r.points=[],r}return(0,s.Z)(n,[{key:"checkIfClosedPolygon",value:function(t){var e=15;if(this.points.length>2){var n=this.points[0];return(t.x-n.x)*(t.x-n.x)+(t.y-n.y)*(t.y-n.y)<(e*=e)}}},{key:"resetDrawing",value:function(){this.isDrawing=!1,this.points=[],this.currentPolygon=null,this.currentLine=null}},{key:"escFunction",value:function(t){this.props.mode!==rt.Q4.EDIT_LINE&&this.props.mode!==rt.Q4.EDIT_POLYGON||27===t.keyCode&&(this.props.dispatch((0,rt.XO)(this.props.selectedCountingArea)),this.resetDrawing())}},{key:"initListeners",value:function(){var t=this;this.editorCanvas.on("mouse:down",(function(e){if(!t.isDrawing){var n=t.props.mode===rt.Q4.EDIT_LINE?"bidirectional":"polygon";t.props.dispatch((0,rt._8)(n))}t.isDrawing=!0;var r=t.editorCanvas.getPointer(e.e);if(t.props.mode===rt.Q4.EDIT_POLYGON){if(t.checkIfClosedPolygon(r))return t.points.push(t.points[0]),t.props.dispatch((0,rt.Co)(t.props.selectedCountingArea,{points:t.points,refResolution:{w:t.editorCanvas.width,h:t.editorCanvas.height}})),t.isDrawing=!1,void(t.points=[]);t.points.push(r),t.editorCanvas.remove(t.currentPolygon),t.currentPolygon=new fabric.Polygon(t.points,{strokeWidth:5,fill:(0,P.SD)(t.props.countingAreas.getIn([t.props.selectedCountingArea,"color"])),stroke:(0,P.SD)(t.props.countingAreas.getIn([t.props.selectedCountingArea,"color"])),opacity:.3,selectable:!1,hasBorders:!1,hasControls:!1}),t.editorCanvas.add(t.currentPolygon)}if(t.props.mode===rt.Q4.EDIT_LINE&&(t.points.push(r),t.points.length>1)){t.isDrawing=!1;var i={x:t.points[0].x,y:t.points[0].y},o={x:t.points[1].x,y:t.points[1].y};return(0,rt.Qk)(i,o)>50?t.props.dispatch((0,rt.Co)(t.props.selectedCountingArea,{points:t.points,refResolution:{w:t.editorCanvas.width,h:t.editorCanvas.height}})):t.props.dispatch((0,rt.XO)(t.props.selectedCountingArea)),t.points=[],void(t.isDrawing=!1)}var a=[r.x,r.y,r.x,r.y];t.points.length>1&&t.currentLine.set({x2:r.x,y2:r.y}),t.currentLine=new fabric.Line(a,{strokeWidth:5,fill:(0,P.SD)(t.props.countingAreas.getIn([t.props.selectedCountingArea,"color"])),stroke:(0,P.SD)(t.props.countingAreas.getIn([t.props.selectedCountingArea,"color"])),originX:"center",originY:"center"}),t.editorCanvas.add(t.currentLine),t.editorCanvas.add(new fabric.Circle({radius:5,fill:(0,P.SD)(t.props.countingAreas.getIn([t.props.selectedCountingArea,"color"])),top:r.y,left:r.x,originX:"center",originY:"center"}))})),this.editorCanvas.on("mouse:move",(function(e){if(t.isDrawing){var n=t.editorCanvas.getPointer(e.e);t.currentLine.set({x2:n.x,y2:n.y}),t.editorCanvas.renderAll()}}))}},{key:"componentDidUpdate",value:function(t){t.countingAreas!==this.props.countingAreas&&this.reRenderCountingAreasInEditor(this.props.countingAreas)}},{key:"componentDidMount",value:function(){if(this.elCanvas){var t=this.elCanvas.getBoundingClientRect(),e=t.width,n=t.height;this.editorCanvas=new fabric.Canvas(this.elCanvas,{selection:!1,width:e,height:n}),0===this.props.countingAreas.size?this.props.dispatch((0,rt.PM)(rt.Q4.SHOW_INSTRUCTION)):this.reRenderCountingAreasInEditor(this.props.countingAreas),this.initListeners()}document.addEventListener("keydown",this.escFunction,!1)}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.escFunction,!1)}},{key:"reRenderCountingAreasInEditor",value:function(t){var e=this;this.editorCanvas.clear(),this.resetDrawing();var n=this.elCanvas.getBoundingClientRect(),r=n.width,i=n.height;t.map((function(t,n){if(void 0!==t.get("location")){for(var o=t.get("location").toJS(),a=t.get("color"),s=r/o.refResolution.w,c=i/o.refResolution.h,l=o.points.map((function(t){return{x:t.x*s,y:t.y*c}})),u=0;u<l.length;u++){var p=l[u];e.editorCanvas.add(new fabric.Circle({radius:5,fill:(0,P.SD)(a),top:p.y,left:p.x,originX:"center",originY:"center"})),u>0&&e.editorCanvas.add(new fabric.Line([l[u-1].x,l[u-1].y,l[u].x,l[u].y],{strokeWidth:5,fill:(0,P.SD)(a),stroke:(0,P.SD)(a),originX:"center",originY:"center"}))}l.length>2&&e.editorCanvas.add(new fabric.Polygon(l,{strokeWidth:5,fill:(0,P.SD)(a),stroke:(0,P.SD)(a),opacity:.3,selectable:!1,hasBorders:!1,hasControls:!1}))}}))}},{key:"render",value:function(){var t=this;return bt("div",{className:g.default.dynamic([["1873663113",[this.props.mode===rt.Q4.ASKNAME||this.props.mode===rt.Q4.SHOW_INSTRUCTION||this.props.mode===rt.Q4.DELETE?"7":"2"]]])+" counting-areas-editor"},this.props.mode===rt.Q4.SHOW_INSTRUCTION&&bt(gt,{close:function(){return t.props.dispatch((0,rt.PM)(rt.Q4.EDIT_LINE))}}),this.props.mode===rt.Q4.ASKNAME&&bt(ut,{save:function(e){t.props.dispatch((0,rt.WM)(t.props.selectedCountingArea,e)),t.props.dispatch((0,rt.PM)(t.props.lastEditingMode))},cancel:function(e){t.props.dispatch((0,rt.XO)(t.props.selectedCountingArea)),t.props.dispatch((0,rt.PM)(t.props.lastEditingMode))}}),this.props.mode===rt.Q4.DELETE&&bt(ft,{countingAreasWithCenters:this.props.countingAreasWithCenters,delete:function(e){return t.props.dispatch((0,rt.XO)(e))},cancel:function(){return t.props.dispatch((0,rt.PM)(t.props.lastEditingMode))}}),this.props.countingAreasWithCenters.entrySeq().map((function(e){var n=(0,tt.Z)(e,2),r=n[0],i=n[1];return bt(p.Fragment,{key:r},"polygon"!==i.get("type")&&i.get("computed")&&i.get("location")&&bt(yt,{key:r,area:i.toJS(),toggleDirection:function(){return t.props.dispatch((0,rt.e3)(r,i.get("type")))}}))})),bt(st,null),bt("canvas",{ref:function(e){return t.elCanvas=e},width:this.props.canvasResolution.get("w"),height:this.props.canvasResolution.get("h"),className:g.default.dynamic([["1873663113",[this.props.mode===rt.Q4.ASKNAME||this.props.mode===rt.Q4.SHOW_INSTRUCTION||this.props.mode===rt.Q4.DELETE?"7":"2"]]])+" editor-canvas"}),bt(g.default,{id:"1873663113",dynamic:[this.props.mode===rt.Q4.ASKNAME||this.props.mode===rt.Q4.SHOW_INSTRUCTION||this.props.mode===rt.Q4.DELETE?"7":"2"]},[".counting-areas-editor.__jsx-style-dynamic-selector,.editor-canvas.__jsx-style-dynamic-selector{position:absolute;top:0;right:0;left:0;bottom:0;z-index:".concat(this.props.mode===rt.Q4.ASKNAME||this.props.mode===rt.Q4.SHOW_INSTRUCTION||this.props.mode===rt.Q4.DELETE?"7":"2",";}")]))}}]),n}(p.Component),St=(0,v.$j)((function(t){var e=(0,rt.b3)(t.counter.get("countingAreas"),t.viewport.get("canvasResolution"));return{countingAreas:t.counter.get("countingAreas"),countingAreasWithCenters:e,selectedCountingArea:t.counter.get("selectedCountingArea"),canvasResolution:t.viewport.get("canvasResolution"),mode:t.counter.get("mode"),lastEditingMode:t.counter.get("lastEditingMode")}}))(kt),Rt=n(5369),Ct=p.createElement;function jt(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var Dt=function(t){(0,c.Z)(n,t);var e=jt(n);function n(){return(0,a.Z)(this,n),e.apply(this,arguments)}return(0,s.Z)(n,[{key:"render",value:function(){return Ct(nt(),{className:"svg-openmoji w-10",cacheRequests:!0,src:"/static/icons/openmojis/".concat(this.props.hexcode,".svg"),"aria-label":this.props.label})}}]),n}(p.Component),Nt=p.createElement;function Et(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var Zt=function(t){(0,c.Z)(n,t);var e=Et(n);function n(t){var r;return(0,a.Z)(this,n),(r=e.call(this,t)).state={showPopover:!1},r.togglePopover=r.togglePopover.bind((0,E.Z)(r)),r.DISPLAY_CLASSES=(0,P.b7)(),r}return(0,s.Z)(n,[{key:"componentDidMount",value:function(){}},{key:"componentWillUnmount",value:function(){}},{key:"togglePopover",value:function(){this.state.showPopover?this.setState({showPopover:!1}):this.setState({showPopover:!0})}},{key:"render",value:function(){var t=this;return Nt(p.Fragment,null,this.props.counterData&&this.state.showPopover&&Nt("div",{style:{top:this.props.area.location.center.y-A.Ys-A.S7/2-A.fQ-5,left:this.props.area.location.center.x-A.qr/2},className:g.default.dynamic([["2957555364",[A.S7,A.S7+A.a6+5,A.S7,A.S7,A.a6,A.a6,t.props.area.computed.lineBearings[0]+90,A.S7-A.a6-3,A.qr,A.Ys,A.fQ,A.fQ]]])+" area-popover bg-default text-inverse"},Nt("h4",{className:g.default.dynamic([["2957555364",[A.S7,A.S7+A.a6+5,A.S7,A.S7,A.a6,A.a6,t.props.area.computed.lineBearings[0]+90,A.S7-A.a6-3,A.qr,A.Ys,A.fQ,A.fQ]]])+" area-popover-title border-b border-default-soft text-center py-2"},this.props.area.name),Nt("div",{className:g.default.dynamic([["2957555364",[A.S7,A.S7+A.a6+5,A.S7,A.S7,A.a6,A.a6,t.props.area.computed.lineBearings[0]+90,A.S7-A.a6-3,A.qr,A.Ys,A.fQ,A.fQ]]])+" area-popover-content"},this.DISPLAY_CLASSES.slice(0,Math.min(this.DISPLAY_CLASSES.length,6)).map((function(e){return Nt("div",{key:e.class,className:g.default.dynamic([["2957555364",[A.S7,A.S7+A.a6+5,A.S7,A.S7,A.a6,A.a6,t.props.area.computed.lineBearings[0]+90,A.S7-A.a6-3,A.qr,A.Ys,A.fQ,A.fQ]]])+" area-popover-item mb-1"},Nt("div",{className:g.default.dynamic([["2957555364",[A.S7,A.S7+A.a6+5,A.S7,A.S7,A.a6,A.a6,t.props.area.computed.lineBearings[0]+90,A.S7-A.a6-3,A.qr,A.Ys,A.fQ,A.fQ]]])+" area-popover-count mr-2"},t.props.counterData.get(e.class)||0),Nt(Dt,{hexcode:e.hexcode,class:e.class}))})))),Nt("div",{onClick:this.togglePopover,style:{top:this.props.area.location.center.y-A.S7/2,left:this.props.area.location.center.x-A.S7/2-(A.a6+5)/2,backgroundColor:(0,P.SD)(this.props.area.color)},className:g.default.dynamic([["2957555364",[A.S7,A.S7+A.a6+5,A.S7,A.S7,A.a6,A.a6,t.props.area.computed.lineBearings[0]+90,A.S7-A.a6-3,A.qr,A.Ys,A.fQ,A.fQ]]])+" circle"},this.props.area.type===A.Og.BIDIRECTIONAL&&Nt("img",{src:"/static/icons/ui/arrow-double.svg",className:g.default.dynamic([["2957555364",[A.S7,A.S7+A.a6+5,A.S7,A.S7,A.a6,A.a6,t.props.area.computed.lineBearings[0]+90,A.S7-A.a6-3,A.qr,A.Ys,A.fQ,A.fQ]]])+" icon-direction"}),this.props.area.type===A.Og.LEFTRIGHT_TOPBOTTOM&&Nt("img",{src:"/static/icons/ui/arrow-up.svg",className:g.default.dynamic([["2957555364",[A.S7,A.S7+A.a6+5,A.S7,A.S7,A.a6,A.a6,t.props.area.computed.lineBearings[0]+90,A.S7-A.a6-3,A.qr,A.Ys,A.fQ,A.fQ]]])+" icon-direction"}),this.props.area.type===A.Og.RIGHTLEFT_BOTTOMTOP&&Nt("img",{src:"/static/icons/ui/arrow-down.svg",className:g.default.dynamic([["2957555364",[A.S7,A.S7+A.a6+5,A.S7,A.S7,A.a6,A.a6,t.props.area.computed.lineBearings[0]+90,A.S7-A.a6-3,A.qr,A.Ys,A.fQ,A.fQ]]])+" icon-direction"}),Nt("div",{className:g.default.dynamic([["2957555364",[A.S7,A.S7+A.a6+5,A.S7,A.S7,A.a6,A.a6,t.props.area.computed.lineBearings[0]+90,A.S7-A.a6-3,A.qr,A.Ys,A.fQ,A.fQ]]])+" counter-value"},this.props.counterData&&this.props.counterData.get("_total")||0)),Nt(g.default,{id:"2957555364",dynamic:[A.S7,A.S7+A.a6+5,A.S7,A.S7,A.a6,A.a6,t.props.area.computed.lineBearings[0]+90,A.S7-A.a6-3,A.qr,A.Ys,A.fQ,A.fQ]},[".circle.__jsx-style-dynamic-selector{position:absolute;border-radius:".concat(A.S7,"px;z-index:2;min-width:").concat(A.S7+A.a6+5,"px;height:").concat(A.S7,"px;line-height:").concat(A.S7,"px;font-size:16px;font-weight:bold;padding-left:5px;padding-right:5px;color:black;text-align:center;cursor:pointer;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;}"),".icon-direction.__jsx-style-dynamic-selector{width:".concat(A.a6,"px;height:").concat(A.a6,"px;-webkit-transform:rotate(").concat(t.props.area.computed.lineBearings[0]+90,"deg);-ms-transform:rotate(").concat(t.props.area.computed.lineBearings[0]+90,"deg);transform:rotate(").concat(t.props.area.computed.lineBearings[0]+90,"deg);}"),".counter-value.__jsx-style-dynamic-selector{min-width:".concat(A.S7-A.a6-3,"px;margin-left:3px;}"),".area-popover.__jsx-style-dynamic-selector{position:absolute;z-index:3;width:".concat(A.qr,"px;height:").concat(A.Ys,"px;}"),".area-popover-title.__jsx-style-dynamic-selector{width:100%;}",".area-popover-content.__jsx-style-dynamic-selector{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;padding:5px;overflow:hidden;}",".area-popover-item.__jsx-style-dynamic-selector{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;width:75px;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;}",'.area-popover.__jsx-style-dynamic-selector:after{top:100%;left:50%;border:solid transparent;content:" ";height:0;width:0;position:absolute;pointer-events:none;border-color:rgba(0,0,0,0);border-top-color:var(--color-default);border-width:'.concat(A.fQ,"px;margin-left:-").concat(A.fQ,"px;}")]))}}]),n}(p.Component),It=p.createElement;function At(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var _t=function(t){(0,c.Z)(n,t);var e=At(n);function n(t){return(0,a.Z)(this,n),e.call(this,t)}return(0,s.Z)(n,[{key:"componentDidMount",value:function(){}},{key:"componentWillUnmount",value:function(){}},{key:"render",value:function(){var t=this,e=Object.keys(this.props.countingAreas);return It(p.Fragment,null,Object.values(this.props.countingAreas).map((function(n,r){return It(Zt,{key:e[r],area:n,counterData:t.props.counterSummary.get(e[r])||(0,Rt.D5)()})})))}}]),n}(p.Component),Pt=(0,v.$j)((function(t){return{countingAreas:(0,rt.b3)(t.counter.get("countingAreas"),t.viewport.get("canvasResolution")).toJS(),counterSummary:t.counter.get("counterSummary")}}))(_t),Tt=p.createElement;function Ot(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var Lt=function(t){(0,c.Z)(n,t);var e=Ot(n);function n(){return(0,a.Z)(this,n),e.apply(this,arguments)}return(0,s.Z)(n,[{key:"componentDidMount",value:function(){this.props.dispatch((0,rt.p6)())}},{key:"render",value:function(){return Tt("div",{className:"jsx-1522581866"},!this.props.isRecording&&Tt(p.Fragment,null,Tt(St,null),Tt(U,{mode:A.qI.COUNTERVIEW})),this.props.isRecording&&this.props.isAtLeastOneCountingAreasDefined&&Tt(p.Fragment,null,Tt(Pt,null),Tt(U,{mode:A.qI.COUNTERVIEW_RECORDING})),this.props.isRecording&&!this.props.isAtLeastOneCountingAreasDefined&&Tt(p.Fragment,null,Tt("div",{className:"jsx-1522581866 modal"},"Not counting lines defined , Blablabalbla Define counting lines before start recording")),Tt(J,null),Tt(g.default,{id:"1522581866"},[".modal.jsx-1522581866{position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);transform:translate(-50%,-50%);}"]))}}]),n}(p.PureComponent),Ft=(0,v.$j)((function(t){return{isAtLeastOneCountingAreasDefined:t.counter.get("countingAreas").size>0,isRecording:t.app.getIn(["recordingStatus","isRecording"])}}))(Lt),Mt=p.createElement;function Wt(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var qt,Qt,Bt=function(t){(0,c.Z)(n,t);var e=Wt(n);function n(t){return(0,a.Z)(this,n),e.call(this,t)}return(0,s.Z)(n,[{key:"componentDidMount",value:function(){}},{key:"componentWillUnmount",value:function(){}},{key:"registerHiddenCanvas",value:function(t){this.hiddenCanvas=t}},{key:"downloadFrame",value:function(){this.downloadBtn.setAttribute("href",this.hiddenCanvas.toDataURL()),this.downloadBtn.setAttribute("download","pathfinder.png")}},{key:"clearCanvas",value:function(){this.clearVisibleCanvas(),this.clearHiddenCanvas()}},{key:"render",value:function(){var t=this;return Mt(p.Fragment,null,Mt(U,{mode:A.qI.PATHVIEW,hidden:this.props.hidden,registerClearCanvas:function(e){return t.clearVisibleCanvas=e}}),Mt(U,{fixedResolution:{w:1280,h:720},mode:A.qI.PATHVIEW,onDomReady:function(e){return t.hiddenCanvas=e},registerClearCanvas:function(e){return t.clearHiddenCanvas=e},hidden:!0}),!this.props.hidden&&Mt(J,null),!this.props.hidden&&Mt("div",{className:"flex fixed bottom-0 left-0 mb-2 ml-2 z-10"},Mt("button",{className:"btn btn-default p-0 rounded-l shadow",onClick:function(){return t.clearCanvas()}},Mt(nt(),{className:"w-10 h-10 svg-icon flex items-center",cacheRequests:!0,src:"/static/icons/ui/delete.svg","aria-label":"icon delete"})),Mt("a",{id:"downloadFrame",ref:function(e){t.downloadBtn=e},className:"btn btn-default p-0 rounded-r shadow cursor-pointer",onClick:function(){return t.downloadFrame()}},Mt(nt(),{className:"w-10 h-10 svg-icon flex items-center",cacheRequests:!0,src:"/static/icons/ui/download.svg","aria-label":"icon take screenshot"}))))}}]),n}(p.PureComponent),zt=p.createElement;function Vt(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var Ut=function(t){(0,c.Z)(r,t);var e=Vt(r);function r(t){var n;return(0,a.Z)(this,r),(n=e.call(this,t)).state={onClient:!1},n}return(0,s.Z)(r,[{key:"componentDidMount",value:function(){this.setState({onClient:!0}),qt=n(3036).LazyLog,Qt=n(3036).ScrollFollow}},{key:"render",value:function(){return zt(p.Fragment,null,this.state.onClient&&zt(Qt,{startFollowing:!0,render:function(t){var e=t.follow,n=t.onScroll;return zt(qt,{url:"/console",stream:!0,follow:e,onScroll:n,overscanRowCount:300})}}))}}]),r}(p.Component),Yt=p.createElement;function Ht(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var Kt=function(t){(0,c.Z)(n,t);var e=Ht(n);function n(t){return(0,a.Z)(this,n),e.call(this,t)}return(0,s.Z)(n,[{key:"render",value:function(){return Yt("div",{className:"jsx-3129463923 console-view bg-default-soft"},Yt("div",{className:"jsx-3129463923 flex justify-end pl-5 pt-5 pr-5"},Yt("a",{target:"_blank",href:"/console",className:"jsx-3129463923 btn btn-light rounded"},"Download logs")),Yt("div",{className:"jsx-3129463923 w-full h-full p-5"},Yt(Ut,null)),Yt(g.default,{id:"3129463923"},[".console-view.jsx-3129463923{width:100%;height:100%;position:fixed;will-change:transform;overflow:scroll;padding-top:3.1rem;top:0;left:0;bottom:0;right:0;}"]))}}]),n}(p.Component),Jt=n(7484),Xt=n.n(Jt),Gt=n(4861),$t=p.createElement;function te(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var ee=function(t){(0,c.Z)(n,t);var e=te(n);function n(){return(0,a.Z)(this,n),e.apply(this,arguments)}return(0,s.Z)(n,[{key:"render",value:function(){var t=this;return $t("div",{onClick:function(){return t.props.onCancel()},className:"jsx-3225436558 overlay"},$t("div",{className:"jsx-3225436558 modal rounded p-10 shadow text-inverse bg-default border-inverse"},$t("h3",{className:"jsx-3225436558 text-center text-xl font-bold"},"Delete Recording"),$t("div",{className:"jsx-3225436558 text-center mt-2"},"Are you sure you want to delete this recording ?"),$t("div",{className:"jsx-3225436558 flex items-center"},$t("button",{onClick:function(){return t.props.onCancel()},className:"jsx-3225436558 btn btn-secondary btn-rounded min-w-100 mt-5 pl-10 pr-10"},"Cancel"),$t("button",{onClick:function(){return t.props.onConfirm()},className:"jsx-3225436558 btn btn-primary btn-rounded min-w-100 mt-5 pl-10 pr-10"},"Confirm"))),$t(g.default,{id:"3225436558"},[".overlay.jsx-3225436558{position:fixed;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,0.8);z-index:8;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;}",".modal.jsx-3225436558{border:1px solid black;width:300px;height:auto;padding:1rem;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;}"]))}}]),n}(p.Component),ne=p.createElement;function re(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var ie=function(t){(0,c.Z)(n,t);var e=re(n);function n(t){var r;return(0,a.Z)(this,n),(r=e.call(this,t)).DISPLAY_CLASSES=(0,P.b7)(),r.state={showDeleteConfirmationModal:!1},r}return(0,s.Z)(n,[{key:"componentDidMount",value:function(){}},{key:"componentWillUnmount",value:function(){}},{key:"renderDateEnd",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e?ne("span",{className:"font-bold",style:{color:"#FF0000"}},"Ongoing"):Xt()(t).format("hh:mm a")}},{key:"render",value:function(){var t=this;return ne("div",{className:"jsx-2750094696 flex flex-initial flex-col recording pl-2 mb-10"},ne("div",{className:"jsx-2750094696 text-inverse flex flex-initial items-center pl-6"},ne("div",{className:"jsx-2750094696"},Xt()(this.props.dateStart).format("MMM DD, YYYY")),ne("div",{className:"jsx-2750094696 ml-10"},Xt()(this.props.dateStart).format("hh:mm a")," ","-",this.renderDateEnd(this.props.dateEnd,this.props.active)),this.props.filename&&ne("div",{className:"jsx-2750094696 ml-10"},this.props.filename),!this.props.active&&ne("button",{onClick:function(){return t.setState({showDeleteConfirmationModal:!0})},className:"jsx-2750094696 btn btn-default p-0 ml-2 shadow rounded"},ne(nt(),{className:"w-6 h-6 svg-icon flex items-center",cacheRequests:!0,src:"/static/icons/ui/delete.svg","aria-label":"icon close"}))),this.state.showDeleteConfirmationModal&&ne(ee,{onCancel:function(){return t.setState({showDeleteConfirmationModal:!1})},onConfirm:function(){return t.props.dispatch((0,Gt.Mj)(t.props.id))}}),ne("div",{className:"jsx-2750094696 flex flex-initial flex-wrap pb-2 pl-1 m-2"},this.props.countingAreas.size>0&&ne("div",{className:"jsx-2750094696 flex flex-initial flex-col rounded bg-white text-black shadow m-2 p-4"},ne("div",{className:"jsx-2750094696 flex items-end justify-between"},ne("h3",{className:"jsx-2750094696 mr-3 text-xl font-bold"},"Counter"),ne("div",{className:"jsx-2750094696"},ne("div",{className:"jsx-2750094696 font-medium mr-2 inline-block"},"Download:"),ne("a",{href:"/recording/".concat(this.props.id,"/counter"),target:"_blank",download:!0,className:"jsx-2750094696 btn-text mr-2"},"JSON"),ne("a",{href:"/recording/".concat(this.props.id,"/counter/csv"),target:"_blank",download:!0,className:"jsx-2750094696 btn-text"},"CSV"))),ne("div",{className:"jsx-2750094696 mt-4 flex flex-wrap"},this.props.countingAreas&&this.props.countingAreas.entrySeq().map((function(e,n){var r=(0,tt.Z)(e,2),i=r[0],o=r[1];return ne("div",{key:i,className:"jsx-2750094696 flex flex-col counter-area bg-gray-200 m-2 rounded p-4"},ne("div",{className:"jsx-2750094696 flex items-center"},ne("h4",{className:"jsx-2750094696 font-medium"},o.get("name")),ne("div",{style:{backgroundColor:(0,P.SD)(o.get("color"))},className:"jsx-2750094696 w-4 h-4 ml-2 rounded-full"}),o.get("type")===A.Og.BIDIRECTIONAL&&ne("img",{style:{transform:"rotate(".concat(o.getIn(["computed","lineBearings"]).first()+90,"deg)")},src:"/static/icons/ui/arrow-double.svg",className:"jsx-2750094696 icon-direction"}),o.get("type")===A.Og.LEFTRIGHT_TOPBOTTOM&&ne("img",{style:{transform:"rotate(".concat(o.getIn(["computed","lineBearings"]).first()+90,"deg)")},src:"/static/icons/ui/arrow-up.svg",className:"jsx-2750094696 icon-direction"}),o.get("type")===A.Og.RIGHTLEFT_BOTTOMTOP&&ne("img",{style:{transform:"rotate(".concat(o.getIn(["computed","lineBearings"]).first()+90,"deg)")},src:"/static/icons/ui/arrow-down.svg",className:"jsx-2750094696 icon-direction"})),ne("div",{className:"jsx-2750094696 flex flex-initial flex-wrap mt-5 w-64"},t.DISPLAY_CLASSES.slice(0,Math.min(t.DISPLAY_CLASSES.length,6)).map((function(e){return ne("div",{key:e.class,className:"jsx-2750094696 flex w-16 m-1 items-center justify-center"},ne("h4",{className:"jsx-2750094696 mr-2"},t.props.counterData&&t.props.counterData.getIn([i,e.class])||0),ne(Dt,{hexcode:e.hexcode,class:e.class}))}))))})))),ne("div",{className:"jsx-2750094696 flex flex-initial flex-col rounded bg-white text-black shadow m-2 p-4"},ne("div",{className:"jsx-2750094696 flex items-end justify-between"},ne("h3",{className:"jsx-2750094696 mr-3 text-xl font-bold"},"Tracker"),ne("div",{className:"jsx-2750094696"},ne("div",{className:"jsx-2750094696 font-medium mr-2 inline-block"},"Download:"),ne("a",{href:"/recording/".concat(this.props.id,"/tracker"),target:"_blank",download:!0,className:"jsx-2750094696 btn-text mr-2"},"JSON"))),ne("div",{className:"jsx-2750094696 mt-6 rounded relative"},ne("div",{style:{bottom:10,left:10},className:"jsx-2750094696 text-white absolute"},ne("h2",{className:"jsx-2750094696 inline text-4xl font-bold"},this.props.nbPaths)," ","objects tracked"),ne("img",{src:"/static/placeholder/pathview.jpg",className:"jsx-2750094696"})))),ne(g.default,{id:"2750094696"},[".counter-area.jsx-2750094696{max-width:350px;-webkit-flex:1;-ms-flex:1;flex:1;}",".icon-direction.jsx-2750094696{margin-left:5px;width:20px;height:20px;}"]))}}]),n}(p.PureComponent),oe=(0,v.$j)()(ie),ae=p.createElement;function se(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var ce=function(t){(0,c.Z)(n,t);var e=se(n);function n(t){return(0,a.Z)(this,n),e.call(this,t)}return(0,s.Z)(n,[{key:"componentDidMount",value:function(){this.props.dispatch((0,Gt.eL)())}},{key:"componentWillUnmount",value:function(){}},{key:"render",value:function(){var t=this,e=this.props.recordingsCursor.toJS(),n=e.total>e.limit,r=Math.ceil(e.total/e.limit),i=new Array(r).fill(0),o=Math.floor(e.offset/e.limit);return ae("div",{className:"jsx-1239669181 data-view bg-default-soft"},this.props.recordingStatus.get("isRecording")&&ae(oe,{id:this.props.recordingStatus.get("recordingId"),dateStart:this.props.recordingStatus.get("dateStarted"),counterData:this.props.counterSummary,countingAreas:this.props.countingAreas,nbPaths:this.props.totalItemsTracked,filename:this.props.recordingStatus.get("filename"),active:!0}),this.props.recordingHistory.map((function(t){return ae(oe,{key:t.get("id"),id:t.get("id"),dateStart:t.get("dateStart"),dateEnd:t.get("dateEnd"),counterData:t.get("counterSummary"),countingAreas:t.get("areas"),filename:t.get("filename"),nbPaths:t.getIn(["trackerSummary","totalItemsTracked"])})})),n&&ae("div",{className:"jsx-1239669181 flex justify-center mb-8"},i.map((function(n,r){return ae("button",{key:r,onClick:function(){t.props.dispatch((0,Gt.eL)(r*e.limit,e.limit))},className:"jsx-1239669181 "+"btn btn-default ".concat(r===o?"btn-default--active":"")},r)}))),ae(g.default,{id:"1239669181"},[".data-view.jsx-1239669181{width:100%;height:100%;overflow:scroll;padding-top:100px;}"]))}}]),n}(p.PureComponent),le=(0,v.$j)((function(t){return{recordingHistory:t.app.getIn(["recordingStatus","isRecording"])?t.history.get("recordingHistory").skip(1):t.history.get("recordingHistory"),recordingStatus:t.app.get("recordingStatus"),recordingsCursor:t.history.get("recordingsCursor"),counterSummary:t.counter.get("counterSummary"),countingAreas:t.counter.get("countingAreas"),totalItemsTracked:t.counter.getIn(["trackerSummary","totalItemsTracked"])}}))(ce),ue=p.createElement;function pe(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var de=function(t){(0,c.Z)(n,t);var e=pe(n);function n(t){return(0,a.Z)(this,n),e.call(this,t)}return(0,s.Z)(n,[{key:"render",value:function(){var t=this;if(this.props.recordingStatus.isRecording)var e=Math.abs(new Date(this.props.recordingStatus.dateStarted)-new Date),n=Math.floor(e/1e3)%60,r=Math.floor(e/1e3/60);return ue(p.Fragment,null,ue("div",{className:"jsx-2283970313 nav"},this.props.recordingStatus.isRecording&&ue("div",{className:"jsx-2283970313 recording-bar"}),ue("div",{className:"jsx-2283970313 recording-status"},this.props.recordingStatus.isRecording&&ue("div",{className:"jsx-2283970313 time text-lg mb-1 font-bold"},r.toString().padStart(2,"0"),":",n.toString().padStart(2,"0")),ue("div",{className:"jsx-2283970313 fps"},this.props.recordingStatus.currentFPS," ","FPS")),ue("div",{className:"jsx-2283970313 flex"},ue("div",{className:"jsx-2283970313 nav-left mt-2 ml-2 shadow flex"},ue("button",{onClick:function(){return t.props.dispatch((0,N.PM)(A.IK.LIVEVIEW))},className:"jsx-2283970313 "+"btn btn-default rounded-l ".concat(this.props.mode===A.IK.LIVEVIEW?"btn-default--active":""," ").concat(this.props.uiSettings.get("pathfinderEnabled")||this.props.uiSettings.get("counterEnabled")?"":"rounded-r")},"Live view"),this.props.uiSettings.get("counterEnabled")&&(!this.props.recordingStatus.isRecording||this.props.isAtLeastOneCountingAreasDefined)&&ue("button",{onClick:function(){return t.props.dispatch((0,N.PM)(A.IK.COUNTERVIEW))},className:"jsx-2283970313 "+"btn btn-default border-r border-l border-default-soft border-solid ".concat(this.props.mode===A.IK.COUNTERVIEW?"btn-default--active":""," ").concat(this.props.uiSettings.get("pathfinderEnabled")?"":"rounded-r")},"Counter"),this.props.uiSettings.get("pathfinderEnabled")&&ue("button",{onClick:function(){return t.props.dispatch((0,N.PM)(A.IK.PATHVIEW))},className:"jsx-2283970313 "+"btn btn-default rounded-r ".concat(this.props.mode===A.IK.PATHVIEW?"btn-default--active":"")},"Pathfinder")),ue("div",{className:"jsx-2283970313 nav-right mt-2 mr-2 flex"},ue("button",{onClick:function(){return t.props.dispatch((0,N.PM)(A.IK.DATAVIEW))},className:"jsx-2283970313 "+"btn btn-default shadow rounded-l ".concat(this.props.mode===A.IK.DATAVIEW?"btn-default--active":"")},"Data"),ue("button",{onClick:function(){return t.props.dispatch((0,N.PM)(A.IK.CONSOLEVIEW))},className:"jsx-2283970313 "+"btn btn-default shadow rounded-r border-l border-default-soft border-solid ".concat(this.props.mode===A.IK.CONSOLEVIEW?"btn-default--active":"")},"Console"),ue("button",{onClick:function(){return t.props.dispatch((0,N.AE)())},className:"jsx-2283970313 btn btn-default shadow ml-2 py-0 px-3 rounded border border-default-soft border-solid"},ue(nt(),{className:"w-5 h-5 svg-icon flex items-center",cacheRequests:!0,src:"/static/icons/ui/menu.svg","aria-label":"icon menu"}))))),ue(g.default,{id:"2283970313"},[".nav.jsx-2283970313{position:fixed;top:0;left:0;right:0;z-index:3;}",".nav-right.jsx-2283970313{position:absolute;right:0;}",".recording-bar.jsx-2283970313{background-color:#FF0000;text-align:center;width:100%;z-index:3;height:0.32rem;}",".recording-status.jsx-2283970313{position:absolute;-webkit-transform:translateX(-50%);-ms-transform:translateX(-50%);transform:translateX(-50%);margin-left:50%;text-align:center;color:#FF0000;text-shadow:0 2px 4px rgba(0,0,0,0.1);top:1rem;}"]))}}]),n}(p.Component),fe=(0,v.$j)((function(t){return{recordingStatus:t.app.get("recordingStatus").toJS(),uiSettings:t.app.get("uiSettings"),mode:t.app.get("mode"),isAtLeastOneCountingAreasDefined:t.counter.get("countingAreas").size>0}}))(de),he=n(8717),me=n(4586),ge=p.createElement;function ve(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var xe=function(t){(0,c.Z)(n,t);var e=ve(n);function n(t){var r;return(0,a.Z)(this,n),(r=e.call(this,t)).state={id:(0,me.Z)()},r}return(0,s.Z)(n,[{key:"render",value:function(){var t=this;return ge("div",{className:"mb-4 mt-4 flex items-center justify-between"},ge("label",{htmlFor:this.state.id,className:"mr-3"},ge("h4",{className:"text-xl font-bold"},this.props.label),ge("p",{className:"text-xs"},this.props.description)),ge("div",{className:"form-switch inline-block align-middle shadow"},ge("input",{type:"checkbox",name:this.state.id,id:this.state.id,checked:this.props.enabled,className:"form-switch-checkbox",onChange:function(e){return t.props.onChange(e.target.checked)}}),ge("label",{className:"form-switch-label",htmlFor:this.state.id})))}}]),n}(p.Component),ye=p.createElement;function be(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var we=function(t){(0,c.Z)(n,t);var e=be(n);function n(t){var r;return(0,a.Z)(this,n),(r=e.call(this,t)).escFunction=r.escFunction.bind((0,E.Z)(r)),r.handleClick=r.handleClick.bind((0,E.Z)(r)),r}return(0,s.Z)(n,[{key:"escFunction",value:function(t){27===t.keyCode&&this.props.dispatch((0,N.SC)())}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.escFunction,!1),document.addEventListener("click",this.handleClick,!1)}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.escFunction,!1),document.removeEventListener("click",this.handleClick,!1)}},{key:"handleClick",value:function(t){this.node.contains(t.target)||this.props.dispatch((0,N.SC)())}},{key:"render",value:function(){var t=this;return ye(p.Fragment,null,ye("div",{ref:function(e){return t.node=e},className:"jsx-3272078245 menu text-inverse bg-default shadow"},ye("button",{onClick:function(){return t.props.dispatch((0,N.SC)())},className:"jsx-3272078245 btn btn-default btn-close p-0 flex items-center shadow rounded"},ye(nt(),{className:"w-12 h-12 svg-icon flex items-center",cacheRequests:!0,src:"/static/icons/ui/close.svg","aria-label":"icon close"})),ye("div",{className:"jsx-3272078245 p-5 w-full overflow-y-scroll"},ye("h3",{className:"jsx-3272078245 mb-4 text-2xl font-bold"},ye("a",{href:"https://github.com/opendatacam/opendatacam",target:"_blank",className:"jsx-3272078245 mt-2"},"OpenDataCam")),ye(xe,{label:"Counter",description:"Count objects on active areas",enabled:this.props.uiSettings.get("counterEnabled"),onChange:function(e){return t.props.dispatch((0,N.Cu)("counterEnabled",e))}}),ye(xe,{label:"Pathfinder",description:"Display paths and positions",enabled:this.props.uiSettings.get("pathfinderEnabled"),onChange:function(e){return t.props.dispatch((0,N.Cu)("pathfinderEnabled",e))}}),ye(xe,{label:"Tracker accuracy",description:"Display tracker accuracy",enabled:this.props.uiSettings.get("heatmapEnabled"),onChange:function(e){return t.props.dispatch((0,N.Cu)("heatmapEnabled",e))}}),ye("div",{className:"jsx-3272078245 mt-16"}),ye(xe,{label:"Dark mode",description:"Turn dark UI elements on",enabled:this.props.userSettings.get("darkMode"),onChange:function(e){t.props.dispatch((0,he.If)("darkMode",e))}}),ye("div",{className:"jsx-3272078245 mb-4 mt-4 flex items-center justify-between"},ye("div",{className:"jsx-3272078245 mr-3"},ye("h4",{className:"jsx-3272078245 text-xl font-bold"},"Dimmer"),ye("p",{className:"jsx-3272078245 text-xs"},"Opacity of camera image")),ye("div",{className:"jsx-3272078245 flex"},ye("button",{onClick:function(){return t.props.dispatch((0,he.If)("dimmerOpacity",Math.max(t.props.userSettings.get("dimmerOpacity")-.1,0)))},className:"jsx-3272078245 btn btn-light py-1 px-3 rounded-l border border-gray-100 border-solid flex items-center text-xl font-bold shadow"},"-"),ye("button",{onClick:function(){return t.props.dispatch((0,he.If)("dimmerOpacity",Math.min(t.props.userSettings.get("dimmerOpacity")+.1,1)))},className:"jsx-3272078245 btn btn-light py-1 px-3 rounded-r border border-gray-100 border-solid flex items-center text-xl font-bold shadow"},"+")))),ye("div",{className:"jsx-3272078245 menu-footer bg-black text-white p-5 w-full"},ye("div",{className:"jsx-3272078245 flex flex-col"},ye("p",{className:"jsx-3272078245"},"Version",this.props.version),ye("a",{target:"_blank",href:"/api/doc",className:"jsx-3272078245 mt-2"},"API documentation"),ye("a",{href:"https://github.com/opendatacam/opendatacam",target:"_blank",className:"jsx-3272078245 mt-2"},"About")))),ye(g.default,{id:"3272078245"},[".menu.jsx-3272078245{position:absolute;top:0;right:0;bottom:0;z-index:10;min-width:250px;max-width:320px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;}",".menu-footer.jsx-3272078245{box-shadow:0 -1px 3px 0 rgba(0,0,0,0.1);}",".btn-close.jsx-3272078245{position:absolute;top:1rem;left:-4rem;}"]))}}]),n}(p.Component),ke=(0,v.$j)((function(t){return{mode:t.app.get("mode"),userSettings:t.usersettings,uiSettings:t.app.get("uiSettings"),version:t.app.getIn(["config","OPENDATACAM_VERSION"])}}))(we),Se=p.createElement;function Re(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var Ce=function(t){(0,c.Z)(n,t);var e=Re(n);function n(t){var r;return(0,a.Z)(this,n),(r=e.call(this,t)).estimatedDuration=30,r.timeStarted=null,r.updateProgress=r.updateProgress.bind((0,E.Z)(r)),r.state={showConsole:!1},r}return(0,s.Z)(n,[{key:"updateProgress",value:function(){var t=((new Date).getTime()-this.timeStarted)/1e3;if(this.progressBar){var e=Math.min(t/this.estimatedDuration,1);this.progressBar.style="transform:scaleX(".concat(e,");")}I()(this.updateProgress)}},{key:"componentDidMount",value:function(){this.timeStarted=(new Date).getTime(),this.updateProgress()}},{key:"render",value:function(){var t=this;return Se("div",{className:"jsx-194310094 initializing-view pt-20 pb-20 pr-12 pl-12"},!this.props.requestedFileRecording&&!this.props.droppedFile&&Se("h2",{className:"jsx-194310094 text-white text-3xl font-bold"},"Initializing OpenDataCam"),this.props.requestedFileRecording&&Se("h2",{className:"jsx-194310094 text-white text-3xl font-bold"},"Restarting to process video file",this.props.fileName.split("/").pop()),this.props.droppedFile&&Se("h2",{className:"jsx-194310094 text-white text-3xl font-bold"},"Uploading and restarting on dropped video file"),Se("div",{className:"jsx-194310094 w-1/5 mt-5 h-5 progress-bar rounded overflow-hidden"},Se("div",{className:"jsx-194310094 shadow w-full h-full bg-gray-900"},Se("div",{ref:function(e){return t.progressBar=e},className:"jsx-194310094 bg-white py-2 progress-bar-content"}))),!this.state.showConsole&&Se("button",{onClick:function(){return t.setState({showConsole:!0})},className:"jsx-194310094 btn btn-light mt-10 rounded"},"Show details"),this.state.showConsole&&Se("div",{className:"jsx-194310094 mt-10"},Se("a",{onClick:function(){return t.setState({showConsole:!1})},className:"jsx-194310094 btn btn-light rounded cursor-pointer"},"Hide details"),Se("a",{target:"_blank",href:"/console",className:"jsx-194310094 ml-2 btn btn-light rounded"},"Download logs")),this.state.showConsole&&Se("div",{className:"jsx-194310094 console mt-10"},Se(Ut,null)),Se(g.default,{id:"194310094"},[".initializing-view.jsx-194310094{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;background-color:black;}",".console.jsx-194310094{width:100%;-webkit-flex:1;-ms-flex:1;flex:1;}",".progress-bar.jsx-194310094{min-width:200px;position:relative;}",".progress-bar-content.jsx-194310094{content:'';position:absolute;top:0;left:0;width:100%;height:100%;-webkit-transform-origin:0 0;-ms-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scaleX(0);-ms-transform:scaleX(0);transform:scaleX(0);}"]))}}]),n}(p.Component),je=(0,v.$j)((function(t){return{fileName:t.app.getIn(["recordingStatus","filename"])}}))(Ce),De=p.createElement;function Ne(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var Ee=function(t){(0,c.Z)(n,t);var e=Ne(n);function n(){return(0,a.Z)(this,n),e.apply(this,arguments)}return(0,s.Z)(n,[{key:"render",value:function(){var t=this;return De("div",{onClick:function(){return t.props.close()},className:"jsx-472049130 overlay"},De("div",{className:"jsx-472049130 modal rounded p-10 shadow text-inverse bg-default border-inverse"},De("h3",{className:"jsx-472049130 text-center text-xl font-bold"},"Tracker accuracy"),De("div",{className:"jsx-472049130 mt-4"},De("p",{className:"jsx-472049130"},"The heatmap highlights the areas where the tracker accuracy",De("strong",{className:"jsx-472049130"},"isn't good")," ","to help you:"),De("ul",{className:"jsx-472049130 list-disc mt-2 ml-6"},De("li",{className:"jsx-472049130"},"Set counter lines where things are well tracked"),De("li",{className:"jsx-472049130"},"Decide if you should eventually change the camera viewpoint"))),De("button",{onClick:function(){return t.props.close()},className:"jsx-472049130 btn btn-primary btn-rounded min-w-100 mt-5 pl-10 pr-10"},"OK")),De(g.default,{id:"472049130"},[".overlay.jsx-472049130{position:fixed;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,0.8);z-index:8;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;}",".modal.jsx-472049130{border:1px solid black;max-width:90%;height:auto;padding:1rem;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;}"]))}}]),n}(p.Component),Ze=p.createElement;function Ie(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var Ae=function(t){(0,c.Z)(n,t);var e=Ie(n);function n(t){var r;return(0,a.Z)(this,n),(r=e.call(this,t)).state={showHelp:!0},r}return(0,s.Z)(n,[{key:"componentDidMount",value:function(){}},{key:"componentWillUnmount",value:function(){}},{key:"clearCanvas",value:function(){this.clearVisibleCanvas()}},{key:"render",value:function(){var t=this;return Ze(p.Fragment,null,this.state.showHelp&&Ze(Ee,{close:function(){return t.setState({showHelp:!1})}}),Ze(U,{mode:A.qI.TRACKER_ACCURACY,fixedResolution:{w:this.props.canvasResolution.get("w")*(0,q._d)().canvasResolutionFactor,h:this.props.canvasResolution.get("h")*(0,q._d)().canvasResolutionFactor},hidden:this.props.hidden,registerClearCanvas:function(e){return t.clearVisibleCanvas=e}}))}}]),n}(p.Component),_e=(0,v.$j)((function(t){return{canvasResolution:t.viewport.get("canvasResolution")}}))(Ae),Pe=p.createElement;function Te(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var Oe=function(t){(0,c.Z)(n,t);var e=Te(n);function n(t){var r;return(0,a.Z)(this,n),(r=e.call(this,t)).state={droppedFile:!1},r}return(0,s.Z)(n,[{key:"componentDidMount",value:function(){this.props.dispatch((0,D.oE)()),this.props.dispatch((0,he.zJ)()),this.props.dispatch((0,N.ZU)()),window.CONFIG=this.props.config.toJS()}},{key:"onDrop",value:function(t){var e=this;t.preventDefault(),this.setState({droppedFile:!0});var n=new FormData;n.append("video",t.dataTransfer.files[0]),y().post("/files",n,{headers:{"Content-Type":"multipart/form-data"}}).then((function(){console.log("success"),e.setState({droppedFile:!1})}),(function(t){console.log("error"),e.setState({droppedFile:!1})}))}},{key:"render",value:function(){var t=this;return Pe("div",{onDragOver:function(t){return t.preventDefault()},onDragStart:function(t){return t.preventDefault()},onDrop:function(e){return t.onDrop(e)},className:"jsx-1468007328 main-page"},"portrait"===this.props.deviceOrientation&&Pe(k,null),!this.props.isListeningToYOLO&&Pe(je,{requestedFileRecording:this.props.requestedFileRecording,droppedFile:this.state.droppedFile}),this.props.isListeningToYOLO&&this.state.droppedFile&&Pe(je,{requestedFileRecording:this.props.requestedFileRecording,droppedFile:this.state.droppedFile}),this.props.isListeningToYOLO&&!this.state.droppedFile&&Pe(p.Fragment,null,Pe(fe,null),this.props.showMenu&&Pe(ke,null),this.props.mode===A.IK.DATAVIEW&&Pe(le,null),this.props.mode===A.IK.CONSOLEVIEW&&Pe(Kt,null),this.props.mode===A.IK.LIVEVIEW&&Pe($,null),this.props.uiSettings.get("counterEnabled")&&this.props.mode===A.IK.COUNTERVIEW&&Pe(Ft,null),this.props.uiSettings.get("pathfinderEnabled")&&Pe(Bt,{hidden:this.props.mode!==A.IK.PATHVIEW}),this.props.uiSettings.get("heatmapEnabled")&&Pe(_e,{hidden:this.props.mode===A.IK.PATHVIEW}),Pe(j,null)),Pe(g.default,{id:"1468007328"},[".main-page.jsx-1468007328{width:100%;height:100%;position:absolute;top:0;left:0;z-index:1;overflow:hidden;}"]))}}]),n}(p.PureComponent),Le=(0,v.$j)((function(t){return{deviceOrientation:t.viewport.get("deviceOrientation"),mode:t.app.get("mode"),isListeningToYOLO:t.app.get("isListeningToYOLO"),requestedFileRecording:t.app.getIn(["recordingStatus","requestedFileRecording"]),showMenu:t.app.get("showMenu"),uiSettings:t.app.get("uiSettings"),config:t.app.get("config")}}))(Oe),Fe=p.createElement;function Me(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=(0,u.Z)(t);if(e){var i=(0,u.Z)(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return(0,l.Z)(this,n)}}var We=function(t){(0,c.Z)(n,t);var e=Me(n);function n(){return(0,a.Z)(this,n),e.apply(this,arguments)}return(0,s.Z)(n,[{key:"render",value:function(){return Fe(m,null,Fe(Le,null))}}],[{key:"getInitialProps",value:function(){var t=(0,o.Z)(i().mark((function t(e){var n,r,o;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=e.store,r=e.isServer,o=e.req,e.query,!r){t.next=10;break}return t.next=4,n.dispatch((0,rt.p6)(o));case 4:return t.next=6,n.dispatch((0,N.f$)(o));case 6:return t.next=8,n.dispatch((0,N.Fu)(o));case 8:return t.next=10,n.dispatch((0,N.ME)(o));case 10:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}]),n}(p.Component)},7263:function(t){t.exports={theme:{extend:{colors:{default:"var(--color-default)","default-soft":"var(--color-default-soft)",inverse:"var(--color-inverse)","inverse-soft":"var(--color-inverse-soft)",primary:"#006CFF"}}},variants:{},plugins:[],purge:!1}},8581:function(t,e,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/",function(){return n(5077)}])}},function(t){t.O(0,[559,774,888,179],(function(){return e=8581,t(t.s=e);var e}));var e=t.O();_N_E=e}]);