{"version": 3, "sources": ["../../lib/recursive-readdir.ts"], "names": ["recursiveReadDir", "dir", "filter", "ignore", "arr", "rootDir", "result", "promises", "readdir", "Promise", "all", "map", "part", "absolutePath", "test", "pathStat", "stat", "isDirectory", "push", "replace", "sort"], "mappings": "+EAAA,sBACA,0BAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACO,cAAeA,CAAAA,gBAAf,CACLC,GADK,CAELC,MAFK,CAGLC,MAHK,CAILC,GAAa,CAAG,EAJX,CAKLC,OAAe,CAAGJ,GALb,CAMc,CACnB,KAAMK,CAAAA,MAAM,CAAG,KAAMC,cAASC,OAAT,CAAiBP,GAAjB,CAArB,CAEA,KAAMQ,CAAAA,OAAO,CAACC,GAAR,CACJJ,MAAM,CAACK,GAAP,CAAW,KAAOC,CAAAA,IAAP,EAAwB,CACjC,KAAMC,CAAAA,YAAY,CAAG,eAAKZ,GAAL,CAAUW,IAAV,CAArB,CACA,GAAIT,MAAM,EAAIA,MAAM,CAACW,IAAP,CAAYF,IAAZ,CAAd,CAAiC,OAEjC,KAAMG,CAAAA,QAAQ,CAAG,KAAMR,cAASS,IAAT,CAAcH,YAAd,CAAvB,CAEA,GAAIE,QAAQ,CAACE,WAAT,EAAJ,CAA4B,CAC1B,KAAMjB,CAAAA,gBAAgB,CAACa,YAAD,CAAeX,MAAf,CAAuBC,MAAvB,CAA+BC,GAA/B,CAAoCC,OAApC,CAAtB,CACA,OACD,CAED,GAAI,CAACH,MAAM,CAACY,IAAP,CAAYF,IAAZ,CAAL,CAAwB,CACtB,OACD,CAEDR,GAAG,CAACc,IAAJ,CAASL,YAAY,CAACM,OAAb,CAAqBd,OAArB,CAA8B,EAA9B,CAAT,EACD,CAhBD,CADI,CAAN,CAoBA,MAAOD,CAAAA,GAAG,CAACgB,IAAJ,EAAP,CACD", "sourcesContent": ["import { promises } from 'fs'\nimport { join } from 'path'\n\n/**\n * Recursively read directory\n * @param  {string} dir Directory to read\n * @param  {RegExp} filter Filter for the file name, only the name part is considered, not the full path\n * @param  {string[]=[]} arr This doesn't have to be provided, it's used for the recursion\n * @param  {string=dir`} rootDir Used to replace the initial path, only the relative path is left, it's faster than path.relative.\n * @returns Promise array holding all relative paths\n */\nexport async function recursiveReadDir(\n  dir: string,\n  filter: RegExp,\n  ignore?: RegExp,\n  arr: string[] = [],\n  rootDir: string = dir\n): Promise<string[]> {\n  const result = await promises.readdir(dir)\n\n  await Promise.all(\n    result.map(async (part: string) => {\n      const absolutePath = join(dir, part)\n      if (ignore && ignore.test(part)) return\n\n      const pathStat = await promises.stat(absolutePath)\n\n      if (pathStat.isDirectory()) {\n        await recursiveReadDir(absolutePath, filter, ignore, arr, rootDir)\n        return\n      }\n\n      if (!filter.test(part)) {\n        return\n      }\n\n      arr.push(absolutePath.replace(rootDir, ''))\n    })\n  )\n\n  return arr.sort()\n}\n"]}