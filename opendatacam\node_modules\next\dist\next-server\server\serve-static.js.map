{"version": 3, "sources": ["../../../next-server/server/serve-static.ts"], "names": ["serveStatic", "req", "res", "path", "Promise", "resolve", "reject", "on", "err", "Error", "code", "pipe", "getContentType", "extWithoutDot", "mime", "send", "getType", "lookup", "getExtension", "contentType", "extension"], "mappings": "6IACA,qE,mFAEO,QAASA,CAAAA,WAAT,CACLC,GADK,CAELC,GAFK,CAGLC,IAHK,CAIU,CACf,MAAO,IAAIC,CAAAA,OAAJ,CAAY,CAACC,OAAD,CAAUC,MAAV,GAAqB,CACtC,kBAAKL,GAAL,CAAUE,IAAV,EACGI,EADH,CACM,WADN,CACmB,IAAM,CACrB;AACA,KAAMC,CAAAA,GAAQ,CAAG,GAAIC,CAAAA,KAAJ,CAAU,qBAAV,CAAjB,CACAD,GAAG,CAACE,IAAJ,CAAW,QAAX,CACAJ,MAAM,CAACE,GAAD,CAAN,CACD,CANH,EAOGD,EAPH,CAOM,OAPN,CAOeD,MAPf,EAQGK,IARH,CAQQT,GARR,EASGK,EATH,CASM,QATN,CASgBF,OAThB,EAUD,CAXM,CAAP,CAYD,CAEM,QAASO,CAAAA,cAAT,CAAwBC,aAAxB,CAA8D,CACnE,KAAM,CAAEC,IAAF,EAAWC,aAAjB,CACA,GAAI,WAAaD,CAAAA,IAAjB,CAAuB,CACrB;AACA,MAAOA,CAAAA,IAAI,CAACE,OAAL,CAAaH,aAAb,CAAP,CACD,CACD;AACA,MAAQC,CAAAA,IAAD,CAAcG,MAAd,CAAqBJ,aAArB,CAAP,CACD,CAEM,QAASK,CAAAA,YAAT,CAAsBC,WAAtB,CAA0D,CAC/D,KAAM,CAAEL,IAAF,EAAWC,aAAjB,CACA,GAAI,gBAAkBD,CAAAA,IAAtB,CAA4B,CAC1B;AACA,MAAOA,CAAAA,IAAI,CAACI,YAAL,CAAkBC,WAAlB,CAAP,CACD,CACD;AACA,MAAQL,CAAAA,IAAD,CAAcM,SAAd,CAAwBD,WAAxB,CAAP,CACD", "sourcesContent": ["import { IncomingMessage, ServerResponse } from 'http'\nimport send from 'next/dist/compiled/send'\n\nexport function serveStatic(\n  req: IncomingMessage,\n  res: ServerResponse,\n  path: string\n): Promise<void> {\n  return new Promise((resolve, reject) => {\n    send(req, path)\n      .on('directory', () => {\n        // We don't allow directories to be read.\n        const err: any = new Error('No directory access')\n        err.code = 'ENOENT'\n        reject(err)\n      })\n      .on('error', reject)\n      .pipe(res)\n      .on('finish', resolve)\n  })\n}\n\nexport function getContentType(extWithoutDot: string): string | null {\n  const { mime } = send\n  if ('getType' in mime) {\n    // 2.0\n    return mime.getType(extWithoutDot)\n  }\n  // 1.0\n  return (mime as any).lookup(extWithoutDot)\n}\n\nexport function getExtension(contentType: string): string | null {\n  const { mime } = send\n  if ('getExtension' in mime) {\n    // 2.0\n    return mime.getExtension(contentType)\n  }\n  // 1.0\n  return (mime as any).extension(contentType)\n}\n"]}