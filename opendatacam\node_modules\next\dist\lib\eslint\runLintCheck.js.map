{"version": 3, "sources": ["../../../lib/eslint/runLintCheck.ts"], "names": ["linteableFileTypes", "lint", "deps", "baseDir", "pagesDir", "eslintrcFile", "pkgJsonPath", "ESLint", "resolved", "eslintVersion", "cwd", "name", "semver", "lt", "Log", "warn", "options", "useEslintrc", "baseConfig", "eslint", "nextEslintPluginIsEnabled", "pagesDirRules", "configFile", "completeConfig", "calculateConfigForFile", "plugins", "includes", "updatedPagesDir", "rule", "rules", "replace", "results", "lintFiles", "join", "getErrorResults", "length", "CompileError", "runLintCheck", "pages", "promises", "readdir", "some", "page", "eslintConfig", "pkgJsonEslintConfig", "eslintIntent", "firstTimeSetup", "err"], "mappings": "uEAAA,sBACA,0BAEA,0EACA,yEAEA,kDACA,8CACA,wDACA,yDAEA,8CACA,uEAKA,mE,w4BAOA,KAAMA,CAAAA,kBAAkB,CAAG,CAAC,KAAD,CAAQ,IAAR,CAAc,IAAd,CAAoB,KAApB,CAA3B,CAEA,cAAeC,CAAAA,IAAf,CACEC,IADF,CAEEC,OAFF,CAGEC,QAHF,CAIEC,YAJF,CAKEC,WALF,CAM0B,2BACxB;AACA,KAAM,CAAEC,MAAF,EAAa,yBAAaL,IAAI,CAACM,QAAlB,gDAAnB,CAEA,GAAI,CAACD,MAAL,CAAa,CACX,KAAME,CAAAA,aAA4B,CAAG,KAAM,yCAAkB,CAC3DC,GAAG,CAAEP,OADsD,CAE3DQ,IAAI,CAAE,QAFqD,CAAlB,CAA3C,CAKA,GAAIF,aAAa,EAAIG,gBAAOC,EAAP,CAAUJ,aAAV,CAAyB,OAAzB,CAArB,CAAwD,CACtDK,GAAG,CAACC,IAAJ,CACG,0DAAyDN,aAAc,0EAD1E,EAGD,CACD,MAAO,KAAP,CACD,CAED,GAAIO,CAAAA,OAAY,CAAG,CACjBC,WAAW,CAAE,IADI,CAEjBC,UAAU,CAAE,EAFK,CAAnB,CAIA,GAAIC,CAAAA,MAAM,CAAG,GAAIZ,CAAAA,MAAJ,CAAWS,OAAX,CAAb,CAEA,GAAII,CAAAA,yBAAyB,CAAG,KAAhC,CACA,KAAMC,CAAAA,aAAa,CAAG,CAAC,mCAAD,CAAtB,CAEA,IAAK,KAAMC,CAAAA,UAAX,GAAyB,CAACjB,YAAD,CAAeC,WAAf,CAAzB,CAAsD,2BACpD,GAAI,CAACgB,UAAL,CAAiB,SAEjB,KAAMC,CAAAA,cAAsB,CAAG,KAAMJ,CAAAA,MAAM,CAACK,sBAAP,CACnCF,UADmC,CAArC,CAIA,0BAAIC,cAAc,CAACE,OAAnB,SAAI,sBAAwBC,QAAxB,CAAiC,YAAjC,CAAJ,CAAoD,CAClDN,yBAAyB,CAAG,IAA5B,CACA,MACD,CACF,CAED,GAAIA,yBAAJ,CAA+B,CAC7B,GAAIO,CAAAA,eAAe,CAAG,KAAtB,CAEA,IAAK,KAAMC,CAAAA,IAAX,GAAmBP,CAAAA,aAAnB,CAAkC,oBAChC,GACE,UAACL,OAAO,CAACE,UAAR,CAAoBW,KAArB,SAAC,OAA4BD,IAA5B,CAAD,GACA,WAACZ,OAAO,CAACE,UAAR,CAAoBW,KAArB,SAAC,QACCD,IAAI,CAACE,OAAL,CAAa,YAAb,CAA2B,yBAA3B,CADD,CAAD,CAFF,CAKE,CACA,GAAI,CAACd,OAAO,CAACE,UAAR,CAAoBW,KAAzB,CAAgC,CAC9Bb,OAAO,CAACE,UAAR,CAAoBW,KAApB,CAA4B,EAA5B,CACD,CACDb,OAAO,CAACE,UAAR,CAAoBW,KAApB,CAA0BD,IAA1B,EAAkC,CAAC,CAAD,CAAIxB,QAAJ,CAAlC,CACAuB,eAAe,CAAG,IAAlB,CACD,CACF,CAED,GAAIA,eAAJ,CAAqB,CACnBR,MAAM,CAAG,GAAIZ,CAAAA,MAAJ,CAAWS,OAAX,CAAT,CACD,CACF,CAED,KAAMe,CAAAA,OAAO,CAAG,KAAMZ,CAAAA,MAAM,CAACa,SAAP,CAAiB,CACpC,GAAE5B,QAAS,UAASJ,kBAAkB,CAACiC,IAAnB,CAAwB,GAAxB,CAA6B,GADb,CAAjB,CAAtB,CAIA,GAAI,wBAAA1B,MAAM,CAAC2B,eAAP,CAAuBH,OAAvB,sCAAiCI,MAAjC,EAA0C,CAA9C,CAAiD,CAC/C,KAAM,IAAIC,2BAAJ,CAAiB,KAAM,mCAAcjC,OAAd,CAAuB4B,OAAvB,CAAvB,CAAN,CACD,CACD,MAAO,CAAAA,OAAO,MAAP,QAAAA,OAAO,CAAEI,MAAT,EAAkB,CAAlB,CAAsB,mCAAchC,OAAd,CAAuB4B,OAAvB,CAAtB,CAAwD,IAA/D,CACD,CAEM,cAAeM,CAAAA,YAAf,CACLlC,OADK,CAELC,QAFK,CAGmB,CACxB,GAAI,kCACF;AACA,KAAMkC,CAAAA,KAAK,CAAG,KAAMC,cAASC,OAAT,CAAiBpC,QAAjB,CAApB,CACA,GACE,CAACkC,KAAK,CAACG,IAAN,CAAYC,IAAD,EACV1C,kBAAkB,CAAC0B,QAAnB,CAA4B,kBAAQgB,IAAR,EAAcZ,OAAd,CAAsB,GAAtB,CAA2B,EAA3B,CAA5B,CADD,CADH,CAIE,CACA,MAAO,KAAP,CACD,CAED;AACA,KAAMzB,CAAAA,YAAY,gBACf,KAAM,oBACL,CACE,cADF,CAEE,gBAFF,CAGE,eAHF,CAIE,gBAJF,CAKE,WALF,CADK,CAQL,CACEK,GAAG,CAAEP,OADP,CARK,CADS,sBAYV,IAZR,CAcA,KAAMG,CAAAA,WAAW,iBAAI,KAAM,oBAAO,cAAP,CAAuB,CAAEI,GAAG,CAAEP,OAAP,CAAvB,CAAV,uBAAuD,IAAxE,CAEA,KAAM,CAAEwC,YAAY,CAAEC,mBAAmB,CAAG,IAAtC,EAA+C,CAAC,CAACtC,WAAF,CACjD,yBAAaA,WAAb,gDADiD,CAEjD,EAFJ,CAIA;AACA,KAAMuC,CAAAA,YAAY,CAAG,KAAM,iCAAcxC,YAAd,CAA4BuC,mBAA5B,CAA3B,CAEA,GAAI,CAACC,YAAL,CAAmB,CACjB,MAAO,KAAP,CACD,CAED,KAAMC,CAAAA,cAAc,CAAGD,YAAY,CAACC,cAApC,CAEA;AACA,KAAM5C,CAAAA,IAA2B,CAAG,KAAM,uDACxCC,OADwC,CAExC,KAFwC,CAGxC,CAAC,CAAC0C,YAHsC,CAIxCxC,YAJwC,CAA1C,CAOA;AACA,GAAIyC,cAAJ,CAAoB,KAAM,2CAAmBzC,YAAnB,CAAiCC,WAAjC,CAAN,CAEpB;AACA,MAAO,MAAML,CAAAA,IAAI,CAACC,IAAD,CAAOC,OAAP,CAAgBC,QAAhB,CAA0BC,YAA1B,CAAwCC,WAAxC,CAAjB,CACD,CAAC,MAAOyC,GAAP,CAAY,CACZ,KAAMA,CAAAA,GAAN,CACD,CACF", "sourcesContent": ["import { promises } from 'fs'\nimport { extname } from 'path'\n\nimport findUp from 'next/dist/compiled/find-up'\nimport semver from 'next/dist/compiled/semver'\n\nimport { formatResults } from './customFormatter'\nimport { getLintIntent } from './getLintIntent'\nimport { writeDefaultConfig } from './writeDefaultConfig'\nimport { getPackageVersion } from '../get-package-version'\n\nimport { CompileError } from '../compile-error'\nimport {\n  hasNecessaryDependencies,\n  NecessaryDependencies,\n} from '../has-necessary-dependencies'\n\nimport * as Log from '../../build/output/log'\n\ntype Config = {\n  plugins: string[]\n  rules: { [key: string]: Array<number | string> }\n}\n\nconst linteableFileTypes = ['jsx', 'js', 'ts', 'tsx']\n\nasync function lint(\n  deps: NecessaryDependencies,\n  baseDir: string,\n  pagesDir: string,\n  eslintrcFile: string | null,\n  pkgJsonPath: string | null\n): Promise<string | null> {\n  // Load ESLint after we're sure it exists:\n  const { ESLint } = await import(deps.resolved)\n\n  if (!ESLint) {\n    const eslintVersion: string | null = await getPackageVersion({\n      cwd: baseDir,\n      name: 'eslint',\n    })\n\n    if (eslintVersion && semver.lt(eslintVersion, '7.0.0')) {\n      Log.warn(\n        `Your project has an older version of ESLint installed (${eslintVersion}). Please upgrade to v7 or later to run ESLint during the build process.`\n      )\n    }\n    return null\n  }\n\n  let options: any = {\n    useEslintrc: true,\n    baseConfig: {},\n  }\n  let eslint = new ESLint(options)\n\n  let nextEslintPluginIsEnabled = false\n  const pagesDirRules = ['@next/next/no-html-link-for-pages']\n\n  for (const configFile of [eslintrcFile, pkgJsonPath]) {\n    if (!configFile) continue\n\n    const completeConfig: Config = await eslint.calculateConfigForFile(\n      configFile\n    )\n\n    if (completeConfig.plugins?.includes('@next/next')) {\n      nextEslintPluginIsEnabled = true\n      break\n    }\n  }\n\n  if (nextEslintPluginIsEnabled) {\n    let updatedPagesDir = false\n\n    for (const rule of pagesDirRules) {\n      if (\n        !options.baseConfig!.rules?.[rule] &&\n        !options.baseConfig!.rules?.[\n          rule.replace('@next/next', '@next/babel-plugin-next')\n        ]\n      ) {\n        if (!options.baseConfig!.rules) {\n          options.baseConfig!.rules = {}\n        }\n        options.baseConfig!.rules[rule] = [1, pagesDir]\n        updatedPagesDir = true\n      }\n    }\n\n    if (updatedPagesDir) {\n      eslint = new ESLint(options)\n    }\n  }\n\n  const results = await eslint.lintFiles([\n    `${pagesDir}/**/*.{${linteableFileTypes.join(',')}}`,\n  ])\n\n  if (ESLint.getErrorResults(results)?.length > 0) {\n    throw new CompileError(await formatResults(baseDir, results))\n  }\n  return results?.length > 0 ? formatResults(baseDir, results) : null\n}\n\nexport async function runLintCheck(\n  baseDir: string,\n  pagesDir: string\n): Promise<string | null> {\n  try {\n    // Check if any pages exist that can be linted\n    const pages = await promises.readdir(pagesDir)\n    if (\n      !pages.some((page) =>\n        linteableFileTypes.includes(extname(page).replace('.', ''))\n      )\n    ) {\n      return null\n    }\n\n    // Find user's .eslintrc file\n    const eslintrcFile =\n      (await findUp(\n        [\n          '.eslintrc.js',\n          '.eslintrc.yaml',\n          '.eslintrc.yml',\n          '.eslintrc.json',\n          '.eslintrc',\n        ],\n        {\n          cwd: baseDir,\n        }\n      )) ?? null\n\n    const pkgJsonPath = (await findUp('package.json', { cwd: baseDir })) ?? null\n\n    const { eslintConfig: pkgJsonEslintConfig = null } = !!pkgJsonPath\n      ? await import(pkgJsonPath!)\n      : {}\n\n    // Check if the project uses ESLint\n    const eslintIntent = await getLintIntent(eslintrcFile, pkgJsonEslintConfig)\n\n    if (!eslintIntent) {\n      return null\n    }\n\n    const firstTimeSetup = eslintIntent.firstTimeSetup\n\n    // Ensure ESLint and necessary plugins and configs are installed:\n    const deps: NecessaryDependencies = await hasNecessaryDependencies(\n      baseDir,\n      false,\n      !!eslintIntent,\n      eslintrcFile\n    )\n\n    // Create the user's eslintrc config for them\n    if (firstTimeSetup) await writeDefaultConfig(eslintrcFile, pkgJsonPath)\n\n    // Run ESLint\n    return await lint(deps, baseDir, pagesDir, eslintrcFile, pkgJsonPath)\n  } catch (err) {\n    throw err\n  }\n}\n"]}