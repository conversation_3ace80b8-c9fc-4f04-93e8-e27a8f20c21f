{"version": 3, "sources": ["../../../../build/webpack/plugins/serverless-plugin.ts"], "names": ["ServerlessPlugin", "apply", "compiler", "hooks", "compilation", "tap", "hook", "isWebpack5", "optimizeChunks", "optimizeChunksBasic", "chunks", "chunk", "hasEntryModule", "dynamicChunks", "getAllAsyncChunks", "dynamicChunk", "module", "chunkGraph", "getChunkModulesIterable", "addModule", "modulesIterable", "GraphHelpers", "connectChunkAndModule"], "mappings": "qEACA,2DAEA;AACA;AACA;AACA;AACA,GAEO,KAAMA,CAAAA,gBAAiB,CAC5BC,KAAK,CAACC,QAAD,CAA6B,CAChCA,QAAQ,CAACC,KAAT,CAAeC,WAAf,CAA2BC,GAA3B,CAA+B,kBAA/B,CAAoDD,WAAD,EAAiB,CAClE,KAAME,CAAAA,IAAI,CAAGC,oBACTH,WAAW,CAACD,KAAZ,CAAkBK,cADT,CAETJ,WAAW,CAACD,KAAZ,CAAkBM,mBAFtB,CAIAH,IAAI,CAACD,GAAL,CAAS,kBAAT,CAA8BK,MAAD,EAAY,CACvC,IAAK,KAAMC,CAAAA,KAAX,GAAoBD,CAAAA,MAApB,CAA4B,CAC1B;AACA,GAAI,CAACC,KAAK,CAACC,cAAN,EAAL,CAA6B,CAC3B,SACD,CAED;AACA,KAAMC,CAAAA,aAAa,CAAGF,KAAK,CAACG,iBAAN,EAAtB,CACA,IAAK,KAAMC,CAAAA,YAAX,GAA2BF,CAAAA,aAA3B,CAA0C,CACxC,GAAIN,mBAAJ,CAAgB,CACd;AACA,IAAK,KAAMS,CAAAA,MAAX,GAAqBZ,CAAAA,WAAW,CAACa,UAAZ,CAAuBC,uBAAvB,CACnBP,KADmB,CAArB,CAEG,CACD;AACAA,KAAK,CAACQ,SAAN,CAAgBH,MAAhB,EACD,CACD,SACD,CAED,IAAK,KAAMA,CAAAA,MAAX,GAAqBD,CAAAA,YAAY,CAACK,eAAlC,CAAmD,CACjD;AACAC,sBAAaC,qBAAb,CAAmCX,KAAnC,CAA0CK,MAA1C,EACD,CACF,CACF,CACF,CA3BD,EA4BD,CAjCD,EAkCD,CApC2B,C", "sourcesContent": ["import { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { isWebpack5, GraphHelpers } from 'next/dist/compiled/webpack/webpack'\n\n/**\n * Makes sure there are no dynamic chunks when the target is serverless\n * The dynamic chunks are integrated back into their parent chunk\n * This is to make sure there is a single render bundle instead of that bundle importing dynamic chunks\n */\n\nexport class ServerlessPlugin {\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.compilation.tap('ServerlessPlugin', (compilation) => {\n      const hook = isWebpack5\n        ? compilation.hooks.optimizeChunks\n        : compilation.hooks.optimizeChunksBasic\n\n      hook.tap('ServerlessPlugin', (chunks) => {\n        for (const chunk of chunks) {\n          // If chunk is not an entry point skip them\n          if (!chunk.hasEntryModule()) {\n            continue\n          }\n\n          // Async chunks are usages of import() for example\n          const dynamicChunks = chunk.getAllAsyncChunks()\n          for (const dynamicChunk of dynamicChunks) {\n            if (isWebpack5) {\n              // @ts-ignore TODO: Remove ignore when webpack 5 is stable\n              for (const module of compilation.chunkGraph.getChunkModulesIterable(\n                chunk\n              )) {\n                // Add module back into the entry chunk\n                chunk.addModule(module)\n              }\n              continue\n            }\n\n            for (const module of dynamicChunk.modulesIterable) {\n              // Webpack 4 has separate GraphHelpers\n              GraphHelpers.connectChunkAndModule(chunk, module)\n            }\n          }\n        }\n      })\n    })\n  }\n}\n"]}