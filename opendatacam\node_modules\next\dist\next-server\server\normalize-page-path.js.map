{"version": 3, "sources": ["../../../next-server/server/normalize-page-path.ts"], "names": ["normalizePagePath", "page", "test", "startsWith", "resolvedPage", "posix", "normalize", "Error"], "mappings": "6IAAA,0BAEA,4D,oIAEO,QAASA,CAAAA,iBAAT,CAA2BC,IAA3B,CAAiD,CACtD;AACA,GAAIA,IAAI,GAAK,GAAb,CAAkB,CAChBA,IAAI,CAAG,QAAP,CACD,CAFD,IAEO,IAAI,iBAAiBC,IAAjB,CAAsBD,IAAtB,CAAJ,CAAiC,CACtCA,IAAI,CAAI,SAAQA,IAAK,EAArB,CACD,CACD;AACA,GAAI,CAACA,IAAI,CAACE,UAAL,CAAgB,GAAhB,CAAL,CAA2B,CACzBF,IAAI,CAAI,IAAGA,IAAK,EAAhB,CACD,CACD;AACA,KAAMG,CAAAA,YAAY,CAAGC,YAAMC,SAAN,CAAgBL,IAAhB,CAArB,CACA,GAAIA,IAAI,GAAKG,YAAb,CAA2B,CACzB,KAAM,IAAIG,CAAAA,KAAJ,CACH,yCAAwCN,IAAK,IAAGG,YAAa,EAD1D,CAAN,CAGD,CACD,MAAOH,CAAAA,IAAP,CACD", "sourcesContent": ["import { posix } from 'path'\n\nexport { normalizePathSep, denormalizePagePath } from './denormalize-page-path'\n\nexport function normalizePagePath(page: string): string {\n  // If the page is `/` we need to append `/index`, otherwise the returned directory root will be bundles instead of pages\n  if (page === '/') {\n    page = '/index'\n  } else if (/^\\/index(\\/|$)/.test(page)) {\n    page = `/index${page}`\n  }\n  // Resolve on anything that doesn't start with `/`\n  if (!page.startsWith('/')) {\n    page = `/${page}`\n  }\n  // Throw when using ../ etc in the pathname\n  const resolvedPage = posix.normalize(page)\n  if (page !== resolvedPage) {\n    throw new Error(\n      `Requested and resolved page mismatch: ${page} ${resolvedPage}`\n    )\n  }\n  return page\n}\n"]}