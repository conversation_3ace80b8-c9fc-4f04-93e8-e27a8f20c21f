{"version": 3, "sources": ["../../../../build/webpack/plugins/nextjs-ssr-module-cache.ts"], "names": ["SSR_MODULE_CACHE_FILENAME", "NextJsSsrImportPlugin", "constructor", "options", "apply", "compiler", "outputPath", "hooks", "emit", "tapAsync", "compilation", "callback", "assets", "sources", "RawSource", "tap", "mainTemplate", "localVars", "intercept", "register", "tapInfo", "name", "originalFn", "fn", "source", "chunk", "pagePath", "relativePathToBaseDir", "relativePathToBaseDirNormalized", "replace", "webpack", "Template", "asString"], "mappings": "4DAAA,2DACA,0BACA,qH,mFACA,KAAMA,CAAAA,yBAAyB,CAAG,qBAAlC,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,KAAMC,CAAAA,qBAAsB,CAGzCC,WAAW,CAACC,OAAD,CAAkC,MAFrCA,OAEqC,QAC3C,KAAKA,OAAL,CAAeA,OAAf,CACD,CACDC,KAAK,CAACC,QAAD,CAA6B,CAChC,KAAM,CAAEC,UAAF,EAAiB,KAAKH,OAA5B,CACAE,QAAQ,CAACE,KAAT,CAAeC,IAAf,CAAoBC,QAApB,CACE,sBADF,CAEE,CAACC,WAAD,CAAcC,QAAd,GAA2B,CACzBD,WAAW,CAACE,MAAZ,CAAmBZ,yBAAnB,EAAgD,GAAIa,kBAAQC,SAAZ,CAAuB;AAC/E;AACA;AACA,OAHwD,CAAhD,CAIAH,QAAQ,GACT,CARH,EAUAN,QAAQ,CAACE,KAAT,CAAeG,WAAf,CAA2BK,GAA3B,CACE,sBADF,CAEGL,WAAD,EAAsB,CACpBA,WAAW,CAACM,YAAZ,CAAyBT,KAAzB,CAA+BU,SAA/B,CAAyCC,SAAzC,CAAmD,CACjDC,QAAQ,CAACC,OAAD,CAAe,CACrB,GAAIA,OAAO,CAACC,IAAR,GAAiB,cAArB,CAAqC,CACnC,KAAMC,CAAAA,UAAU,CAAGF,OAAO,CAACG,EAA3B,CACAH,OAAO,CAACG,EAAR,CAAa,CAACC,MAAD,CAAcC,KAAd,GAA6B,CACxC;AACA;AACA;AAEA,GAAI,CAAC,oCAAuBA,KAAK,CAACJ,IAA7B,CAAL,CAAyC,CACvC,MAAOC,CAAAA,UAAU,CAACE,MAAD,CAASC,KAAT,CAAjB,CACD,CACD,KAAMC,CAAAA,QAAQ,CAAG,eAAKpB,UAAL,CAAiB,kBAAQmB,KAAK,CAACJ,IAAd,CAAjB,CAAjB,CACA,GAAIM,CAAAA,qBAAqB,CAAG,mBAC1BD,QAD0B,CAE1B,eAAKpB,UAAL,CAAiBN,yBAAjB,CAF0B,CAA5B,CAKA;AACA;AACA,KAAM4B,CAAAA,+BAA+B,CAAGD,qBAAqB,CAACE,OAAtB,CACtC,KADsC,CAEtC,GAFsC,CAAxC,CAIA,MAAQC,iBAAD,CAAiBC,QAAjB,CAA0BC,QAA1B,CAAmC,CACxCR,MADwC,CAExC,qBAFwC,CAGvC,mCAAkCI,+BAAgC,KAH3B,CAAnC,CAAP,CAKD,CAzBD,CA0BD,CACD,MAAOR,CAAAA,OAAP,CACD,CAhCgD,CAAnD,EAkCD,CArCH,EAuCD,CAzDwC,C", "sourcesContent": ["import { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport { join, relative, dirname } from 'path'\nimport getRouteFromEntrypoint from '../../../next-server/server/get-route-from-entrypoint'\nconst SSR_MODULE_CACHE_FILENAME = 'ssr-module-cache.js'\n\n// By default webpack keeps initialized modules per-module.\n// This means that if you have 2 entrypoints loaded into the same app\n// they will *not* share the same instance\n// This creates many issues when developers / libraries rely on the singleton pattern\n// As this pattern assumes every module will have 1 instance\n// This plugin overrides webpack's code generation step to replace `installedModules`\n// The replacement is a require for a file that's also generated here that only exports an empty object\n// Because of Node.js's single instance modules this makes webpack share all initialized instances\n// Do note that this module is only geared towards the `node` compilation target.\n// For the client side compilation we use `runtimeChunk: 'single'`\nexport default class NextJsSsrImportPlugin {\n  private options: { outputPath: string }\n\n  constructor(options: { outputPath: string }) {\n    this.options = options\n  }\n  apply(compiler: webpack.Compiler) {\n    const { outputPath } = this.options\n    compiler.hooks.emit.tapAsync(\n      'NextJsSSRModuleCache',\n      (compilation, callback) => {\n        compilation.assets[SSR_MODULE_CACHE_FILENAME] = new sources.RawSource(`\n      /* This cache is used by webpack for instantiated modules */\n      module.exports = {}\n      `)\n        callback()\n      }\n    )\n    compiler.hooks.compilation.tap(\n      'NextJsSSRModuleCache',\n      (compilation: any) => {\n        compilation.mainTemplate.hooks.localVars.intercept({\n          register(tapInfo: any) {\n            if (tapInfo.name === 'MainTemplate') {\n              const originalFn = tapInfo.fn\n              tapInfo.fn = (source: any, chunk: any) => {\n                // If the chunk is not part of the pages directory we have to keep the original behavior,\n                // otherwise webpack will error out when the file is used before the compilation finishes\n                // this is the case with mini-css-extract-plugin\n\n                if (!getRouteFromEntrypoint(chunk.name)) {\n                  return originalFn(source, chunk)\n                }\n                const pagePath = join(outputPath, dirname(chunk.name))\n                let relativePathToBaseDir = relative(\n                  pagePath,\n                  join(outputPath, SSR_MODULE_CACHE_FILENAME)\n                )\n\n                // Make sure even in windows, the path looks like in unix\n                // Node.js require system will convert it accordingly\n                const relativePathToBaseDirNormalized = relativePathToBaseDir.replace(\n                  /\\\\/g,\n                  '/'\n                )\n                return (webpack as any).Template.asString([\n                  source,\n                  '// The module cache',\n                  `var installedModules = require('${relativePathToBaseDirNormalized}');`,\n                ])\n              }\n            }\n            return tapInfo\n          },\n        })\n      }\n    )\n  }\n}\n"]}