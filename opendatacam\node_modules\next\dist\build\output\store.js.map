{"version": 3, "sources": ["../../../build/output/store.ts"], "names": ["store", "appUrl", "bindAddr", "bootstrap", "lastStore", "hasStoreChanged", "nextStore", "Set", "Object", "keys", "every", "key", "is", "subscribe", "state", "Log", "ready", "loading", "wait", "errors", "error", "cleanError", "indexOf", "matches", "match", "prop", "split", "shift", "substr", "console", "log", "warnings", "warn", "join", "info", "typeChecking", "event"], "mappings": "0DAAA,6EACA,gFAEA,kD,w4BAcO,KAAMA,CAAAA,KAAK,CAAG,sBAAyB,CAC5CC,MAAM,CAAE,IADoC,CAE5CC,QAAQ,CAAE,IAFkC,CAG5CC,SAAS,CAAE,IAHiC,CAAzB,CAAd,C,oBAMP,GAAIC,CAAAA,SAAsB,CAAG,CAAEH,MAAM,CAAE,IAAV,CAAgBC,QAAQ,CAAE,IAA1B,CAAgCC,SAAS,CAAE,IAA3C,CAA7B,CACA,QAASE,CAAAA,eAAT,CAAyBC,SAAzB,CAAiD,CAC/C,GACG,CACC,GAAG,GAAIC,CAAAA,GAAJ,CAAQ,CAAC,GAAGC,MAAM,CAACC,IAAP,CAAYL,SAAZ,CAAJ,CAA4B,GAAGI,MAAM,CAACC,IAAP,CAAYH,SAAZ,CAA/B,CAAR,CADJ,CAAD,CAE+BI,KAF/B,CAEsCC,GAAD,EACnCH,MAAM,CAACI,EAAP,CAAUR,SAAS,CAACO,GAAD,CAAnB,CAA0BL,SAAS,CAACK,GAAD,CAAnC,CAHF,CADF,CAME,CACA,MAAO,MAAP,CACD,CAEDP,SAAS,CAAGE,SAAZ,CACA,MAAO,KAAP,CACD,CAEDN,KAAK,CAACa,SAAN,CAAiBC,KAAD,EAAW,CACzB,GAAI,CAACT,eAAe,CAACS,KAAD,CAApB,CAA6B,CAC3B,OACD,CAED,GAAIA,KAAK,CAACX,SAAV,CAAqB,CACnB,GAAIW,KAAK,CAACb,MAAV,CAAkB,CAChBc,GAAG,CAACC,KAAJ,CAAW,qBAAoBF,KAAK,CAACZ,QAAS,UAASY,KAAK,CAACb,MAAO,EAApE,EACD,CACD,OACD,CAED,GAAIa,KAAK,CAACG,OAAV,CAAmB,CACjBF,GAAG,CAACG,IAAJ,CAAS,cAAT,EACA,OACD,CAED,GAAIJ,KAAK,CAACK,MAAV,CAAkB,CAChBJ,GAAG,CAACK,KAAJ,CAAUN,KAAK,CAACK,MAAN,CAAa,CAAb,CAAV,EAEA,KAAME,CAAAA,UAAU,CAAG,uBAAUP,KAAK,CAACK,MAAN,CAAa,CAAb,CAAV,CAAnB,CACA,GAAIE,UAAU,CAACC,OAAX,CAAmB,aAAnB,EAAoC,CAAC,CAAzC,CAA4C,CAC1C,KAAMC,CAAAA,OAAO,CAAGF,UAAU,CAACG,KAAX,CAAiB,SAAjB,CAAhB,CACA,GAAID,OAAJ,CAAa,CACX,IAAK,KAAMC,CAAAA,KAAX,GAAoBD,CAAAA,OAApB,CAA6B,CAC3B,KAAME,CAAAA,IAAI,CAAG,CAACD,KAAK,CAACE,KAAN,CAAY,GAAZ,EAAiBC,KAAjB,IAA4B,EAA7B,EAAiCC,MAAjC,CAAwC,CAAxC,CAAb,CACAC,OAAO,CAACC,GAAR,CACG,oBAAmBL,IAAK,oDAAmDA,IAAK,8DADnF,EAGD,CACD,OACD,CACF,CAED,OACD,CAED,GAAIX,KAAK,CAACiB,QAAV,CAAoB,CAClBhB,GAAG,CAACiB,IAAJ,CAASlB,KAAK,CAACiB,QAAN,CAAeE,IAAf,CAAoB,MAApB,CAAT,EACA,GAAInB,KAAK,CAACb,MAAV,CAAkB,CAChBc,GAAG,CAACmB,IAAJ,CAAU,YAAWpB,KAAK,CAACb,MAAO,EAAlC,EACD,CACD,OACD,CAED,GAAIa,KAAK,CAACqB,YAAV,CAAwB,CACtBpB,GAAG,CAACmB,IAAJ,CAAS,wDAAT,EACA,OACD,CAEDnB,GAAG,CAACqB,KAAJ,CAAU,uBAAV,EACD,CAnDD", "sourcesContent": ["import createStore from 'next/dist/compiled/unistore'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\n\nimport * as Log from './log'\n\nexport type OutputState =\n  | { bootstrap: true; appUrl: string | null; bindAddr: string | null }\n  | ({ bootstrap: false; appUrl: string | null; bindAddr: string | null } & (\n      | { loading: true }\n      | {\n          loading: false\n          typeChecking: boolean\n          errors: string[] | null\n          warnings: string[] | null\n        }\n    ))\n\nexport const store = createStore<OutputState>({\n  appUrl: null,\n  bindAddr: null,\n  bootstrap: true,\n})\n\nlet lastStore: OutputState = { appUrl: null, bindAddr: null, bootstrap: true }\nfunction hasStoreChanged(nextStore: OutputState) {\n  if (\n    ([\n      ...new Set([...Object.keys(lastStore), ...Object.keys(nextStore)]),\n    ] as Array<keyof OutputState>).every((key) =>\n      Object.is(lastStore[key], nextStore[key])\n    )\n  ) {\n    return false\n  }\n\n  lastStore = nextStore\n  return true\n}\n\nstore.subscribe((state) => {\n  if (!hasStoreChanged(state)) {\n    return\n  }\n\n  if (state.bootstrap) {\n    if (state.appUrl) {\n      Log.ready(`started server on ${state.bindAddr}, url: ${state.appUrl}`)\n    }\n    return\n  }\n\n  if (state.loading) {\n    Log.wait('compiling...')\n    return\n  }\n\n  if (state.errors) {\n    Log.error(state.errors[0])\n\n    const cleanError = stripAnsi(state.errors[0])\n    if (cleanError.indexOf('SyntaxError') > -1) {\n      const matches = cleanError.match(/\\[.*\\]=/)\n      if (matches) {\n        for (const match of matches) {\n          const prop = (match.split(']').shift() || '').substr(1)\n          console.log(\n            `AMP bind syntax [${prop}]='' is not supported in JSX, use 'data-amp-bind-${prop}' instead. https://nextjs.org/docs/messages/amp-bind-jsx-alt`\n          )\n        }\n        return\n      }\n    }\n\n    return\n  }\n\n  if (state.warnings) {\n    Log.warn(state.warnings.join('\\n\\n'))\n    if (state.appUrl) {\n      Log.info(`ready on ${state.appUrl}`)\n    }\n    return\n  }\n\n  if (state.typeChecking) {\n    Log.info('bundled successfully, waiting for typecheck results...')\n    return\n  }\n\n  Log.event('compiled successfully')\n})\n"]}