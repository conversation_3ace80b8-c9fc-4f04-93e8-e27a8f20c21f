{"version": 3, "sources": ["../../build/write-build-id.ts"], "names": ["writeBuildId", "distDir", "buildId", "buildIdPath", "BUILD_ID_FILE", "promises", "writeFile"], "mappings": "uEAAA,sBACA,0BACA,uDAEO,cAAeA,CAAAA,YAAf,CACLC,OADK,CAELC,OAFK,CAGU,CACf,KAAMC,CAAAA,WAAW,CAAG,eAAKF,OAAL,CAAcG,wBAAd,CAApB,CACA,KAAMC,cAASC,SAAT,CAAmBH,WAAnB,CAAgCD,OAAhC,CAAyC,MAAzC,CAAN,CACD", "sourcesContent": ["import { promises } from 'fs'\nimport { join } from 'path'\nimport { BUILD_ID_FILE } from '../next-server/lib/constants'\n\nexport async function writeBuildId(\n  distDir: string,\n  buildId: string\n): Promise<void> {\n  const buildIdPath = join(distDir, BUILD_ID_FILE)\n  await promises.writeFile(buildIdPath, buildId, 'utf8')\n}\n"]}