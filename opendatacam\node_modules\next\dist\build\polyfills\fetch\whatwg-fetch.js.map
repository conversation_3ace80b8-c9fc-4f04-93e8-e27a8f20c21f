{"version": 3, "sources": ["../../../../build/polyfills/fetch/whatwg-fetch.js"], "names": ["exports", "Headers", "self", "Request", "Response", "fetch"], "mappings": "aAAA,kBACAA,OAAO,CAACC,OAAR,CAAkBC,IAAI,CAACD,OAAvB,CACAD,OAAO,CAACG,OAAR,CAAkBD,IAAI,CAACC,OAAvB,CACAH,OAAO,CAACI,QAAR,CAAmBF,IAAI,CAACE,QAAxB,CACAJ,OAAO,CAACK,KAAR,CAAgBH,IAAI,CAACG,KAArB", "sourcesContent": ["/* globals self */\nexports.Headers = self.Headers\nexports.Request = self.Request\nexports.Response = self.Response\nexports.fetch = self.fetch\n"]}