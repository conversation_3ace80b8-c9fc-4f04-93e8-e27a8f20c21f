{"version": 3, "sources": ["../../../../build/babel/loader/util.ts"], "names": ["consumeIterator", "iter", "value", "done", "next"], "mappings": "6EAAO,QAASA,CAAAA,eAAT,CAAyBC,IAAzB,CAA8C,CACnD,MAAO,IAAP,CAAa,CACX,KAAM,CAAEC,KAAF,CAASC,IAAT,EAAkBF,IAAI,CAACG,IAAL,EAAxB,CACA,GAAID,IAAJ,CAAU,CACR,MAAOD,CAAAA,KAAP,CACD,CACF,CACF", "sourcesContent": ["export function consumeIterator(iter: Iterator<any>) {\n  while (true) {\n    const { value, done } = iter.next()\n    if (done) {\n      return value\n    }\n  }\n}\n"]}