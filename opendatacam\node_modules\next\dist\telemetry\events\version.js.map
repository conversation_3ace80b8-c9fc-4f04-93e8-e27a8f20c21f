{"version": 3, "sources": ["../../../telemetry/events/version.ts"], "names": ["EVENT_VERSION", "hasBabelConfig", "dir", "noopFile", "path", "join", "res", "require", "loadPartialConfig", "cwd", "filename", "sourceFileName", "isForTooling", "options", "presets", "every", "e", "file", "request", "plugins", "length", "hasFilesystemConfig", "getNextConfig", "phase", "configurationPath", "findUp", "sync", "CONFIG_FILE", "configurationModule", "default", "eventCliSession", "event", "process", "env", "__NEXT_VERSION", "userConfiguration", "images", "i18n", "payload", "nextVersion", "nodeVersion", "version", "cliCommand", "isSrcDir", "has<PERSON>ow<PERSON><PERSON>", "isCustomServer", "hasNextConfig", "buildTarget", "target", "hasWebpackConfig", "webpack", "imageEnabled", "basePathEnabled", "basePath", "i18nEnabled", "locales", "localeDomainsCount", "domains", "localeDetectionEnabled", "localeDetection", "imageDomainsCount", "imageSizes", "sizes", "imageLoader", "loader", "trailingSlashEnabled", "trailingSlash", "reactStrictMode", "webpackVersion", "eventName"], "mappings": "6EAAA,0EACA,kDACA,0DAMA,uD,mFAEA,KAAMA,CAAAA,aAAa,CAAG,0BAAtB,CA2BA,QAASC,CAAAA,cAAT,CAAwBC,GAAxB,CAA8C,CAC5C,GAAI,2EACF,KAAMC,CAAAA,QAAQ,CAAGC,cAAKC,IAAL,CAAUH,GAAV,CAAe,SAAf,CAAjB,CACA,KAAMI,CAAAA,GAAG,CAAGC,OAAO,CAAC,+BAAD,CAAP,CAAyCC,iBAAzC,CAA2D,CACrEC,GAAG,CAAEP,GADgE,CAErEQ,QAAQ,CAAEP,QAF2D,CAGrEQ,cAAc,CAAER,QAHqD,CAA3D,CAAZ,CAKA,KAAMS,CAAAA,YAAY,CAChB,eAAAN,GAAG,CAACO,OAAJ,kDAAaC,OAAb,oCAAsBC,KAAtB,CACGC,CAAD,qBAAY,CAAAA,CAAC,MAAD,iBAAAA,CAAC,CAAEC,IAAH,uBAASC,OAAT,IAAqB,YAAjC,EADF,IAEK,gBAAAZ,GAAG,CAACO,OAAJ,oDAAaM,OAAb,qCAAsBC,MAAtB,IAAiC,CAHxC,CAIA,MAAOd,CAAAA,GAAG,CAACe,mBAAJ,IAA6B,CAACT,YAArC,CACD,CAAC,cAAM,CACN,MAAO,MAAP,CACD,CACF,CAOD,QAASU,CAAAA,aAAT,CACEC,KADF,CAEErB,GAFF,CAGiC,CAC/B,GAAI,CACF,KAAMsB,CAAAA,iBAAiB,CAAGC,gBAAOC,IAAP,CAAYC,sBAAZ,CAAyB,CACjDlB,GAAG,CAAEP,GAD4C,CAAzB,CAA1B,CAIA,GAAIsB,iBAAJ,CAAuB,CACrB;AACA;AACA,KAAMI,CAAAA,mBAAmB,CAAGrB,OAAO,CAACiB,iBAAD,CAAnC,CAEA;AACA,MAAO,4BACLD,KADK,CAELK,mBAAmB,CAACC,OAApB,EAA+BD,mBAF1B,CAAP,CAID,CACF,CAAC,eAAM,CACN;AACD,CACD,MAAO,KAAP,CACD,CAEM,QAASE,CAAAA,eAAT,CACLP,KADK,CAELrB,GAFK,CAGL6B,KAHK,CAuBqD,2BAC1D;AACA,GAAI,MAAOC,CAAAA,OAAO,CAACC,GAAR,CAAYC,cAAnB,GAAsC,QAA1C,CAAoD,CAClD,MAAO,EAAP,CACD,CAED,KAAMC,CAAAA,iBAAiB,CAAGb,aAAa,CAACC,KAAD,CAAQrB,GAAR,CAAvC,CAEA,KAAM,CAAEkC,MAAF,CAAUC,IAAV,EAAmBF,iBAAiB,EAAI,EAA9C,CAEA,KAAMG,CAAAA,OAA+B,CAAG,CACtCC,WAAW,CAAEP,OAAO,CAACC,GAAR,CAAYC,cADa,CAEtCM,WAAW,CAAER,OAAO,CAACS,OAFiB,CAGtCC,UAAU,CAAEX,KAAK,CAACW,UAHoB,CAItCC,QAAQ,CAAEZ,KAAK,CAACY,QAJsB,CAKtCC,UAAU,CAAEb,KAAK,CAACa,UALoB,CAMtCC,cAAc,CAAEd,KAAK,CAACc,cANgB,CAOtCC,aAAa,CAAE,CAAC,CAACX,iBAPqB,CAQtCY,WAAW,wBAAEZ,iBAAF,cAAEA,iBAAiB,CAAEa,MAArB,8BAA+B,SARJ,CAStCC,gBAAgB,CAAE,OAAOd,iBAAP,cAAOA,iBAAiB,CAAEe,OAA1B,IAAsC,UATlB,CAUtCjD,cAAc,CAAEA,cAAc,CAACC,GAAD,CAVQ,CAWtCiD,YAAY,CAAE,CAAC,CAACf,MAXsB,CAYtCgB,eAAe,CAAE,CAAC,EAACjB,iBAAD,QAACA,iBAAiB,CAAEkB,QAApB,CAZoB,CAatCC,WAAW,CAAE,CAAC,CAACjB,IAbuB,CActCkB,OAAO,CAAElB,IAAI,MAAJ,EAAAA,IAAI,CAAEkB,OAAN,CAAgBlB,IAAI,CAACkB,OAAL,CAAalD,IAAb,CAAkB,GAAlB,CAAhB,CAAyC,IAdZ,CAetCmD,kBAAkB,CAAEnB,IAAI,MAAJ,EAAAA,IAAI,CAAEoB,OAAN,CAAgBpB,IAAI,CAACoB,OAAL,CAAarC,MAA7B,CAAsC,IAfpB,CAgBtCsC,sBAAsB,CAAE,CAACrB,IAAD,CAAQ,IAAR,CAAeA,IAAI,CAACsB,eAAL,GAAyB,KAhB1B,CAiBtCC,iBAAiB,CAAExB,MAAM,MAAN,EAAAA,MAAM,CAAEqB,OAAR,CAAkBrB,MAAM,CAACqB,OAAP,CAAerC,MAAjC,CAA0C,IAjBvB,CAkBtCyC,UAAU,CAAEzB,MAAM,MAAN,EAAAA,MAAM,CAAE0B,KAAR,CAAgB1B,MAAM,CAAC0B,KAAP,CAAazD,IAAb,CAAkB,GAAlB,CAAhB,CAAyC,IAlBf,CAmBtC0D,WAAW,CAAE3B,MAAF,cAAEA,MAAM,CAAE4B,MAnBiB,CAoBtCC,oBAAoB,CAAE,CAAC,EAAC9B,iBAAD,QAACA,iBAAiB,CAAE+B,aAApB,CApBe,CAqBtCC,eAAe,CAAE,CAAC,EAAChC,iBAAD,QAACA,iBAAiB,CAAEgC,eAApB,CArBoB,CAsBtCC,cAAc,CAAErC,KAAK,CAACqC,cAAN,EAAwB,IAtBF,CAAxC,CAwBA,MAAO,CAAC,CAAEC,SAAS,CAAErE,aAAb,CAA4BsC,OAA5B,CAAD,CAAP,CACD", "sourcesContent": ["import findUp from 'next/dist/compiled/find-up'\nimport path from 'path'\nimport {\n  CONFIG_FILE,\n  PHASE_DEVELOPMENT_SERVER,\n  PHASE_EXPORT,\n  PHASE_PRODUCTION_BUILD,\n} from '../../next-server/lib/constants'\nimport { normalizeConfig } from '../../next-server/server/config'\n\nconst EVENT_VERSION = 'NEXT_CLI_SESSION_STARTED'\n\ntype EventCliSessionStarted = {\n  nextVersion: string\n  nodeVersion: string\n  cliCommand: string\n  isSrcDir: boolean | null\n  hasNowJson: boolean\n  isCustomServer: boolean | null\n  hasNextConfig: boolean\n  buildTarget: string\n  hasWebpackConfig: boolean\n  hasBabelConfig: boolean\n  basePathEnabled: boolean\n  i18nEnabled: boolean\n  imageEnabled: boolean\n  locales: string | null\n  localeDomainsCount: number | null\n  localeDetectionEnabled: boolean | null\n  imageDomainsCount: number | null\n  imageSizes: string | null\n  imageLoader: string | null\n  trailingSlashEnabled: boolean\n  reactStrictMode: boolean\n  webpackVersion: number | null\n}\n\nfunction hasBabelConfig(dir: string): boolean {\n  try {\n    const noopFile = path.join(dir, 'noop.js')\n    const res = require('next/dist/compiled/babel/core').loadPartialConfig({\n      cwd: dir,\n      filename: noopFile,\n      sourceFileName: noopFile,\n    }) as any\n    const isForTooling =\n      res.options?.presets?.every(\n        (e: any) => e?.file?.request === 'next/babel'\n      ) && res.options?.plugins?.length === 0\n    return res.hasFilesystemConfig() && !isForTooling\n  } catch {\n    return false\n  }\n}\n\ntype NextConfigurationPhase =\n  | typeof PHASE_DEVELOPMENT_SERVER\n  | typeof PHASE_PRODUCTION_BUILD\n  | typeof PHASE_EXPORT\n\nfunction getNextConfig(\n  phase: NextConfigurationPhase,\n  dir: string\n): { [key: string]: any } | null {\n  try {\n    const configurationPath = findUp.sync(CONFIG_FILE, {\n      cwd: dir,\n    })\n\n    if (configurationPath) {\n      // This should've already been loaded, and thus should be cached / won't\n      // be re-evaluated.\n      const configurationModule = require(configurationPath)\n\n      // Re-normalize the configuration.\n      return normalizeConfig(\n        phase,\n        configurationModule.default || configurationModule\n      )\n    }\n  } catch {\n    // ignored\n  }\n  return null\n}\n\nexport function eventCliSession(\n  phase: NextConfigurationPhase,\n  dir: string,\n  event: Omit<\n    EventCliSessionStarted,\n    | 'nextVersion'\n    | 'nodeVersion'\n    | 'hasNextConfig'\n    | 'buildTarget'\n    | 'hasWebpackConfig'\n    | 'hasBabelConfig'\n    | 'basePathEnabled'\n    | 'i18nEnabled'\n    | 'imageEnabled'\n    | 'locales'\n    | 'localeDomainsCount'\n    | 'localeDetectionEnabled'\n    | 'imageDomainsCount'\n    | 'imageSizes'\n    | 'imageLoader'\n    | 'trailingSlashEnabled'\n    | 'reactStrictMode'\n  >\n): { eventName: string; payload: EventCliSessionStarted }[] {\n  // This should be an invariant, if it fails our build tooling is broken.\n  if (typeof process.env.__NEXT_VERSION !== 'string') {\n    return []\n  }\n\n  const userConfiguration = getNextConfig(phase, dir)\n\n  const { images, i18n } = userConfiguration || {}\n\n  const payload: EventCliSessionStarted = {\n    nextVersion: process.env.__NEXT_VERSION,\n    nodeVersion: process.version,\n    cliCommand: event.cliCommand,\n    isSrcDir: event.isSrcDir,\n    hasNowJson: event.hasNowJson,\n    isCustomServer: event.isCustomServer,\n    hasNextConfig: !!userConfiguration,\n    buildTarget: userConfiguration?.target ?? 'default',\n    hasWebpackConfig: typeof userConfiguration?.webpack === 'function',\n    hasBabelConfig: hasBabelConfig(dir),\n    imageEnabled: !!images,\n    basePathEnabled: !!userConfiguration?.basePath,\n    i18nEnabled: !!i18n,\n    locales: i18n?.locales ? i18n.locales.join(',') : null,\n    localeDomainsCount: i18n?.domains ? i18n.domains.length : null,\n    localeDetectionEnabled: !i18n ? null : i18n.localeDetection !== false,\n    imageDomainsCount: images?.domains ? images.domains.length : null,\n    imageSizes: images?.sizes ? images.sizes.join(',') : null,\n    imageLoader: images?.loader,\n    trailingSlashEnabled: !!userConfiguration?.trailingSlash,\n    reactStrictMode: !!userConfiguration?.reactStrictMode,\n    webpackVersion: event.webpackVersion || null,\n  }\n  return [{ eventName: EVENT_VERSION, payload }]\n}\n"]}