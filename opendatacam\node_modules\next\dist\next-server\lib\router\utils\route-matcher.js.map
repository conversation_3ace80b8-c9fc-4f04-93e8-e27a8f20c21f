{"version": 3, "sources": ["../../../../../next-server/lib/router/utils/route-matcher.ts"], "names": ["getRouteMatcher", "routeRegex", "re", "groups", "pathname", "routeMatch", "exec", "decode", "param", "decodeURIComponent", "_", "err", "Error", "code", "params", "Object", "keys", "for<PERSON>ach", "slug<PERSON><PERSON>", "g", "m", "pos", "undefined", "indexOf", "split", "map", "entry", "repeat"], "mappings": "6EAEO,QAASA,CAAAA,eAAT,CAAyBC,UAAzB,CAAuE,CAC5E,KAAM,CAAEC,EAAF,CAAMC,MAAN,EAAiBF,UAAvB,CACA,MAAQG,CAAAA,QAAD,EAAyC,CAC9C,KAAMC,CAAAA,UAAU,CAAGH,EAAE,CAACI,IAAH,CAAQF,QAAR,CAAnB,CACA,GAAI,CAACC,UAAL,CAAiB,CACf,MAAO,MAAP,CACD,CAED,KAAME,CAAAA,MAAM,CAAIC,KAAD,EAAmB,CAChC,GAAI,CACF,MAAOC,CAAAA,kBAAkB,CAACD,KAAD,CAAzB,CACD,CAAC,MAAOE,CAAP,CAAU,CACV,KAAMC,CAAAA,GAA8B,CAAG,GAAIC,CAAAA,KAAJ,CACrC,wBADqC,CAAvC,CAGAD,GAAG,CAACE,IAAJ,CAAW,eAAX,CACA,KAAMF,CAAAA,GAAN,CACD,CACF,CAVD,CAWA,KAAMG,CAAAA,MAAkD,CAAG,EAA3D,CAEAC,MAAM,CAACC,IAAP,CAAYb,MAAZ,EAAoBc,OAApB,CAA6BC,QAAD,EAAsB,CAChD,KAAMC,CAAAA,CAAC,CAAGhB,MAAM,CAACe,QAAD,CAAhB,CACA,KAAME,CAAAA,CAAC,CAAGf,UAAU,CAACc,CAAC,CAACE,GAAH,CAApB,CACA,GAAID,CAAC,GAAKE,SAAV,CAAqB,CACnBR,MAAM,CAACI,QAAD,CAAN,CAAmB,CAACE,CAAC,CAACG,OAAF,CAAU,GAAV,CAAD,CACfH,CAAC,CAACI,KAAF,CAAQ,GAAR,EAAaC,GAAb,CAAkBC,KAAD,EAAWnB,MAAM,CAACmB,KAAD,CAAlC,CADe,CAEfP,CAAC,CAACQ,MAAF,CACA,CAACpB,MAAM,CAACa,CAAD,CAAP,CADA,CAEAb,MAAM,CAACa,CAAD,CAJV,CAKD,CACF,CAVD,EAWA,MAAON,CAAAA,MAAP,CACD,CA/BD,CAgCD", "sourcesContent": ["import { getRouteRegex } from './route-regex'\n\nexport function getRouteMatcher(routeRegex: ReturnType<typeof getRouteRegex>) {\n  const { re, groups } = routeRegex\n  return (pathname: string | null | undefined) => {\n    const routeMatch = re.exec(pathname!)\n    if (!routeMatch) {\n      return false\n    }\n\n    const decode = (param: string) => {\n      try {\n        return decodeURIComponent(param)\n      } catch (_) {\n        const err: Error & { code?: string } = new Error(\n          'failed to decode param'\n        )\n        err.code = 'DECODE_FAILED'\n        throw err\n      }\n    }\n    const params: { [paramName: string]: string | string[] } = {}\n\n    Object.keys(groups).forEach((slugName: string) => {\n      const g = groups[slugName]\n      const m = routeMatch[g.pos]\n      if (m !== undefined) {\n        params[slugName] = ~m.indexOf('/')\n          ? m.split('/').map((entry) => decode(entry))\n          : g.repeat\n          ? [decode(m)]\n          : decode(m)\n      }\n    })\n    return params\n  }\n}\n"]}