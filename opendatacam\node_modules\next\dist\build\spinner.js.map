{"version": 3, "sources": ["../../build/spinner.ts"], "names": ["dots<PERSON>pinner", "frames", "interval", "createSpinner", "text", "options", "logFn", "console", "log", "spinner", "prefixText", "process", "stdout", "isTTY", "undefined", "stream", "start", "origLog", "origWarn", "warn", "origError", "error", "origStop", "stop", "bind", "origStopAndPersist", "stopAndPersist", "logHandle", "method", "args", "resetLog"], "mappings": "mEAAA,mE,mFAEA,KAAMA,CAAAA,WAAW,CAAG,CAClBC,MAAM,CAAE,CAAC,GAAD,CAAM,IAAN,CAAY,KAAZ,CADU,CAElBC,QAAQ,CAAE,GAFQ,CAApB,CAKe,QAASC,CAAAA,aAAT,CACbC,IADa,CAEbC,OAAoB,CAAG,EAFV,CAGbC,KAA+B,CAAGC,OAAO,CAACC,GAH7B,CAIb,CACA,GAAIC,CAAAA,OAAJ,CACA,GAAIC,CAAAA,UAAU,CAAGN,IAAI,EAAI,MAAOA,CAAAA,IAAP,GAAgB,QAAxB,EAAoCA,IAAI,CAACM,UAA1D,CAEA,GAAIC,OAAO,CAACC,MAAR,CAAeC,KAAnB,CAA0B,CACxBJ,OAAO,CAAG,iBAAI,CACZL,IAAI,CAAE,MAAOA,CAAAA,IAAP,GAAgB,QAAhB,CAA2BA,IAA3B,CAAkCU,SAD5B,CAEZJ,UAAU,CAAE,MAAOA,CAAAA,UAAP,GAAsB,QAAtB,CAAiCA,UAAjC,CAA8CI,SAF9C,CAGZL,OAAO,CAAET,WAHG,CAIZe,MAAM,CAAEJ,OAAO,CAACC,MAJJ,CAKZ,GAAGP,OALS,CAAJ,EAMPW,KANO,EAAV,CAQA;AACA;AACA,KAAMC,CAAAA,OAAO,CAAGV,OAAO,CAACC,GAAxB,CACA,KAAMU,CAAAA,QAAQ,CAAGX,OAAO,CAACY,IAAzB,CACA,KAAMC,CAAAA,SAAS,CAAGb,OAAO,CAACc,KAA1B,CACA,KAAMC,CAAAA,QAAQ,CAAGb,OAAO,CAACc,IAAR,CAAaC,IAAb,CAAkBf,OAAlB,CAAjB,CACA,KAAMgB,CAAAA,kBAAkB,CAAGhB,OAAO,CAACiB,cAAR,CAAuBF,IAAvB,CAA4Bf,OAA5B,CAA3B,CAEA,KAAMkB,CAAAA,SAAS,CAAG,CAACC,MAAD,CAAcC,IAAd,GAA8B,CAC9CP,QAAQ,GACRM,MAAM,CAAC,GAAGC,IAAJ,CAAN,CACApB,OAAO,CAAEO,KAAT,GACD,CAJD,CAMAT,OAAO,CAACC,GAAR,CAAc,CAAC,GAAGqB,IAAJ,GAAkBF,SAAS,CAACV,OAAD,CAAUY,IAAV,CAAzC,CACAtB,OAAO,CAACY,IAAR,CAAe,CAAC,GAAGU,IAAJ,GAAkBF,SAAS,CAACT,QAAD,CAAWW,IAAX,CAA1C,CACAtB,OAAO,CAACc,KAAR,CAAgB,CAAC,GAAGQ,IAAJ,GAAkBF,SAAS,CAACP,SAAD,CAAYS,IAAZ,CAA3C,CAEA,KAAMC,CAAAA,QAAQ,CAAG,IAAM,CACrBvB,OAAO,CAACC,GAAR,CAAcS,OAAd,CACAV,OAAO,CAACY,IAAR,CAAeD,QAAf,CACAX,OAAO,CAACc,KAAR,CAAgBD,SAAhB,CACD,CAJD,CAKAX,OAAO,CAACc,IAAR,CAAe,IAAe,CAC5BD,QAAQ,GACRQ,QAAQ,GACR,MAAOrB,CAAAA,OAAP,CACD,CAJD,CAKAA,OAAO,CAACiB,cAAR,CAAyB,IAAe,CACtCD,kBAAkB,GAClBK,QAAQ,GACR,MAAOrB,CAAAA,OAAP,CACD,CAJD,CAKD,CA1CD,IA0CO,IAAIC,UAAU,EAAIN,IAAlB,CAAwB,CAC7BE,KAAK,CAACI,UAAU,CAAGA,UAAU,CAAG,KAAhB,CAAwBN,IAAnC,CAAL,CACD,CAED,MAAOK,CAAAA,OAAP,CACD", "sourcesContent": ["import ora from 'next/dist/compiled/ora'\n\nconst dotsSpinner = {\n  frames: ['.', '..', '...'],\n  interval: 200,\n}\n\nexport default function createSpinner(\n  text: string | { prefixText: string },\n  options: ora.Options = {},\n  logFn: (...data: any[]) => void = console.log\n) {\n  let spinner: undefined | ora.Ora\n  let prefixText = text && typeof text === 'object' && text.prefixText\n\n  if (process.stdout.isTTY) {\n    spinner = ora({\n      text: typeof text === 'string' ? text : undefined,\n      prefixText: typeof prefixText === 'string' ? prefixText : undefined,\n      spinner: dotsSpinner,\n      stream: process.stdout,\n      ...options,\n    }).start()\n\n    // Add capturing of console.log/warn/error to allow pausing\n    // the spinner before logging and then restarting spinner after\n    const origLog = console.log\n    const origWarn = console.warn\n    const origError = console.error\n    const origStop = spinner.stop.bind(spinner)\n    const origStopAndPersist = spinner.stopAndPersist.bind(spinner)\n\n    const logHandle = (method: any, args: any[]) => {\n      origStop()\n      method(...args)\n      spinner!.start()\n    }\n\n    console.log = (...args: any) => logHandle(origLog, args)\n    console.warn = (...args: any) => logHandle(origWarn, args)\n    console.error = (...args: any) => logHandle(origError, args)\n\n    const resetLog = () => {\n      console.log = origLog\n      console.warn = origWarn\n      console.error = origError\n    }\n    spinner.stop = (): ora.Ora => {\n      origStop()\n      resetLog()\n      return spinner!\n    }\n    spinner.stopAndPersist = (): ora.Ora => {\n      origStopAndPersist()\n      resetLog()\n      return spinner!\n    }\n  } else if (prefixText || text) {\n    logFn(prefixText ? prefixText + '...' : text)\n  }\n\n  return spinner\n}\n"]}