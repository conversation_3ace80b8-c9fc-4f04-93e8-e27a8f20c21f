{"version": 3, "sources": ["../../../lib/typescript/writeAppTypeDeclarations.ts"], "names": ["writeAppTypeDeclarations", "baseDir", "appTypeDeclarations", "path", "join", "hasAppTypeDeclarations", "fs", "writeFile", "os", "EOL"], "mappings": "+FAAA,sBACA,8CACA,kDACA,0C,mFAEO,cAAeA,CAAAA,wBAAf,CAAwCC,OAAxC,CAAwE,CAC7E;AACA,KAAMC,CAAAA,mBAAmB,CAAGC,cAAKC,IAAL,CAAUH,OAAV,CAAmB,eAAnB,CAA5B,CACA,KAAMI,CAAAA,sBAAsB,CAAG,KAAM,2BAAWH,mBAAX,CAArC,CACA,GAAI,CAACG,sBAAL,CAA6B,CAC3B,KAAMC,cAAGC,SAAH,CACJL,mBADI,CAEJ,iCACEM,YAAGC,GADL,CAEE,6CAFF,CAGED,YAAGC,GALD,CAAN,CAOD,CACF", "sourcesContent": ["import { promises as fs } from 'fs'\nimport os from 'os'\nimport path from 'path'\nimport { fileExists } from '../file-exists'\n\nexport async function writeAppTypeDeclarations(baseDir: string): Promise<void> {\n  // Reference `next` types\n  const appTypeDeclarations = path.join(baseDir, 'next-env.d.ts')\n  const hasAppTypeDeclarations = await fileExists(appTypeDeclarations)\n  if (!hasAppTypeDeclarations) {\n    await fs.writeFile(\n      appTypeDeclarations,\n      '/// <reference types=\"next\" />' +\n        os.EOL +\n        '/// <reference types=\"next/types/global\" />' +\n        os.EOL\n    )\n  }\n}\n"]}