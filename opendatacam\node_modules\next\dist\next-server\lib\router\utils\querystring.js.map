{"version": 3, "sources": ["../../../../../next-server/lib/router/utils/querystring.ts"], "names": ["searchParamsToUrlQuery", "searchParams", "query", "for<PERSON>ach", "value", "key", "Array", "isArray", "push", "stringifyUrlQueryParam", "param", "isNaN", "String", "urlQueryToSearchParams", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "result", "URLSearchParams", "Object", "entries", "item", "append", "set", "assign", "target", "searchParamsList", "from", "keys", "delete"], "mappings": "uKAEO,QAASA,CAAAA,sBAAT,CACLC,YADK,CAEW,CAChB,KAAMC,CAAAA,KAAqB,CAAG,EAA9B,CACAD,YAAY,CAACE,OAAb,CAAqB,CAACC,KAAD,CAAQC,GAAR,GAAgB,CACnC,GAAI,MAAOH,CAAAA,KAAK,CAACG,GAAD,CAAZ,GAAsB,WAA1B,CAAuC,CACrCH,KAAK,CAACG,GAAD,CAAL,CAAaD,KAAb,CACD,CAFD,IAEO,IAAIE,KAAK,CAACC,OAAN,CAAcL,KAAK,CAACG,GAAD,CAAnB,CAAJ,CAA+B,CACpC,CAAEH,KAAK,CAACG,GAAD,CAAN,CAAyBG,IAAzB,CAA8BJ,KAA9B,EACF,CAFM,IAEA,CACLF,KAAK,CAACG,GAAD,CAAL,CAAa,CAACH,KAAK,CAACG,GAAD,CAAN,CAAuBD,KAAvB,CAAb,CACD,CACF,CARD,EASA,MAAOF,CAAAA,KAAP,CACD,CAED,QAASO,CAAAA,sBAAT,CAAgCC,KAAhC,CAAuD,CACrD,GACE,MAAOA,CAAAA,KAAP,GAAiB,QAAjB,EACC,MAAOA,CAAAA,KAAP,GAAiB,QAAjB,EAA6B,CAACC,KAAK,CAACD,KAAD,CADpC,EAEA,MAAOA,CAAAA,KAAP,GAAiB,SAHnB,CAIE,CACA,MAAOE,CAAAA,MAAM,CAACF,KAAD,CAAb,CACD,CAND,IAMO,CACL,MAAO,EAAP,CACD,CACF,CAEM,QAASG,CAAAA,sBAAT,CACLC,QADK,CAEY,CACjB,KAAMC,CAAAA,MAAM,CAAG,GAAIC,CAAAA,eAAJ,EAAf,CACAC,MAAM,CAACC,OAAP,CAAeJ,QAAf,EAAyBX,OAAzB,CAAiC,CAAC,CAACE,GAAD,CAAMD,KAAN,CAAD,GAAkB,CACjD,GAAIE,KAAK,CAACC,OAAN,CAAcH,KAAd,CAAJ,CAA0B,CACxBA,KAAK,CAACD,OAAN,CAAegB,IAAD,EAAUJ,MAAM,CAACK,MAAP,CAAcf,GAAd,CAAmBI,sBAAsB,CAACU,IAAD,CAAzC,CAAxB,EACD,CAFD,IAEO,CACLJ,MAAM,CAACM,GAAP,CAAWhB,GAAX,CAAgBI,sBAAsB,CAACL,KAAD,CAAtC,EACD,CACF,CAND,EAOA,MAAOW,CAAAA,MAAP,CACD,CAEM,QAASO,CAAAA,MAAT,CACLC,MADK,CAEL,GAAGC,gBAFE,CAGY,CACjBA,gBAAgB,CAACrB,OAAjB,CAA0BF,YAAD,EAAkB,CACzCK,KAAK,CAACmB,IAAN,CAAWxB,YAAY,CAACyB,IAAb,EAAX,EAAgCvB,OAAhC,CAAyCE,GAAD,EAASkB,MAAM,CAACI,MAAP,CAActB,GAAd,CAAjD,EACAJ,YAAY,CAACE,OAAb,CAAqB,CAACC,KAAD,CAAQC,GAAR,GAAgBkB,MAAM,CAACH,MAAP,CAAcf,GAAd,CAAmBD,KAAnB,CAArC,EACD,CAHD,EAIA,MAAOmB,CAAAA,MAAP,CACD", "sourcesContent": ["import { ParsedUrlQuery } from 'querystring'\n\nexport function searchParamsToUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  const query: ParsedUrlQuery = {}\n  searchParams.forEach((value, key) => {\n    if (typeof query[key] === 'undefined') {\n      query[key] = value\n    } else if (Array.isArray(query[key])) {\n      ;(query[key] as string[]).push(value)\n    } else {\n      query[key] = [query[key] as string, value]\n    }\n  })\n  return query\n}\n\nfunction stringifyUrlQueryParam(param: string): string {\n  if (\n    typeof param === 'string' ||\n    (typeof param === 'number' && !isNaN(param)) ||\n    typeof param === 'boolean'\n  ) {\n    return String(param)\n  } else {\n    return ''\n  }\n}\n\nexport function urlQueryToSearchParams(\n  urlQuery: ParsedUrlQuery\n): URLSearchParams {\n  const result = new URLSearchParams()\n  Object.entries(urlQuery).forEach(([key, value]) => {\n    if (Array.isArray(value)) {\n      value.forEach((item) => result.append(key, stringifyUrlQueryParam(item)))\n    } else {\n      result.set(key, stringifyUrlQueryParam(value))\n    }\n  })\n  return result\n}\n\nexport function assign(\n  target: URLSearchParams,\n  ...searchParamsList: URLSearchParams[]\n): URLSearchParams {\n  searchParamsList.forEach((searchParams) => {\n    Array.from(searchParams.keys()).forEach((key) => target.delete(key))\n    searchParams.forEach((value, key) => target.append(key, value))\n  })\n  return target\n}\n"]}