{"version": 3, "sources": ["../../../../../../build/webpack/config/blocks/css/overrideCssConfiguration.ts"], "names": ["__overrideCssConfiguration", "rootDirectory", "isProduction", "config", "post<PERSON>s<PERSON><PERSON><PERSON>", "patch", "rule", "options", "postcssOptions", "plugins", "Array", "isArray", "oneOf", "for<PERSON>ach", "use", "u", "module", "rules", "entry"], "mappings": "mGACA,kCAEO,cAAeA,CAAAA,0BAAf,CACLC,aADK,CAELC,YAFK,CAGLC,MAHK,CAIL,yCACA,KAAMC,CAAAA,cAAc,CAAG,KAAM,+BAAkBH,aAAlB,CAAiCC,YAAjC,CAA7B,CAEA,QAASG,CAAAA,KAAT,CAAeC,IAAf,CAA0C,CACxC,GACEA,IAAI,CAACC,OAAL,EACA,MAAOD,CAAAA,IAAI,CAACC,OAAZ,GAAwB,QADxB,EAEA,MAAOD,CAAAA,IAAI,CAACC,OAAL,CAAaC,cAApB,GAAuC,QAHzC,CAIE,CACAF,IAAI,CAACC,OAAL,CAAaC,cAAb,CAA4BC,OAA5B,CAAsCL,cAAtC,CACD,CAND,IAMO,IAAIM,KAAK,CAACC,OAAN,CAAcL,IAAI,CAACM,KAAnB,CAAJ,CAA+B,CACpCN,IAAI,CAACM,KAAL,CAAWC,OAAX,CAAmBR,KAAnB,EACD,CAFM,IAEA,IAAIK,KAAK,CAACC,OAAN,CAAcL,IAAI,CAACQ,GAAnB,CAAJ,CAA6B,CAClCR,IAAI,CAACQ,GAAL,CAASD,OAAT,CAAkBE,CAAD,EAAO,CACtB,GAAI,MAAOA,CAAAA,CAAP,GAAa,QAAjB,CAA2B,CACzBV,KAAK,CAACU,CAAD,CAAL,CACD,CACF,CAJD,EAKD,CACF,CAED,gBAAAZ,MAAM,CAACa,MAAP,oDAAeC,KAAf,oCAAsBJ,OAAtB,CAA+BK,KAAD,EAAW,CACvCb,KAAK,CAACa,KAAD,CAAL,CACD,CAFD,EAGD", "sourcesContent": ["import { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { getPostCssPlugins } from './plugins'\n\nexport async function __overrideCssConfiguration(\n  rootDirectory: string,\n  isProduction: boolean,\n  config: webpack.Configuration\n) {\n  const postCssPlugins = await getPostCssPlugins(rootDirectory, isProduction)\n\n  function patch(rule: webpack.RuleSetRule) {\n    if (\n      rule.options &&\n      typeof rule.options === 'object' &&\n      typeof rule.options.postcssOptions === 'object'\n    ) {\n      rule.options.postcssOptions.plugins = postCssPlugins\n    } else if (Array.isArray(rule.oneOf)) {\n      rule.oneOf.forEach(patch)\n    } else if (Array.isArray(rule.use)) {\n      rule.use.forEach((u) => {\n        if (typeof u === 'object') {\n          patch(u)\n        }\n      })\n    }\n  }\n\n  config.module?.rules?.forEach((entry) => {\n    patch(entry)\n  })\n}\n"]}