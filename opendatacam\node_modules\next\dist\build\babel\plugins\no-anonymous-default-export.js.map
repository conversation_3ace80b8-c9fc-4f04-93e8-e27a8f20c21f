{"version": 3, "sources": ["../../../../build/babel/plugins/no-anonymous-default-export.ts"], "names": ["NoAnonymousDefaultExport", "types", "t", "babel", "onWarning", "caller", "visitor", "warn", "ExportDefaultDeclaration", "path", "def", "node", "declaration", "type", "chalk", "yellow", "bold", "cyan", "join", "isAnonymous", "Boolean", "id", "_"], "mappings": "8EACA,oD,mFAEe,QAASA,CAAAA,wBAAT,CAAkC,CAC/CC,KAAK,CAAEC,CADwC,CAE/C,GAAGC,KAF4C,CAAlC,CAMI,CACjB,GAAIC,CAAAA,SAAoD,CAAG,IAA3D,CACAD,KAAK,CAACE,MAAN,CAAcA,MAAD,EAAY,CACvBD,SAAS,CAAGC,MAAM,CAACD,SAAnB,CACA,MAAO,EAAP,CAAU;AACX,CAHD,EAKA,GAAI,MAAOA,CAAAA,SAAP,GAAqB,UAAzB,CAAqC,CACnC,MAAO,CAAEE,OAAO,CAAE,EAAX,CAAP,CACD,CAED,KAAMC,CAAAA,IAAI,CAAGH,SAAb,CACA,MAAO,CACLE,OAAO,CAAE,CACPE,wBAAwB,CAACC,IAAD,CAAO,CAC7B,KAAMC,CAAAA,GAAG,CAAGD,IAAI,CAACE,IAAL,CAAUC,WAAtB,CAEA,GACE,EACEF,GAAG,CAACG,IAAJ,GAAa,yBAAb,EACAH,GAAG,CAACG,IAAJ,GAAa,qBAFf,CADF,CAKE,CACA,OACD,CAED,OAAQH,GAAG,CAACG,IAAZ,EACE,IAAK,yBAAL,CAAgC,CAC9BN,IAAI,CACF,CACEO,eAAMC,MAAN,CAAaC,IAAb,CACE,qFADF,CADF,CAIE,kDAJF,CAKE,EALF,CAMEF,eAAME,IAAN,CAAW,QAAX,CANF,CAOEF,eAAMG,IAAN,CAAW,+BAAX,CAPF,CAQE,EARF,CASEH,eAAME,IAAN,CAAW,OAAX,CATF,CAUEF,eAAMG,IAAN,CAAW,8BAAX,CAVF,CAWEH,eAAMG,IAAN,CAAW,uBAAX,CAXF,CAYE,EAZF,CAaG,wDAAuDH,eAAMG,IAAN,CACtD,iCADsD,CAEtD,EAfJ,EAgBEC,IAhBF,CAgBO,IAhBP,CADE,CAAJ,CAmBA,MACD,CACD,IAAK,qBAAL,CAA4B,CAC1B,KAAMC,CAAAA,WAAW,CAAG,CAACC,OAAO,CAACV,GAAG,CAACW,EAAL,CAA5B,CACA,GAAIF,WAAJ,CAAiB,CACfZ,IAAI,CACF,CACEO,eAAMC,MAAN,CAAaC,IAAb,CACE,2FADF,CADF,CAIE,kDAJF,CAKE,EALF,CAMEF,eAAME,IAAN,CAAW,QAAX,CANF,CAOEF,eAAMG,IAAN,CAAW,0CAAX,CAPF,CAQE,EARF,CASEH,eAAME,IAAN,CAAW,OAAX,CATF,CAUEF,eAAMG,IAAN,CAAW,+CAAX,CAVF,CAWE,EAXF,CAYG,wDAAuDH,eAAMG,IAAN,CACtD,iCADsD,CAEtD,EAdJ,EAeEC,IAfF,CAeO,IAfP,CADE,CAAJ,CAkBD,CACD,MACD,CACD,QAAS,CACP;AACA,KAAMI,CAAAA,CAAQ,CAAGZ,GAAjB,CACD,CAlDH,CAoDD,CAjEM,CADJ,CAAP,CAqED", "sourcesContent": ["import { PluginObj, types as BabelTypes } from 'next/dist/compiled/babel/core'\nimport chalk from 'chalk'\n\nexport default function NoAnonymousDefaultExport({\n  types: t,\n  ...babel\n}: {\n  types: typeof BabelTypes\n  caller: (callerCallback: (caller: any) => any) => any\n}): PluginObj<any> {\n  let onWarning: ((reason: string | Error) => void) | null = null\n  babel.caller((caller) => {\n    onWarning = caller.onWarning\n    return '' // Intentionally empty to not invalidate cache\n  })\n\n  if (typeof onWarning !== 'function') {\n    return { visitor: {} }\n  }\n\n  const warn = onWarning!\n  return {\n    visitor: {\n      ExportDefaultDeclaration(path) {\n        const def = path.node.declaration\n\n        if (\n          !(\n            def.type === 'ArrowFunctionExpression' ||\n            def.type === 'FunctionDeclaration'\n          )\n        ) {\n          return\n        }\n\n        switch (def.type) {\n          case 'ArrowFunctionExpression': {\n            warn(\n              [\n                chalk.yellow.bold(\n                  'Anonymous arrow functions cause Fast Refresh to not preserve local component state.'\n                ),\n                'Please add a name to your function, for example:',\n                '',\n                chalk.bold('Before'),\n                chalk.cyan('export default () => <div />;'),\n                '',\n                chalk.bold('After'),\n                chalk.cyan('const Named = () => <div />;'),\n                chalk.cyan('export default Named;'),\n                '',\n                `A codemod is available to fix the most common cases: ${chalk.cyan(\n                  'https://nextjs.link/codemod-ndc'\n                )}`,\n              ].join('\\n')\n            )\n            break\n          }\n          case 'FunctionDeclaration': {\n            const isAnonymous = !Boolean(def.id)\n            if (isAnonymous) {\n              warn(\n                [\n                  chalk.yellow.bold(\n                    'Anonymous function declarations cause Fast Refresh to not preserve local component state.'\n                  ),\n                  'Please add a name to your function, for example:',\n                  '',\n                  chalk.bold('Before'),\n                  chalk.cyan('export default function () { /* ... */ }'),\n                  '',\n                  chalk.bold('After'),\n                  chalk.cyan('export default function Named() { /* ... */ }'),\n                  '',\n                  `A codemod is available to fix the most common cases: ${chalk.cyan(\n                    'https://nextjs.link/codemod-ndc'\n                  )}`,\n                ].join('\\n')\n              )\n            }\n            break\n          }\n          default: {\n            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n            const _: never = def\n          }\n        }\n      },\n    },\n  }\n}\n"]}