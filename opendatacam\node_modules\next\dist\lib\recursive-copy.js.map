{"version": 3, "sources": ["../../lib/recursive-copy.ts"], "names": ["COPYFILE_EXCL", "constants", "recursiveCopy", "source", "dest", "concurrency", "overwrite", "filter", "cwdPath", "process", "cwd", "from", "path", "resolve", "to", "sema", "<PERSON><PERSON>", "_copy", "item", "target", "replace", "stats", "promises", "stat", "acquire", "isDirectory", "mkdir", "err", "code", "release", "files", "readdir", "Promise", "all", "map", "file", "join", "isFile", "copyFile", "undefined"], "mappings": "yEAAA,kDACA,sBACA,wD,mFAEA,KAAMA,CAAAA,aAAa,CAAGC,cAAUD,aAAhC,CAEO,cAAeE,CAAAA,aAAf,CACLC,MADK,CAELC,IAFK,CAGL,CACEC,WAAW,CAAG,EADhB,CAEEC,SAAS,CAAG,KAFd,CAGEC,MAAM,CAAG,IAAM,IAHjB,EAQI,EAXC,CAYU,CACf,KAAMC,CAAAA,OAAO,CAAGC,OAAO,CAACC,GAAR,EAAhB,CACA,KAAMC,CAAAA,IAAI,CAAGC,cAAKC,OAAL,CAAaL,OAAb,CAAsBL,MAAtB,CAAb,CACA,KAAMW,CAAAA,EAAE,CAAGF,cAAKC,OAAL,CAAaL,OAAb,CAAsBJ,IAAtB,CAAX,CAEA,KAAMW,CAAAA,IAAI,CAAG,GAAIC,gBAAJ,CAASX,WAAT,CAAb,CAEA,cAAeY,CAAAA,KAAf,CAAqBC,IAArB,CAAkD,CAChD,KAAMC,CAAAA,MAAM,CAAGD,IAAI,CAACE,OAAL,CAAaT,IAAb,CAAmBG,EAAnB,CAAf,CACA,KAAMO,CAAAA,KAAK,CAAG,KAAMC,cAASC,IAAT,CAAcL,IAAd,CAApB,CAEA,KAAMH,CAAAA,IAAI,CAACS,OAAL,EAAN,CAEA,GAAIH,KAAK,CAACI,WAAN,EAAJ,CAAyB,CACvB,GAAI,CACF,KAAMH,cAASI,KAAT,CAAeP,MAAf,CAAN,CACD,CAAC,MAAOQ,GAAP,CAAY,CACZ;AACA,GAAIA,GAAG,CAACC,IAAJ,GAAa,QAAjB,CAA2B,CACzB,KAAMD,CAAAA,GAAN,CACD,CACF,CACDZ,IAAI,CAACc,OAAL,GACA,KAAMC,CAAAA,KAAK,CAAG,KAAMR,cAASS,OAAT,CAAiBb,IAAjB,CAApB,CACA,KAAMc,CAAAA,OAAO,CAACC,GAAR,CAAYH,KAAK,CAACI,GAAN,CAAWC,IAAD,EAAUlB,KAAK,CAACL,cAAKwB,IAAL,CAAUlB,IAAV,CAAgBiB,IAAhB,CAAD,CAAzB,CAAZ,CAAN,CACD,CAZD,IAYO,IACLd,KAAK,CAACgB,MAAN,IACA;AACA;AACA9B,MAAM,CAACW,IAAI,CAACE,OAAL,CAAaT,IAAb,CAAmB,EAAnB,EAAuBS,OAAvB,CAA+B,KAA/B,CAAsC,GAAtC,CAAD,CAJD,CAKL,CACA,KAAME,cAASgB,QAAT,CACJpB,IADI,CAEJC,MAFI,CAGJb,SAAS,CAAGiC,SAAH,CAAevC,aAHpB,CAAN,CAKAe,IAAI,CAACc,OAAL,GACD,CACF,CAED,KAAMZ,CAAAA,KAAK,CAACN,IAAD,CAAX,CACD", "sourcesContent": ["import path from 'path'\nimport { promises, constants } from 'fs'\nimport { Sema } from 'next/dist/compiled/async-sema'\n\nconst COPYFILE_EXCL = constants.COPYFILE_EXCL\n\nexport async function recursiveCopy(\n  source: string,\n  dest: string,\n  {\n    concurrency = 32,\n    overwrite = false,\n    filter = () => true,\n  }: {\n    concurrency?: number\n    overwrite?: boolean\n    filter?(filePath: string): boolean\n  } = {}\n): Promise<void> {\n  const cwdPath = process.cwd()\n  const from = path.resolve(cwdPath, source)\n  const to = path.resolve(cwdPath, dest)\n\n  const sema = new Sema(concurrency)\n\n  async function _copy(item: string): Promise<void> {\n    const target = item.replace(from, to)\n    const stats = await promises.stat(item)\n\n    await sema.acquire()\n\n    if (stats.isDirectory()) {\n      try {\n        await promises.mkdir(target)\n      } catch (err) {\n        // do not throw `folder already exists` errors\n        if (err.code !== 'EEXIST') {\n          throw err\n        }\n      }\n      sema.release()\n      const files = await promises.readdir(item)\n      await Promise.all(files.map((file) => _copy(path.join(item, file))))\n    } else if (\n      stats.isFile() &&\n      // before we send the path to filter\n      // we remove the base path (from) and replace \\ by / (windows)\n      filter(item.replace(from, '').replace(/\\\\/g, '/'))\n    ) {\n      await promises.copyFile(\n        item,\n        target,\n        overwrite ? undefined : COPYFILE_EXCL\n      )\n      sema.release()\n    }\n  }\n\n  await _copy(from)\n}\n"]}