{"version": 3, "sources": ["../../../next-server/server/utils.ts"], "names": ["isBlockedPage", "pathname", "BLOCKED_PAGES", "includes", "cleanAmpPath", "match", "replace"], "mappings": "2GAAA,2CAEO,QAASA,CAAAA,aAAT,CAAuBC,QAAvB,CAAkD,CACvD,MAAOC,0BAAcC,QAAd,CAAuBF,QAAvB,CAAP,CACD,CAEM,QAASG,CAAAA,YAAT,CAAsBH,QAAtB,CAAgD,CACrD,GAAIA,QAAQ,CAACI,KAAT,CAAe,sBAAf,CAAJ,CAA4C,CAC1CJ,QAAQ,CAAGA,QAAQ,CAACK,OAAT,CAAiB,wBAAjB,CAA2C,GAA3C,CAAX,CACD,CACD,GAAIL,QAAQ,CAACI,KAAT,CAAe,qBAAf,CAAJ,CAA2C,CACzCJ,QAAQ,CAAGA,QAAQ,CAACK,OAAT,CAAiB,qBAAjB,CAAwC,EAAxC,CAAX,CACD,CACDL,QAAQ,CAAGA,QAAQ,CAACK,OAAT,CAAiB,KAAjB,CAAwB,EAAxB,CAAX,CACA,MAAOL,CAAAA,QAAP,CACD", "sourcesContent": ["import { BLOCKED_PAGES } from '../lib/constants'\n\nexport function isBlockedPage(pathname: string): boolean {\n  return BLOCKED_PAGES.includes(pathname)\n}\n\nexport function cleanAmpPath(pathname: string): string {\n  if (pathname.match(/\\?amp=(y|yes|true|1)/)) {\n    pathname = pathname.replace(/\\?amp=(y|yes|true|1)&?/, '?')\n  }\n  if (pathname.match(/&amp=(y|yes|true|1)/)) {\n    pathname = pathname.replace(/&amp=(y|yes|true|1)/, '')\n  }\n  pathname = pathname.replace(/\\?$/, '')\n  return pathname\n}\n"]}