{"version": 3, "sources": ["../../../../../next-server/lib/router/utils/route-regex.ts"], "names": ["escapeRegex", "str", "replace", "parseParameter", "param", "optional", "startsWith", "endsWith", "slice", "repeat", "key", "getRouteRegex", "normalizedRoute", "segments", "split", "groups", "groupIndex", "parameterizedRoute", "map", "segment", "pos", "join", "window", "routeKeyCharCode", "routeKeyCharLength", "getSafeRouteKey", "routeKey", "i", "String", "fromCharCode", "routeKeys", "namedParameterizedRoute", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "length", "isNaN", "parseInt", "substr", "re", "RegExp", "namedRegex"], "mappings": "yEAMA;AACA;AACA,QAASA,CAAAA,WAAT,CAAqBC,GAArB,CAAkC,CAChC,MAAOA,CAAAA,GAAG,CAACC,OAAJ,CAAY,sBAAZ,CAAoC,MAApC,CAAP,CACD,CAED,QAASC,CAAAA,cAAT,CAAwBC,KAAxB,CAAuC,CACrC,KAAMC,CAAAA,QAAQ,CAAGD,KAAK,CAACE,UAAN,CAAiB,GAAjB,GAAyBF,KAAK,CAACG,QAAN,CAAe,GAAf,CAA1C,CACA,GAAIF,QAAJ,CAAc,CACZD,KAAK,CAAGA,KAAK,CAACI,KAAN,CAAY,CAAZ,CAAe,CAAC,CAAhB,CAAR,CACD,CACD,KAAMC,CAAAA,MAAM,CAAGL,KAAK,CAACE,UAAN,CAAiB,KAAjB,CAAf,CACA,GAAIG,MAAJ,CAAY,CACVL,KAAK,CAAGA,KAAK,CAACI,KAAN,CAAY,CAAZ,CAAR,CACD,CACD,MAAO,CAAEE,GAAG,CAAEN,KAAP,CAAcK,MAAd,CAAsBJ,QAAtB,CAAP,CACD,CAEM,QAASM,CAAAA,aAAT,CACLC,eADK,CAOL,CACA,KAAMC,CAAAA,QAAQ,CAAG,CAACD,eAAe,CAACV,OAAhB,CAAwB,KAAxB,CAA+B,EAA/B,GAAsC,GAAvC,EACdM,KADc,CACR,CADQ,EAEdM,KAFc,CAER,GAFQ,CAAjB,CAIA,KAAMC,CAAAA,MAAsC,CAAG,EAA/C,CACA,GAAIC,CAAAA,UAAU,CAAG,CAAjB,CACA,KAAMC,CAAAA,kBAAkB,CAAGJ,QAAQ,CAChCK,GADwB,CACnBC,OAAD,EAAa,CAChB,GAAIA,OAAO,CAACb,UAAR,CAAmB,GAAnB,GAA2Ba,OAAO,CAACZ,QAAR,CAAiB,GAAjB,CAA/B,CAAsD,CACpD,KAAM,CAAEG,GAAF,CAAOL,QAAP,CAAiBI,MAAjB,EAA4BN,cAAc,CAACgB,OAAO,CAACX,KAAR,CAAc,CAAd,CAAiB,CAAC,CAAlB,CAAD,CAAhD,CACAO,MAAM,CAACL,GAAD,CAAN,CAAc,CAAEU,GAAG,CAAEJ,UAAU,EAAjB,CAAqBP,MAArB,CAA6BJ,QAA7B,CAAd,CACA,MAAOI,CAAAA,MAAM,CAAIJ,QAAQ,CAAG,aAAH,CAAmB,QAA/B,CAA2C,WAAxD,CACD,CAJD,IAIO,CACL,MAAQ,IAAGL,WAAW,CAACmB,OAAD,CAAU,EAAhC,CACD,CACF,CATwB,EAUxBE,IAVwB,CAUnB,EAVmB,CAA3B,CAYA;AACA;AACA,GAAI,MAAOC,CAAAA,MAAP,GAAkB,WAAtB,CAAmC,CACjC,GAAIC,CAAAA,gBAAgB,CAAG,EAAvB,CACA,GAAIC,CAAAA,kBAAkB,CAAG,CAAzB,CAEA;AACA,KAAMC,CAAAA,eAAe,CAAG,IAAM,CAC5B,GAAIC,CAAAA,QAAQ,CAAG,EAAf,CAEA,IAAK,GAAIC,CAAAA,CAAC,CAAG,CAAb,CAAgBA,CAAC,CAAGH,kBAApB,CAAwCG,CAAC,EAAzC,CAA6C,CAC3CD,QAAQ,EAAIE,MAAM,CAACC,YAAP,CAAoBN,gBAApB,CAAZ,CACAA,gBAAgB,GAEhB,GAAIA,gBAAgB,CAAG,GAAvB,CAA4B,CAC1BC,kBAAkB,GAClBD,gBAAgB,CAAG,EAAnB,CACD,CACF,CACD,MAAOG,CAAAA,QAAP,CACD,CAbD,CAeA,KAAMI,CAAAA,SAAsC,CAAG,EAA/C,CAEA,GAAIC,CAAAA,uBAAuB,CAAGlB,QAAQ,CACnCK,GAD2B,CACtBC,OAAD,EAAa,CAChB,GAAIA,OAAO,CAACb,UAAR,CAAmB,GAAnB,GAA2Ba,OAAO,CAACZ,QAAR,CAAiB,GAAjB,CAA/B,CAAsD,CACpD,KAAM,CAAEG,GAAF,CAAOL,QAAP,CAAiBI,MAAjB,EAA4BN,cAAc,CAACgB,OAAO,CAACX,KAAR,CAAc,CAAd,CAAiB,CAAC,CAAlB,CAAD,CAAhD,CACA;AACA;AACA,GAAIwB,CAAAA,UAAU,CAAGtB,GAAG,CAACR,OAAJ,CAAY,KAAZ,CAAmB,EAAnB,CAAjB,CACA,GAAI+B,CAAAA,UAAU,CAAG,KAAjB,CAEA;AACA;AACA,GAAID,UAAU,CAACE,MAAX,GAAsB,CAAtB,EAA2BF,UAAU,CAACE,MAAX,CAAoB,EAAnD,CAAuD,CACrDD,UAAU,CAAG,IAAb,CACD,CACD,GAAI,CAACE,KAAK,CAACC,QAAQ,CAACJ,UAAU,CAACK,MAAX,CAAkB,CAAlB,CAAqB,CAArB,CAAD,CAAT,CAAV,CAA+C,CAC7CJ,UAAU,CAAG,IAAb,CACD,CAED,GAAIA,UAAJ,CAAgB,CACdD,UAAU,CAAGP,eAAe,EAA5B,CACD,CAEDK,SAAS,CAACE,UAAD,CAAT,CAAwBtB,GAAxB,CACA,MAAOD,CAAAA,MAAM,CACTJ,QAAQ,CACL,UAAS2B,UAAW,SADf,CAEL,OAAMA,UAAW,OAHX,CAIR,OAAMA,UAAW,UAJtB,CAKD,CA1BD,IA0BO,CACL,MAAQ,IAAGhC,WAAW,CAACmB,OAAD,CAAU,EAAhC,CACD,CACF,CA/B2B,EAgC3BE,IAhC2B,CAgCtB,EAhCsB,CAA9B,CAkCA,MAAO,CACLiB,EAAE,CAAE,GAAIC,CAAAA,MAAJ,CAAY,IAAGtB,kBAAmB,SAAlC,CADC,CAELF,MAFK,CAGLe,SAHK,CAILU,UAAU,CAAG,IAAGT,uBAAwB,SAJnC,CAAP,CAMD,CAED,MAAO,CACLO,EAAE,CAAE,GAAIC,CAAAA,MAAJ,CAAY,IAAGtB,kBAAmB,SAAlC,CADC,CAELF,MAFK,CAAP,CAID", "sourcesContent": ["export interface Group {\n  pos: number\n  repeat: boolean\n  optional: boolean\n}\n\n// this isn't importing the escape-string-regex module\n// to reduce bytes\nfunction escapeRegex(str: string) {\n  return str.replace(/[|\\\\{}()[\\]^$+*?.-]/g, '\\\\$&')\n}\n\nfunction parseParameter(param: string) {\n  const optional = param.startsWith('[') && param.endsWith(']')\n  if (optional) {\n    param = param.slice(1, -1)\n  }\n  const repeat = param.startsWith('...')\n  if (repeat) {\n    param = param.slice(3)\n  }\n  return { key: param, repeat, optional }\n}\n\nexport function getRouteRegex(\n  normalizedRoute: string\n): {\n  re: RegExp\n  namedRegex?: string\n  routeKeys?: { [named: string]: string }\n  groups: { [groupName: string]: Group }\n} {\n  const segments = (normalizedRoute.replace(/\\/$/, '') || '/')\n    .slice(1)\n    .split('/')\n\n  const groups: { [groupName: string]: Group } = {}\n  let groupIndex = 1\n  const parameterizedRoute = segments\n    .map((segment) => {\n      if (segment.startsWith('[') && segment.endsWith(']')) {\n        const { key, optional, repeat } = parseParameter(segment.slice(1, -1))\n        groups[key] = { pos: groupIndex++, repeat, optional }\n        return repeat ? (optional ? '(?:/(.+?))?' : '/(.+?)') : '/([^/]+?)'\n      } else {\n        return `/${escapeRegex(segment)}`\n      }\n    })\n    .join('')\n\n  // dead code eliminate for browser since it's only needed\n  // while generating routes-manifest\n  if (typeof window === 'undefined') {\n    let routeKeyCharCode = 97\n    let routeKeyCharLength = 1\n\n    // builds a minimal routeKey using only a-z and minimal number of characters\n    const getSafeRouteKey = () => {\n      let routeKey = ''\n\n      for (let i = 0; i < routeKeyCharLength; i++) {\n        routeKey += String.fromCharCode(routeKeyCharCode)\n        routeKeyCharCode++\n\n        if (routeKeyCharCode > 122) {\n          routeKeyCharLength++\n          routeKeyCharCode = 97\n        }\n      }\n      return routeKey\n    }\n\n    const routeKeys: { [named: string]: string } = {}\n\n    let namedParameterizedRoute = segments\n      .map((segment) => {\n        if (segment.startsWith('[') && segment.endsWith(']')) {\n          const { key, optional, repeat } = parseParameter(segment.slice(1, -1))\n          // replace any non-word characters since they can break\n          // the named regex\n          let cleanedKey = key.replace(/\\W/g, '')\n          let invalidKey = false\n\n          // check if the key is still invalid and fallback to using a known\n          // safe key\n          if (cleanedKey.length === 0 || cleanedKey.length > 30) {\n            invalidKey = true\n          }\n          if (!isNaN(parseInt(cleanedKey.substr(0, 1)))) {\n            invalidKey = true\n          }\n\n          if (invalidKey) {\n            cleanedKey = getSafeRouteKey()\n          }\n\n          routeKeys[cleanedKey] = key\n          return repeat\n            ? optional\n              ? `(?:/(?<${cleanedKey}>.+?))?`\n              : `/(?<${cleanedKey}>.+?)`\n            : `/(?<${cleanedKey}>[^/]+?)`\n        } else {\n          return `/${escapeRegex(segment)}`\n        }\n      })\n      .join('')\n\n    return {\n      re: new RegExp(`^${parameterizedRoute}(?:/)?$`),\n      groups,\n      routeKeys,\n      namedRegex: `^${namedParameterizedRoute}(?:/)?$`,\n    }\n  }\n\n  return {\n    re: new RegExp(`^${parameterizedRoute}(?:/)?$`),\n    groups,\n  }\n}\n"]}