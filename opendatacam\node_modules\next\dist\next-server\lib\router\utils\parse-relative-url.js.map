{"version": 3, "sources": ["../../../../../next-server/lib/router/utils/parse-relative-url.ts"], "names": ["parseRelativeUrl", "url", "base", "globalBase", "URL", "window", "resolvedBase", "pathname", "searchParams", "search", "hash", "href", "origin", "Error", "query", "slice", "length"], "mappings": "+EAAA,kCACA,0CAEA;AACA;AACA;AACA;AACA;AACA,GACO,QAASA,CAAAA,gBAAT,CAA0BC,GAA1B,CAAuCC,IAAvC,CAAsD,CAC3D,KAAMC,CAAAA,UAAU,CAAG,GAAIC,CAAAA,GAAJ,CACjB,MAAOC,CAAAA,MAAP,GAAkB,WAAlB,CAAgC,UAAhC,CAA6C,8BAD5B,CAAnB,CAGA,KAAMC,CAAAA,YAAY,CAAGJ,IAAI,CAAG,GAAIE,CAAAA,GAAJ,CAAQF,IAAR,CAAcC,UAAd,CAAH,CAA+BA,UAAxD,CACA,KAAM,CAAEI,QAAF,CAAYC,YAAZ,CAA0BC,MAA1B,CAAkCC,IAAlC,CAAwCC,IAAxC,CAA8CC,MAA9C,EAAyD,GAAIR,CAAAA,GAAJ,CAC7DH,GAD6D,CAE7DK,YAF6D,CAA/D,CAIA,GAAIM,MAAM,GAAKT,UAAU,CAACS,MAA1B,CAAkC,CAChC,KAAM,IAAIC,CAAAA,KAAJ,CAAW,oDAAmDZ,GAAI,EAAlE,CAAN,CACD,CACD,MAAO,CACLM,QADK,CAELO,KAAK,CAAE,wCAAuBN,YAAvB,CAFF,CAGLC,MAHK,CAILC,IAJK,CAKLC,IAAI,CAAEA,IAAI,CAACI,KAAL,CAAWZ,UAAU,CAACS,MAAX,CAAkBI,MAA7B,CALD,CAAP,CAOD", "sourcesContent": ["import { getLocationOrigin } from '../../utils'\nimport { searchParamsToUrlQuery } from './querystring'\n\n/**\n * Parses path-relative urls (e.g. `/hello/world?foo=bar`). If url isn't path-relative\n * (e.g. `./hello`) then at least base must be.\n * Absolute urls are rejected with one exception, in the browser, absolute urls that are on\n * the current origin will be parsed as relative\n */\nexport function parseRelativeUrl(url: string, base?: string) {\n  const globalBase = new URL(\n    typeof window === 'undefined' ? 'http://n' : getLocationOrigin()\n  )\n  const resolvedBase = base ? new URL(base, globalBase) : globalBase\n  const { pathname, searchParams, search, hash, href, origin } = new URL(\n    url,\n    resolvedBase\n  )\n  if (origin !== globalBase.origin) {\n    throw new Error(`invariant: invalid relative URL, router received ${url}`)\n  }\n  return {\n    pathname,\n    query: searchParamsToUrlQuery(searchParams),\n    search,\n    hash,\n    href: href.slice(globalBase.origin.length),\n  }\n}\n"]}