{"version": 3, "sources": ["../../../../../../build/webpack/config/blocks/css/plugins.ts"], "names": ["genericErrorText", "getError_NullConfig", "pluginName", "chalk", "red", "bold", "isIgnoredPlugin", "pluginPath", "ignoredRegex", "match", "exec", "plugin", "pop", "console", "warn", "yellow", "underline", "createLazyPostCssPlugin", "fn", "result", "undefined", "args", "postcss", "loadPlugin", "dir", "options", "error", "Error", "require", "resolve", "paths", "keys", "Object", "length", "getDefaultPlugins", "baseDirectory", "isProduction", "browsers", "browserslist", "loadConfig", "path", "env", "autoprefixer", "flexbox", "stage", "features", "getPostCssPlugins", "defaults", "config", "plugins", "<PERSON><PERSON><PERSON>", "find", "key", "Array", "isArray", "pc", "reduce", "acc", "curr", "p", "push", "parsed", "for<PERSON>ach", "pluginConfig", "resolved", "Promise", "all", "map", "filtered", "filter", "Boolean"], "mappings": "iFAAA,oDACA,0DACA,kE,mFAYA,KAAMA,CAAAA,gBAAgB,CAAG,iCAAzB,CAEA,QAASC,CAAAA,mBAAT,CAA6BC,UAA7B,CAAiD,CAC/C,MAAQ,GAAEC,eAAMC,GAAN,CAAUC,IAAV,CACR,OADQ,CAER,qCAAoCH,UAAW,iBAAgBC,eAAME,IAAN,CAC/D,MAD+D,CAE/D,gCAA+BH,UAAW,WAAUC,eAAME,IAAN,CACpD,OADoD,CAEpD,qBAAoBF,eAAME,IAAN,CAAW,MAAX,CAAmB,6BANzC,CAOD,CAED,QAASC,CAAAA,eAAT,CAAyBC,UAAzB,CAAsD,CACpD,KAAMC,CAAAA,YAAY,CAAG,wJAArB,CACA,KAAMC,CAAAA,KAAK,CAAGD,YAAY,CAACE,IAAb,CAAkBH,UAAlB,CAAd,CACA,GAAIE,KAAK,EAAI,IAAb,CAAmB,CACjB,MAAO,MAAP,CACD,CAED,KAAME,CAAAA,MAAM,CAAGF,KAAK,CAACG,GAAN,EAAf,CACAC,OAAO,CAACC,IAAR,CACG,GAAEX,eAAMY,MAAN,CAAaV,IAAb,CAAkB,SAAlB,CAA6B,uBAAsBF,eAAMa,SAAN,CACpDL,MADoD,CAEpD,2CAFF,CAGG,uDAHH,CAIE,oEALJ,EAOA,MAAO,KAAP,CACD,CAED,KAAMM,CAAAA,uBAAuB,CAC3BC,EAD8B,EAEO,CACrC,GAAIC,CAAAA,MAAW,CAAGC,SAAlB,CACA,KAAMT,CAAAA,MAAM,CAAG,CAAC,GAAGU,IAAJ,GAAoB,CACjC,GAAIF,MAAM,GAAKC,SAAf,CAA0BD,MAAM,CAAGD,EAAE,EAAX,CAC1B,GAAIC,MAAM,CAACG,OAAP,GAAmB,IAAvB,CAA6B,CAC3B,MAAOH,CAAAA,MAAM,CAAC,GAAGE,IAAJ,CAAb,CACD,CAFD,IAEO,IAAIF,MAAM,CAACG,OAAX,CAAoB,CACzB,MAAOH,CAAAA,MAAM,CAACG,OAAd,CACD,CACD,MAAOH,CAAAA,MAAP,CACD,CARD,CASAR,MAAM,CAACW,OAAP,CAAiB,IAAjB,CACA,MAAOX,CAAAA,MAAP,CACD,CAfD,CAiBA,cAAeY,CAAAA,UAAf,CACEC,GADF,CAEEtB,UAFF,CAGEuB,OAHF,CAIqD,CACnD,GAAIA,OAAO,GAAK,KAAZ,EAAqBnB,eAAe,CAACJ,UAAD,CAAxC,CAAsD,CACpD,MAAO,MAAP,CACD,CAED,GAAIuB,OAAO,EAAI,IAAf,CAAqB,CACnBZ,OAAO,CAACa,KAAR,CAAczB,mBAAmB,CAACC,UAAD,CAAjC,EACA,KAAM,IAAIyB,CAAAA,KAAJ,CAAU3B,gBAAV,CAAN,CACD,CAED,KAAMO,CAAAA,UAAU,CAAGqB,OAAO,CAACC,OAAR,CAAgB3B,UAAhB,CAA4B,CAAE4B,KAAK,CAAE,CAACN,GAAD,CAAT,CAA5B,CAAnB,CACA,GAAIlB,eAAe,CAACC,UAAD,CAAnB,CAAiC,CAC/B,MAAO,MAAP,CACD,CAFD,IAEO,IAAIkB,OAAO,GAAK,IAAhB,CAAsB,CAC3B,MAAOR,CAAAA,uBAAuB,CAAC,IAAMW,OAAO,CAACrB,UAAD,CAAd,CAA9B,CACD,CAFM,IAEA,CACL,KAAMwB,CAAAA,IAAI,CAAGC,MAAM,CAACD,IAAP,CAAYN,OAAZ,CAAb,CACA,GAAIM,IAAI,CAACE,MAAL,GAAgB,CAApB,CAAuB,CACrB,MAAOhB,CAAAA,uBAAuB,CAAC,IAAMW,OAAO,CAACrB,UAAD,CAAd,CAA9B,CACD,CACD,MAAOU,CAAAA,uBAAuB,CAAC,IAAMW,OAAO,CAACrB,UAAD,CAAP,CAAoBkB,OAApB,CAAP,CAA9B,CACD,CACF,CAED,QAASS,CAAAA,iBAAT,CACEC,aADF,CAEEC,YAFF,CAGuB,eACrB,GAAIC,CAAAA,QAAJ,CACA,GAAI,CACFA,QAAQ,CAAGC,sBAAaC,UAAb,CAAwB,CACjCC,IAAI,CAAEL,aAD2B,CAEjCM,GAAG,CAAEL,YAAY,CAAG,YAAH,CAAkB,aAFF,CAAxB,CAAX,CAID,CAAC,cAAM,CAAE,CAEV,MAAO,CACLR,OAAO,CAACC,OAAR,CAAgB,2CAAhB,CADK,CAEL,CACED,OAAO,CAACC,OAAR,CAAgB,uCAAhB,CADF,CAEE,CACEQ,QAAQ,YAAEA,QAAF,kBAAc,CAAC,UAAD,CADxB,CAEEK,YAAY,CAAE,CACZ;AACAC,OAAO,CAAE,SAFG,CAFhB,CAME;AACA;AACAC,KAAK,CAAE,CART,CASEC,QAAQ,CAAE,CACR,oBAAqB,KADb,CATZ,CAFF,CAFK,CAAP,CAmBD,CAEM,cAAeC,CAAAA,iBAAf,CACLtB,GADK,CAELY,YAFK,CAGLW,QAAiB,CAAG,KAHf,CAIwC,CAC7C,GAAIC,CAAAA,MAAM,CAAGD,QAAQ,CACjB,IADiB,CAEjB,KAAM,2BAA6CvB,GAA7C,CAAkD,SAAlD,CAFV,CAIA,GAAIwB,MAAM,EAAI,IAAd,CAAoB,CAClBA,MAAM,CAAG,CAAEC,OAAO,CAAEf,iBAAiB,CAACV,GAAD,CAAMY,YAAN,CAA5B,CAAT,CACD,CAED,GAAI,MAAOY,CAAAA,MAAP,GAAkB,UAAtB,CAAkC,CAChC,KAAM,IAAIrB,CAAAA,KAAJ,CACH,sGAAD,CACE,8DAFE,CAAN,CAID,CAED;AACA,KAAMuB,CAAAA,UAAU,CAAGlB,MAAM,CAACD,IAAP,CAAYiB,MAAZ,EAAoBG,IAApB,CAA0BC,GAAD,EAASA,GAAG,GAAK,SAA1C,CAAnB,CACA,GAAIF,UAAJ,CAAgB,CACdrC,OAAO,CAACC,IAAR,CACG,GAAEX,eAAMY,MAAN,CAAaV,IAAb,CACD,SADC,CAED,0EAAyE6C,UAAW,OAFtF,CAGG,yCAJL,EAMD,CAED;AACA,GAAID,CAAAA,OAAO,CAAGD,MAAM,CAACC,OAArB,CACA,GAAIA,OAAO,EAAI,IAAX,EAAmB,MAAOA,CAAAA,OAAP,GAAmB,QAA1C,CAAoD,CAClD,KAAM,IAAItB,CAAAA,KAAJ,CACH,kEADG,CAAN,CAGD,CAED,GAAI,CAAC0B,KAAK,CAACC,OAAN,CAAcL,OAAd,CAAL,CAA6B,CAC3B;AACA,KAAMM,CAAAA,EAAE,CAAGN,OAAX,CAEAA,OAAO,CAAGjB,MAAM,CAACD,IAAP,CAAYkB,OAAZ,EAAqBO,MAArB,CAA4B,CAACC,GAAD,CAAMC,IAAN,GAAe,CACnD,KAAMC,CAAAA,CAAC,CAAGJ,EAAE,CAACG,IAAD,CAAZ,CACA,GAAI,MAAOC,CAAAA,CAAP,GAAa,WAAjB,CAA8B,CAC5B9C,OAAO,CAACa,KAAR,CAAczB,mBAAmB,CAACyD,IAAD,CAAjC,EACA,KAAM,IAAI/B,CAAAA,KAAJ,CAAU3B,gBAAV,CAAN,CACD,CAEDyD,GAAG,CAACG,IAAJ,CAAS,CAACF,IAAD,CAAOC,CAAP,CAAT,EACA,MAAOF,CAAAA,GAAP,CACD,CATS,CASP,EATO,CAAV,CAUD,CAED,KAAMI,CAAAA,MAAwB,CAAG,EAAjC,CACAZ,OAAO,CAACa,OAAR,CAAiBnD,MAAD,EAAY,CAC1B,GAAIA,MAAM,EAAI,IAAd,CAAoB,CAClBE,OAAO,CAACC,IAAR,CACG,GAAEX,eAAMY,MAAN,CAAaV,IAAb,CAAkB,SAAlB,CAA6B,OAAMF,eAAME,IAAN,CACpC,MADoC,CAEpC,2DAHJ,EAKD,CAND,IAMO,IAAI,MAAOM,CAAAA,MAAP,GAAkB,QAAtB,CAAgC,CACrCkD,MAAM,CAACD,IAAP,CAAY,CAACjD,MAAD,CAAS,IAAT,CAAZ,EACD,CAFM,IAEA,IAAI0C,KAAK,CAACC,OAAN,CAAc3C,MAAd,CAAJ,CAA2B,CAChC,KAAMT,CAAAA,UAAU,CAAGS,MAAM,CAAC,CAAD,CAAzB,CACA,KAAMoD,CAAAA,YAAY,CAAGpD,MAAM,CAAC,CAAD,CAA3B,CACA,GACE,MAAOT,CAAAA,UAAP,GAAsB,QAAtB,GACC,MAAO6D,CAAAA,YAAP,GAAwB,SAAxB,EAAqC,MAAOA,CAAAA,YAAP,GAAwB,QAD9D,CADF,CAGE,CACAF,MAAM,CAACD,IAAP,CAAY,CAAC1D,UAAD,CAAa6D,YAAb,CAAZ,EACD,CALD,IAKO,CACL,GAAI,MAAO7D,CAAAA,UAAP,GAAsB,QAA1B,CAAoC,CAClCW,OAAO,CAACa,KAAR,CACG,GAAEvB,eAAMC,GAAN,CAAUC,IAAV,CACD,OADC,CAED,4CAA2CF,eAAME,IAAN,CAC3C,QAD2C,CAE3C,uBAAsBH,UAAW,MAJnC,CAKE,2DANJ,EAQD,CATD,IASO,CACLW,OAAO,CAACa,KAAR,CACG,GAAEvB,eAAMC,GAAN,CAAUC,IAAV,CACD,OADC,CAED,qFAAoFH,UAAW,OAFjG,CAGE,2DAJJ,EAMD,CACD,KAAM,IAAIyB,CAAAA,KAAJ,CAAU3B,gBAAV,CAAN,CACD,CACF,CA5BM,IA4BA,IAAI,MAAOW,CAAAA,MAAP,GAAkB,UAAtB,CAAkC,CACvCE,OAAO,CAACa,KAAR,CACG,GAAEvB,eAAMC,GAAN,CAAUC,IAAV,CACD,OADC,CAED,6FAA4FF,eAAME,IAAN,CAC5F,QAD4F,CAE5F,8DALJ,EAOA,KAAM,IAAIsB,CAAAA,KAAJ,CAAU3B,gBAAV,CAAN,CACD,CATM,IASA,CACLa,OAAO,CAACa,KAAR,CACG,GAAEvB,eAAMC,GAAN,CAAUC,IAAV,CACD,OADC,CAED,6CAA4CM,MAAO,MAFrD,CAGE,2DAJJ,EAMA,KAAM,IAAIgB,CAAAA,KAAJ,CAAU3B,gBAAV,CAAN,CACD,CACF,CAvDD,EAyDA,KAAMgE,CAAAA,QAAQ,CAAG,KAAMC,CAAAA,OAAO,CAACC,GAAR,CACrBL,MAAM,CAACM,GAAP,CAAYR,CAAD,EAAOpC,UAAU,CAACC,GAAD,CAAMmC,CAAC,CAAC,CAAD,CAAP,CAAYA,CAAC,CAAC,CAAD,CAAb,CAA5B,CADqB,CAAvB,CAGA,KAAMS,CAAAA,QAA4C,CAAGJ,QAAQ,CAACK,MAAT,CACnDC,OADmD,CAArD,CAIA,MAAOF,CAAAA,QAAP,CACD", "sourcesContent": ["import chalk from 'chalk'\nimport { findConfig } from '../../../../../lib/find-config'\nimport browserslist from 'browserslist'\n\ntype CssPluginCollection_Array = (string | [string, boolean | object])[]\n\ntype CssPluginCollection_Object = { [key: string]: object | boolean }\n\ntype CssPluginCollection =\n  | CssPluginCollection_Array\n  | CssPluginCollection_Object\n\ntype CssPluginShape = [string, object | boolean]\n\nconst genericErrorText = 'Malformed PostCSS Configuration'\n\nfunction getError_NullConfig(pluginName: string) {\n  return `${chalk.red.bold(\n    'Error'\n  )}: Your PostCSS configuration for '${pluginName}' cannot have ${chalk.bold(\n    'null'\n  )} configuration.\\nTo disable '${pluginName}', pass ${chalk.bold(\n    'false'\n  )}, otherwise, pass ${chalk.bold('true')} or a configuration object.`\n}\n\nfunction isIgnoredPlugin(pluginPath: string): boolean {\n  const ignoredRegex = /(?:^|[\\\\/])(postcss-modules-values|postcss-modules-scope|postcss-modules-extract-imports|postcss-modules-local-by-default|postcss-modules)(?:[\\\\/]|$)/i\n  const match = ignoredRegex.exec(pluginPath)\n  if (match == null) {\n    return false\n  }\n\n  const plugin = match.pop()!\n  console.warn(\n    `${chalk.yellow.bold('Warning')}: Please remove the ${chalk.underline(\n      plugin\n    )} plugin from your PostCSS configuration. ` +\n      `This plugin is automatically configured by Next.js.\\n` +\n      'Read more: https://nextjs.org/docs/messages/postcss-ignored-plugin'\n  )\n  return true\n}\n\nconst createLazyPostCssPlugin = (\n  fn: () => import('postcss').AcceptedPlugin\n): import('postcss').AcceptedPlugin => {\n  let result: any = undefined\n  const plugin = (...args: any[]) => {\n    if (result === undefined) result = fn() as any\n    if (result.postcss === true) {\n      return result(...args)\n    } else if (result.postcss) {\n      return result.postcss\n    }\n    return result\n  }\n  plugin.postcss = true\n  return plugin\n}\n\nasync function loadPlugin(\n  dir: string,\n  pluginName: string,\n  options: boolean | object\n): Promise<import('postcss').AcceptedPlugin | false> {\n  if (options === false || isIgnoredPlugin(pluginName)) {\n    return false\n  }\n\n  if (options == null) {\n    console.error(getError_NullConfig(pluginName))\n    throw new Error(genericErrorText)\n  }\n\n  const pluginPath = require.resolve(pluginName, { paths: [dir] })\n  if (isIgnoredPlugin(pluginPath)) {\n    return false\n  } else if (options === true) {\n    return createLazyPostCssPlugin(() => require(pluginPath))\n  } else {\n    const keys = Object.keys(options)\n    if (keys.length === 0) {\n      return createLazyPostCssPlugin(() => require(pluginPath))\n    }\n    return createLazyPostCssPlugin(() => require(pluginPath)(options))\n  }\n}\n\nfunction getDefaultPlugins(\n  baseDirectory: string,\n  isProduction: boolean\n): CssPluginCollection {\n  let browsers: any\n  try {\n    browsers = browserslist.loadConfig({\n      path: baseDirectory,\n      env: isProduction ? 'production' : 'development',\n    })\n  } catch {}\n\n  return [\n    require.resolve('next/dist/compiled/postcss-flexbugs-fixes'),\n    [\n      require.resolve('next/dist/compiled/postcss-preset-env'),\n      {\n        browsers: browsers ?? ['defaults'],\n        autoprefixer: {\n          // Disable legacy flexbox support\n          flexbox: 'no-2009',\n        },\n        // Enable CSS features that have shipped to the\n        // web platform, i.e. in 2+ browsers unflagged.\n        stage: 3,\n        features: {\n          'custom-properties': false,\n        },\n      },\n    ],\n  ]\n}\n\nexport async function getPostCssPlugins(\n  dir: string,\n  isProduction: boolean,\n  defaults: boolean = false\n): Promise<import('postcss').AcceptedPlugin[]> {\n  let config = defaults\n    ? null\n    : await findConfig<{ plugins: CssPluginCollection }>(dir, 'postcss')\n\n  if (config == null) {\n    config = { plugins: getDefaultPlugins(dir, isProduction) }\n  }\n\n  if (typeof config === 'function') {\n    throw new Error(\n      `Your custom PostCSS configuration may not export a function. Please export a plain object instead.\\n` +\n        'Read more: https://nextjs.org/docs/messages/postcss-function'\n    )\n  }\n\n  // Warn user about configuration keys which are not respected\n  const invalidKey = Object.keys(config).find((key) => key !== 'plugins')\n  if (invalidKey) {\n    console.warn(\n      `${chalk.yellow.bold(\n        'Warning'\n      )}: Your PostCSS configuration defines a field which is not supported (\\`${invalidKey}\\`). ` +\n        `Please remove this configuration value.`\n    )\n  }\n\n  // Enforce the user provided plugins if the configuration file is present\n  let plugins = config.plugins\n  if (plugins == null || typeof plugins !== 'object') {\n    throw new Error(\n      `Your custom PostCSS configuration must export a \\`plugins\\` key.`\n    )\n  }\n\n  if (!Array.isArray(plugins)) {\n    // Capture variable so TypeScript is happy\n    const pc = plugins\n\n    plugins = Object.keys(plugins).reduce((acc, curr) => {\n      const p = pc[curr]\n      if (typeof p === 'undefined') {\n        console.error(getError_NullConfig(curr))\n        throw new Error(genericErrorText)\n      }\n\n      acc.push([curr, p])\n      return acc\n    }, [] as CssPluginCollection_Array)\n  }\n\n  const parsed: CssPluginShape[] = []\n  plugins.forEach((plugin) => {\n    if (plugin == null) {\n      console.warn(\n        `${chalk.yellow.bold('Warning')}: A ${chalk.bold(\n          'null'\n        )} PostCSS plugin was provided. This entry will be ignored.`\n      )\n    } else if (typeof plugin === 'string') {\n      parsed.push([plugin, true])\n    } else if (Array.isArray(plugin)) {\n      const pluginName = plugin[0]\n      const pluginConfig = plugin[1]\n      if (\n        typeof pluginName === 'string' &&\n        (typeof pluginConfig === 'boolean' || typeof pluginConfig === 'object')\n      ) {\n        parsed.push([pluginName, pluginConfig])\n      } else {\n        if (typeof pluginName !== 'string') {\n          console.error(\n            `${chalk.red.bold(\n              'Error'\n            )}: A PostCSS Plugin must be provided as a ${chalk.bold(\n              'string'\n            )}. Instead, we got: '${pluginName}'.\\n` +\n              'Read more: https://nextjs.org/docs/messages/postcss-shape'\n          )\n        } else {\n          console.error(\n            `${chalk.red.bold(\n              'Error'\n            )}: A PostCSS Plugin was passed as an array but did not provide its configuration ('${pluginName}').\\n` +\n              'Read more: https://nextjs.org/docs/messages/postcss-shape'\n          )\n        }\n        throw new Error(genericErrorText)\n      }\n    } else if (typeof plugin === 'function') {\n      console.error(\n        `${chalk.red.bold(\n          'Error'\n        )}: A PostCSS Plugin was passed as a function using require(), but it must be provided as a ${chalk.bold(\n          'string'\n        )}.\\nRead more: https://nextjs.org/docs/messages/postcss-shape`\n      )\n      throw new Error(genericErrorText)\n    } else {\n      console.error(\n        `${chalk.red.bold(\n          'Error'\n        )}: An unknown PostCSS plugin was provided (${plugin}).\\n` +\n          'Read more: https://nextjs.org/docs/messages/postcss-shape'\n      )\n      throw new Error(genericErrorText)\n    }\n  })\n\n  const resolved = await Promise.all(\n    parsed.map((p) => loadPlugin(dir, p[0], p[1]))\n  )\n  const filtered: import('postcss').AcceptedPlugin[] = resolved.filter(\n    Boolean\n  ) as import('postcss').AcceptedPlugin[]\n\n  return filtered\n}\n"]}