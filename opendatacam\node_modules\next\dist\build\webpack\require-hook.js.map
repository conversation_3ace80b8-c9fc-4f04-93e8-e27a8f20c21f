{"version": 3, "sources": ["../../../build/webpack/require-hook.ts"], "names": ["hookPropertyMap", "Map", "map", "request", "replacement", "require", "resolve", "mod", "resolveFilename", "_resolveFilename", "parent", "is<PERSON><PERSON>", "options", "hookResolved", "get", "call"], "mappings": "aAAA;AACA;AACA;AAEA,KAAMA,CAAAA,eAAe,CAAG,GAAIC,CAAAA,GAAJ,CACtB,CACE,CAAC,SAAD,CAAY,wCAAZ,CADF,CAEE,CAAC,iBAAD,CAAoB,oCAApB,CAFF,CAGE,CAAC,sBAAD,CAAyB,oCAAzB,CAHF,CAIE,CAAC,qBAAD,CAAwB,wCAAxB,CAJF,CAKE,CAAC,wBAAD,CAA2B,wCAA3B,CALF,CAME,CACE,wCADF,CAEE,kDAFF,CANF,CAUE,CACE,2CADF,CAEE,kDAFF,CAVF,CAcE,CACE,sCADF,CAEE,qDAFF,CAdF,CAkBE,CACE,yCADF,CAEE,qDAFF,CAlBF,CAsBE,CACE,mCADF,CAEE,6CAFF,CAtBF,CA0BE,CACE,sCADF,CAEE,6CAFF,CA1BF,CA8BE,CACE,mCADF,CAEE,kDAFF,CA9BF,CAkCE,CACE,sCADF,CAEE,kDAFF,CAlCF,CAsCE,CAAC,0BAAD,CAA6B,yCAA7B,CAtCF,CAuCE,CAAC,6BAAD,CAAgC,yCAAhC,CAvCF,CAwCE,CAAC,0BAAD,CAA6B,yCAA7B,CAxCF,CAyCE,CAAC,iBAAD,CAAoB,oCAApB,CAzCF,CA0CE,CAAC,qBAAD,CAAwB,oCAAxB,CA1CF,CA2CE,CAAC,2BAAD,CAA8B,oCAA9B,CA3CF,CA4CE,CAAC,8BAAD,CAAiC,oCAAjC,CA5CF,EA6CEC,GA7CF,CA6CM,CAAC,CAACC,OAAD,CAAUC,WAAV,CAAD,GAA4B,CAACD,OAAD,CAAUE,OAAO,CAACC,OAAR,CAAgBF,WAAhB,CAAV,CA7ClC,CADsB,CAAxB,CAiDA,KAAMG,CAAAA,GAAG,CAAGF,OAAO,CAAC,QAAD,CAAnB,CACA,KAAMG,CAAAA,eAAe,CAAGD,GAAG,CAACE,gBAA5B,CACAF,GAAG,CAACE,gBAAJ,CAAuB,SACrBN,OADqB,CAErBO,MAFqB,CAGrBC,MAHqB,CAIrBC,OAJqB,CAKrB,CACA,KAAMC,CAAAA,YAAY,CAAGb,eAAe,CAACc,GAAhB,CAAoBX,OAApB,CAArB,CACA,GAAIU,YAAJ,CAAkBV,OAAO,CAAGU,YAAV,CAClB,MAAOL,CAAAA,eAAe,CAACO,IAAhB,CAAqBR,GAArB,CAA0BJ,OAA1B,CAAmCO,MAAnC,CAA2CC,MAA3C,CAAmDC,OAAnD,CAAP,CACD,CATD", "sourcesContent": ["// sync injects a hook for webpack and webpack/... requires to use the internal ncc webpack version\n// this is in order for userland plugins to attach to the same webpack instance as next.js\n// the individual compiled modules are as defined for the compilation in bundles/webpack/packages/*\n\nconst hookPropertyMap = new Map(\n  [\n    ['webpack', 'next/dist/compiled/webpack/webpack-lib'],\n    ['webpack/package', 'next/dist/compiled/webpack/package'],\n    ['webpack/package.json', 'next/dist/compiled/webpack/package'],\n    ['webpack/lib/webpack', 'next/dist/compiled/webpack/webpack-lib'],\n    ['webpack/lib/webpack.js', 'next/dist/compiled/webpack/webpack-lib'],\n    [\n      'webpack/lib/node/NodeEnvironmentPlugin',\n      'next/dist/compiled/webpack/NodeEnvironmentPlugin',\n    ],\n    [\n      'webpack/lib/node/NodeEnvironmentPlugin.js',\n      'next/dist/compiled/webpack/NodeEnvironmentPlugin',\n    ],\n    [\n      'webpack/lib/BasicEvaluatedExpression',\n      'next/dist/compiled/webpack/BasicEvaluatedExpression',\n    ],\n    [\n      'webpack/lib/BasicEvaluatedExpression.js',\n      'next/dist/compiled/webpack/BasicEvaluatedExpression',\n    ],\n    [\n      'webpack/lib/node/NodeTargetPlugin',\n      'next/dist/compiled/webpack/NodeTargetPlugin',\n    ],\n    [\n      'webpack/lib/node/NodeTargetPlugin.js',\n      'next/dist/compiled/webpack/NodeTargetPlugin',\n    ],\n    [\n      'webpack/lib/ModuleFilenameHelpers',\n      'next/dist/compiled/webpack/ModuleFilenameHelpers',\n    ],\n    [\n      'webpack/lib/ModuleFilenameHelpers.js',\n      'next/dist/compiled/webpack/ModuleFilenameHelpers',\n    ],\n    ['webpack/lib/GraphHelpers', 'next/dist/compiled/webpack/GraphHelpers'],\n    ['webpack/lib/GraphHelpers.js', 'next/dist/compiled/webpack/GraphHelpers'],\n    ['webpack/lib/NormalModule', 'next/dist/compiled/webpack/NormalModule'],\n    ['webpack-sources', 'next/dist/compiled/webpack/sources'],\n    ['webpack-sources/lib', 'next/dist/compiled/webpack/sources'],\n    ['webpack-sources/lib/index', 'next/dist/compiled/webpack/sources'],\n    ['webpack-sources/lib/index.js', 'next/dist/compiled/webpack/sources'],\n  ].map(([request, replacement]) => [request, require.resolve(replacement)])\n)\n\nconst mod = require('module')\nconst resolveFilename = mod._resolveFilename\nmod._resolveFilename = function (\n  request: string,\n  parent: any,\n  isMain: boolean,\n  options: any\n) {\n  const hookResolved = hookPropertyMap.get(request)\n  if (hookResolved) request = hookResolved\n  return resolveFilename.call(mod, request, parent, isMain, options)\n}\n"]}