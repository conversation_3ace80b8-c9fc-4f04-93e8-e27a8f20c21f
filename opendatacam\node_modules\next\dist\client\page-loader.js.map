{"version": 3, "sources": ["../../client/page-loader.ts"], "names": ["normalizeRoute", "route", "Error", "replace", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "buildId", "assetPrefix", "promisedSsgManifest", "promisedDevPagesManifest", "routeLoader", "Promise", "resolve", "window", "__SSG_MANIFEST", "__SSG_MANIFEST_CB", "getPageList", "process", "env", "NODE_ENV", "then", "manifest", "sortedPages", "__DEV_PAGES_MANIFEST", "pages", "fetch", "res", "json", "catch", "err", "console", "log", "getDataHref", "href", "<PERSON><PERSON><PERSON>", "ssg", "locale", "pathname", "hrefPathname", "query", "search", "asPathname", "getHrefForSlug", "path", "dataRoute", "isDynamic", "interpolatedRoute", "result", "_isSsg", "s", "has", "loadPage", "loadRoute", "page", "component", "mod", "exports", "styleSheets", "styles", "map", "o", "text", "content", "error", "prefetch"], "mappings": "oOAEA,wDAKA,wHACA,qEACA,oFACA,kEACA,oEAKA,QAASA,CAAAA,cAAT,CAAwBC,KAAxB,CAA+C,CAC7C,GAAIA,KAAK,CAAC,CAAD,CAAL,GAAa,GAAjB,CAAsB,CACpB,KAAM,IAAIC,CAAAA,KAAJ,CAAW,4CAA2CD,KAAM,GAA5D,CAAN,CACD,CAED,GAAIA,KAAK,GAAK,GAAd,CAAmB,MAAOA,CAAAA,KAAP,CACnB,MAAOA,CAAAA,KAAK,CAACE,OAAN,CAAc,KAAd,CAAqB,EAArB,CAAP,CACD,CASc,KAAMC,CAAAA,UAAW,CAQ9BC,WAAW,CAACC,OAAD,CAAkBC,WAAlB,CAAuC,MAP1CD,OAO0C,aAN1CC,WAM0C,aAJ1CC,mBAI0C,aAH1CC,wBAG0C,aAF3CC,WAE2C,QAChD,KAAKA,WAAL,CAAmB,yBAAkBH,WAAlB,CAAnB,CAEA,KAAKD,OAAL,CAAeA,OAAf,CACA,KAAKC,WAAL,CAAmBA,WAAnB,CAEA,mCACA,KAAKC,mBAAL,CAA2B,GAAIG,CAAAA,OAAJ,CAAaC,OAAD,EAAa,CAClD,GAAKC,MAAD,CAAgBC,cAApB,CAAoC,CAClCF,OAAO,CAAEC,MAAD,CAAgBC,cAAjB,CAAP,CACD,CAFD,IAEO,CACL,CAAED,MAAD,CAAgBE,iBAAhB,CAAoC,IAAM,CACzCH,OAAO,CAAEC,MAAD,CAAgBC,cAAjB,CAAP,CACD,CAFA,CAGF,CACF,CAR0B,CAA3B,CASD,CAEDE,WAAW,EAAG,CACZ,GAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,GAAyB,YAA7B,CAA2C,CACzC,MAAO,0CAAyBC,IAAzB,CAA+BC,QAAD,EAAcA,QAAQ,CAACC,WAArD,CAAP,CACD,CAFD,IAEO,CACL,GAAKT,MAAD,CAAgBU,oBAApB,CAA0C,CACxC,MAAQV,CAAAA,MAAD,CAAgBU,oBAAhB,CAAqCC,KAA5C,CACD,CAFD,IAEO,CACL,GAAI,CAAC,KAAKf,wBAAV,CAAoC,CAClC,KAAKA,wBAAL,CAAgCgB,KAAK,CAClC,GAAE,KAAKlB,WAAY,kDADe,CAAL,CAG7Ba,IAH6B,CAGvBM,GAAD,EAASA,GAAG,CAACC,IAAJ,EAHe,EAI7BP,IAJ6B,CAIvBC,QAAD,EAAc,CAClB,CAAER,MAAD,CAAgBU,oBAAhB,CAAuCF,QAAvC,CACD,MAAOA,CAAAA,QAAQ,CAACG,KAAhB,CACD,CAP6B,EAQ7BI,KAR6B,CAQtBC,GAAD,EAAS,CACdC,OAAO,CAACC,GAAR,CAAa,kCAAb,CAAgDF,GAAhD,EACD,CAV6B,CAAhC,CAWD,CACD,MAAO,MAAKpB,wBAAZ,CACD,CACF,CACF,CAED;AACF;AACA;AACA;AACA,KACEuB,WAAW,CACTC,IADS,CAETC,MAFS,CAGTC,GAHS,CAITC,MAJS,CAKD,CACR,KAAM,CAAEC,QAAQ,CAAEC,YAAZ,CAA0BC,KAA1B,CAAiCC,MAAjC,EAA4C,uCAAiBP,IAAjB,CAAlD,CACA,KAAM,CAAEI,QAAQ,CAAEI,UAAZ,EAA2B,uCAAiBP,MAAjB,CAAjC,CACA,KAAMjC,CAAAA,KAAK,CAAGD,cAAc,CAACsC,YAAD,CAA5B,CAEA,KAAMI,CAAAA,cAAc,CAAIC,IAAD,EAAkB,CACvC,KAAMC,CAAAA,SAAS,CAAG,mCAChB,oDAAwB,sBAAUD,IAAV,CAAgBP,MAAhB,CAAxB,CADgB,CAEhB,OAFgB,CAAlB,CAIA,MAAO,wBACJ,eAAc,KAAK9B,OAAQ,GAAEsC,SAAU,GAAET,GAAG,CAAG,EAAH,CAAQK,MAAO,EADvD,CAAP,CAGD,CARD,CAUA,KAAMK,CAAAA,SAAkB,CAAG,8BAAe5C,KAAf,CAA3B,CACA,KAAM6C,CAAAA,iBAAiB,CAAGD,SAAS,CAC/B,0BAAcP,YAAd,CAA4BG,UAA5B,CAAwCF,KAAxC,EAA+CQ,MADhB,CAE/B,EAFJ,CAIA,MAAOF,CAAAA,SAAS,CACZC,iBAAiB,EAAIJ,cAAc,CAACI,iBAAD,CADvB,CAEZJ,cAAc,CAACzC,KAAD,CAFlB,CAGD,CAED;AACF;AACA,KACE+C,MAAM,CAAC/C,KAAD,CAAkC,CACtC,MAAO,MAAKO,mBAAL,CAA0BY,IAA1B,CAAgC6B,CAAD,EACpCA,CAAC,CAACC,GAAF,CAAMjD,KAAN,CADK,CAAP,CAGD,CAEDkD,QAAQ,CAAClD,KAAD,CAAwC,CAC9C,MAAO,MAAKS,WAAL,CAAiB0C,SAAjB,CAA2BnD,KAA3B,EAAkCmB,IAAlC,CAAwCM,GAAD,EAAS,CACrD,GAAI,aAAeA,CAAAA,GAAnB,CAAwB,CACtB,MAAO,CACL2B,IAAI,CAAE3B,GAAG,CAAC4B,SADL,CAELC,GAAG,CAAE7B,GAAG,CAAC8B,OAFJ,CAGLC,WAAW,CAAE/B,GAAG,CAACgC,MAAJ,CAAWC,GAAX,CAAgBC,CAAD,GAAQ,CAClC3B,IAAI,CAAE2B,CAAC,CAAC3B,IAD0B,CAElC4B,IAAI,CAAED,CAAC,CAACE,OAF0B,CAAR,CAAf,CAHR,CAAP,CAQD,CACD,KAAMpC,CAAAA,GAAG,CAACqC,KAAV,CACD,CAZM,CAAP,CAaD,CAEDC,QAAQ,CAAC/D,KAAD,CAA+B,CACrC,MAAO,MAAKS,WAAL,CAAiBsD,QAAjB,CAA0B/D,KAA1B,CAAP,CACD,CAjH6B,C", "sourcesContent": ["import { ComponentType } from 'react'\nimport { ClientSsgManifest } from '../build'\nimport {\n  addBasePath,\n  addLocale,\n  interpolateAs,\n} from '../next-server/lib/router/router'\nimport getAssetPathFromRoute from '../next-server/lib/router/utils/get-asset-path-from-route'\nimport { isDynamicRoute } from '../next-server/lib/router/utils/is-dynamic'\nimport { parseRelativeUrl } from '../next-server/lib/router/utils/parse-relative-url'\nimport { removePathTrailingSlash } from './normalize-trailing-slash'\nimport createRouteLoader, {\n  getClientBuildManifest,\n  RouteLoader,\n} from './route-loader'\n\nfunction normalizeRoute(route: string): string {\n  if (route[0] !== '/') {\n    throw new Error(`Route name should start with a \"/\", got \"${route}\"`)\n  }\n\n  if (route === '/') return route\n  return route.replace(/\\/$/, '')\n}\n\nexport type StyleSheetTuple = { href: string; text: string }\nexport type GoodPageCache = {\n  page: ComponentType\n  mod: any\n  styleSheets: StyleSheetTuple[]\n}\n\nexport default class PageLoader {\n  private buildId: string\n  private assetPrefix: string\n\n  private promisedSsgManifest?: Promise<ClientSsgManifest>\n  private promisedDevPagesManifest?: Promise<any>\n  public routeLoader: RouteLoader\n\n  constructor(buildId: string, assetPrefix: string) {\n    this.routeLoader = createRouteLoader(assetPrefix)\n\n    this.buildId = buildId\n    this.assetPrefix = assetPrefix\n\n    /** @type {Promise<Set<string>>} */\n    this.promisedSsgManifest = new Promise((resolve) => {\n      if ((window as any).__SSG_MANIFEST) {\n        resolve((window as any).__SSG_MANIFEST)\n      } else {\n        ;(window as any).__SSG_MANIFEST_CB = () => {\n          resolve((window as any).__SSG_MANIFEST)\n        }\n      }\n    })\n  }\n\n  getPageList() {\n    if (process.env.NODE_ENV === 'production') {\n      return getClientBuildManifest().then((manifest) => manifest.sortedPages)\n    } else {\n      if ((window as any).__DEV_PAGES_MANIFEST) {\n        return (window as any).__DEV_PAGES_MANIFEST.pages\n      } else {\n        if (!this.promisedDevPagesManifest) {\n          this.promisedDevPagesManifest = fetch(\n            `${this.assetPrefix}/_next/static/development/_devPagesManifest.json`\n          )\n            .then((res) => res.json())\n            .then((manifest) => {\n              ;(window as any).__DEV_PAGES_MANIFEST = manifest\n              return manifest.pages\n            })\n            .catch((err) => {\n              console.log(`Failed to fetch devPagesManifest`, err)\n            })\n        }\n        return this.promisedDevPagesManifest\n      }\n    }\n  }\n\n  /**\n   * @param {string} href the route href (file-system path)\n   * @param {string} asPath the URL as shown in browser (virtual path); used for dynamic routes\n   * @returns {string}\n   */\n  getDataHref(\n    href: string,\n    asPath: string,\n    ssg: boolean,\n    locale?: string | false\n  ): string {\n    const { pathname: hrefPathname, query, search } = parseRelativeUrl(href)\n    const { pathname: asPathname } = parseRelativeUrl(asPath)\n    const route = normalizeRoute(hrefPathname)\n\n    const getHrefForSlug = (path: string) => {\n      const dataRoute = getAssetPathFromRoute(\n        removePathTrailingSlash(addLocale(path, locale)),\n        '.json'\n      )\n      return addBasePath(\n        `/_next/data/${this.buildId}${dataRoute}${ssg ? '' : search}`\n      )\n    }\n\n    const isDynamic: boolean = isDynamicRoute(route)\n    const interpolatedRoute = isDynamic\n      ? interpolateAs(hrefPathname, asPathname, query).result\n      : ''\n\n    return isDynamic\n      ? interpolatedRoute && getHrefForSlug(interpolatedRoute)\n      : getHrefForSlug(route)\n  }\n\n  /**\n   * @param {string} route - the route (file-system path)\n   */\n  _isSsg(route: string): Promise<boolean> {\n    return this.promisedSsgManifest!.then((s: ClientSsgManifest) =>\n      s.has(route)\n    )\n  }\n\n  loadPage(route: string): Promise<GoodPageCache> {\n    return this.routeLoader.loadRoute(route).then((res) => {\n      if ('component' in res) {\n        return {\n          page: res.component,\n          mod: res.exports,\n          styleSheets: res.styles.map((o) => ({\n            href: o.href,\n            text: o.content,\n          })),\n        }\n      }\n      throw res.error\n    })\n  }\n\n  prefetch(route: string): Promise<void> {\n    return this.routeLoader.prefetch(route)\n  }\n}\n"]}