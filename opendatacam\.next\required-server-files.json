{"version": 1, "config": {"env": [], "webpack": null, "webpackDevMiddleware": null, "distDir": ".next", "assetPrefix": "", "configOrigin": "default", "useFileSystemPublicRoutes": true, "generateEtags": true, "pageExtensions": ["tsx", "ts", "jsx", "js"], "target": "server", "poweredByHeader": true, "compress": false, "analyticsId": "", "images": {"deviceSizes": [640, 750, 828, 1080, 1200, 1920, 2048, 3840], "imageSizes": [16, 32, 48, 64, 96, 128, 256, 384], "path": "/_next/image", "loader": "default", "domains": [], "enableBlurryPlaceholder": false}, "devIndicators": {"buildActivity": true}, "onDemandEntries": {"maxInactiveAge": 60000, "pagesBufferLength": 2}, "amp": {"canonicalBase": ""}, "basePath": "", "sassOptions": {}, "trailingSlash": false, "i18n": null, "productionBrowserSourceMaps": false, "optimizeFonts": true, "experimental": {"cpus": 15, "plugins": false, "profiling": false, "sprFlushToDisk": true, "workerThreads": false, "pageEnv": false, "optimizeImages": false, "optimizeCss": false, "scrollRestoration": false, "scriptLoader": false, "stats": false, "externalDir": false, "serialWebpackBuild": false, "turboMode": false, "eslint": false, "reactRoot": false, "enableBlurryPlaceholder": false, "disableOptimizedLoading": true, "gzipSize": true}, "future": {"strictPostcssConfiguration": false, "excludeDefaultMomentLocales": false, "webpack5": false}, "serverRuntimeConfig": {}, "publicRuntimeConfig": {}, "reactStrictMode": false}, "appDir": "C:\\Users\\<USER>\\OneDrive\\Desktop\\OpenDataCam\\opendatacam", "files": [".next\\routes-manifest.json", ".next\\server\\pages-manifest.json", ".next\\build-manifest.json", ".next\\prerender-manifest.json", ".next\\react-loadable-manifest.json", ".next\\server\\font-manifest.json", ".next\\BUILD_ID"], "ignore": ["node_modules\\next\\dist\\compiled\\@ampproject\\toolbox-optimizer\\**\\*"]}