{"version": 3, "sources": ["../../../../../build/webpack/plugins/wellknown-errors-plugin/parseCss.ts"], "names": ["chalk", "Chalk", "constructor", "enabled", "regexCssError", "getCssError", "fileName", "err", "name", "stack", "SyntaxError", "res", "exec", "message", "_lineNumer", "_column", "reason", "lineNumber", "Math", "max", "parseInt", "column", "SimpleWebpackError", "cyan", "yellow", "toString", "red", "bold", "concat"], "mappings": "qEAAA,oDACA,wD,mFAEA,KAAMA,CAAAA,KAAK,CAAG,GAAIC,gBAAMC,WAAV,CAAsB,CAAEC,OAAO,CAAE,IAAX,CAAtB,CAAd,CACA,KAAMC,CAAAA,aAAa,CAAG,qEAAtB,CAEO,QAASC,CAAAA,WAAT,CACLC,QADK,CAELC,GAFK,CAGuB,CAC5B,GACE,EACE,CAACA,GAAG,CAACC,IAAJ,GAAa,gBAAb,EAAiCD,GAAG,CAACC,IAAJ,GAAa,aAA/C,GACCD,GAAD,CAAaE,KAAb,GAAuB,KADvB,EAEA,EAAEF,GAAG,WAAYG,CAAAA,WAAjB,CAHF,CADF,CAME,CACA,MAAO,MAAP,CACD,CAED;AAEA,KAAMC,CAAAA,GAAG,CAAGP,aAAa,CAACQ,IAAd,CAAmBL,GAAG,CAACM,OAAvB,CAAZ,CACA,GAAIF,GAAJ,CAAS,CACP,KAAM,EAAGG,UAAH,CAAeC,OAAf,CAAwBC,MAAxB,EAAkCL,GAAxC,CACA,KAAMM,CAAAA,UAAU,CAAGC,IAAI,CAACC,GAAL,CAAS,CAAT,CAAYC,QAAQ,CAACN,UAAD,CAAa,EAAb,CAApB,CAAnB,CACA,KAAMO,CAAAA,MAAM,CAAGH,IAAI,CAACC,GAAL,CAAS,CAAT,CAAYC,QAAQ,CAACL,OAAD,CAAU,EAAV,CAApB,CAAf,CAEA,MAAO,IAAIO,uCAAJ,CACJ,GAAEtB,KAAK,CAACuB,IAAN,CAAWjB,QAAX,CAAqB,IAAGN,KAAK,CAACwB,MAAN,CACzBP,UAAU,CAACQ,QAAX,EADyB,CAEzB,IAAGzB,KAAK,CAACwB,MAAN,CAAaH,MAAM,CAACI,QAAP,EAAb,CAAgC,EAHhC,CAILzB,KAAK,CAAC0B,GAAN,CAAUC,IAAV,CAAe,cAAf,EAA+BC,MAA/B,CAAuC,KAAIZ,MAAO,EAAlD,CAJK,CAAP,CAMD,CAED,MAAO,MAAP,CACD", "sourcesContent": ["import Chalk from 'chalk'\nimport { SimpleWebpackError } from './simpleWebpackError'\n\nconst chalk = new Chalk.constructor({ enabled: true })\nconst regexCssError = /^(?:CssSyntaxError|SyntaxError)\\n\\n\\((\\d+):(\\d*)\\) (.*)$/s\n\nexport function getCssError(\n  fileName: string,\n  err: Error\n): SimpleWebpackError | false {\n  if (\n    !(\n      (err.name === 'CssSyntaxError' || err.name === 'SyntaxError') &&\n      (err as any).stack === false &&\n      !(err instanceof SyntaxError)\n    )\n  ) {\n    return false\n  }\n\n  // https://github.com/postcss/postcss-loader/blob/d6931da177ac79707bd758436e476036a55e4f59/src/Error.js\n\n  const res = regexCssError.exec(err.message)\n  if (res) {\n    const [, _lineNumer, _column, reason] = res\n    const lineNumber = Math.max(1, parseInt(_lineNumer, 10))\n    const column = Math.max(1, parseInt(_column, 10))\n\n    return new SimpleWebpackError(\n      `${chalk.cyan(fileName)}:${chalk.yellow(\n        lineNumber.toString()\n      )}:${chalk.yellow(column.toString())}`,\n      chalk.red.bold('Syntax error').concat(`: ${reason}`)\n    )\n  }\n\n  return false\n}\n"]}