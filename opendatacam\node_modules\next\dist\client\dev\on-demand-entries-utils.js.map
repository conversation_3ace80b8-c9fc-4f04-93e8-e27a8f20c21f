{"version": 3, "sources": ["../../../client/dev/on-demand-entries-utils.js"], "names": ["evtSource", "currentPage", "closePing", "close", "setupPing", "assetPrefix", "pathnameFn", "retry", "pathname", "path", "timeout", "addMessageListener", "event", "data", "indexOf", "payload", "JSON", "parse", "invalid", "fetch", "location", "href", "credentials", "then", "pageRes", "status", "reload", "err", "console", "error"], "mappings": "wHACA,wDADA,qBAGA,GAAIA,CAAAA,SAAJ,CACO,GAAIC,CAAAA,WAAJ,C,gCAEA,QAASC,CAAAA,SAAT,EAAqB,CAC1B,GAAIF,SAAJ,CAAeA,SAAS,CAACG,KAAV,GACfH,SAAS,CAAG,IAAZ,CACD,CAEM,QAASI,CAAAA,SAAT,CAAmBC,WAAnB,CAAgCC,UAAhC,CAA4CC,KAA5C,CAAmD,CACxD,KAAMC,CAAAA,QAAQ,CAAGF,UAAU,EAA3B,CAEA;AACA,GAAIE,QAAQ,GAAKP,WAAb,EAA4B,CAACM,KAAjC,CAAwC,OACxC,oBAAAN,WAAW,CAAGO,QAAd,CACA;AACAN,SAAS,GAETF,SAAS,CAAG,uCAAsB,CAChCS,IAAI,CAAG,GAAEJ,WAAY,2BAA0BJ,WAAY,EAD3B,CAEhCS,OAAO,CAAE,IAFuB,CAAtB,CAAZ,CAKAV,SAAS,CAACW,kBAAV,CAA8BC,KAAD,EAAW,CACtC,GAAIA,KAAK,CAACC,IAAN,CAAWC,OAAX,CAAmB,GAAnB,IAA4B,CAAC,CAAjC,CAAoC,OACpC,GAAI,CACF,KAAMC,CAAAA,OAAO,CAAGC,IAAI,CAACC,KAAL,CAAWL,KAAK,CAACC,IAAjB,CAAhB,CACA,GAAIE,OAAO,CAACG,OAAZ,CAAqB,CACnB;AACA;AACAC,KAAK,CAACC,QAAQ,CAACC,IAAV,CAAgB,CACnBC,WAAW,CAAE,aADM,CAAhB,CAAL,CAEGC,IAFH,CAESC,OAAD,EAAa,CACnB,GAAIA,OAAO,CAACC,MAAR,GAAmB,GAAvB,CAA4B,CAC1BL,QAAQ,CAACM,MAAT,GACD,CACF,CAND,EAOD,CACF,CAAC,MAAOC,GAAP,CAAY,CACZC,OAAO,CAACC,KAAR,CAAc,4CAAd,CAA4DF,GAA5D,EACD,CACF,CAlBD,EAmBD", "sourcesContent": ["/* global location */\nimport { getEventSourceWrapper } from './error-overlay/eventsource'\n\nlet evtSource\nexport let currentPage\n\nexport function closePing() {\n  if (evtSource) evtSource.close()\n  evtSource = null\n}\n\nexport function setupPing(assetPrefix, pathnameFn, retry) {\n  const pathname = pathnameFn()\n\n  // Make sure to only create new EventSource request if page has changed\n  if (pathname === currentPage && !retry) return\n  currentPage = pathname\n  // close current EventSource connection\n  closePing()\n\n  evtSource = getEventSourceWrapper({\n    path: `${assetPrefix}/_next/webpack-hmr?page=${currentPage}`,\n    timeout: 5000,\n  })\n\n  evtSource.addMessageListener((event) => {\n    if (event.data.indexOf('{') === -1) return\n    try {\n      const payload = JSON.parse(event.data)\n      if (payload.invalid) {\n        // Payload can be invalid even if the page does not exist.\n        // So, we need to make sure it exists before reloading.\n        fetch(location.href, {\n          credentials: 'same-origin',\n        }).then((pageRes) => {\n          if (pageRes.status === 200) {\n            location.reload()\n          }\n        })\n      }\n    } catch (err) {\n      console.error('on-demand-entries failed to parse response', err)\n    }\n  })\n}\n"]}