{"version": 3, "sources": ["../../../../../../build/webpack/loaders/babel-loader/src/transform.js"], "names": ["transform", "_transform", "source", "options", "result", "err", "message", "codeFrame", "LoaderError", "ast", "code", "map", "metadata", "sourceType", "sourcesContent", "length"], "mappings": "8DAAA,mDACA,0BACA,sD,mFAEA,KAAMA,CAAAA,SAAS,CAAG,oBAAUC,eAAV,CAAlB,CAEe,wBAAgBC,MAAhB,CAAwBC,OAAxB,CAAiC,CAC9C,GAAIC,CAAAA,MAAJ,CACA,GAAI,CACFA,MAAM,CAAG,KAAMJ,CAAAA,SAAS,CAACE,MAAD,CAASC,OAAT,CAAxB,CACD,CAAC,MAAOE,GAAP,CAAY,CACZ,KAAMA,CAAAA,GAAG,CAACC,OAAJ,EAAeD,GAAG,CAACE,SAAnB,CAA+B,GAAIC,eAAJ,CAAgBH,GAAhB,CAA/B,CAAsDA,GAA5D,CACD,CAED,GAAI,CAACD,MAAL,CAAa,MAAO,KAAP,CAEb;AACA;AACA;AACA;AACA;AACA,KAAM,CAAEK,GAAF,CAAOC,IAAP,CAAaC,GAAb,CAAkBC,QAAlB,CAA4BC,UAA5B,EAA2CT,MAAjD,CAEA,GAAIO,GAAG,GAAK,CAACA,GAAG,CAACG,cAAL,EAAuB,CAACH,GAAG,CAACG,cAAJ,CAAmBC,MAAhD,CAAP,CAAgE,CAC9DJ,GAAG,CAACG,cAAJ,CAAqB,CAACZ,MAAD,CAArB,CACD,CAED,MAAO,CAAEO,GAAF,CAAOC,IAAP,CAAaC,GAAb,CAAkBC,QAAlB,CAA4BC,UAA5B,CAAP,CACD", "sourcesContent": ["import { transform as _transform } from 'next/dist/compiled/babel/core'\nimport { promisify } from 'util'\nimport LoaderError from './Error'\n\nconst transform = promisify(_transform)\n\nexport default async function (source, options) {\n  let result\n  try {\n    result = await transform(source, options)\n  } catch (err) {\n    throw err.message && err.codeFrame ? new LoaderError(err) : err\n  }\n\n  if (!result) return null\n\n  // We don't return the full result here because some entries are not\n  // really serializable. For a full list of properties see here:\n  // https://github.com/babel/babel/blob/main/packages/babel-core/src/transformation/index.js\n  // For discussion on this topic see here:\n  // https://github.com/babel/babel-loader/pull/629\n  const { ast, code, map, metadata, sourceType } = result\n\n  if (map && (!map.sourcesContent || !map.sourcesContent.length)) {\n    map.sourcesContent = [source]\n  }\n\n  return { ast, code, map, metadata, sourceType }\n}\n"]}