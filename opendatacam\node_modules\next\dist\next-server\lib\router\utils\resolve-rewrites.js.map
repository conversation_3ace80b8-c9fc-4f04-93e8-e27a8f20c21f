{"version": 3, "sources": ["../../../../../next-server/lib/router/utils/resolve-rewrites.ts"], "names": ["customRouteMatcher", "resolveRewrites", "<PERSON><PERSON><PERSON>", "pages", "rewrites", "query", "resolveHref", "locales", "matchedPage", "parsedAs", "fsPathname", "pathname", "resolvedHref", "handleRewrite", "rewrite", "matcher", "source", "params", "has", "hasParams", "headers", "host", "document", "location", "hostname", "cookies", "cookie", "split", "reduce", "acc", "item", "key", "value", "join", "Object", "assign", "destination", "destRes", "parsedDestination", "newUrl", "includes", "finished", "i", "beforeFiles", "length", "afterFiles", "fallback"], "mappings": "qEACA,+DACA,kFAEA,mFACA,qEACA,sDACA,iC,w4BAEA,KAAMA,CAAAA,kBAAkB,CAAG,uBAAU,IAAV,CAA3B,CAEe,QAASC,CAAAA,eAAT,CACbC,MADa,CAEbC,KAFa,CAGbC,QAHa,CAQbC,KARa,CASbC,WATa,CAUbC,OAVa,CAgBb,CACA,GAAIC,CAAAA,WAAW,CAAG,KAAlB,CACA,GAAIC,CAAAA,QAAQ,CAAG,uCAAiBP,MAAjB,CAAf,CACA,GAAIQ,CAAAA,UAAU,CAAG,oDACf,6CAAoB,wBAAYD,QAAQ,CAACE,QAArB,CAApB,CAAoDJ,OAApD,EAA6DI,QAD9C,CAAjB,CAGA,GAAIC,CAAAA,YAAJ,CAEA,KAAMC,CAAAA,aAAa,CAAIC,OAAD,EAAsB,CAC1C,KAAMC,CAAAA,OAAO,CAAGf,kBAAkB,CAACc,OAAO,CAACE,MAAT,CAAlC,CACA,GAAIC,CAAAA,MAAM,CAAGF,OAAO,CAACN,QAAQ,CAACE,QAAV,CAApB,CAEA,GAAIG,OAAO,CAACI,GAAR,EAAeD,MAAnB,CAA2B,CACzB,KAAME,CAAAA,SAAS,CAAG,iCAChB,CACEC,OAAO,CAAE,CACPC,IAAI,CAAEC,QAAQ,CAACC,QAAT,CAAkBC,QADjB,CADX,CAIEC,OAAO,CAAEH,QAAQ,CAACI,MAAT,CACNC,KADM,CACA,IADA,EAENC,MAFM,CAEyB,CAACC,GAAD,CAAMC,IAAN,GAAe,CAC7C,KAAM,CAACC,GAAD,CAAM,GAAGC,KAAT,EAAkBF,IAAI,CAACH,KAAL,CAAW,GAAX,CAAxB,CACAE,GAAG,CAACE,GAAD,CAAH,CAAWC,KAAK,CAACC,IAAN,CAAW,GAAX,CAAX,CACA,MAAOJ,CAAAA,GAAP,CACD,CANM,CAMJ,EANI,CAJX,CADgB,CAahBf,OAAO,CAACI,GAbQ,CAchBT,QAAQ,CAACJ,KAdO,CAAlB,CAiBA,GAAIc,SAAJ,CAAe,CACbe,MAAM,CAACC,MAAP,CAAclB,MAAd,CAAsBE,SAAtB,EACD,CAFD,IAEO,CACLF,MAAM,CAAG,KAAT,CACD,CACF,CAED,GAAIA,MAAJ,CAAY,CACV,GAAI,CAACH,OAAO,CAACsB,WAAb,CAA0B,CACxB;AACA,MAAO,KAAP,CACD,CACD,KAAMC,CAAAA,OAAO,CAAG,gCACdvB,OAAO,CAACsB,WADM,CAEdnB,MAFc,CAGdZ,KAHc,CAId,IAJc,CAAhB,CAMAI,QAAQ,CAAG4B,OAAO,CAACC,iBAAnB,CACApC,MAAM,CAAGmC,OAAO,CAACE,MAAjB,CACAL,MAAM,CAACC,MAAP,CAAc9B,KAAd,CAAqBgC,OAAO,CAACC,iBAAR,CAA0BjC,KAA/C,EAEAK,UAAU,CAAG,oDACX,6CAAoB,wBAAYR,MAAZ,CAApB,CAAyCK,OAAzC,EAAkDI,QADvC,CAAb,CAIA,GAAIR,KAAK,CAACqC,QAAN,CAAe9B,UAAf,CAAJ,CAAgC,CAC9B;AACA;AACAF,WAAW,CAAG,IAAd,CACAI,YAAY,CAAGF,UAAf,CACA,MAAO,KAAP,CACD,CAED;AACAE,YAAY,CAAGN,WAAW,CAACI,UAAD,CAA1B,CAEA,GAAIE,YAAY,GAAKV,MAAjB,EAA2BC,KAAK,CAACqC,QAAN,CAAe5B,YAAf,CAA/B,CAA6D,CAC3DJ,WAAW,CAAG,IAAd,CACA,MAAO,KAAP,CACD,CACF,CACF,CAhED,CAiEA,GAAIiC,CAAAA,QAAQ,CAAG,KAAf,CAEA,IAAK,GAAIC,CAAAA,CAAC,CAAG,CAAb,CAAgBA,CAAC,CAAGtC,QAAQ,CAACuC,WAAT,CAAqBC,MAAzC,CAAiDF,CAAC,EAAlD,CAAsD,CACpD;AACA;AACA7B,aAAa,CAACT,QAAQ,CAACuC,WAAT,CAAqBD,CAArB,CAAD,CAAb,CACD,CACDlC,WAAW,CAAGL,KAAK,CAACqC,QAAN,CAAe9B,UAAf,CAAd,CAEA,GAAI,CAACF,WAAL,CAAkB,CAChB,GAAI,CAACiC,QAAL,CAAe,CACb,IAAK,GAAIC,CAAAA,CAAC,CAAG,CAAb,CAAgBA,CAAC,CAAGtC,QAAQ,CAACyC,UAAT,CAAoBD,MAAxC,CAAgDF,CAAC,EAAjD,CAAqD,CACnD,GAAI7B,aAAa,CAACT,QAAQ,CAACyC,UAAT,CAAoBH,CAApB,CAAD,CAAjB,CAA2C,CACzCD,QAAQ,CAAG,IAAX,CACA,MACD,CACF,CACF,CAED;AACA,GAAI,CAACA,QAAL,CAAe,CACb7B,YAAY,CAAGN,WAAW,CAACI,UAAD,CAA1B,CACAF,WAAW,CAAGL,KAAK,CAACqC,QAAN,CAAe5B,YAAf,CAAd,CACA6B,QAAQ,CAAGjC,WAAX,CACD,CAED,GAAI,CAACiC,QAAL,CAAe,CACb,IAAK,GAAIC,CAAAA,CAAC,CAAG,CAAb,CAAgBA,CAAC,CAAGtC,QAAQ,CAAC0C,QAAT,CAAkBF,MAAtC,CAA8CF,CAAC,EAA/C,CAAmD,CACjD,GAAI7B,aAAa,CAACT,QAAQ,CAAC0C,QAAT,CAAkBJ,CAAlB,CAAD,CAAjB,CAAyC,CACvCD,QAAQ,CAAG,IAAX,CACA,MACD,CACF,CACF,CACF,CAED,MAAO,CACLvC,MADK,CAELO,QAFK,CAGLD,WAHK,CAILI,YAJK,CAAP,CAMD", "sourcesContent": ["import { ParsedUrlQuery } from 'querystring'\nimport pathMatch from './path-match'\nimport prepareDestination, { matchHas } from './prepare-destination'\nimport { Rewrite } from '../../../../lib/load-custom-routes'\nimport { removePathTrailingSlash } from '../../../../client/normalize-trailing-slash'\nimport { normalizeLocalePath } from '../../i18n/normalize-locale-path'\nimport { parseRelativeUrl } from './parse-relative-url'\nimport { delBasePath } from '../router'\n\nconst customRouteMatcher = pathMatch(true)\n\nexport default function resolveRewrites(\n  asPath: string,\n  pages: string[],\n  rewrites: {\n    beforeFiles: Rewrite[]\n    afterFiles: Rewrite[]\n    fallback: Rewrite[]\n  },\n  query: ParsedUrlQuery,\n  resolveHref: (path: string) => string,\n  locales?: string[]\n): {\n  matchedPage: boolean\n  parsedAs: ReturnType<typeof parseRelativeUrl>\n  asPath: string\n  resolvedHref?: string\n} {\n  let matchedPage = false\n  let parsedAs = parseRelativeUrl(asPath)\n  let fsPathname = removePathTrailingSlash(\n    normalizeLocalePath(delBasePath(parsedAs.pathname), locales).pathname\n  )\n  let resolvedHref\n\n  const handleRewrite = (rewrite: Rewrite) => {\n    const matcher = customRouteMatcher(rewrite.source)\n    let params = matcher(parsedAs.pathname)\n\n    if (rewrite.has && params) {\n      const hasParams = matchHas(\n        {\n          headers: {\n            host: document.location.hostname,\n          },\n          cookies: document.cookie\n            .split('; ')\n            .reduce<Record<string, string>>((acc, item) => {\n              const [key, ...value] = item.split('=')\n              acc[key] = value.join('=')\n              return acc\n            }, {}),\n        } as any,\n        rewrite.has,\n        parsedAs.query\n      )\n\n      if (hasParams) {\n        Object.assign(params, hasParams)\n      } else {\n        params = false\n      }\n    }\n\n    if (params) {\n      if (!rewrite.destination) {\n        // this is a proxied rewrite which isn't handled on the client\n        return true\n      }\n      const destRes = prepareDestination(\n        rewrite.destination,\n        params,\n        query,\n        true\n      )\n      parsedAs = destRes.parsedDestination\n      asPath = destRes.newUrl\n      Object.assign(query, destRes.parsedDestination.query)\n\n      fsPathname = removePathTrailingSlash(\n        normalizeLocalePath(delBasePath(asPath), locales).pathname\n      )\n\n      if (pages.includes(fsPathname)) {\n        // check if we now match a page as this means we are done\n        // resolving the rewrites\n        matchedPage = true\n        resolvedHref = fsPathname\n        return true\n      }\n\n      // check if we match a dynamic-route, if so we break the rewrites chain\n      resolvedHref = resolveHref(fsPathname)\n\n      if (resolvedHref !== asPath && pages.includes(resolvedHref)) {\n        matchedPage = true\n        return true\n      }\n    }\n  }\n  let finished = false\n\n  for (let i = 0; i < rewrites.beforeFiles.length; i++) {\n    // we don't end after match in beforeFiles to allow\n    // continuing through all beforeFiles rewrites\n    handleRewrite(rewrites.beforeFiles[i])\n  }\n  matchedPage = pages.includes(fsPathname)\n\n  if (!matchedPage) {\n    if (!finished) {\n      for (let i = 0; i < rewrites.afterFiles.length; i++) {\n        if (handleRewrite(rewrites.afterFiles[i])) {\n          finished = true\n          break\n        }\n      }\n    }\n\n    // check dynamic route before processing fallback rewrites\n    if (!finished) {\n      resolvedHref = resolveHref(fsPathname)\n      matchedPage = pages.includes(resolvedHref)\n      finished = matchedPage\n    }\n\n    if (!finished) {\n      for (let i = 0; i < rewrites.fallback.length; i++) {\n        if (handleRewrite(rewrites.fallback[i])) {\n          finished = true\n          break\n        }\n      }\n    }\n  }\n\n  return {\n    asPath,\n    parsedAs,\n    matchedPage,\n    resolvedHref,\n  }\n}\n"]}