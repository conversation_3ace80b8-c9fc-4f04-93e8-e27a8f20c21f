{"version": 3, "sources": ["../../../next-server/lib/side-effect.tsx"], "names": ["isServer", "window", "Component", "constructor", "props", "_hasHeadManager", "emitChange", "headManager", "updateHead", "reduceComponentsToState", "mountedInstances", "add", "componentDidMount", "componentDidUpdate", "componentWillUnmount", "delete", "render"], "mappings": "4DAAA,4BAEA,KAAMA,CAAAA,QAAQ,CAAG,MAAOC,CAAAA,MAAP,GAAkB,WAAnC,CAce,sBAAcC,iBAA2B,CActDC,WAAW,CAACC,KAAD,CAAa,CACtB,MAAMA,KAAN,EADsB,KAbhBC,eAagB,aAXxBC,UAWwB,CAXX,IAAY,CACvB,GAAI,KAAKD,eAAT,CAA0B,CACxB,KAAKD,KAAL,CAAWG,WAAX,CAAuBC,UAAvB,CACE,KAAKJ,KAAL,CAAWK,uBAAX,CACE,CAAC,GAAG,KAAKL,KAAL,CAAWG,WAAX,CAAuBG,gBAA3B,CADF,CAEE,KAAKN,KAFP,CADF,EAMD,CACF,CAEuB,CAEtB,KAAKC,eAAL,CACE,KAAKD,KAAL,CAAWG,WAAX,EAA0B,KAAKH,KAAL,CAAWG,WAAX,CAAuBG,gBADnD,CAGA,GAAIV,QAAQ,EAAI,KAAKK,eAArB,CAAsC,CACpC,KAAKD,KAAL,CAAWG,WAAX,CAAuBG,gBAAvB,CAAwCC,GAAxC,CAA4C,IAA5C,EACA,KAAKL,UAAL,GACD,CACF,CACDM,iBAAiB,EAAG,CAClB,GAAI,KAAKP,eAAT,CAA0B,CACxB,KAAKD,KAAL,CAAWG,WAAX,CAAuBG,gBAAvB,CAAwCC,GAAxC,CAA4C,IAA5C,EACD,CACD,KAAKL,UAAL,GACD,CACDO,kBAAkB,EAAG,CACnB,KAAKP,UAAL,GACD,CACDQ,oBAAoB,EAAG,CACrB,GAAI,KAAKT,eAAT,CAA0B,CACxB,KAAKD,KAAL,CAAWG,WAAX,CAAuBG,gBAAvB,CAAwCK,MAAxC,CAA+C,IAA/C,EACD,CACD,KAAKT,UAAL,GACD,CAEDU,MAAM,EAAG,CACP,MAAO,KAAP,CACD,CA1CqD,C", "sourcesContent": ["import React, { Component } from 'react'\n\nconst isServer = typeof window === 'undefined'\n\ntype State = JSX.Element[] | undefined\n\ntype SideEffectProps = {\n  reduceComponentsToState: <T>(\n    components: Array<React.ReactElement<any>>,\n    props: T\n  ) => State\n  handleStateChange?: (state: State) => void\n  headManager: any\n  inAmpMode?: boolean\n}\n\nexport default class extends Component<SideEffectProps> {\n  private _hasHeadManager: boolean\n\n  emitChange = (): void => {\n    if (this._hasHeadManager) {\n      this.props.headManager.updateHead(\n        this.props.reduceComponentsToState(\n          [...this.props.headManager.mountedInstances],\n          this.props\n        )\n      )\n    }\n  }\n\n  constructor(props: any) {\n    super(props)\n    this._hasHeadManager =\n      this.props.headManager && this.props.headManager.mountedInstances\n\n    if (isServer && this._hasHeadManager) {\n      this.props.headManager.mountedInstances.add(this)\n      this.emitChange()\n    }\n  }\n  componentDidMount() {\n    if (this._hasHeadManager) {\n      this.props.headManager.mountedInstances.add(this)\n    }\n    this.emitChange()\n  }\n  componentDidUpdate() {\n    this.emitChange()\n  }\n  componentWillUnmount() {\n    if (this._hasHeadManager) {\n      this.props.headManager.mountedInstances.delete(this)\n    }\n    this.emitChange()\n  }\n\n  render() {\n    return null\n  }\n}\n"]}