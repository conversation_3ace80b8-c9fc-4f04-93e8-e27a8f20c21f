{"version": 3, "sources": ["../../../../build/babel/plugins/optimize-hook-destructuring.ts"], "names": ["isHook", "isBuiltInHook", "types", "t", "visitor", "CallExpression", "path", "state", "onlyBuiltIns", "opts", "libs", "lib", "concat", "isVariableDeclarator", "parent", "isArrayPattern", "id", "<PERSON><PERSON><PERSON>", "node", "callee", "name", "binding", "scope", "getBinding", "kind", "specifier", "source", "value", "some", "test", "objectPattern", "elements", "reduce", "patterns", "element", "i", "objectProperty", "numericLiteral", "Program", "traverse"], "mappings": "8DAMA;AACA,KAAMA,CAAAA,MAAM,CAAG,WAAf,CAEA;AACA,KAAMC,CAAAA,aAAa,CAAG,gGAAtB,CAEe,kBAAU,CACvBC,KAAK,CAAEC,CADgB,CAAV,CAII,CACjB,KAAMC,CAAAA,OAAO,CAAG,CACdC,cAAc,CAACC,IAAD,CAA4CC,KAA5C,CAAwD,CACpE,KAAMC,CAAAA,YAAY,CAAGD,KAAK,CAACE,IAAN,CAAWD,YAAhC,CAEA;AACA,KAAME,CAAAA,IAAI,CACRH,KAAK,CAACE,IAAN,CAAWE,GAAX,GACCJ,KAAK,CAACE,IAAN,CAAWE,GAAX,GAAmB,IAAnB,CACG,CAAC,OAAD,CAAU,cAAV,CADH,CAEG,GAAGC,MAAH,CAAUL,KAAK,CAACE,IAAN,CAAWE,GAArB,CAHJ,CADF,CAMA;AACA,GAAI,CAACR,CAAC,CAACU,oBAAF,CAAuBP,IAAI,CAACQ,MAA5B,CAAL,CAA0C,OAE1C;AACA,GAAI,CAACX,CAAC,CAACY,cAAF,CAAiBT,IAAI,CAACQ,MAAL,CAAYE,EAA7B,CAAL,CAAuC,OAEvC;AACA,KAAMC,CAAAA,QAAQ,CAAIX,IAAI,CAACY,IAAL,CAAUC,MAAX,CAA4CC,IAA7D,CAEA,GAAIV,IAAJ,CAAU,CACR,KAAMW,CAAAA,OAAO,CAAGf,IAAI,CAACgB,KAAL,CAAWC,UAAX,CAAsBN,QAAtB,CAAhB,CACA;AACA,GAAI,CAACI,OAAD,EAAYA,OAAO,CAACG,IAAR,GAAiB,QAAjC,CAA2C,OAE3C,KAAMC,CAAAA,SAAS,CAAIJ,OAAO,CAACf,IAAR,CAAaQ,MAAd,CACfY,MADe,CACRC,KADV,CAEA;AACA,GAAI,CAACjB,IAAI,CAACkB,IAAL,CAAWjB,GAAD,EAASA,GAAG,GAAKc,SAA3B,CAAL,CAA4C,OAC7C,CAED;AACA,GAAI,CAAC,CAACjB,YAAY,CAAGP,aAAH,CAAmBD,MAAhC,EAAwC6B,IAAxC,CAA6CZ,QAA7C,CAAL,CAA6D,OAE7DX,IAAI,CAACQ,MAAL,CAAYE,EAAZ,CAAiBb,CAAC,CAAC2B,aAAF,CACfxB,IAAI,CAACQ,MAAL,CAAYE,EAAZ,CAAee,QAAf,CAAwBC,MAAxB,CACE,CAACC,QAAD,CAAWC,OAAX,CAAoBC,CAApB,GAA0B,CACxB,GAAID,OAAO,GAAK,IAAhB,CAAsB,CACpB,MAAOD,CAAAA,QAAP,CACD,CAED,MAAOA,CAAAA,QAAQ,CAACrB,MAAT,CACLT,CAAC,CAACiC,cAAF,CAAiBjC,CAAC,CAACkC,cAAF,CAAiBF,CAAjB,CAAjB,CAAsCD,OAAtC,CADK,CAAP,CAGD,CATH,CAUE,EAVF,CADe,CAAjB,CAcD,CAhDa,CAAhB,CAmDA,MAAO,CACLd,IAAI,CAAE,6BADD,CAELhB,OAAO,CAAE,CACP;AACAkC,OAAO,CAAChC,IAAD,CAAOC,KAAP,CAAc,CACnBD,IAAI,CAACiC,QAAL,CAAcnC,OAAd,CAAuBG,KAAvB,EACD,CAJM,CAFJ,CAAP,CASD", "sourcesContent": ["import {\n  Node<PERSON><PERSON>,\n  PluginObj,\n  types as BabelTypes,\n} from 'next/dist/compiled/babel/core'\n\n// matches any hook-like (the default)\nconst isHook = /^use[A-Z]/\n\n// matches only built-in hooks provided by <PERSON><PERSON> et al\nconst isBuiltInHook = /^use(Callback|Context|DebugValue|Effect|ImperativeHandle|LayoutEffect|Memo|Reducer|Ref|State)$/\n\nexport default function ({\n  types: t,\n}: {\n  types: typeof BabelTypes\n}): PluginObj<any> {\n  const visitor = {\n    CallExpression(path: NodePath<BabelTypes.CallExpression>, state: any) {\n      const onlyBuiltIns = state.opts.onlyBuiltIns\n\n      // if specified, options.lib is a list of libraries that provide hook functions\n      const libs =\n        state.opts.lib &&\n        (state.opts.lib === true\n          ? ['react', 'preact/hooks']\n          : [].concat(state.opts.lib))\n\n      // skip function calls that are not the init of a variable declaration:\n      if (!t.isVariableDeclarator(path.parent)) return\n\n      // skip function calls where the return value is not Array-destructured:\n      if (!t.isArrayPattern(path.parent.id)) return\n\n      // name of the (hook) function being called:\n      const hookName = (path.node.callee as BabelTypes.Identifier).name\n\n      if (libs) {\n        const binding = path.scope.getBinding(hookName)\n        // not an import\n        if (!binding || binding.kind !== 'module') return\n\n        const specifier = (binding.path.parent as BabelTypes.ImportDeclaration)\n          .source.value\n        // not a match\n        if (!libs.some((lib) => lib === specifier)) return\n      }\n\n      // only match function calls with names that look like a hook\n      if (!(onlyBuiltIns ? isBuiltInHook : isHook).test(hookName)) return\n\n      path.parent.id = t.objectPattern(\n        path.parent.id.elements.reduce<Array<BabelTypes.ObjectProperty>>(\n          (patterns, element, i) => {\n            if (element === null) {\n              return patterns\n            }\n\n            return patterns.concat(\n              t.objectProperty(t.numericLiteral(i), element)\n            )\n          },\n          []\n        )\n      )\n    },\n  }\n\n  return {\n    name: 'optimize-hook-destructuring',\n    visitor: {\n      // this is a workaround to run before preset-env destroys destructured assignments\n      Program(path, state) {\n        path.traverse(visitor, state)\n      },\n    },\n  }\n}\n"]}