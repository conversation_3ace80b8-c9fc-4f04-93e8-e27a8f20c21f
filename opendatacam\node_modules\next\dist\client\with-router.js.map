{"version": 3, "sources": ["../../client/with-router.tsx"], "names": ["with<PERSON><PERSON><PERSON>", "ComposedComponent", "WithRouterWrapper", "props", "getInitialProps", "origGetInitialProps", "process", "env", "NODE_ENV", "name", "displayName"], "mappings": "mJAAA,oDAEA,gCAWe,QAASA,CAAAA,UAAT,CAIbC,iBAJa,CAK+B,CAC5C,QAASC,CAAAA,iBAAT,CAA2BC,KAA3B,CAAoD,CAClD,mBAAO,6BAAC,iBAAD,gBAAmB,MAAM,CAAE,uBAA3B,EAA4CA,KAA5C,EAAP,CACD,CAEDD,iBAAiB,CAACE,eAAlB,CAAoCH,iBAAiB,CAACG,eACtD;AADA,CAEEF,iBAAD,CAA2BG,mBAA3B,CAAkDJ,iBAAD,CAA2BI,mBAA5E,CACD,GAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,GAAyB,YAA7B,CAA2C,CACzC,KAAMC,CAAAA,IAAI,CACRR,iBAAiB,CAACS,WAAlB,EAAiCT,iBAAiB,CAACQ,IAAnD,EAA2D,SAD7D,CAEAP,iBAAiB,CAACQ,WAAlB,CAAiC,cAAaD,IAAK,GAAnD,CACD,CAED,MAAOP,CAAAA,iBAAP,CACD", "sourcesContent": ["import React from 'react'\nimport { NextComponentType, NextPageContext } from '../next-server/lib/utils'\nimport { NextRouter, useRouter } from './router'\n\nexport type WithRouterProps = {\n  router: NextRouter\n}\n\nexport type ExcludeRouterProps<P> = Pick<\n  P,\n  Exclude<keyof P, keyof WithRouterProps>\n>\n\nexport default function withRouter<\n  P extends WithRouterProps,\n  C = NextPageContext\n>(\n  ComposedComponent: NextComponentType<C, any, P>\n): React.ComponentType<ExcludeRouterProps<P>> {\n  function WithRouterWrapper(props: any): JSX.Element {\n    return <ComposedComponent router={useRouter()} {...props} />\n  }\n\n  WithRouterWrapper.getInitialProps = ComposedComponent.getInitialProps\n  // This is needed to allow checking for custom getInitialProps in _app\n  ;(WithRouterWrapper as any).origGetInitialProps = (ComposedComponent as any).origGetInitialProps\n  if (process.env.NODE_ENV !== 'production') {\n    const name =\n      ComposedComponent.displayName || ComposedComponent.name || 'Unknown'\n    WithRouterWrapper.displayName = `withRouter(${name})`\n  }\n\n  return WithRouterWrapper\n}\n"]}