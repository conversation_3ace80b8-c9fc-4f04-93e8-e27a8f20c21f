{"version": 3, "sources": ["../../../client/dev/webpack-hot-middleware-client.js"], "names": ["devClient", "subscribeToHmrEvent", "obj", "action", "window", "location", "reload", "page", "data", "next", "router", "pathname", "components", "Error"], "mappings": "+IAAA,oF,aAEe,IAAM,CACnB,KAAMA,CAAAA,SAAS,CAAG,2BAAlB,CAEAA,SAAS,CAACC,mBAAV,CAA+BC,GAAD,EAAS,CACrC,GAAIA,GAAG,CAACC,MAAJ,GAAe,YAAnB,CAAiC,CAC/B,MAAOC,CAAAA,MAAM,CAACC,QAAP,CAAgBC,MAAhB,EAAP,CACD,CACD,GAAIJ,GAAG,CAACC,MAAJ,GAAe,aAAnB,CAAkC,CAChC,KAAM,CAACI,IAAD,EAASL,GAAG,CAACM,IAAnB,CACA,GAAID,IAAI,GAAKH,MAAM,CAACK,IAAP,CAAYC,MAAZ,CAAmBC,QAAhC,CAA0C,CACxC,MAAOP,CAAAA,MAAM,CAACC,QAAP,CAAgBC,MAAhB,EAAP,CACD,CACD,OACD,CACD,GAAIJ,GAAG,CAACC,MAAJ,GAAe,WAAnB,CAAgC,CAC9B,KAAM,CAACI,IAAD,EAASL,GAAG,CAACM,IAAnB,CACA,GACED,IAAI,GAAKH,MAAM,CAACK,IAAP,CAAYC,MAAZ,CAAmBC,QAA5B,EACA,MAAOP,CAAAA,MAAM,CAACK,IAAP,CAAYC,MAAZ,CAAmBE,UAAnB,CAA8BL,IAA9B,CAAP,GAA+C,WAFjD,CAGE,CACA,MAAOH,CAAAA,MAAM,CAACC,QAAP,CAAgBC,MAAhB,EAAP,CACD,CACD,OACD,CACD,KAAM,IAAIO,CAAAA,KAAJ,CAAU,qBAAuBX,GAAG,CAACC,MAArC,CAAN,CACD,CAtBD,EAwBA,MAAOH,CAAAA,SAAP,CACD,C", "sourcesContent": ["import connect from './error-overlay/hot-dev-client'\n\nexport default () => {\n  const devClient = connect()\n\n  devClient.subscribeToHmrEvent((obj) => {\n    if (obj.action === 'reloadPage') {\n      return window.location.reload()\n    }\n    if (obj.action === 'removedPage') {\n      const [page] = obj.data\n      if (page === window.next.router.pathname) {\n        return window.location.reload()\n      }\n      return\n    }\n    if (obj.action === 'addedPage') {\n      const [page] = obj.data\n      if (\n        page === window.next.router.pathname &&\n        typeof window.next.router.components[page] === 'undefined'\n      ) {\n        return window.location.reload()\n      }\n      return\n    }\n    throw new Error('Unexpected action ' + obj.action)\n  })\n\n  return devClient\n}\n"]}