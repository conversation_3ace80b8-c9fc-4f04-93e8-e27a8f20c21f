{"version": 3, "sources": ["../../../../../next-server/server/lib/squoosh/text-decoder.ts"], "names": ["textDecoder", "TextDecoder", "require"], "mappings": "gEAAA,KAAMA,CAAAA,WAAW,CACf,MAAOC,CAAAA,WAAP,GAAuB,WAAvB,CAAqCA,WAArC,CAAmDC,OAAO,CAAC,MAAD,CAAP,CAAgBD,WADrE,C", "sourcesContent": ["const textDecoder =\n  typeof TextDecoder !== 'undefined' ? TextDecoder : require('util').TextDecoder\n\nexport { textDecoder as TextDecoder }\n"]}