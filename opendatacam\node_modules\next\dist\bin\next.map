{"version": 3, "sources": ["../../bin/next.ts"], "names": ["for<PERSON>ach", "dependency", "require", "resolve", "err", "console", "warn", "defaultCommand", "commands", "build", "then", "i", "nextBuild", "start", "nextStart", "export", "nextExport", "dev", "nextDev", "telemetry", "nextTelemetry", "args", "Boolean", "permissive", "log", "process", "env", "__NEXT_VERSION", "exit", "foundCommand", "_", "Object", "keys", "join", "command", "forwarded<PERSON>rgs", "slice", "Error", "push", "defaultEnv", "standardEnv", "NODE_ENV", "includes", "NON_STANDARD_NODE_ENV", "React", "Suspense", "on", "exec", "CONFIG_FILE", "watchFile", "cwd", "cur", "prev", "size"], "mappings": "AAAA;aACA,gEACA,8EACA,2C,w4BACC,CAAC,OAAD,CAAU,WAAV,EAAuBA,OAAvB,CAAgCC,UAAD,EAAgB,CAC9C,GAAI,CACF;AACAC,OAAO,CAACC,OAAR,CAAgBF,UAAhB,EACD,CAAC,MAAOG,GAAP,CAAY,CACZC,OAAO,CAACC,IAAR,CACG,eAAcL,UAAW,+HAA8HA,UAAW,GADrK,EAGD,CACF,CATA,EAWD,KAAMM,CAAAA,cAAc,CAAG,KAAvB,CAEA,KAAMC,CAAAA,QAA0D,CAAG,CACjEC,KAAK,CAAE,IAAM,2DAAO,mBAAP,IAA4BC,IAA5B,CAAkCC,CAAD,EAAOA,CAAC,CAACC,SAA1C,CADoD,CAEjEC,KAAK,CAAE,IAAM,2DAAO,mBAAP,IAA4BH,IAA5B,CAAkCC,CAAD,EAAOA,CAAC,CAACG,SAA1C,CAFoD,CAGjEC,MAAM,CAAE,IAAM,2DAAO,oBAAP,IAA6BL,IAA7B,CAAmCC,CAAD,EAAOA,CAAC,CAACK,UAA3C,CAHmD,CAIjEC,GAAG,CAAE,IAAM,2DAAO,iBAAP,IAA0BP,IAA1B,CAAgCC,CAAD,EAAOA,CAAC,CAACO,OAAxC,CAJsD,CAKjEC,SAAS,CAAE,IAAM,2DAAO,uBAAP,IAAgCT,IAAhC,CAAsCC,CAAD,EAAOA,CAAC,CAACS,aAA9C,CALgD,CAAnE,CAQA,KAAMC,CAAAA,IAAI,CAAG,mBACX,CACE;AACA,YAAaC,OAFf,CAGE,SAAUA,OAHZ,CAIE,YAAaA,OAJf,CAME;AACA,KAAM,WAPR,CAQE,KAAM,QARR,CADW,CAWX,CACEC,UAAU,CAAE,IADd,CAXW,CAAb,CAgBA;AACA,GAAIF,IAAI,CAAC,WAAD,CAAR,CAAuB,CACrBhB,OAAO,CAACmB,GAAR,CAAa,YAAWC,OAAO,CAACC,GAAR,CAAYC,cAAe,EAAnD,EACAF,OAAO,CAACG,IAAR,CAAa,CAAb,EACD,CAED;AACA,KAAMC,CAAAA,YAAY,CAAGP,OAAO,CAACd,QAAQ,CAACa,IAAI,CAACS,CAAL,CAAO,CAAP,CAAD,CAAT,CAA5B,CAEA;AACA;AACA;AACA,GAAI,CAACD,YAAD,EAAiBR,IAAI,CAAC,QAAD,CAAzB,CAAqC,CACnChB,OAAO,CAACmB,GAAR,CAAa;AACf;AACA;AACA;AACA;AACA,QAAQO,MAAM,CAACC,IAAP,CAAYxB,QAAZ,EAAsByB,IAAtB,CAA2B,IAA3B,CAAiC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAbE,EAcAR,OAAO,CAACG,IAAR,CAAa,CAAb,EACD,CAED,KAAMM,CAAAA,OAAO,CAAGL,YAAY,CAAGR,IAAI,CAACS,CAAL,CAAO,CAAP,CAAH,CAAevB,cAA3C,CACA,KAAM4B,CAAAA,aAAa,CAAGN,YAAY,CAAGR,IAAI,CAACS,CAAL,CAAOM,KAAP,CAAa,CAAb,CAAH,CAAqBf,IAAI,CAACS,CAA5D,CAEA,GAAIT,IAAI,CAAC,WAAD,CAAR,CACE,KAAM,IAAIgB,CAAAA,KAAJ,CACH,sGAAqGH,OAAQ,EAD1G,CAAN,CAIF;AACA,GAAIb,IAAI,CAAC,QAAD,CAAR,CAAoB,CAClBc,aAAa,CAACG,IAAd,CAAmB,QAAnB,EACD,CAED,KAAMC,CAAAA,UAAU,CAAGL,OAAO,GAAK,KAAZ,CAAoB,aAApB,CAAoC,YAAvD,CAEA,KAAMM,CAAAA,WAAW,CAAG,CAAC,YAAD,CAAe,aAAf,CAA8B,MAA9B,CAApB,CAEA,GAAIf,OAAO,CAACC,GAAR,CAAYe,QAAZ,EAAwB,CAACD,WAAW,CAACE,QAAZ,CAAqBjB,OAAO,CAACC,GAAR,CAAYe,QAAjC,CAA7B,CAAyE,CACvEjB,GAAG,CAAClB,IAAJ,CAASqC,gCAAT,EACD,CAED,CAAElB,OAAO,CAACC,GAAT,CAAqBe,QAArB,CAAgChB,OAAO,CAACC,GAAR,CAAYe,QAAZ,EAAwBF,UAAxD,CAED;AACA;AACA,KAAMK,CAAAA,KAAK,CAAG1C,OAAO,CAAC,OAAD,CAArB,CAEA,GAAI,MAAO0C,CAAAA,KAAK,CAACC,QAAb,GAA0B,WAA9B,CAA2C,CACzC,KAAM,IAAIR,CAAAA,KAAJ,CACH,gOADG,CAAN,CAGD,CAED;AACAZ,OAAO,CAACqB,EAAR,CAAW,SAAX,CAAsB,IAAMrB,OAAO,CAACG,IAAR,CAAa,CAAb,CAA5B,EACAH,OAAO,CAACqB,EAAR,CAAW,QAAX,CAAqB,IAAMrB,OAAO,CAACG,IAAR,CAAa,CAAb,CAA3B,EAEApB,QAAQ,CAAC0B,OAAD,CAAR,GACGxB,IADH,CACSqC,IAAD,EAAUA,IAAI,CAACZ,aAAD,CADtB,EAEGzB,IAFH,CAEQ,IAAM,CACV,GAAIwB,OAAO,GAAK,OAAhB,CAAyB,CACvB;AACA;AACAT,OAAO,CAACG,IAAR,CAAa,CAAb,EACD,CACF,CARH,EAUA,GAAIM,OAAO,GAAK,KAAhB,CAAuB,CACrB,KAAM,CAAEc,WAAF,EAAkB9C,OAAO,CAAC,8BAAD,CAA/B,CACA,KAAM,CAAE+C,SAAF,EAAgB/C,OAAO,CAAC,IAAD,CAA7B,CACA+C,SAAS,CAAE,GAAExB,OAAO,CAACyB,GAAR,EAAc,IAAGF,WAAY,EAAjC,CAAoC,CAACG,GAAD,CAAWC,IAAX,GAAyB,CACpE,GAAID,GAAG,CAACE,IAAJ,CAAW,CAAX,EAAgBD,IAAI,CAACC,IAAL,CAAY,CAAhC,CAAmC,CACjChD,OAAO,CAACmB,GAAR,CACG,yBAAwBwB,WAAY,oDADvC,EAGD,CACF,CANQ,CAAT,CAOD", "sourcesContent": ["#!/usr/bin/env node\nimport * as log from '../build/output/log'\nimport arg from 'next/dist/compiled/arg/index.js'\nimport { NON_STANDARD_NODE_ENV } from '../lib/constants'\n;['react', 'react-dom'].forEach((dependency) => {\n  try {\n    // When 'npm link' is used it checks the clone location. Not the project.\n    require.resolve(dependency)\n  } catch (err) {\n    console.warn(\n      `The module '${dependency}' was not found. Next.js requires that you include it in 'dependencies' of your 'package.json'. To add it, run 'npm install ${dependency}'`\n    )\n  }\n})\n\nconst defaultCommand = 'dev'\nexport type cliCommand = (argv?: string[]) => void\nconst commands: { [command: string]: () => Promise<cliCommand> } = {\n  build: () => import('../cli/next-build').then((i) => i.nextBuild),\n  start: () => import('../cli/next-start').then((i) => i.nextStart),\n  export: () => import('../cli/next-export').then((i) => i.nextExport),\n  dev: () => import('../cli/next-dev').then((i) => i.nextDev),\n  telemetry: () => import('../cli/next-telemetry').then((i) => i.nextTelemetry),\n}\n\nconst args = arg(\n  {\n    // Types\n    '--version': Boolean,\n    '--help': Boolean,\n    '--inspect': Boolean,\n\n    // Aliases\n    '-v': '--version',\n    '-h': '--help',\n  },\n  {\n    permissive: true,\n  }\n)\n\n// Version is inlined into the file using taskr build pipeline\nif (args['--version']) {\n  console.log(`Next.js v${process.env.__NEXT_VERSION}`)\n  process.exit(0)\n}\n\n// Check if we are running `next <subcommand>` or `next`\nconst foundCommand = Boolean(commands[args._[0]])\n\n// Makes sure the `next --help` case is covered\n// This help message is only showed for `next --help`\n// `next <subcommand> --help` falls through to be handled later\nif (!foundCommand && args['--help']) {\n  console.log(`\n    Usage\n      $ next <command>\n\n    Available commands\n      ${Object.keys(commands).join(', ')}\n\n    Options\n      --version, -v   Version number\n      --help, -h      Displays this message\n\n    For more information run a command with the --help flag\n      $ next build --help\n  `)\n  process.exit(0)\n}\n\nconst command = foundCommand ? args._[0] : defaultCommand\nconst forwardedArgs = foundCommand ? args._.slice(1) : args._\n\nif (args['--inspect'])\n  throw new Error(\n    `--inspect flag is deprecated. Use env variable NODE_OPTIONS instead: NODE_OPTIONS='--inspect' next ${command}`\n  )\n\n// Make sure the `next <subcommand> --help` case is covered\nif (args['--help']) {\n  forwardedArgs.push('--help')\n}\n\nconst defaultEnv = command === 'dev' ? 'development' : 'production'\n\nconst standardEnv = ['production', 'development', 'test']\n\nif (process.env.NODE_ENV && !standardEnv.includes(process.env.NODE_ENV)) {\n  log.warn(NON_STANDARD_NODE_ENV)\n}\n\n;(process.env as any).NODE_ENV = process.env.NODE_ENV || defaultEnv\n\n// this needs to come after we set the correct NODE_ENV or\n// else it might cause SSR to break\nconst React = require('react')\n\nif (typeof React.Suspense === 'undefined') {\n  throw new Error(\n    `The version of React you are using is lower than the minimum required version needed for Next.js. Please upgrade \"react\" and \"react-dom\": \"npm install react react-dom\" https://nextjs.org/docs/messages/invalid-react-version`\n  )\n}\n\n// Make sure commands gracefully respect termination signals (e.g. from Docker)\nprocess.on('SIGTERM', () => process.exit(0))\nprocess.on('SIGINT', () => process.exit(0))\n\ncommands[command]()\n  .then((exec) => exec(forwardedArgs))\n  .then(() => {\n    if (command === 'build') {\n      // ensure process exits after build completes so open handles/connections\n      // don't cause process to hang\n      process.exit(0)\n    }\n  })\n\nif (command === 'dev') {\n  const { CONFIG_FILE } = require('../next-server/lib/constants')\n  const { watchFile } = require('fs')\n  watchFile(`${process.cwd()}/${CONFIG_FILE}`, (cur: any, prev: any) => {\n    if (cur.size > 0 || prev.size > 0) {\n      console.log(\n        `\\n> Found a change in ${CONFIG_FILE}. Restart the server to see the changes in effect.`\n      )\n    }\n  })\n}\n"]}