{"version": 3, "sources": ["../../client/route-loader.ts"], "names": ["MS_MAX_IDLE_DELAY", "withFuture", "key", "map", "generator", "entry", "get", "future", "Promise", "resolve", "resolver", "prom", "set", "then", "value", "hasPrefetch", "link", "document", "createElement", "window", "MSInputMethodContext", "documentMode", "relList", "supports", "canPrefetch", "prefetchViaDom", "href", "as", "res", "rej", "querySelector", "rel", "crossOrigin", "process", "env", "__NEXT_CROSS_ORIGIN", "onload", "onerror", "head", "append<PERSON><PERSON><PERSON>", "ASSET_LOAD_ERROR", "Symbol", "<PERSON><PERSON><PERSON><PERSON>", "err", "Object", "defineProperty", "isAssetError", "appendScript", "src", "script", "reject", "Error", "body", "resolvePromiseWithTimeout", "p", "ms", "cancelled", "r", "catch", "setTimeout", "getClientBuildManifest", "self", "__BUILD_MANIFEST", "onBuildManifest", "cb", "__BUILD_MANIFEST_CB", "getFilesForRoute", "assetPrefix", "route", "NODE_ENV", "scripts", "encodeURI", "css", "manifest", "allFiles", "filter", "v", "endsWith", "createRouteLoader", "entrypoints", "Map", "loadedScripts", "styleSheets", "routes", "maybeExecuteScript", "fetchStyleSheet", "fetch", "ok", "text", "content", "whenEntrypoint", "onEntrypoint", "execute", "fn", "exports", "component", "default", "error", "input", "old", "loadRoute", "prefetch", "all", "has", "entrypoint", "styles", "assign", "cn", "navigator", "connection", "saveData", "test", "effectiveType", "output"], "mappings": "6QAEA,wHACA,4DAEA;AACA;AACA;AACA;AACA,KAAMA,CAAAA,iBAAiB,CAAG,IAA1B,CAmCA,QAASC,CAAAA,UAAT,CACEC,GADF,CAEEC,GAFF,CAGEC,SAHF,CAIc,CACZ,GAAIC,CAAAA,KAAgC,CAAGF,GAAG,CAACG,GAAJ,CAAQJ,GAAR,CAAvC,CACA,GAAIG,KAAJ,CAAW,CACT,GAAI,UAAYA,CAAAA,KAAhB,CAAuB,CACrB,MAAOA,CAAAA,KAAK,CAACE,MAAb,CACD,CACD,MAAOC,CAAAA,OAAO,CAACC,OAAR,CAAgBJ,KAAhB,CAAP,CACD,CACD,GAAIK,CAAAA,QAAJ,CACA,KAAMC,CAAAA,IAAgB,CAAG,GAAIH,CAAAA,OAAJ,CAAgBC,OAAD,EAAa,CACnDC,QAAQ,CAAGD,OAAX,CACD,CAFwB,CAAzB,CAGAN,GAAG,CAACS,GAAJ,CAAQV,GAAR,CAAcG,KAAK,CAAG,CAAEI,OAAO,CAAEC,QAAX,CAAsBH,MAAM,CAAEI,IAA9B,CAAtB,EACA,MAAOP,CAAAA,SAAS,CACZ;AACAA,SAAS,GAAGS,IAAZ,CAAkBC,KAAD,GAAYJ,QAAQ,CAACI,KAAD,CAAR,CAAiBA,KAA7B,CAAjB,CAFY,CAGZH,IAHJ,CAID,CASD,QAASI,CAAAA,WAAT,CAAqBC,IAArB,CAAsD,CACpD,GAAI,CACFA,IAAI,CAAGC,QAAQ,CAACC,aAAT,CAAuB,MAAvB,CAAP,CACA,MACE;AACA;AACC,CAAC,CAACC,MAAM,CAACC,oBAAT,EAAiC,CAAC,CAAEH,QAAD,CAAkBI,YAAtD,EACAL,IAAI,CAACM,OAAL,CAAaC,QAAb,CAAsB,UAAtB,CAJF,EAMD,CAAC,cAAM,CACN,MAAO,MAAP,CACD,CACF,CAED,KAAMC,CAAAA,WAAoB,CAAGT,WAAW,EAAxC,CAEA,QAASU,CAAAA,cAAT,CACEC,IADF,CAEEC,EAFF,CAGEX,IAHF,CAIgB,CACd,MAAO,IAAIR,CAAAA,OAAJ,CAAY,CAACoB,GAAD,CAAMC,GAAN,GAAc,CAC/B,GAAIZ,QAAQ,CAACa,aAAT,CAAwB,+BAA8BJ,IAAK,IAA3D,CAAJ,CAAqE,CACnE,MAAOE,CAAAA,GAAG,EAAV,CACD,CAEDZ,IAAI,CAAGC,QAAQ,CAACC,aAAT,CAAuB,MAAvB,CAAP,CAEA;AACA,GAAIS,EAAJ,CAAQX,IAAI,CAAEW,EAAN,CAAWA,EAAX,CACRX,IAAI,CAAEe,GAAN,CAAa,UAAb,CACAf,IAAI,CAAEgB,WAAN,CAAoBC,OAAO,CAACC,GAAR,CAAYC,mBAAhC,CACAnB,IAAI,CAAEoB,MAAN,CAAeR,GAAf,CACAZ,IAAI,CAAEqB,OAAN,CAAgBR,GAAhB,CAEA;AACAb,IAAI,CAAEU,IAAN,CAAaA,IAAb,CAEAT,QAAQ,CAACqB,IAAT,CAAcC,WAAd,CAA0BvB,IAA1B,EACD,CAlBM,CAAP,CAmBD,CAED,KAAMwB,CAAAA,gBAAgB,CAAGC,MAAM,CAAC,kBAAD,CAA/B,CACA;AACO,QAASC,CAAAA,cAAT,CAAwBC,GAAxB,CAA2C,CAChD,MAAOC,CAAAA,MAAM,CAACC,cAAP,CAAsBF,GAAtB,CAA2BH,gBAA3B,CAA6C,EAA7C,CAAP,CACD,CAEM,QAASM,CAAAA,YAAT,CAAsBH,GAAtB,CAAwD,CAC7D,MAAOA,CAAAA,GAAG,EAAIH,gBAAgB,GAAIG,CAAAA,GAAlC,CACD,CAED,QAASI,CAAAA,YAAT,CACEC,GADF,CAEEC,MAFF,CAGoB,CAClB,MAAO,IAAIzC,CAAAA,OAAJ,CAAY,CAACC,OAAD,CAAUyC,MAAV,GAAqB,CACtCD,MAAM,CAAGhC,QAAQ,CAACC,aAAT,CAAuB,QAAvB,CAAT,CAEA;AACA;AACA;AACA+B,MAAM,CAACb,MAAP,CAAgB3B,OAAhB,CACAwC,MAAM,CAACZ,OAAP,CAAiB,IACfa,MAAM,CAACR,cAAc,CAAC,GAAIS,CAAAA,KAAJ,CAAW,0BAAyBH,GAAI,EAAxC,CAAD,CAAf,CADR,CAGA;AACA;AACAC,MAAM,CAACjB,WAAP,CAAqBC,OAAO,CAACC,GAAR,CAAYC,mBAAjC,CAEA;AACA;AACAc,MAAM,CAACD,GAAP,CAAaA,GAAb,CACA/B,QAAQ,CAACmC,IAAT,CAAcb,WAAd,CAA0BU,MAA1B,EACD,CAlBM,CAAP,CAmBD,CAED;AACA,QAASI,CAAAA,yBAAT,CACEC,CADF,CAEEC,EAFF,CAGEZ,GAHF,CAIc,CACZ,MAAO,IAAInC,CAAAA,OAAJ,CAAY,CAACC,OAAD,CAAUyC,MAAV,GAAqB,CACtC,GAAIM,CAAAA,SAAS,CAAG,KAAhB,CAEAF,CAAC,CAACzC,IAAF,CAAQ4C,CAAD,EAAO,CACZ;AACAD,SAAS,CAAG,IAAZ,CACA/C,OAAO,CAACgD,CAAD,CAAP,CACD,CAJD,EAIGC,KAJH,CAISR,MAJT,EAMA,6CAAoB,IAClBS,UAAU,CAAC,IAAM,CACf,GAAI,CAACH,SAAL,CAAgB,CACdN,MAAM,CAACP,GAAD,CAAN,CACD,CACF,CAJS,CAIPY,EAJO,CADZ,EAOD,CAhBM,CAAP,CAiBD,CAED;AACA;AACA;AACA;AACA;AACA;AACO,QAASK,CAAAA,sBAAT,EAAgE,CACrE,GAAIC,IAAI,CAACC,gBAAT,CAA2B,CACzB,MAAOtD,CAAAA,OAAO,CAACC,OAAR,CAAgBoD,IAAI,CAACC,gBAArB,CAAP,CACD,CAED,KAAMC,CAAAA,eAA6C,CAAG,GAAIvD,CAAAA,OAAJ,CAEnDC,OAAD,EAAa,CACb;AACA,KAAMuD,CAAAA,EAAE,CAAGH,IAAI,CAACI,mBAAhB,CACAJ,IAAI,CAACI,mBAAL,CAA2B,IAAM,CAC/BxD,OAAO,CAACoD,IAAI,CAACC,gBAAN,CAAP,CACAE,EAAE,EAAIA,EAAE,EAAR,CACD,CAHD,CAID,CATqD,CAAtD,CAWA,MAAOX,CAAAA,yBAAyB,CAC9BU,eAD8B,CAE9B/D,iBAF8B,CAG9B0C,cAAc,CAAC,GAAIS,CAAAA,KAAJ,CAAU,sCAAV,CAAD,CAHgB,CAAhC,CAKD,CAMD,QAASe,CAAAA,gBAAT,CACEC,WADF,CAEEC,KAFF,CAGuB,CACrB,GAAInC,OAAO,CAACC,GAAR,CAAYmC,QAAZ,GAAyB,aAA7B,CAA4C,CAC1C,MAAO7D,CAAAA,OAAO,CAACC,OAAR,CAAgB,CACrB6D,OAAO,CAAE,CACPH,WAAW,CACT,4BADF,CAEEI,SAAS,CAAC,mCAAsBH,KAAtB,CAA6B,KAA7B,CAAD,CAHJ,CADY,CAMrB;AACAI,GAAG,CAAE,EAPgB,CAAhB,CAAP,CASD,CACD,MAAOZ,CAAAA,sBAAsB,GAAG/C,IAAzB,CAA+B4D,QAAD,EAAc,CACjD,GAAI,EAAEL,KAAK,GAAIK,CAAAA,QAAX,CAAJ,CAA0B,CACxB,KAAM/B,CAAAA,cAAc,CAAC,GAAIS,CAAAA,KAAJ,CAAW,2BAA0BiB,KAAM,EAA3C,CAAD,CAApB,CACD,CACD,KAAMM,CAAAA,QAAQ,CAAGD,QAAQ,CAACL,KAAD,CAAR,CAAgBjE,GAAhB,CACdE,KAAD,EAAW8D,WAAW,CAAG,SAAd,CAA0BI,SAAS,CAAClE,KAAD,CAD/B,CAAjB,CAGA,MAAO,CACLiE,OAAO,CAAEI,QAAQ,CAACC,MAAT,CAAiBC,CAAD,EAAOA,CAAC,CAACC,QAAF,CAAW,KAAX,CAAvB,CADJ,CAELL,GAAG,CAAEE,QAAQ,CAACC,MAAT,CAAiBC,CAAD,EAAOA,CAAC,CAACC,QAAF,CAAW,MAAX,CAAvB,CAFA,CAAP,CAID,CAXM,CAAP,CAYD,CAED,QAASC,CAAAA,iBAAT,CAA2BX,WAA3B,CAA6D,CAC3D,KAAMY,CAAAA,WAGL,CAAG,GAAIC,CAAAA,GAAJ,EAHJ,CAIA,KAAMC,CAAAA,aAA4C,CAAG,GAAID,CAAAA,GAAJ,EAArD,CACA,KAAME,CAAAA,WAAkD,CAAG,GAAIF,CAAAA,GAAJ,EAA3D,CACA,KAAMG,CAAAA,MAGL,CAAG,GAAIH,CAAAA,GAAJ,EAHJ,CAKA,QAASI,CAAAA,kBAAT,CAA4BpC,GAA5B,CAA2D,CACzD,GAAIrC,CAAAA,IAAkC,CAAGsE,aAAa,CAAC3E,GAAd,CAAkB0C,GAAlB,CAAzC,CACA,GAAIrC,IAAJ,CAAU,CACR,MAAOA,CAAAA,IAAP,CACD,CAED;AACA,GAAIM,QAAQ,CAACa,aAAT,CAAwB,gBAAekB,GAAI,IAA3C,CAAJ,CAAqD,CACnD,MAAOxC,CAAAA,OAAO,CAACC,OAAR,EAAP,CACD,CAEDwE,aAAa,CAACrE,GAAd,CAAkBoC,GAAlB,CAAwBrC,IAAI,CAAGoC,YAAY,CAACC,GAAD,CAA3C,EACA,MAAOrC,CAAAA,IAAP,CACD,CAED,QAAS0E,CAAAA,eAAT,CAAyB3D,IAAzB,CAAiE,CAC/D,GAAIf,CAAAA,IAA0C,CAAGuE,WAAW,CAAC5E,GAAZ,CAAgBoB,IAAhB,CAAjD,CACA,GAAIf,IAAJ,CAAU,CACR,MAAOA,CAAAA,IAAP,CACD,CAEDuE,WAAW,CAACtE,GAAZ,CACEc,IADF,CAEGf,IAAI,CAAG2E,KAAK,CAAC5D,IAAD,CAAL,CACLb,IADK,CACCe,GAAD,EAAS,CACb,GAAI,CAACA,GAAG,CAAC2D,EAAT,CAAa,CACX,KAAM,IAAIpC,CAAAA,KAAJ,CAAW,8BAA6BzB,IAAK,EAA7C,CAAN,CACD,CACD,MAAOE,CAAAA,GAAG,CAAC4D,IAAJ,GAAW3E,IAAX,CAAiB2E,IAAD,GAAW,CAAE9D,IAAI,CAAEA,IAAR,CAAc+D,OAAO,CAAED,IAAvB,CAAX,CAAhB,CAAP,CACD,CANK,EAOL9B,KAPK,CAOEf,GAAD,EAAS,CACd,KAAMD,CAAAA,cAAc,CAACC,GAAD,CAApB,CACD,CATK,CAFV,EAaA,MAAOhC,CAAAA,IAAP,CACD,CAED,MAAO,CACL+E,cAAc,CAACtB,KAAD,CAAgB,CAC5B,MAAOnE,CAAAA,UAAU,CAACmE,KAAD,CAAQW,WAAR,CAAjB,CACD,CAHI,CAILY,YAAY,CAACvB,KAAD,CAAgBwB,OAAhB,CAAwC,CAClDpF,OAAO,CAACC,OAAR,CAAgBmF,OAAhB,EACG/E,IADH,CACSgF,EAAD,EAAQA,EAAE,EADlB,EAEGhF,IAFH,CAGKiF,OAAD,GAAmB,CACjBC,SAAS,CAAGD,OAAO,EAAIA,OAAO,CAACE,OAApB,EAAgCF,OAD1B,CAEjBA,OAAO,CAAEA,OAFQ,CAAnB,CAHJ,CAOKnD,GAAD,GAAU,CAAEsD,KAAK,CAAEtD,GAAT,CAAV,CAPJ,EASG9B,IATH,CASSqF,KAAD,EAA4B,CAChC,KAAMC,CAAAA,GAAG,CAAGpB,WAAW,CAACzE,GAAZ,CAAgB8D,KAAhB,CAAZ,CACAW,WAAW,CAACnE,GAAZ,CAAgBwD,KAAhB,CAAuB8B,KAAvB,EACA,GAAIC,GAAG,EAAI,WAAaA,CAAAA,GAAxB,CAA6BA,GAAG,CAAC1F,OAAJ,CAAYyF,KAAZ,EAC9B,CAbH,EAcD,CAnBI,CAoBLE,SAAS,CAAChC,KAAD,CAAgBiC,QAAhB,CAAoC,CAC3C,MAAOpG,CAAAA,UAAU,CAAmBmE,KAAnB,CAA0Be,MAA1B,CAAkC,IAAM,CACvD,MAAO9B,CAAAA,yBAAyB,CAC9Ba,gBAAgB,CAACC,WAAD,CAAcC,KAAd,CAAhB,CACGvD,IADH,CACQ,CAAC,CAAEyD,OAAF,CAAWE,GAAX,CAAD,GAAsB,CAC1B,MAAOhE,CAAAA,OAAO,CAAC8F,GAAR,CAAY,CACjBvB,WAAW,CAACwB,GAAZ,CAAgBnC,KAAhB,EACI,EADJ,CAEI5D,OAAO,CAAC8F,GAAR,CAAYhC,OAAO,CAACnE,GAAR,CAAYiF,kBAAZ,CAAZ,CAHa,CAIjB5E,OAAO,CAAC8F,GAAR,CAAY9B,GAAG,CAACrE,GAAJ,CAAQkF,eAAR,CAAZ,CAJiB,CAAZ,CAAP,CAMD,CARH,EASGxE,IATH,CASSe,GAAD,EAAS,CACb,MAAO,MAAK8D,cAAL,CAAoBtB,KAApB,EAA2BvD,IAA3B,CAAiC2F,UAAD,GAAiB,CACtDA,UADsD,CAEtDC,MAAM,CAAE7E,GAAG,CAAC,CAAD,CAF2C,CAAjB,CAAhC,CAAP,CAID,CAdH,CAD8B,CAgB9B5B,iBAhB8B,CAiB9B0C,cAAc,CAAC,GAAIS,CAAAA,KAAJ,CAAW,mCAAkCiB,KAAM,EAAnD,CAAD,CAjBgB,CAAzB,CAmBJvD,IAnBI,CAmBC,CAAC,CAAE2F,UAAF,CAAcC,MAAd,CAAD,GAA4B,CAChC,KAAM7E,CAAAA,GAAqB,CAAGgB,MAAM,CAAC8D,MAAP,CAG5B,CAAED,MAAM,CAAEA,MAAV,CAH4B,CAGPD,UAHO,CAA9B,CAIA,MAAO,SAAWA,CAAAA,UAAX,CAAwBA,UAAxB,CAAqC5E,GAA5C,CACD,CAzBI,EA0BJ8B,KA1BI,CA0BGf,GAAD,EAAS,CACd,GAAI0D,QAAJ,CAAc,CACZ;AACA,KAAM1D,CAAAA,GAAN,CACD,CACD,MAAO,CAAEsD,KAAK,CAAEtD,GAAT,CAAP,CACD,CAhCI,CAAP,CAiCD,CAlCgB,CAAjB,CAmCD,CAxDI,CAyDL0D,QAAQ,CAACjC,KAAD,CAA+B,CACrC;AACA;AACA,GAAIuC,CAAAA,EAAJ,CACA,GAAKA,EAAE,CAAIC,SAAD,CAAmBC,UAA7B,CAA0C,CACxC;AACA,GAAIF,EAAE,CAACG,QAAH,EAAe,KAAKC,IAAL,CAAUJ,EAAE,CAACK,aAAb,CAAnB,CAAgD,MAAOxG,CAAAA,OAAO,CAACC,OAAR,EAAP,CACjD,CACD,MAAOyD,CAAAA,gBAAgB,CAACC,WAAD,CAAcC,KAAd,CAAhB,CACJvD,IADI,CACEoG,MAAD,EACJzG,OAAO,CAAC8F,GAAR,CACE9E,WAAW,CACPyF,MAAM,CAAC3C,OAAP,CAAenE,GAAf,CAAoB8C,MAAD,EAAYxB,cAAc,CAACwB,MAAD,CAAS,QAAT,CAA7C,CADO,CAEP,EAHN,CAFG,EAQJpC,IARI,CAQC,IAAM,CACV,6CAAoB,IAAM,KAAKuF,SAAL,CAAehC,KAAf,CAAsB,IAAtB,EAA4BV,KAA5B,CAAkC,IAAM,CAAE,CAA1C,CAA1B,EACD,CAVI,EAWJA,KAXI,CAYH;AACA,IAAM,CAAE,CAbL,CAAP,CAeD,CAhFI,CAAP,CAkFD,C,aAEcoB,iB", "sourcesContent": ["import { ComponentType } from 'react'\nimport { ClientBuildManifest } from '../build/webpack/plugins/build-manifest-plugin'\nimport getAssetPathFromRoute from '../next-server/lib/router/utils/get-asset-path-from-route'\nimport { requestIdleCallback } from './request-idle-callback'\n\n// 3.8s was arbitrarily chosen as it's what https://web.dev/interactive\n// considers as \"Good\" time-to-interactive. We must assume something went\n// wrong beyond this point, and then fall-back to a full page transition to\n// show the user something of value.\nconst MS_MAX_IDLE_DELAY = 3800\n\ndeclare global {\n  interface Window {\n    __BUILD_MANIFEST?: ClientBuildManifest\n    __BUILD_MANIFEST_CB?: Function\n  }\n}\n\nexport interface LoadedEntrypointSuccess {\n  component: ComponentType\n  exports: any\n}\nexport interface LoadedEntrypointFailure {\n  error: unknown\n}\nexport type RouteEntrypoint = LoadedEntrypointSuccess | LoadedEntrypointFailure\n\nexport interface RouteStyleSheet {\n  href: string\n  content: string\n}\n\nexport interface LoadedRouteSuccess extends LoadedEntrypointSuccess {\n  styles: RouteStyleSheet[]\n}\nexport interface LoadedRouteFailure {\n  error: unknown\n}\nexport type RouteLoaderEntry = LoadedRouteSuccess | LoadedRouteFailure\n\nexport type Future<V> = {\n  resolve: (entrypoint: V) => void\n  future: Promise<V>\n}\nfunction withFuture<T>(\n  key: string,\n  map: Map<string, Future<T> | T>,\n  generator?: () => Promise<T>\n): Promise<T> {\n  let entry: Future<T> | T | undefined = map.get(key)\n  if (entry) {\n    if ('future' in entry) {\n      return entry.future\n    }\n    return Promise.resolve(entry)\n  }\n  let resolver: (entrypoint: T) => void\n  const prom: Promise<T> = new Promise<T>((resolve) => {\n    resolver = resolve\n  })\n  map.set(key, (entry = { resolve: resolver!, future: prom }))\n  return generator\n    ? // eslint-disable-next-line no-sequences\n      generator().then((value) => (resolver(value), value))\n    : prom\n}\n\nexport interface RouteLoader {\n  whenEntrypoint(route: string): Promise<RouteEntrypoint>\n  onEntrypoint(route: string, execute: () => unknown): void\n  loadRoute(route: string, prefetch?: boolean): Promise<RouteLoaderEntry>\n  prefetch(route: string): Promise<void>\n}\n\nfunction hasPrefetch(link?: HTMLLinkElement): boolean {\n  try {\n    link = document.createElement('link')\n    return (\n      // detect IE11 since it supports prefetch but isn't detected\n      // with relList.support\n      (!!window.MSInputMethodContext && !!(document as any).documentMode) ||\n      link.relList.supports('prefetch')\n    )\n  } catch {\n    return false\n  }\n}\n\nconst canPrefetch: boolean = hasPrefetch()\n\nfunction prefetchViaDom(\n  href: string,\n  as: string,\n  link?: HTMLLinkElement\n): Promise<any> {\n  return new Promise((res, rej) => {\n    if (document.querySelector(`link[rel=\"prefetch\"][href^=\"${href}\"]`)) {\n      return res()\n    }\n\n    link = document.createElement('link')\n\n    // The order of property assignment here is intentional:\n    if (as) link!.as = as\n    link!.rel = `prefetch`\n    link!.crossOrigin = process.env.__NEXT_CROSS_ORIGIN!\n    link!.onload = res\n    link!.onerror = rej\n\n    // `href` should always be last:\n    link!.href = href\n\n    document.head.appendChild(link)\n  })\n}\n\nconst ASSET_LOAD_ERROR = Symbol('ASSET_LOAD_ERROR')\n// TODO: unexport\nexport function markAssetError(err: Error): Error {\n  return Object.defineProperty(err, ASSET_LOAD_ERROR, {})\n}\n\nexport function isAssetError(err?: Error): boolean | undefined {\n  return err && ASSET_LOAD_ERROR in err\n}\n\nfunction appendScript(\n  src: string,\n  script?: HTMLScriptElement\n): Promise<unknown> {\n  return new Promise((resolve, reject) => {\n    script = document.createElement('script')\n\n    // The order of property assignment here is intentional.\n    // 1. Setup success/failure hooks in case the browser synchronously\n    //    executes when `src` is set.\n    script.onload = resolve\n    script.onerror = () =>\n      reject(markAssetError(new Error(`Failed to load script: ${src}`)))\n\n    // 2. Configure the cross-origin attribute before setting `src` in case the\n    //    browser begins to fetch.\n    script.crossOrigin = process.env.__NEXT_CROSS_ORIGIN!\n\n    // 3. Finally, set the source and inject into the DOM in case the child\n    //    must be appended for fetching to start.\n    script.src = src\n    document.body.appendChild(script)\n  })\n}\n\n// Resolve a promise that times out after given amount of milliseconds.\nfunction resolvePromiseWithTimeout<T>(\n  p: Promise<T>,\n  ms: number,\n  err: Error\n): Promise<T> {\n  return new Promise((resolve, reject) => {\n    let cancelled = false\n\n    p.then((r) => {\n      // Resolved, cancel the timeout\n      cancelled = true\n      resolve(r)\n    }).catch(reject)\n\n    requestIdleCallback(() =>\n      setTimeout(() => {\n        if (!cancelled) {\n          reject(err)\n        }\n      }, ms)\n    )\n  })\n}\n\n// TODO: stop exporting or cache the failure\n// It'd be best to stop exporting this. It's an implementation detail. We're\n// only exporting it for backwards compatibilty with the `page-loader`.\n// Only cache this response as a last resort if we cannot eliminate all other\n// code branches that use the Build Manifest Callback and push them through\n// the Route Loader interface.\nexport function getClientBuildManifest(): Promise<ClientBuildManifest> {\n  if (self.__BUILD_MANIFEST) {\n    return Promise.resolve(self.__BUILD_MANIFEST)\n  }\n\n  const onBuildManifest: Promise<ClientBuildManifest> = new Promise<\n    ClientBuildManifest\n  >((resolve) => {\n    // Mandatory because this is not concurrent safe:\n    const cb = self.__BUILD_MANIFEST_CB\n    self.__BUILD_MANIFEST_CB = () => {\n      resolve(self.__BUILD_MANIFEST!)\n      cb && cb()\n    }\n  })\n\n  return resolvePromiseWithTimeout<ClientBuildManifest>(\n    onBuildManifest,\n    MS_MAX_IDLE_DELAY,\n    markAssetError(new Error('Failed to load client build manifest'))\n  )\n}\n\ninterface RouteFiles {\n  scripts: string[]\n  css: string[]\n}\nfunction getFilesForRoute(\n  assetPrefix: string,\n  route: string\n): Promise<RouteFiles> {\n  if (process.env.NODE_ENV === 'development') {\n    return Promise.resolve({\n      scripts: [\n        assetPrefix +\n          '/_next/static/chunks/pages' +\n          encodeURI(getAssetPathFromRoute(route, '.js')),\n      ],\n      // Styles are handled by `style-loader` in development:\n      css: [],\n    })\n  }\n  return getClientBuildManifest().then((manifest) => {\n    if (!(route in manifest)) {\n      throw markAssetError(new Error(`Failed to lookup route: ${route}`))\n    }\n    const allFiles = manifest[route].map(\n      (entry) => assetPrefix + '/_next/' + encodeURI(entry)\n    )\n    return {\n      scripts: allFiles.filter((v) => v.endsWith('.js')),\n      css: allFiles.filter((v) => v.endsWith('.css')),\n    }\n  })\n}\n\nfunction createRouteLoader(assetPrefix: string): RouteLoader {\n  const entrypoints: Map<\n    string,\n    Future<RouteEntrypoint> | RouteEntrypoint\n  > = new Map()\n  const loadedScripts: Map<string, Promise<unknown>> = new Map()\n  const styleSheets: Map<string, Promise<RouteStyleSheet>> = new Map()\n  const routes: Map<\n    string,\n    Future<RouteLoaderEntry> | RouteLoaderEntry\n  > = new Map()\n\n  function maybeExecuteScript(src: string): Promise<unknown> {\n    let prom: Promise<unknown> | undefined = loadedScripts.get(src)\n    if (prom) {\n      return prom\n    }\n\n    // Skip executing script if it's already in the DOM:\n    if (document.querySelector(`script[src^=\"${src}\"]`)) {\n      return Promise.resolve()\n    }\n\n    loadedScripts.set(src, (prom = appendScript(src)))\n    return prom\n  }\n\n  function fetchStyleSheet(href: string): Promise<RouteStyleSheet> {\n    let prom: Promise<RouteStyleSheet> | undefined = styleSheets.get(href)\n    if (prom) {\n      return prom\n    }\n\n    styleSheets.set(\n      href,\n      (prom = fetch(href)\n        .then((res) => {\n          if (!res.ok) {\n            throw new Error(`Failed to load stylesheet: ${href}`)\n          }\n          return res.text().then((text) => ({ href: href, content: text }))\n        })\n        .catch((err) => {\n          throw markAssetError(err)\n        }))\n    )\n    return prom\n  }\n\n  return {\n    whenEntrypoint(route: string) {\n      return withFuture(route, entrypoints)\n    },\n    onEntrypoint(route: string, execute: () => unknown) {\n      Promise.resolve(execute)\n        .then((fn) => fn())\n        .then(\n          (exports: any) => ({\n            component: (exports && exports.default) || exports,\n            exports: exports,\n          }),\n          (err) => ({ error: err })\n        )\n        .then((input: RouteEntrypoint) => {\n          const old = entrypoints.get(route)\n          entrypoints.set(route, input)\n          if (old && 'resolve' in old) old.resolve(input)\n        })\n    },\n    loadRoute(route: string, prefetch?: boolean) {\n      return withFuture<RouteLoaderEntry>(route, routes, () => {\n        return resolvePromiseWithTimeout(\n          getFilesForRoute(assetPrefix, route)\n            .then(({ scripts, css }) => {\n              return Promise.all([\n                entrypoints.has(route)\n                  ? []\n                  : Promise.all(scripts.map(maybeExecuteScript)),\n                Promise.all(css.map(fetchStyleSheet)),\n              ] as const)\n            })\n            .then((res) => {\n              return this.whenEntrypoint(route).then((entrypoint) => ({\n                entrypoint,\n                styles: res[1],\n              }))\n            }),\n          MS_MAX_IDLE_DELAY,\n          markAssetError(new Error(`Route did not complete loading: ${route}`))\n        )\n          .then(({ entrypoint, styles }) => {\n            const res: RouteLoaderEntry = Object.assign<\n              { styles: RouteStyleSheet[] },\n              RouteEntrypoint\n            >({ styles: styles! }, entrypoint)\n            return 'error' in entrypoint ? entrypoint : res\n          })\n          .catch((err) => {\n            if (prefetch) {\n              // we don't want to cache errors during prefetch\n              throw err\n            }\n            return { error: err }\n          })\n      })\n    },\n    prefetch(route: string): Promise<void> {\n      // https://github.com/GoogleChromeLabs/quicklink/blob/453a661fa1fa940e2d2e044452398e38c67a98fb/src/index.mjs#L115-L118\n      // License: Apache 2.0\n      let cn\n      if ((cn = (navigator as any).connection)) {\n        // Don't prefetch if using 2G or if Save-Data is enabled.\n        if (cn.saveData || /2g/.test(cn.effectiveType)) return Promise.resolve()\n      }\n      return getFilesForRoute(assetPrefix, route)\n        .then((output) =>\n          Promise.all(\n            canPrefetch\n              ? output.scripts.map((script) => prefetchViaDom(script, 'script'))\n              : []\n          )\n        )\n        .then(() => {\n          requestIdleCallback(() => this.loadRoute(route, true).catch(() => {}))\n        })\n        .catch(\n          // swallow prefetch errors\n          () => {}\n        )\n    },\n  }\n}\n\nexport default createRouteLoader\n"]}