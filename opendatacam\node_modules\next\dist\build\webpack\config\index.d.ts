import { webpack } from 'next/dist/compiled/webpack/webpack';
import { NextConfig } from '../../../next-server/server/config';
export declare function build(config: webpack.Configuration, { rootDirectory, customAppFile, isDevelopment, isServer, assetPrefix, sassOptions, productionBrowserSourceMaps, future, }: {
    rootDirectory: string;
    customAppFile: string | null;
    isDevelopment: boolean;
    isServer: boolean;
    assetPrefix: string;
    sassOptions: any;
    productionBrowserSourceMaps: boolean;
    future: NextConfig['future'];
}): Promise<webpack.Configuration>;
