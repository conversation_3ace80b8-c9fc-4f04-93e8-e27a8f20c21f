{"version": 3, "sources": ["../../../../build/webpack/config/utils.ts"], "names": ["pipe", "fns", "param", "reduce", "result", "next"], "mappings": "yDAyBO,KAAMA,CAAAA,IAAI,CAAG,CAAI,GAAGC,GAAP,GAClBC,KADkE,EAGlED,GAAG,CAACE,MAAJ,CAAW,MAAOC,MAAP,CAA+BC,IAA/B,GAAwCA,IAAI,CAAC,KAAMD,CAAAA,MAAP,CAAvD,CAAuEF,KAAvE,CAHK,C", "sourcesContent": ["import { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { NextConfig } from '../../../next-server/server/config'\n\nexport type ConfigurationContext = {\n  rootDirectory: string\n  customAppFile: string | null\n\n  isDevelopment: boolean\n  isProduction: boolean\n\n  isServer: boolean\n  isClient: boolean\n\n  assetPrefix: string\n\n  sassOptions: any\n  productionBrowserSourceMaps: boolean\n\n  future: NextConfig['future']\n}\n\nexport type ConfigurationFn = (\n  a: webpack.Configuration\n) => webpack.Configuration\n\nexport const pipe = <R>(...fns: Array<(a: R) => R | Promise<R>>) => (\n  param: R\n) =>\n  fns.reduce(async (result: R | Promise<R>, next) => next(await result), param)\n"]}