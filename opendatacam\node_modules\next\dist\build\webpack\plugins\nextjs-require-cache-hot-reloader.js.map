{"version": 3, "sources": ["../../../../build/webpack/plugins/nextjs-require-cache-hot-reloader.ts"], "names": ["originModules", "require", "resolve", "deleteCache", "filePath", "e", "code", "module", "cache", "originModule", "parent", "idx", "children", "indexOf", "splice", "child", "PLUGIN_NAME", "NextJsRequireCacheHotReloader", "prevAssets", "previousOutputPathsWebpack5", "Set", "currentOutputPathsWebpack5", "apply", "compiler", "isWebpack5", "hooks", "assetEmitted", "tap", "_file", "targetPath", "add", "afterEmit", "compilation", "runtimeChunk<PERSON><PERSON>", "path", "join", "outputOptions", "entries", "keys", "filter", "entry", "toString", "startsWith", "for<PERSON>ach", "page", "outputPath", "clear", "tapAsync", "callback", "assets", "f", "Object", "existsAt"], "mappings": "kFACA,2DACA,sBACA,kD,mFAEA,KAAMA,CAAAA,aAAa,CAAG,CACpBC,OAAO,CAACC,OAAR,CAAgB,qCAAhB,CADoB,CAEpBD,OAAO,CAACC,OAAR,CAAgB,6CAAhB,CAFoB,CAAtB,CAKA,QAASC,CAAAA,WAAT,CAAqBC,QAArB,CAAuC,CACrC,GAAI,CACFA,QAAQ,CAAG,qBAAaA,QAAb,CAAX,CACD,CAAC,MAAOC,CAAP,CAAU,CACV,GAAIA,CAAC,CAACC,IAAF,GAAW,QAAf,CAAyB,KAAMD,CAAAA,CAAN,CAC1B,CACD,KAAME,CAAAA,MAAM,CAAGN,OAAO,CAACO,KAAR,CAAcJ,QAAd,CAAf,CACA,GAAIG,MAAJ,CAAY,CACV;AACA,IAAK,KAAME,CAAAA,YAAX,GAA2BT,CAAAA,aAA3B,CAA0C,CACxC,KAAMU,CAAAA,MAAM,CAAGT,OAAO,CAACO,KAAR,CAAcC,YAAd,CAAf,CACA,GAAIC,MAAJ,CAAY,CACV,KAAMC,CAAAA,GAAG,CAAGD,MAAM,CAACE,QAAP,CAAgBC,OAAhB,CAAwBN,MAAxB,CAAZ,CACA,GAAII,GAAG,EAAI,CAAX,CAAcD,MAAM,CAACE,QAAP,CAAgBE,MAAhB,CAAuBH,GAAvB,CAA4B,CAA5B,EACf,CACF,CACD;AACA,IAAK,KAAMI,CAAAA,KAAX,GAAoBR,CAAAA,MAAM,CAACK,QAA3B,CAAqC,CACnCG,KAAK,CAACL,MAAN,CAAe,IAAf,CACD,CACF,CACD,MAAOT,CAAAA,OAAO,CAACO,KAAR,CAAcJ,QAAd,CAAP,CACD,CAED,KAAMY,CAAAA,WAAW,CAAG,+BAApB,CAEA;AACO,KAAMC,CAAAA,6BAAwD,oBACnEC,UADmE,CACjD,IADiD,MAEnEC,2BAFmE,CAExB,GAAIC,CAAAA,GAAJ,EAFwB,MAGnEC,0BAHmE,CAGzB,GAAID,CAAAA,GAAJ,EAHyB,EAKnEE,KAAK,CAACC,QAAD,CAA6B,CAChC,GAAIC,mBAAJ,CAAgB,CACd;AACAD,QAAQ,CAACE,KAAT,CAAeC,YAAf,CAA4BC,GAA5B,CACEX,WADF,CAEE,CAACY,KAAD,CAAa,CAAEC,UAAF,CAAb,GAAqC,CACnC,KAAKR,0BAAL,CAAgCS,GAAhC,CAAoCD,UAApC,EACA1B,WAAW,CAAC0B,UAAD,CAAX,CACD,CALH,EAQAN,QAAQ,CAACE,KAAT,CAAeM,SAAf,CAAyBJ,GAAzB,CAA6BX,WAA7B,CAA2CgB,WAAD,EAAiB,CACzD,KAAMC,CAAAA,gBAAgB,CAAGC,cAAKC,IAAL,CACvBH,WAAW,CAACI,aAAZ,CAA0BF,IADH,CAEvB,oBAFuB,CAAzB,CAIA/B,WAAW,CAAC8B,gBAAD,CAAX,CAEA;AACA;AACA;AACA,KAAMI,CAAAA,OAAO,CAAG,CAAC,GAAGL,WAAW,CAACK,OAAZ,CAAoBC,IAApB,EAAJ,EAAgCC,MAAhC,CAAwCC,KAAD,EACrDA,KAAK,CAACC,QAAN,GAAiBC,UAAjB,CAA4B,QAA5B,CADc,CAAhB,CAIAL,OAAO,CAACM,OAAR,CAAiBC,IAAD,EAAU,CACxB,KAAMC,CAAAA,UAAU,CAAGX,cAAKC,IAAL,CACjBH,WAAW,CAACI,aAAZ,CAA0BF,IADT,CAEjBU,IAAI,CAAG,KAFU,CAAnB,CAIAzC,WAAW,CAAC0C,UAAD,CAAX,CACD,CAND,EAOD,CArBD,EAuBA,KAAK1B,2BAAL,CAAmC,GAAIC,CAAAA,GAAJ,CACjC,KAAKC,0BAD4B,CAAnC,CAGA,KAAKA,0BAAL,CAAgCyB,KAAhC,GACA,OACD,CAEDvB,QAAQ,CAACE,KAAT,CAAeM,SAAf,CAAyBgB,QAAzB,CAAkC/B,WAAlC,CAA+C,CAACgB,WAAD,CAAcgB,QAAd,GAA2B,CACxE,KAAM,CAAEC,MAAF,EAAajB,WAAnB,CAEA,GAAI,KAAKd,UAAT,CAAqB,CACnB,IAAK,KAAMgC,CAAAA,CAAX,GAAgBC,CAAAA,MAAM,CAACb,IAAP,CAAYW,MAAZ,CAAhB,CAAqC,CACnC9C,WAAW,CAAC8C,MAAM,CAACC,CAAD,CAAN,CAAUE,QAAX,CAAX,CACD,CACD,IAAK,KAAMF,CAAAA,CAAX,GAAgBC,CAAAA,MAAM,CAACb,IAAP,CAAY,KAAKpB,UAAjB,CAAhB,CAA8C,CAC5C,GAAI,CAAC+B,MAAM,CAACC,CAAD,CAAX,CAAgB,CACd/C,WAAW,CAAC,KAAKe,UAAL,CAAgBgC,CAAhB,EAAmBE,QAApB,CAAX,CACD,CACF,CACF,CACD,KAAKlC,UAAL,CAAkB+B,MAAlB,CAEAD,QAAQ,GACT,CAhBD,EAiBD,CA/DkE,C", "sourcesContent": ["import { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { isWebpack5 } from 'next/dist/compiled/webpack/webpack'\nimport { realpathSync } from 'fs'\nimport path from 'path'\n\nconst originModules = [\n  require.resolve('../../../next-server/server/require'),\n  require.resolve('../../../next-server/server/load-components'),\n]\n\nfunction deleteCache(filePath: string) {\n  try {\n    filePath = realpathSync(filePath)\n  } catch (e) {\n    if (e.code !== 'ENOENT') throw e\n  }\n  const module = require.cache[filePath]\n  if (module) {\n    // remove the child reference from the originModules\n    for (const originModule of originModules) {\n      const parent = require.cache[originModule]\n      if (parent) {\n        const idx = parent.children.indexOf(module)\n        if (idx >= 0) parent.children.splice(idx, 1)\n      }\n    }\n    // remove parent references from external modules\n    for (const child of module.children) {\n      child.parent = null\n    }\n  }\n  delete require.cache[filePath]\n}\n\nconst PLUGIN_NAME = 'NextJsRequireCacheHotReloader'\n\n// This plugin flushes require.cache after emitting the files. Providing 'hot reloading' of server files.\nexport class NextJsRequireCacheHotReloader implements webpack.Plugin {\n  prevAssets: any = null\n  previousOutputPathsWebpack5: Set<string> = new Set()\n  currentOutputPathsWebpack5: Set<string> = new Set()\n\n  apply(compiler: webpack.Compiler) {\n    if (isWebpack5) {\n      // @ts-ignored Webpack has this hooks\n      compiler.hooks.assetEmitted.tap(\n        PLUGIN_NAME,\n        (_file: any, { targetPath }: any) => {\n          this.currentOutputPathsWebpack5.add(targetPath)\n          deleteCache(targetPath)\n        }\n      )\n\n      compiler.hooks.afterEmit.tap(PLUGIN_NAME, (compilation) => {\n        const runtimeChunkPath = path.join(\n          compilation.outputOptions.path,\n          'webpack-runtime.js'\n        )\n        deleteCache(runtimeChunkPath)\n\n        // we need to make sure to clear all server entries from cache\n        // since they can have a stale webpack-runtime cache\n        // which needs to always be in-sync\n        const entries = [...compilation.entries.keys()].filter((entry) =>\n          entry.toString().startsWith('pages/')\n        )\n\n        entries.forEach((page) => {\n          const outputPath = path.join(\n            compilation.outputOptions.path,\n            page + '.js'\n          )\n          deleteCache(outputPath)\n        })\n      })\n\n      this.previousOutputPathsWebpack5 = new Set(\n        this.currentOutputPathsWebpack5\n      )\n      this.currentOutputPathsWebpack5.clear()\n      return\n    }\n\n    compiler.hooks.afterEmit.tapAsync(PLUGIN_NAME, (compilation, callback) => {\n      const { assets } = compilation\n\n      if (this.prevAssets) {\n        for (const f of Object.keys(assets)) {\n          deleteCache(assets[f].existsAt)\n        }\n        for (const f of Object.keys(this.prevAssets)) {\n          if (!assets[f]) {\n            deleteCache(this.prevAssets[f].existsAt)\n          }\n        }\n      }\n      this.prevAssets = assets\n\n      callback()\n    })\n  }\n}\n"]}