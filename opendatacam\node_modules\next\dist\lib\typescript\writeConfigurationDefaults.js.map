{"version": 3, "sources": ["../../../lib/typescript/writeConfigurationDefaults.ts"], "names": ["getDesiredCompilerOptions", "ts", "o", "target", "suggested", "lib", "allowJs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "strict", "forceConsistentCasingInFileNames", "noEmit", "esModuleInterop", "value", "reason", "module", "parsedValue", "Module<PERSON>ind", "ESNext", "parsed<PERSON><PERSON>ues", "ES2020", "CommonJS", "AMD", "moduleResolution", "ModuleResolutionKind", "NodeJs", "resolveJsonModule", "isolatedModules", "jsx", "JsxEmit", "Preserve", "getRequiredConfiguration", "res", "desiredCompilerOptions", "optionKey", "Object", "keys", "ev", "writeConfigurationDefaults", "tsConfigPath", "isFirstTimeSetup", "fs", "writeFile", "os", "EOL", "options", "tsOptions", "raw", "rawConfig", "userTsConfigContent", "readFile", "encoding", "userTsConfig", "CommentJson", "parse", "compilerOptions", "suggestedActions", "requiredActions", "check", "push", "chalk", "cyan", "bold", "includes", "_", "include", "exclude", "length", "stringify", "console", "log", "green", "for<PERSON>ach", "action"], "mappings": "6JAAA,sBACA,oDACA,oFACA,8CACA,wE,w4BAaA,QAASA,CAAAA,yBAAT,CACEC,EADF,CAE+B,CAC7B,KAAMC,CAAAA,CAA8B,CAAG,CACrC;AACA;AACAC,MAAM,CAAE,CAAEC,SAAS,CAAE,KAAb,CAH6B,CAIrCC,GAAG,CAAE,CAAED,SAAS,CAAE,CAAC,KAAD,CAAQ,cAAR,CAAwB,QAAxB,CAAb,CAJgC,CAKrCE,OAAO,CAAE,CAAEF,SAAS,CAAE,IAAb,CAL4B,CAMrCG,YAAY,CAAE,CAAEH,SAAS,CAAE,IAAb,CANuB,CAOrCI,MAAM,CAAE,CAAEJ,SAAS,CAAE,KAAb,CAP6B,CAQrCK,gCAAgC,CAAE,CAAEL,SAAS,CAAE,IAAb,CARG,CASrCM,MAAM,CAAE,CAAEN,SAAS,CAAE,IAAb,CAT6B,CAWrC;AACA;AACA;AACAO,eAAe,CAAE,CACfC,KAAK,CAAE,IADQ,CAEfC,MAAM,CAAE,uBAFO,CAdoB,CAkBrCC,MAAM,CAAE,CACNC,WAAW,CAAEd,EAAE,CAACe,UAAH,CAAcC,MADrB,CAEN;AACAC,YAAY,CAAE,CACZjB,EAAE,CAACe,UAAH,CAAcG,MADF,CAEZlB,EAAE,CAACe,UAAH,CAAcC,MAFF,CAGZhB,EAAE,CAACe,UAAH,CAAcI,QAHF,CAIZnB,EAAE,CAACe,UAAH,CAAcK,GAJF,CAHR,CASNT,KAAK,CAAE,QATD,CAUNC,MAAM,CAAE,8BAVF,CAlB6B,CA8BrCS,gBAAgB,CAAE,CAChBP,WAAW,CAAEd,EAAE,CAACsB,oBAAH,CAAwBC,MADrB,CAEhBZ,KAAK,CAAE,MAFS,CAGhBC,MAAM,CAAE,6BAHQ,CA9BmB,CAmCrCY,iBAAiB,CAAE,CAAEb,KAAK,CAAE,IAAT,CAAeC,MAAM,CAAE,6BAAvB,CAnCkB,CAoCrCa,eAAe,CAAE,CACfd,KAAK,CAAE,IADQ,CAEfC,MAAM,CAAE,uBAFO,CApCoB,CAwCrCc,GAAG,CAAE,CACHZ,WAAW,CAAEd,EAAE,CAAC2B,OAAH,CAAWC,QADrB,CAEHjB,KAAK,CAAE,UAFJ,CAGHC,MAAM,CAAE,oDAHL,CAxCgC,CAAvC,CA+CA,MAAOX,CAAAA,CAAP,CACD,CAEM,QAAS4B,CAAAA,wBAAT,CACL7B,EADK,CAE0C,CAC/C,KAAM8B,CAAAA,GAAkD,CAAG,EAA3D,CAEA,KAAMC,CAAAA,sBAAsB,CAAGhC,yBAAyB,CAACC,EAAD,CAAxD,CACA,IAAK,KAAMgC,CAAAA,SAAX,GAAwBC,CAAAA,MAAM,CAACC,IAAP,CAAYH,sBAAZ,CAAxB,CAA6D,qBAC3D,KAAMI,CAAAA,EAAE,CAAGJ,sBAAsB,CAACC,SAAD,CAAjC,CACA,GAAI,EAAE,SAAWG,CAAAA,EAAb,CAAJ,CAAsB,CACpB,SACD,CACDL,GAAG,CAACE,SAAD,CAAH,kBAAiBG,EAAE,CAACrB,WAApB,wBAAmCqB,EAAE,CAACxB,KAAtC,CACD,CAED,MAAOmB,CAAAA,GAAP,CACD,CAEM,cAAeM,CAAAA,0BAAf,CACLpC,EADK,CAELqC,YAFK,CAGLC,gBAHK,CAIU,CACf,GAAIA,gBAAJ,CAAsB,CACpB,KAAMC,cAAGC,SAAH,CAAaH,YAAb,CAA2B,KAAOI,YAAGC,GAArC,CAAN,CACD,CAED,KAAMX,CAAAA,sBAAsB,CAAGhC,yBAAyB,CAACC,EAAD,CAAxD,CACA,KAAM,CACJ2C,OAAO,CAAEC,SADL,CAEJC,GAAG,CAAEC,SAFD,EAGF,KAAM,2DAA2B9C,EAA3B,CAA+BqC,YAA/B,CAA6C,IAA7C,CAHV,CAKA,KAAMU,CAAAA,mBAAmB,CAAG,KAAMR,cAAGS,QAAH,CAAYX,YAAZ,CAA0B,CAC1DY,QAAQ,CAAE,MADgD,CAA1B,CAAlC,CAGA,KAAMC,CAAAA,YAAY,CAAGC,WAAW,CAACC,KAAZ,CAAkBL,mBAAlB,CAArB,CACA,GAAIG,YAAY,CAACG,eAAb,EAAgC,IAAhC,EAAwC,EAAE,WAAaP,CAAAA,SAAf,CAA5C,CAAuE,CACrEI,YAAY,CAACG,eAAb,CAA+B,EAA/B,CACAf,gBAAgB,CAAG,IAAnB,CACD,CAED,KAAMgB,CAAAA,gBAA0B,CAAG,EAAnC,CACA,KAAMC,CAAAA,eAAyB,CAAG,EAAlC,CACA,IAAK,KAAMvB,CAAAA,SAAX,GAAwBC,CAAAA,MAAM,CAACC,IAAP,CAAYH,sBAAZ,CAAxB,CAA6D,CAC3D,KAAMyB,CAAAA,KAAK,CAAGzB,sBAAsB,CAACC,SAAD,CAApC,CACA,GAAI,aAAewB,CAAAA,KAAnB,CAA0B,CACxB,GAAI,EAAExB,SAAS,GAAIY,CAAAA,SAAf,CAAJ,CAA+B,CAC7BM,YAAY,CAACG,eAAb,CAA6BrB,SAA7B,EAA0CwB,KAAK,CAACrD,SAAhD,CACAmD,gBAAgB,CAACG,IAAjB,CACEC,eAAMC,IAAN,CAAW3B,SAAX,EAAwB,cAAxB,CAAyC0B,eAAME,IAAN,CAAWJ,KAAK,CAACrD,SAAjB,CAD3C,EAGD,CACF,CAPD,IAOO,IAAI,SAAWqD,CAAAA,KAAf,CAAsB,yBAC3B,KAAMrB,CAAAA,EAAE,CAAGS,SAAS,CAACZ,SAAD,CAApB,CACA,GACE,EAAE,gBAAkBwB,CAAAA,KAAlB,sBACEA,KAAK,CAACvC,YADR,eACE,oBAAoB4C,QAApB,CAA6B1B,EAA7B,CADF,CAEE,eAAiBqB,CAAAA,KAAjB,CACAA,KAAK,CAAC1C,WAAN,GAAsBqB,EADtB,CAEAqB,KAAK,CAAC7C,KAAN,GAAgBwB,EAJpB,CADF,CAME,CACAe,YAAY,CAACG,eAAb,CAA6BrB,SAA7B,EAA0CwB,KAAK,CAAC7C,KAAhD,CACA4C,eAAe,CAACE,IAAhB,CACEC,eAAMC,IAAN,CAAW3B,SAAX,EACE,cADF,CAEE0B,eAAME,IAAN,CAAWJ,KAAK,CAAC7C,KAAjB,CAFF,CAGG,KAAI6C,KAAK,CAAC5C,MAAO,GAJtB,EAMD,CACF,CAjBM,IAiBA,CACL;AACA,KAAMkD,CAAAA,CAAQ,CAAGN,KAAjB,CACD,CACF,CAED,GAAI,EAAE,WAAaV,CAAAA,SAAf,CAAJ,CAA+B,CAC7BI,YAAY,CAACa,OAAb,CAAuB,CAAC,eAAD,CAAkB,SAAlB,CAA6B,UAA7B,CAAvB,CACAT,gBAAgB,CAACG,IAAjB,CACEC,eAAMC,IAAN,CAAW,SAAX,EACE,cADF,CAEED,eAAME,IAAN,CAAY,0CAAZ,CAHJ,EAKD,CAED,GAAI,EAAE,WAAad,CAAAA,SAAf,CAAJ,CAA+B,CAC7BI,YAAY,CAACc,OAAb,CAAuB,CAAC,cAAD,CAAvB,CACAV,gBAAgB,CAACG,IAAjB,CACEC,eAAMC,IAAN,CAAW,SAAX,EAAwB,cAAxB,CAAyCD,eAAME,IAAN,CAAY,kBAAZ,CAD3C,EAGD,CAED,GAAIN,gBAAgB,CAACW,MAAjB,CAA0B,CAA1B,EAA+BV,eAAe,CAACU,MAAhB,CAAyB,CAA5D,CAA+D,CAC7D,OACD,CAED,KAAM1B,cAAGC,SAAH,CACJH,YADI,CAEJc,WAAW,CAACe,SAAZ,CAAsBhB,YAAtB,CAAoC,IAApC,CAA0C,CAA1C,EAA+CT,YAAGC,GAF9C,CAAN,CAKA,GAAIJ,gBAAJ,CAAsB,CACpB6B,OAAO,CAACC,GAAR,CACEV,eAAMW,KAAN,CACG,wDAAuDX,eAAME,IAAN,CACtD,eADsD,CAEtD,gBAHJ,EAII,IALN,EAOA,OACD,CAEDO,OAAO,CAACC,GAAR,CACEV,eAAMW,KAAN,CACG,gEAA+DX,eAAME,IAAN,CAC9D,eAD8D,CAE9D,wCAAuCF,eAAME,IAAN,CAAW,OAAX,CAAoB,cAH/D,EAII,IALN,EAOA,GAAIN,gBAAgB,CAACW,MAArB,CAA6B,CAC3BE,OAAO,CAACC,GAAR,CACG,qDAAoDV,eAAMC,IAAN,CACnD,eADmD,CAEnD,kBAAiBD,eAAME,IAAN,CACjB,gBADiB,CAEjB,iCALJ,EAQAN,gBAAgB,CAACgB,OAAjB,CAA0BC,MAAD,EAAYJ,OAAO,CAACC,GAAR,CAAa,OAAMG,MAAO,EAA1B,CAArC,EAEAJ,OAAO,CAACC,GAAR,CAAY,EAAZ,EACD,CAED,GAAIb,eAAe,CAACU,MAApB,CAA4B,CAC1BE,OAAO,CAACC,GAAR,CACG,iBAAgBV,eAAME,IAAN,CACf,mBADe,CAEf,sBAAqBF,eAAMC,IAAN,CAAW,eAAX,CAA4B,KAHrD,EAMAJ,eAAe,CAACe,OAAhB,CAAyBC,MAAD,EAAYJ,OAAO,CAACC,GAAR,CAAa,OAAMG,MAAO,EAA1B,CAApC,EAEAJ,OAAO,CAACC,GAAR,CAAY,EAAZ,EACD,CACF", "sourcesContent": ["import { promises as fs } from 'fs'\nimport chalk from 'chalk'\nimport * as Comment<PERSON>son from 'next/dist/compiled/comment-json'\nimport os from 'os'\nimport { getTypeScriptConfiguration } from './getTypeScriptConfiguration'\n\ntype DesiredCompilerOptionsShape = {\n  [key: string]:\n    | { suggested: any }\n    | {\n        parsedValue?: any\n        parsedValues?: Array<any>\n        value: any\n        reason: string\n      }\n}\n\nfunction getDesiredCompilerOptions(\n  ts: typeof import('typescript')\n): DesiredCompilerOptionsShape {\n  const o: DesiredCompilerOptionsShape = {\n    // These are suggested values and will be set when not present in the\n    // tsconfig.json\n    target: { suggested: 'es5' },\n    lib: { suggested: ['dom', 'dom.iterable', 'esnext'] },\n    allowJs: { suggested: true },\n    skipLibCheck: { suggested: true },\n    strict: { suggested: false },\n    forceConsistentCasingInFileNames: { suggested: true },\n    noEmit: { suggested: true },\n\n    // These values are required and cannot be changed by the user\n    // Keep this in sync with the webpack config\n    // 'parsedValue' matches the output value from ts.parseJsonConfigFileContent()\n    esModuleInterop: {\n      value: true,\n      reason: 'requirement for babel',\n    },\n    module: {\n      parsedValue: ts.ModuleKind.ESNext,\n      // All of these values work:\n      parsedValues: [\n        ts.ModuleKind.ES2020,\n        ts.ModuleKind.ESNext,\n        ts.ModuleKind.CommonJS,\n        ts.ModuleKind.AMD,\n      ],\n      value: 'esnext',\n      reason: 'for dynamic import() support',\n    },\n    moduleResolution: {\n      parsedValue: ts.ModuleResolutionKind.NodeJs,\n      value: 'node',\n      reason: 'to match webpack resolution',\n    },\n    resolveJsonModule: { value: true, reason: 'to match webpack resolution' },\n    isolatedModules: {\n      value: true,\n      reason: 'requirement for babel',\n    },\n    jsx: {\n      parsedValue: ts.JsxEmit.Preserve,\n      value: 'preserve',\n      reason: 'next.js implements its own optimized jsx transform',\n    },\n  }\n\n  return o\n}\n\nexport function getRequiredConfiguration(\n  ts: typeof import('typescript')\n): Partial<import('typescript').CompilerOptions> {\n  const res: Partial<import('typescript').CompilerOptions> = {}\n\n  const desiredCompilerOptions = getDesiredCompilerOptions(ts)\n  for (const optionKey of Object.keys(desiredCompilerOptions)) {\n    const ev = desiredCompilerOptions[optionKey]\n    if (!('value' in ev)) {\n      continue\n    }\n    res[optionKey] = ev.parsedValue ?? ev.value\n  }\n\n  return res\n}\n\nexport async function writeConfigurationDefaults(\n  ts: typeof import('typescript'),\n  tsConfigPath: string,\n  isFirstTimeSetup: boolean\n): Promise<void> {\n  if (isFirstTimeSetup) {\n    await fs.writeFile(tsConfigPath, '{}' + os.EOL)\n  }\n\n  const desiredCompilerOptions = getDesiredCompilerOptions(ts)\n  const {\n    options: tsOptions,\n    raw: rawConfig,\n  } = await getTypeScriptConfiguration(ts, tsConfigPath, true)\n\n  const userTsConfigContent = await fs.readFile(tsConfigPath, {\n    encoding: 'utf8',\n  })\n  const userTsConfig = CommentJson.parse(userTsConfigContent)\n  if (userTsConfig.compilerOptions == null && !('extends' in rawConfig)) {\n    userTsConfig.compilerOptions = {}\n    isFirstTimeSetup = true\n  }\n\n  const suggestedActions: string[] = []\n  const requiredActions: string[] = []\n  for (const optionKey of Object.keys(desiredCompilerOptions)) {\n    const check = desiredCompilerOptions[optionKey]\n    if ('suggested' in check) {\n      if (!(optionKey in tsOptions)) {\n        userTsConfig.compilerOptions[optionKey] = check.suggested\n        suggestedActions.push(\n          chalk.cyan(optionKey) + ' was set to ' + chalk.bold(check.suggested)\n        )\n      }\n    } else if ('value' in check) {\n      const ev = tsOptions[optionKey]\n      if (\n        !('parsedValues' in check\n          ? check.parsedValues?.includes(ev)\n          : 'parsedValue' in check\n          ? check.parsedValue === ev\n          : check.value === ev)\n      ) {\n        userTsConfig.compilerOptions[optionKey] = check.value\n        requiredActions.push(\n          chalk.cyan(optionKey) +\n            ' was set to ' +\n            chalk.bold(check.value) +\n            ` (${check.reason})`\n        )\n      }\n    } else {\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      const _: never = check\n    }\n  }\n\n  if (!('include' in rawConfig)) {\n    userTsConfig.include = ['next-env.d.ts', '**/*.ts', '**/*.tsx']\n    suggestedActions.push(\n      chalk.cyan('include') +\n        ' was set to ' +\n        chalk.bold(`['next-env.d.ts', '**/*.ts', '**/*.tsx']`)\n    )\n  }\n\n  if (!('exclude' in rawConfig)) {\n    userTsConfig.exclude = ['node_modules']\n    suggestedActions.push(\n      chalk.cyan('exclude') + ' was set to ' + chalk.bold(`['node_modules']`)\n    )\n  }\n\n  if (suggestedActions.length < 1 && requiredActions.length < 1) {\n    return\n  }\n\n  await fs.writeFile(\n    tsConfigPath,\n    CommentJson.stringify(userTsConfig, null, 2) + os.EOL\n  )\n\n  if (isFirstTimeSetup) {\n    console.log(\n      chalk.green(\n        `We detected TypeScript in your project and created a ${chalk.bold(\n          'tsconfig.json'\n        )} file for you.`\n      ) + '\\n'\n    )\n    return\n  }\n\n  console.log(\n    chalk.green(\n      `We detected TypeScript in your project and reconfigured your ${chalk.bold(\n        'tsconfig.json'\n      )} file for you. Strict-mode is set to ${chalk.bold('false')} by default.`\n    ) + '\\n'\n  )\n  if (suggestedActions.length) {\n    console.log(\n      `The following suggested values were added to your ${chalk.cyan(\n        'tsconfig.json'\n      )}. These values ${chalk.bold(\n        'can be changed'\n      )} to fit your project's needs:\\n`\n    )\n\n    suggestedActions.forEach((action) => console.log(`\\t- ${action}`))\n\n    console.log('')\n  }\n\n  if (requiredActions.length) {\n    console.log(\n      `The following ${chalk.bold(\n        'mandatory changes'\n      )} were made to your ${chalk.cyan('tsconfig.json')}:\\n`\n    )\n\n    requiredActions.forEach((action) => console.log(`\\t- ${action}`))\n\n    console.log('')\n  }\n}\n"]}