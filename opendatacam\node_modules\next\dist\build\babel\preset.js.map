{"version": 3, "sources": ["../../../build/babel/preset.ts"], "names": ["isLoadIntentTest", "process", "env", "NODE_ENV", "isLoadIntentDevelopment", "styledJsxOptions", "options", "Array", "isArray", "plugins", "map", "plugin", "name", "pluginOptions", "require", "resolve", "supportsStaticESM", "caller", "api", "supportsESM", "isServer", "isCallerDevelopment", "isDev", "isTest", "isDevelopment", "isProduction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useJsxRuntime", "runtime", "Boolean", "hasJsxRuntime", "presetEnvConfig", "modules", "exclude", "include", "targets", "node", "sourceType", "presets", "development", "pragma", "allowNamespaces", "module", "importAs", "property", "lib", "useBuiltIns", "corejs", "helpers", "regenerator", "useESModules", "absoluteRuntime", "undefined", "removeImport", "filter"], "mappings": "4DACA,0BAEA,KAAMA,CAAAA,gBAAgB,CAAGC,OAAO,CAACC,GAAR,CAAYC,QAAZ,GAAyB,MAAlD,CACA,KAAMC,CAAAA,uBAAuB,CAAGH,OAAO,CAACC,GAAR,CAAYC,QAAZ,GAAyB,aAAzD,CAUA;AACA,QAASE,CAAAA,gBAAT,CAA0BC,OAA1B,CAA0D,CACxD,GAAI,CAACA,OAAL,CAAc,CACZ,MAAO,EAAP,CACD,CAED,GAAI,CAACC,KAAK,CAACC,OAAN,CAAcF,OAAO,CAACG,OAAtB,CAAL,CAAqC,CACnC,MAAOH,CAAAA,OAAP,CACD,CAEDA,OAAO,CAACG,OAAR,CAAkBH,OAAO,CAACG,OAAR,CAAgBC,GAAhB,CACfC,MAAD,EAA8C,CAC5C,GAAIJ,KAAK,CAACC,OAAN,CAAcG,MAAd,CAAJ,CAA2B,CACzB,KAAM,CAACC,IAAD,CAAOC,aAAP,EAAwBF,MAA9B,CACA,MAAO,CAACG,OAAO,CAACC,OAAR,CAAgBH,IAAhB,CAAD,CAAwBC,aAAxB,CAAP,CACD,CAED,MAAOC,CAAAA,OAAO,CAACC,OAAR,CAAgBJ,MAAhB,CAAP,CACD,CARe,CAAlB,CAWA,MAAOL,CAAAA,OAAP,CACD,CAkBD;AACA,QAASU,CAAAA,iBAAT,CAA2BC,MAA3B,CAAiD,CAC/C,MAAO,CAAC,EAACA,MAAD,QAACA,MAAM,CAAED,iBAAT,CAAR,CACD,C,aAEc,CACbE,GADa,CAEbZ,OAA+B,CAAG,EAFrB,GAGG,gDAChB,KAAMa,CAAAA,WAAW,CAAGD,GAAG,CAACD,MAAJ,CAAWD,iBAAX,CAApB,CACA,KAAMI,CAAAA,QAAQ,CAAGF,GAAG,CAACD,MAAJ,CAAYA,MAAD,EAAiB,CAAC,CAACA,MAAF,EAAYA,MAAM,CAACG,QAA/C,CAAjB,CACA,KAAMC,CAAAA,mBAAmB,CAAGH,GAAG,CAACD,MAAJ,CAAYA,MAAD,EAAiBA,MAAjB,cAAiBA,MAAM,CAAEK,KAApC,CAA5B,CAEA;AACA,KAAMC,CAAAA,MAAM,CAAGF,mBAAmB,EAAI,IAAvB,EAA+BrB,gBAA9C,CAEA;AACA,KAAMwB,CAAAA,aAAa,CACjBH,mBAAmB,GAAK,IAAxB,EACCA,mBAAmB,EAAI,IAAvB,EAA+BjB,uBAFlC,CAIA;AACA,KAAMqB,CAAAA,YAAY,CAAG,EAAEF,MAAM,EAAIC,aAAZ,CAArB,CAEA,KAAME,CAAAA,aAAa,CAAGR,GAAG,CAACD,MAAJ,CACnBA,MAAD,EACE,CAAC,CAACA,MAAF,GACCA,MAAM,CAACL,IAAP,GAAgB,cAAhB,EACCK,MAAM,CAACL,IAAP,GAAgB,yBAFlB,CAFkB,CAAtB,CAOA,KAAMe,CAAAA,aAAa,CACjB,uBAAArB,OAAO,CAAC,cAAD,CAAP,oCAAyBsB,OAAzB,IAAqC,WAArC,EACCC,OAAO,CAACX,GAAG,CAACD,MAAJ,CAAYA,MAAD,EAAiB,CAAC,CAACA,MAAF,EAAYA,MAAM,CAACa,aAA/C,CAAD,CAAP,EACC,wBAAAxB,OAAO,CAAC,cAAD,CAAP,qCAAyBsB,OAAzB,IAAqC,SAHzC,CAKA,KAAMG,CAAAA,eAAe,CAAG,CACtB;AACA;AACAC,OAAO,CAAE,MAHa,CAItBC,OAAO,CAAE,CAAC,yBAAD,CAJa,CAKtBC,OAAO,CAAE,CACP,0CADO,CAEP,oDAFO,CALa,CAStB,GAAG5B,OAAO,CAAC,YAAD,CATY,CAAxB,CAYA;AACA;AACA,GACE,CAACc,QAAQ,EAAIG,MAAb,IACC,CAACQ,eAAe,CAACI,OAAjB,EACC,EACE,MAAOJ,CAAAA,eAAe,CAACI,OAAvB,GAAmC,QAAnC,EACA,QAAUJ,CAAAA,eAAe,CAACI,OAF5B,CAFF,CADF,CAOE,CACAJ,eAAe,CAACI,OAAhB,CAA0B,CACxB;AACA;AACAC,IAAI,CAAE,SAHkB,CAA1B,CAKD,CAED,MAAO,CACLC,UAAU,CAAE,aADP,CAELC,OAAO,CAAE,CACP,CAACxB,OAAO,CAAC,qCAAD,CAAR,CAAiDiB,eAAjD,CADO,CAEP,CACEjB,OAAO,CAAC,uCAAD,CADT,CAEE,CACE;AACA;AACAyB,WAAW,CAAEf,aAAa,EAAID,MAHhC,CAIE,IAAII,aAAa,CAAG,CAAEC,OAAO,CAAE,WAAX,CAAH,CAA8B,CAAEY,MAAM,CAAE,OAAV,CAA/C,CAJF,CAKE,GAAGlC,OAAO,CAAC,cAAD,CALZ,CAFF,CAFO,CAYP,CACEQ,OAAO,CAAC,4CAAD,CADT,CAEE,CAAE2B,eAAe,CAAE,IAAnB,CAAyB,GAAGnC,OAAO,CAAC,mBAAD,CAAnC,CAFF,CAZO,CAFJ,CAmBLG,OAAO,CAAE,CACP,CAACkB,aAAD,EAAkB,CAChBb,OAAO,CAAC,sBAAD,CADS,CAEhB,CACE;AACA;AACA;AACA4B,MAAM,CAAE,OAJV,CAKEC,QAAQ,CAAE,OALZ,CAMEH,MAAM,CAAE,OANV,CAOEI,QAAQ,CAAE,eAPZ,CAFgB,CADX,CAaP,CACE9B,OAAO,CAAC,uCAAD,CADT,CAEE,CACE;AACA+B,GAAG,CAAE,IAFP,CAFF,CAbO,CAoBP/B,OAAO,CAAC,uDAAD,CApBA,CAqBPA,OAAO,CAAC,iCAAD,CArBA,CAsBP,CACEA,OAAO,CAAC,2DAAD,CADT,CAEER,OAAO,CAAC,kBAAD,CAAP,EAA+B,EAFjC,CAtBO,CA0BP,CACEQ,OAAO,CAAC,6DAAD,CADT,CAEE,CACEgC,WAAW,CAAE,IADf,CAFF,CA1BO,CAgCP,CAAC1B,QAAD,EAAa,CACXN,OAAO,CAAC,mDAAD,CADI,CAEX,CACEiC,MAAM,CAAE,KADV,CAEEC,OAAO,CAAE,IAFX,CAGEC,WAAW,CAAE,IAHf,CAIEC,YAAY,CAAE/B,WAAW,EAAIY,eAAe,CAACC,OAAhB,GAA4B,UAJ3D,CAKEmB,eAAe,CAAEzB,aAAa,CAC1B,kBAAQZ,OAAO,CAACC,OAAR,CAAgB,6BAAhB,CAAR,CAD0B,CAE1BqC,SAPN,CAQE,GAAG9C,OAAO,CAAC,mBAAD,CARZ,CAFW,CAhCN,CA6CP,CACEiB,MAAM,EAAIjB,OAAO,CAAC,YAAD,CAAjB,EAAmCA,OAAO,CAAC,YAAD,CAAP,CAAsB,YAAtB,CAAnC,CACIQ,OAAO,CAAC,uBAAD,CADX,CAEIA,OAAO,CAAC,kBAAD,CAHb,CAIET,gBAAgB,CAACC,OAAO,CAAC,YAAD,CAAR,CAJlB,CA7CO,CAmDPQ,OAAO,CAAC,0BAAD,CAnDA,CAoDPW,YAAY,EAAI,CACdX,OAAO,CAAC,mEAAD,CADO,CAEd,CACEuC,YAAY,CAAE,IADhB,CAFc,CApDT,CA0DPjC,QAAQ,EAAIN,OAAO,CAAC,+CAAD,CA1DZ,CA2DP;AACA;AACAA,OAAO,CAAC,4DAAD,CA7DA,CA8DPA,OAAO,CAAC,gEAAD,CA9DA,EA+DPwC,MA/DO,CA+DAzB,OA/DA,CAnBJ,CAAP,CAoFD,C", "sourcesContent": ["import { PluginItem } from 'next/dist/compiled/babel/core'\nimport { dirname } from 'path'\n\nconst isLoadIntentTest = process.env.NODE_ENV === 'test'\nconst isLoadIntentDevelopment = process.env.NODE_ENV === 'development'\n\ntype StyledJsxPlugin = [string, any] | string\ntype StyledJsxBabelOptions =\n  | {\n      plugins?: StyledJsxPlugin[]\n      'babel-test'?: boolean\n    }\n  | undefined\n\n// Resolve styled-jsx plugins\nfunction styledJsxOptions(options: StyledJsxBabelOptions) {\n  if (!options) {\n    return {}\n  }\n\n  if (!Array.isArray(options.plugins)) {\n    return options\n  }\n\n  options.plugins = options.plugins.map(\n    (plugin: StyledJsxPlugin): StyledJsxPlugin => {\n      if (Array.isArray(plugin)) {\n        const [name, pluginOptions] = plugin\n        return [require.resolve(name), pluginOptions]\n      }\n\n      return require.resolve(plugin)\n    }\n  )\n\n  return options\n}\n\ntype NextBabelPresetOptions = {\n  'preset-env'?: any\n  'preset-react'?: any\n  'class-properties'?: any\n  'transform-runtime'?: any\n  'styled-jsx'?: StyledJsxBabelOptions\n  'preset-typescript'?: any\n}\n\ntype BabelPreset = {\n  presets?: PluginItem[] | null\n  plugins?: PluginItem[] | null\n  sourceType?: 'script' | 'module' | 'unambiguous'\n  overrides?: Array<{ test: RegExp } & Omit<BabelPreset, 'overrides'>>\n}\n\n// Taken from https://github.com/babel/babel/commit/d60c5e1736543a6eac4b549553e107a9ba967051#diff-b4beead8ad9195361b4537601cc22532R158\nfunction supportsStaticESM(caller: any): boolean {\n  return !!caller?.supportsStaticESM\n}\n\nexport default (\n  api: any,\n  options: NextBabelPresetOptions = {}\n): BabelPreset => {\n  const supportsESM = api.caller(supportsStaticESM)\n  const isServer = api.caller((caller: any) => !!caller && caller.isServer)\n  const isCallerDevelopment = api.caller((caller: any) => caller?.isDev)\n\n  // Look at external intent if used without a caller (e.g. via Jest):\n  const isTest = isCallerDevelopment == null && isLoadIntentTest\n\n  // Look at external intent if used without a caller (e.g. Storybook):\n  const isDevelopment =\n    isCallerDevelopment === true ||\n    (isCallerDevelopment == null && isLoadIntentDevelopment)\n\n  // Default to production mode if not `test` nor `development`:\n  const isProduction = !(isTest || isDevelopment)\n\n  const isBabelLoader = api.caller(\n    (caller: any) =>\n      !!caller &&\n      (caller.name === 'babel-loader' ||\n        caller.name === 'next-babel-turbo-loader')\n  )\n\n  const useJsxRuntime =\n    options['preset-react']?.runtime === 'automatic' ||\n    (Boolean(api.caller((caller: any) => !!caller && caller.hasJsxRuntime)) &&\n      options['preset-react']?.runtime !== 'classic')\n\n  const presetEnvConfig = {\n    // In the test environment `modules` is often needed to be set to true, babel figures that out by itself using the `'auto'` option\n    // In production/development this option is set to `false` so that webpack can handle import/export with tree-shaking\n    modules: 'auto',\n    exclude: ['transform-typeof-symbol'],\n    include: [\n      '@babel/plugin-proposal-optional-chaining',\n      '@babel/plugin-proposal-nullish-coalescing-operator',\n    ],\n    ...options['preset-env'],\n  }\n\n  // When transpiling for the server or tests, target the current Node version\n  // if not explicitly specified:\n  if (\n    (isServer || isTest) &&\n    (!presetEnvConfig.targets ||\n      !(\n        typeof presetEnvConfig.targets === 'object' &&\n        'node' in presetEnvConfig.targets\n      ))\n  ) {\n    presetEnvConfig.targets = {\n      // Targets the current process' version of Node. This requires apps be\n      // built and deployed on the same version of Node.\n      node: 'current',\n    }\n  }\n\n  return {\n    sourceType: 'unambiguous',\n    presets: [\n      [require('next/dist/compiled/babel/preset-env'), presetEnvConfig],\n      [\n        require('next/dist/compiled/babel/preset-react'),\n        {\n          // This adds @babel/plugin-transform-react-jsx-source and\n          // @babel/plugin-transform-react-jsx-self automatically in development\n          development: isDevelopment || isTest,\n          ...(useJsxRuntime ? { runtime: 'automatic' } : { pragma: '__jsx' }),\n          ...options['preset-react'],\n        },\n      ],\n      [\n        require('next/dist/compiled/babel/preset-typescript'),\n        { allowNamespaces: true, ...options['preset-typescript'] },\n      ],\n    ],\n    plugins: [\n      !useJsxRuntime && [\n        require('./plugins/jsx-pragma'),\n        {\n          // This produces the following injected import for modules containing JSX:\n          //   import React from 'react';\n          //   var __jsx = React.createElement;\n          module: 'react',\n          importAs: 'React',\n          pragma: '__jsx',\n          property: 'createElement',\n        },\n      ],\n      [\n        require('./plugins/optimize-hook-destructuring'),\n        {\n          // only optimize hook functions imported from React/Preact\n          lib: true,\n        },\n      ],\n      require('next/dist/compiled/babel/plugin-syntax-dynamic-import'),\n      require('./plugins/react-loadable-plugin'),\n      [\n        require('next/dist/compiled/babel/plugin-proposal-class-properties'),\n        options['class-properties'] || {},\n      ],\n      [\n        require('next/dist/compiled/babel/plugin-proposal-object-rest-spread'),\n        {\n          useBuiltIns: true,\n        },\n      ],\n      !isServer && [\n        require('next/dist/compiled/babel/plugin-transform-runtime'),\n        {\n          corejs: false,\n          helpers: true,\n          regenerator: true,\n          useESModules: supportsESM && presetEnvConfig.modules !== 'commonjs',\n          absoluteRuntime: isBabelLoader\n            ? dirname(require.resolve('@babel/runtime/package.json'))\n            : undefined,\n          ...options['transform-runtime'],\n        },\n      ],\n      [\n        isTest && options['styled-jsx'] && options['styled-jsx']['babel-test']\n          ? require('styled-jsx/babel-test')\n          : require('styled-jsx/babel'),\n        styledJsxOptions(options['styled-jsx']),\n      ],\n      require('./plugins/amp-attributes'),\n      isProduction && [\n        require('next/dist/compiled/babel/plugin-transform-react-remove-prop-types'),\n        {\n          removeImport: true,\n        },\n      ],\n      isServer && require('next/dist/compiled/babel/plugin-syntax-bigint'),\n      // Always compile numeric separator because the resulting number is\n      // smaller.\n      require('next/dist/compiled/babel/plugin-proposal-numeric-separator'),\n      require('next/dist/compiled/babel/plugin-proposal-export-namespace-from'),\n    ].filter(Boolean),\n  }\n}\n"]}