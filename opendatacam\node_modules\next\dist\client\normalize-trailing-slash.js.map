{"version": 3, "sources": ["../../client/normalize-trailing-slash.ts"], "names": ["removePathTrailingSlash", "path", "endsWith", "slice", "normalizePathTrailingSlash", "process", "env", "__NEXT_TRAILING_SLASH", "test"], "mappings": "uIAAA;AACA;AACA,GACO,QAASA,CAAAA,uBAAT,CAAiCC,IAAjC,CAAuD,CAC5D,MAAOA,CAAAA,IAAI,CAACC,QAAL,CAAc,GAAd,GAAsBD,IAAI,GAAK,GAA/B,CAAqCA,IAAI,CAACE,KAAL,CAAW,CAAX,CAAc,CAAC,CAAf,CAArC,CAAyDF,IAAhE,CACD,CAED;AACA;AACA;AACA,GACO,KAAMG,CAAAA,0BAA0B,CAAGC,OAAO,CAACC,GAAR,CAAYC,qBAAZ,CACrCN,IAAD,EAA0B,CACxB,GAAI,cAAcO,IAAd,CAAmBP,IAAnB,CAAJ,CAA8B,CAC5B,MAAOD,CAAAA,uBAAuB,CAACC,IAAD,CAA9B,CACD,CAFD,IAEO,IAAIA,IAAI,CAACC,QAAL,CAAc,GAAd,CAAJ,CAAwB,CAC7B,MAAOD,CAAAA,IAAP,CACD,CAFM,IAEA,CACL,MAAOA,CAAAA,IAAI,CAAG,GAAd,CACD,CACF,CATqC,CAUtCD,uBAVG,C", "sourcesContent": ["/**\n * Removes the trailing slash of a path if there is one. Preserves the root path `/`.\n */\nexport function removePathTrailingSlash(path: string): string {\n  return path.endsWith('/') && path !== '/' ? path.slice(0, -1) : path\n}\n\n/**\n * Normalizes the trailing slash of a path according to the `trailingSlash` option\n * in `next.config.js`.\n */\nexport const normalizePathTrailingSlash = process.env.__NEXT_TRAILING_SLASH\n  ? (path: string): string => {\n      if (/\\.[^/]+\\/?$/.test(path)) {\n        return removePathTrailingSlash(path)\n      } else if (path.endsWith('/')) {\n        return path\n      } else {\n        return path + '/'\n      }\n    }\n  : removePathTrailingSlash\n"]}