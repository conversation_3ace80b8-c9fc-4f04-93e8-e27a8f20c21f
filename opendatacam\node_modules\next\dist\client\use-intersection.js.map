{"version": 3, "sources": ["../../client/use-intersection.tsx"], "names": ["hasIntersectionObserver", "IntersectionObserver", "useIntersection", "rootMargin", "disabled", "isDisabled", "unobserve", "visible", "setVisible", "setRef", "el", "current", "undefined", "tagName", "observe", "isVisible", "idleCallback", "element", "callback", "options", "id", "observer", "elements", "createObserver", "set", "delete", "size", "disconnect", "observers", "Map", "instance", "get", "entries", "for<PERSON>ach", "entry", "target", "isIntersecting", "intersectionRatio"], "mappings": "6EAAA,4BACA,4DAcA,KAAMA,CAAAA,uBAAuB,CAAG,MAAOC,CAAAA,oBAAP,GAAgC,WAAhE,CAEO,QAASC,CAAAA,eAAT,CAA4C,CACjDC,UADiD,CAEjDC,QAFiD,CAA5C,CAGqD,CAC1D,KAAMC,CAAAA,UAAmB,CAAGD,QAAQ,EAAI,CAACJ,uBAAzC,CAEA,KAAMM,CAAAA,SAAS,CAAG,mBAAlB,CACA,KAAM,CAACC,OAAD,CAAUC,UAAV,EAAwB,oBAAS,KAAT,CAA9B,CAEA,KAAMC,CAAAA,MAAM,CAAG,uBACZC,EAAD,EAAkB,CAChB,GAAIJ,SAAS,CAACK,OAAd,CAAuB,CACrBL,SAAS,CAACK,OAAV,GACAL,SAAS,CAACK,OAAV,CAAoBC,SAApB,CACD,CAED,GAAIP,UAAU,EAAIE,OAAlB,CAA2B,OAE3B,GAAIG,EAAE,EAAIA,EAAE,CAACG,OAAb,CAAsB,CACpBP,SAAS,CAACK,OAAV,CAAoBG,OAAO,CACzBJ,EADyB,CAExBK,SAAD,EAAeA,SAAS,EAAIP,UAAU,CAACO,SAAD,CAFb,CAGzB,CAAEZ,UAAF,CAHyB,CAA3B,CAKD,CACF,CAhBY,CAiBb,CAACE,UAAD,CAAaF,UAAb,CAAyBI,OAAzB,CAjBa,CAAf,CAoBA,qBAAU,IAAM,CACd,GAAI,CAACP,uBAAL,CAA8B,CAC5B,GAAI,CAACO,OAAL,CAAc,CACZ,KAAMS,CAAAA,YAAY,CAAG,6CAAoB,IAAMR,UAAU,CAAC,IAAD,CAApC,CAArB,CACA,MAAO,IAAM,4CAAmBQ,YAAnB,CAAb,CACD,CACF,CACF,CAPD,CAOG,CAACT,OAAD,CAPH,EASA,MAAO,CAACE,MAAD,CAASF,OAAT,CAAP,CACD,CAED,QAASO,CAAAA,OAAT,CACEG,OADF,CAEEC,QAFF,CAGEC,OAHF,CAIc,CACZ,KAAM,CAAEC,EAAF,CAAMC,QAAN,CAAgBC,QAAhB,EAA6BC,cAAc,CAACJ,OAAD,CAAjD,CACAG,QAAQ,CAACE,GAAT,CAAaP,OAAb,CAAsBC,QAAtB,EAEAG,QAAQ,CAACP,OAAT,CAAiBG,OAAjB,EACA,MAAO,SAASX,CAAAA,SAAT,EAA2B,CAChCgB,QAAQ,CAACG,MAAT,CAAgBR,OAAhB,EACAI,QAAQ,CAACf,SAAT,CAAmBW,OAAnB,EAEA;AACA,GAAIK,QAAQ,CAACI,IAAT,GAAkB,CAAtB,CAAyB,CACvBL,QAAQ,CAACM,UAAT,GACAC,SAAS,CAACH,MAAV,CAAiBL,EAAjB,EACD,CACF,CATD,CAUD,CAED,KAAMQ,CAAAA,SAAS,CAAG,GAAIC,CAAAA,GAAJ,EAAlB,CACA,QAASN,CAAAA,cAAT,CAAwBJ,OAAxB,CAAwE,CACtE,KAAMC,CAAAA,EAAE,CAAGD,OAAO,CAAChB,UAAR,EAAsB,EAAjC,CACA,GAAI2B,CAAAA,QAAQ,CAAGF,SAAS,CAACG,GAAV,CAAcX,EAAd,CAAf,CACA,GAAIU,QAAJ,CAAc,CACZ,MAAOA,CAAAA,QAAP,CACD,CAED,KAAMR,CAAAA,QAAQ,CAAG,GAAIO,CAAAA,GAAJ,EAAjB,CACA,KAAMR,CAAAA,QAAQ,CAAG,GAAIpB,CAAAA,oBAAJ,CAA0B+B,OAAD,EAAa,CACrDA,OAAO,CAACC,OAAR,CAAiBC,KAAD,EAAW,CACzB,KAAMhB,CAAAA,QAAQ,CAAGI,QAAQ,CAACS,GAAT,CAAaG,KAAK,CAACC,MAAnB,CAAjB,CACA,KAAMpB,CAAAA,SAAS,CAAGmB,KAAK,CAACE,cAAN,EAAwBF,KAAK,CAACG,iBAAN,CAA0B,CAApE,CACA,GAAInB,QAAQ,EAAIH,SAAhB,CAA2B,CACzBG,QAAQ,CAACH,SAAD,CAAR,CACD,CACF,CAND,EAOD,CARgB,CAQdI,OARc,CAAjB,CAUAS,SAAS,CAACJ,GAAV,CACEJ,EADF,CAEGU,QAAQ,CAAG,CACVV,EADU,CAEVC,QAFU,CAGVC,QAHU,CAFd,EAQA,MAAOQ,CAAAA,QAAP,CACD", "sourcesContent": ["import { useCallback, useEffect, useRef, useState } from 'react'\nimport {\n  requestIdleCallback,\n  cancelIdleCallback,\n} from './request-idle-callback'\n\ntype UseIntersectionObserverInit = Pick<IntersectionObserverInit, 'rootMargin'>\ntype UseIntersection = { disabled?: boolean } & UseIntersectionObserverInit\ntype ObserveCallback = (isVisible: boolean) => void\ntype Observer = {\n  id: string\n  observer: IntersectionObserver\n  elements: Map<Element, ObserveCallback>\n}\n\nconst hasIntersectionObserver = typeof IntersectionObserver !== 'undefined'\n\nexport function useIntersection<T extends Element>({\n  rootMargin,\n  disabled,\n}: UseIntersection): [(element: T | null) => void, boolean] {\n  const isDisabled: boolean = disabled || !hasIntersectionObserver\n\n  const unobserve = useRef<Function>()\n  const [visible, setVisible] = useState(false)\n\n  const setRef = useCallback(\n    (el: T | null) => {\n      if (unobserve.current) {\n        unobserve.current()\n        unobserve.current = undefined\n      }\n\n      if (isDisabled || visible) return\n\n      if (el && el.tagName) {\n        unobserve.current = observe(\n          el,\n          (isVisible) => isVisible && setVisible(isVisible),\n          { rootMargin }\n        )\n      }\n    },\n    [isDisabled, rootMargin, visible]\n  )\n\n  useEffect(() => {\n    if (!hasIntersectionObserver) {\n      if (!visible) {\n        const idleCallback = requestIdleCallback(() => setVisible(true))\n        return () => cancelIdleCallback(idleCallback)\n      }\n    }\n  }, [visible])\n\n  return [setRef, visible]\n}\n\nfunction observe(\n  element: Element,\n  callback: ObserveCallback,\n  options: UseIntersectionObserverInit\n): () => void {\n  const { id, observer, elements } = createObserver(options)\n  elements.set(element, callback)\n\n  observer.observe(element)\n  return function unobserve(): void {\n    elements.delete(element)\n    observer.unobserve(element)\n\n    // Destroy observer when there's nothing left to watch:\n    if (elements.size === 0) {\n      observer.disconnect()\n      observers.delete(id)\n    }\n  }\n}\n\nconst observers = new Map<string, Observer>()\nfunction createObserver(options: UseIntersectionObserverInit): Observer {\n  const id = options.rootMargin || ''\n  let instance = observers.get(id)\n  if (instance) {\n    return instance\n  }\n\n  const elements = new Map<Element, ObserveCallback>()\n  const observer = new IntersectionObserver((entries) => {\n    entries.forEach((entry) => {\n      const callback = elements.get(entry.target)\n      const isVisible = entry.isIntersecting || entry.intersectionRatio > 0\n      if (callback && isVisible) {\n        callback(isVisible)\n      }\n    })\n  }, options)\n\n  observers.set(\n    id,\n    (instance = {\n      id,\n      observer,\n      elements,\n    })\n  )\n  return instance\n}\n"]}