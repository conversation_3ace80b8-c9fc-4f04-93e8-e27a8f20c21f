{"version": 3, "sources": ["../../../../build/webpack/plugins/nextjs-ssr-import.ts"], "names": ["NextJsSsrImportPlugin", "apply", "compiler", "hooks", "compilation", "tap", "mainTemplate", "requireEnsure", "code", "chunk", "outputPath", "pagePath", "name", "relativePathToBaseDir", "relativePathToBaseDirNormalized", "replace"], "mappings": "4DAAA,0BAGA;AACA;AACe,KAAMA,CAAAA,qBAAsB,CACzCC,KAAK,CAACC,QAAD,CAA6B,CAChCA,QAAQ,CAACC,KAAT,CAAeC,WAAf,CAA2BC,GAA3B,CAA+B,iBAA/B,CAAmDD,WAAD,EAAsB,CACtEA,WAAW,CAACE,YAAZ,CAAyBH,KAAzB,CAA+BI,aAA/B,CAA6CF,GAA7C,CACE,iBADF,CAEE,CAACG,IAAD,CAAeC,KAAf,GAA8B,CAC5B;AACA,KAAMC,CAAAA,UAAU,CAAG,kBAAQ,GAAR,CAAnB,CACA,KAAMC,CAAAA,QAAQ,CAAG,eAAK,GAAL,CAAU,kBAAQF,KAAK,CAACG,IAAd,CAAV,CAAjB,CACA,KAAMC,CAAAA,qBAAqB,CAAG,mBAASF,QAAT,CAAmBD,UAAnB,CAA9B,CACA;AACA;AACA,KAAMI,CAAAA,+BAA+B,CAAGD,qBAAqB,CAACE,OAAtB,CACtC,KADsC,CAEtC,GAFsC,CAAxC,CAIA,MAAOP,CAAAA,IAAI,CACRO,OADI,CAEH,cAFG,CAGF,YAAWD,+BAAgC,IAHzC,EAKJC,OALI,CAMH,yBANG,CAOF,6BAA4BD,+BAAgC,GAP1D,CAAP,CASD,CAtBH,EAwBD,CAzBD,EA0BD,CA5BwC,C", "sourcesContent": ["import { join, resolve, relative, dirname } from 'path'\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\n\n// This plugin modifies the require-ensure code generated by Webpack\n// to work with Next.js SSR\nexport default class NextJsSsrImportPlugin {\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.compilation.tap('NextJsSSRImport', (compilation: any) => {\n      compilation.mainTemplate.hooks.requireEnsure.tap(\n        'NextJsSSRImport',\n        (code: string, chunk: any) => {\n          // Update to load chunks from our custom chunks directory\n          const outputPath = resolve('/')\n          const pagePath = join('/', dirname(chunk.name))\n          const relativePathToBaseDir = relative(pagePath, outputPath)\n          // Make sure even in windows, the path looks like in unix\n          // Node.js require system will convert it accordingly\n          const relativePathToBaseDirNormalized = relativePathToBaseDir.replace(\n            /\\\\/g,\n            '/'\n          )\n          return code\n            .replace(\n              'require(\"./\"',\n              `require(\"${relativePathToBaseDirNormalized}/\"`\n            )\n            .replace(\n              'readFile(join(__dirname',\n              `readFile(join(__dirname, \"${relativePathToBaseDirNormalized}\"`\n            )\n        }\n      )\n    })\n  }\n}\n"]}