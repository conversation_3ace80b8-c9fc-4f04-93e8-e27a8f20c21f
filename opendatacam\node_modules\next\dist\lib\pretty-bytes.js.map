{"version": 3, "sources": ["../../lib/pretty-bytes.ts"], "names": ["UNITS", "toLocaleString", "number", "locale", "result", "prettyBytes", "options", "Number", "isFinite", "TypeError", "Object", "assign", "signed", "isNegative", "prefix", "numberString", "exponent", "Math", "min", "floor", "log10", "length", "pow", "toPrecision", "unit"], "mappings": "iEAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAEA,KAAMA,CAAAA,KAAK,CAAG,CAAC,GAAD,CAAM,IAAN,CAAY,IAAZ,CAAkB,IAAlB,CAAwB,IAAxB,CAA8B,IAA9B,CAAoC,IAApC,CAA0C,IAA1C,CAAgD,IAAhD,CAAd,CAEA;AACA;AACA;AACA;AACA;AACA,EACA,KAAMC,CAAAA,cAAc,CAAG,CAACC,MAAD,CAAiBC,MAAjB,GAAiC,CACtD,GAAIC,CAAAA,MAAW,CAAGF,MAAlB,CACA,GAAI,MAAOC,CAAAA,MAAP,GAAkB,QAAtB,CAAgC,CAC9BC,MAAM,CAAGF,MAAM,CAACD,cAAP,CAAsBE,MAAtB,CAAT,CACD,CAFD,IAEO,IAAIA,MAAM,GAAK,IAAf,CAAqB,CAC1BC,MAAM,CAAGF,MAAM,CAACD,cAAP,EAAT,CACD,CAED,MAAOG,CAAAA,MAAP,CACD,CATD,CAWe,QAASC,CAAAA,WAAT,CAAqBH,MAArB,CAAqCI,OAArC,CAA4D,CACzE,GAAI,CAACC,MAAM,CAACC,QAAP,CAAgBN,MAAhB,CAAL,CAA8B,CAC5B,KAAM,IAAIO,CAAAA,SAAJ,CACH,iCAAgC,MAAOP,CAAAA,MAAO,KAAIA,MAAO,EADtD,CAAN,CAGD,CAEDI,OAAO,CAAGI,MAAM,CAACC,MAAP,CAAc,EAAd,CAAkBL,OAAlB,CAAV,CAEA,GAAIA,OAAO,CAACM,MAAR,EAAkBV,MAAM,GAAK,CAAjC,CAAoC,CAClC,MAAO,MAAP,CACD,CAED,KAAMW,CAAAA,UAAU,CAAGX,MAAM,CAAG,CAA5B,CACA,KAAMY,CAAAA,MAAM,CAAGD,UAAU,CAAG,GAAH,CAASP,OAAO,CAACM,MAAR,CAAiB,GAAjB,CAAuB,EAAzD,CAEA,GAAIC,UAAJ,CAAgB,CACdX,MAAM,CAAG,CAACA,MAAV,CACD,CAED,GAAIA,MAAM,CAAG,CAAb,CAAgB,CACd,KAAMa,CAAAA,YAAY,CAAGd,cAAc,CAACC,MAAD,CAASI,OAAO,CAACH,MAAjB,CAAnC,CACA,MAAOW,CAAAA,MAAM,CAAGC,YAAT,CAAwB,IAA/B,CACD,CAED,KAAMC,CAAAA,QAAQ,CAAGC,IAAI,CAACC,GAAL,CACfD,IAAI,CAACE,KAAL,CAAWF,IAAI,CAACG,KAAL,CAAWlB,MAAX,EAAqB,CAAhC,CADe,CAEfF,KAAK,CAACqB,MAAN,CAAe,CAFA,CAAjB,CAKAnB,MAAM,CAAGK,MAAM,CAAC,CAACL,MAAM,CAAGe,IAAI,CAACK,GAAL,CAAS,IAAT,CAAeN,QAAf,CAAV,EAAoCO,WAApC,CAAgD,CAAhD,CAAD,CAAf,CACA,KAAMR,CAAAA,YAAY,CAAGd,cAAc,CAACC,MAAD,CAASI,OAAO,CAACH,MAAjB,CAAnC,CAEA,KAAMqB,CAAAA,IAAI,CAAGxB,KAAK,CAACgB,QAAD,CAAlB,CAEA,MAAOF,CAAAA,MAAM,CAAGC,YAAT,CAAwB,GAAxB,CAA8BS,IAArC,CACD", "sourcesContent": ["/*\nMIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/\n\nconst UNITS = ['B', 'kB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']\n\n/*\nFormats the given number using `Number#toLocaleString`.\n- If locale is a string, the value is expected to be a locale-key (for example: `de`).\n- If locale is true, the system default locale is used for translation.\n- If no value for locale is specified, the number is returned unmodified.\n*/\nconst toLocaleString = (number: number, locale: any) => {\n  let result: any = number\n  if (typeof locale === 'string') {\n    result = number.toLocaleString(locale)\n  } else if (locale === true) {\n    result = number.toLocaleString()\n  }\n\n  return result\n}\n\nexport default function prettyBytes(number: number, options?: any): string {\n  if (!Number.isFinite(number)) {\n    throw new TypeError(\n      `Expected a finite number, got ${typeof number}: ${number}`\n    )\n  }\n\n  options = Object.assign({}, options)\n\n  if (options.signed && number === 0) {\n    return ' 0 B'\n  }\n\n  const isNegative = number < 0\n  const prefix = isNegative ? '-' : options.signed ? '+' : ''\n\n  if (isNegative) {\n    number = -number\n  }\n\n  if (number < 1) {\n    const numberString = toLocaleString(number, options.locale)\n    return prefix + numberString + ' B'\n  }\n\n  const exponent = Math.min(\n    Math.floor(Math.log10(number) / 3),\n    UNITS.length - 1\n  )\n\n  number = Number((number / Math.pow(1000, exponent)).toPrecision(3))\n  const numberString = toLocaleString(number, options.locale)\n\n  const unit = UNITS[exponent]\n\n  return prefix + numberString + ' ' + unit\n}\n"]}