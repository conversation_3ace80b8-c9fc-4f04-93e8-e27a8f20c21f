{"version": 3, "sources": ["../../../../build/webpack/plugins/react-loadable-plugin.ts"], "names": ["getModuleId", "compilation", "module", "isWebpack5", "chunkGraph", "id", "getModuleFromDependency", "dep", "moduleGraph", "getModule", "getOriginModuleFromDependency", "getParentModule", "originModule", "getChunkGroupFromBlock", "block", "getBlockChunkGroup", "chunkGroup", "buildManifest", "_compiler", "pagesDir", "manifest", "handleBlock", "blocks", "for<PERSON>ach", "dependency", "dependencies", "type", "startsWith", "originRequest", "resource", "key", "path", "relative", "request", "files", "Set", "file", "add", "chunk", "chunks", "endsWith", "match", "Array", "from", "modules", "Object", "keys", "sort", "reduce", "a", "c", "ReactLoadablePlugin", "constructor", "opts", "filename", "createAssets", "compiler", "assets", "sources", "RawSource", "JSON", "stringify", "apply", "hooks", "make", "tap", "processAssets", "name", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS", "emit"], "mappings": "wEAuBA,2DAMA,kD,mFA7BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,E,CACA;AACA;AAUA,QAASA,CAAAA,WAAT,CAAqBC,WAArB,CAAuCC,MAAvC,CAAqE,CACnE,GAAIC,mBAAJ,CAAgB,CACd,MAAOF,CAAAA,WAAW,CAACG,UAAZ,CAAuBJ,WAAvB,CAAmCE,MAAnC,CAAP,CACD,CAED,MAAOA,CAAAA,MAAM,CAACG,EAAd,CACD,CAED,QAASC,CAAAA,uBAAT,CACEL,WADF,CAEEM,GAFF,CAG0C,CACxC,GAAIJ,mBAAJ,CAAgB,CACd,MAAOF,CAAAA,WAAW,CAACO,WAAZ,CAAwBC,SAAxB,CAAkCF,GAAlC,CAAP,CACD,CAED,MAAOA,CAAAA,GAAG,CAACL,MAAX,CACD,CAED,QAASQ,CAAAA,6BAAT,CACET,WADF,CAEEM,GAFF,CAG0C,CACxC,GAAIJ,mBAAJ,CAAgB,CACd,MAAOF,CAAAA,WAAW,CAACO,WAAZ,CAAwBG,eAAxB,CAAwCJ,GAAxC,CAAP,CACD,CAED,MAAOA,CAAAA,GAAG,CAACK,YAAX,CACD,CAED,QAASC,CAAAA,sBAAT,CACEZ,WADF,CAEEa,KAFF,CAGkC,CAChC,GAAIX,mBAAJ,CAAgB,CACd,MAAOF,CAAAA,WAAW,CAACG,UAAZ,CAAuBW,kBAAvB,CAA0CD,KAA1C,CAAP,CACD,CAED,MAAOA,CAAAA,KAAK,CAACE,UAAb,CACD,CAED,QAASC,CAAAA,aAAT,CACEC,SADF,CAEEjB,WAFF,CAGEkB,QAHF,CAIE,CACA,GAAIC,CAAAA,QAAmE,CAAG,EAA1E,CAEA;AACA;AAEA;AACA;AACA;AAEA;AACA,KAAMC,CAAAA,WAAW,CAAIP,KAAD,EAAgB,CAClCA,KAAK,CAACQ,MAAN,CAAaC,OAAb,CAAqBF,WAArB,EACA,KAAML,CAAAA,UAAU,CAAGH,sBAAsB,CAACZ,WAAD,CAAca,KAAd,CAAzC,CACA,IAAK,KAAMU,CAAAA,UAAX,GAAyBV,CAAAA,KAAK,CAACW,YAA/B,CAA6C,CAC3C,GAAID,UAAU,CAACE,IAAX,CAAgBC,UAAhB,CAA2B,UAA3B,CAAJ,CAA4C,CAC1C;AACA,KAAMzB,CAAAA,MAAM,CAAGI,uBAAuB,CAACL,WAAD,CAAcuB,UAAd,CAAtC,CACA,GAAI,CAACtB,MAAL,CAAa,OAEb;AACA,KAAMU,CAAAA,YAAY,CAAGF,6BAA6B,CAChDT,WADgD,CAEhDuB,UAFgD,CAAlD,CAIA,KAAMI,CAAAA,aAAiC,CAAGhB,YAAH,cAAGA,YAAY,CAAEiB,QAAxD,CACA,GAAI,CAACD,aAAL,CAAoB,OAEpB;AACA;AACA;AACA,KAAME,CAAAA,GAAG,CAAI,GAAEC,cAAKC,QAAL,CAAcb,QAAd,CAAwBS,aAAxB,CAAuC,OACpDJ,UAAU,CAACS,OACZ,EAFD,CAIA;AACA,KAAMC,CAAAA,KAAK,CAAG,GAAIC,CAAAA,GAAJ,EAAd,CAEA,GAAIf,QAAQ,CAACU,GAAD,CAAZ,CAAmB,CACjB;AACA;AACA;AACA;AACA;AACA,IAAK,KAAMM,CAAAA,IAAX,GAAmBhB,CAAAA,QAAQ,CAACU,GAAD,CAAR,CAAcI,KAAjC,CAAwC,CACtCA,KAAK,CAACG,GAAN,CAAUD,IAAV,EACD,CACF,CAED;AACA;AACA;AACA,GAAIpB,UAAJ,CAAgB,CACd,IAAK,KAAMsB,CAAAA,KAAX,GAAqBtB,CAAAA,UAAD,CACjBuB,MADH,CAC0C,CACxCD,KAAK,CAACJ,KAAN,CAAYX,OAAZ,CAAqBa,IAAD,EAAkB,CACpC,GACE,CAACA,IAAI,CAACI,QAAL,CAAc,KAAd,GAAwBJ,IAAI,CAACI,QAAL,CAAc,MAAd,CAAzB,GACAJ,IAAI,CAACK,KAAL,CAAW,yBAAX,CAFF,CAGE,CACAP,KAAK,CAACG,GAAN,CAAUD,IAAV,EACD,CACF,CAPD,EAQD,CACF,CAED;AACA;AACA;AAEA;AACA,KAAM/B,CAAAA,EAAE,CAAGL,WAAW,CAACC,WAAD,CAAcC,MAAd,CAAtB,CACAkB,QAAQ,CAACU,GAAD,CAAR,CAAgB,CAAEzB,EAAF,CAAM6B,KAAK,CAAEQ,KAAK,CAACC,IAAN,CAAWT,KAAX,CAAb,CAAhB,CACD,CACF,CACF,CAhED,CAiEA,IAAK,KAAMhC,CAAAA,MAAX,GAAqBD,CAAAA,WAAW,CAAC2C,OAAjC,CAA0C,CACxC1C,MAAM,CAACoB,MAAP,CAAcC,OAAd,CAAsBF,WAAtB,EACD,CAEDD,QAAQ,CAAGyB,MAAM,CAACC,IAAP,CAAY1B,QAAZ,EACR2B,IADQ,EAET;AAFS,CAGRC,MAHQ,CAGD,CAACC,CAAD,CAAIC,CAAJ,IAAYD,CAAC,CAACC,CAAD,CAAD,CAAO9B,QAAQ,CAAC8B,CAAD,CAAhB,CAAsBD,CAAjC,CAHC,CAGoC,EAHpC,CAAX,CAKA,MAAO7B,CAAAA,QAAP,CACD,CAEM,KAAM+B,CAAAA,mBAAoB,CAI/BC,WAAW,CAACC,IAAD,CAA+C,MAHlDC,QAGkD,aAFlDnC,QAEkD,QACxD,KAAKmC,QAAL,CAAgBD,IAAI,CAACC,QAArB,CACA,KAAKnC,QAAL,CAAgBkC,IAAI,CAAClC,QAArB,CACD,CAEDoC,YAAY,CAACC,QAAD,CAAgBvD,WAAhB,CAAkCwD,MAAlC,CAA+C,CACzD,KAAMrC,CAAAA,QAAQ,CAAGH,aAAa,CAACuC,QAAD,CAAWvD,WAAX,CAAwB,KAAKkB,QAA7B,CAA9B,CACA;AACAsC,MAAM,CAAC,KAAKH,QAAN,CAAN,CAAwB,GAAII,kBAAQC,SAAZ,CACtBC,IAAI,CAACC,SAAL,CAAezC,QAAf,CAAyB,IAAzB,CAA+B,CAA/B,CADsB,CAAxB,CAGA,MAAOqC,CAAAA,MAAP,CACD,CAEDK,KAAK,CAACN,QAAD,CAA6B,CAChC,GAAIrD,mBAAJ,CAAgB,CACdqD,QAAQ,CAACO,KAAT,CAAeC,IAAf,CAAoBC,GAApB,CAAwB,uBAAxB,CAAkDhE,WAAD,EAAiB,CAChE;AACAA,WAAW,CAAC8D,KAAZ,CAAkBG,aAAlB,CAAgCD,GAAhC,CACE,CACEE,IAAI,CAAE,uBADR,CAEE;AACAC,KAAK,CAAEC,iBAAQC,WAAR,CAAoBC,8BAH7B,CADF,CAMGd,MAAD,EAAiB,CACf,KAAKF,YAAL,CAAkBC,QAAlB,CAA4BvD,WAA5B,CAAyCwD,MAAzC,EACD,CARH,EAUD,CAZD,EAaA,OACD,CAEDD,QAAQ,CAACO,KAAT,CAAeS,IAAf,CAAoBP,GAApB,CAAwB,uBAAxB,CAAkDhE,WAAD,EAAsB,CACrE,KAAKsD,YAAL,CAAkBC,QAAlB,CAA4BvD,WAA5B,CAAyCA,WAAW,CAACwD,MAArD,EACD,CAFD,EAGD,CAvC8B,C", "sourcesContent": ["/**\nCOPYRIGHT (c) 2017-present <PERSON> <<EMAIL>>\n MIT License\n Permission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n The above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWAR\n*/\n// Implementation of this PR: https://github.com/jamiebuilds/react-loadable/pull/132\n// Modified to strip out unneeded results for Next's specific use case\n\nimport {\n  webpack,\n  isWebpack5,\n  sources,\n} from 'next/dist/compiled/webpack/webpack'\n\nimport path from 'path'\n\nfunction getModuleId(compilation: any, module: any): string | number {\n  if (isWebpack5) {\n    return compilation.chunkGraph.getModuleId(module)\n  }\n\n  return module.id\n}\n\nfunction getModuleFromDependency(\n  compilation: any,\n  dep: any\n): webpack.Module & { resource?: string } {\n  if (isWebpack5) {\n    return compilation.moduleGraph.getModule(dep)\n  }\n\n  return dep.module\n}\n\nfunction getOriginModuleFromDependency(\n  compilation: any,\n  dep: any\n): webpack.Module & { resource?: string } {\n  if (isWebpack5) {\n    return compilation.moduleGraph.getParentModule(dep)\n  }\n\n  return dep.originModule\n}\n\nfunction getChunkGroupFromBlock(\n  compilation: any,\n  block: any\n): webpack.compilation.ChunkGroup {\n  if (isWebpack5) {\n    return compilation.chunkGraph.getBlockChunkGroup(block)\n  }\n\n  return block.chunkGroup\n}\n\nfunction buildManifest(\n  _compiler: webpack.Compiler,\n  compilation: webpack.compilation.Compilation,\n  pagesDir: string\n) {\n  let manifest: { [k: string]: { id: string | number; files: string[] } } = {}\n\n  // This is allowed:\n  // import(\"./module\"); <- ImportDependency\n\n  // We don't support that:\n  // import(/* webpackMode: \"eager\" */ \"./module\") <- ImportEagerDependency\n  // import(`./module/${param}`) <- ImportContextDependency\n\n  // Find all dependencies blocks which contains a `import()` dependency\n  const handleBlock = (block: any) => {\n    block.blocks.forEach(handleBlock)\n    const chunkGroup = getChunkGroupFromBlock(compilation, block)\n    for (const dependency of block.dependencies) {\n      if (dependency.type.startsWith('import()')) {\n        // get the referenced module\n        const module = getModuleFromDependency(compilation, dependency)\n        if (!module) return\n\n        // get the module containing the import()\n        const originModule = getOriginModuleFromDependency(\n          compilation,\n          dependency\n        )\n        const originRequest: string | undefined = originModule?.resource\n        if (!originRequest) return\n\n        // We construct a \"unique\" key from origin module and request\n        // It's not perfect unique, but that will be fine for us.\n        // We also need to construct the same in the babel plugin.\n        const key = `${path.relative(pagesDir, originRequest)} -> ${\n          dependency.request\n        }`\n\n        // Capture all files that need to be loaded.\n        const files = new Set<string>()\n\n        if (manifest[key]) {\n          // In the \"rare\" case where multiple chunk groups\n          // are created for the same `import()` or multiple\n          // import()s reference the same module, we merge\n          // the files to make sure to not miss files\n          // This may cause overfetching in edge cases.\n          for (const file of manifest[key].files) {\n            files.add(file)\n          }\n        }\n\n        // There might not be a chunk group when all modules\n        // are already loaded. In this case we only need need\n        // the module id and no files\n        if (chunkGroup) {\n          for (const chunk of (chunkGroup as any)\n            .chunks as webpack.compilation.Chunk[]) {\n            chunk.files.forEach((file: string) => {\n              if (\n                (file.endsWith('.js') || file.endsWith('.css')) &&\n                file.match(/^static\\/(chunks|css)\\//)\n              ) {\n                files.add(file)\n              }\n            })\n          }\n        }\n\n        // usually we have to add the parent chunk groups too\n        // but we assume that all parents are also imported by\n        // next/dynamic so they are loaded by the same technique\n\n        // add the id and files to the manifest\n        const id = getModuleId(compilation, module)\n        manifest[key] = { id, files: Array.from(files) }\n      }\n    }\n  }\n  for (const module of compilation.modules) {\n    module.blocks.forEach(handleBlock)\n  }\n\n  manifest = Object.keys(manifest)\n    .sort()\n    // eslint-disable-next-line no-sequences\n    .reduce((a, c) => ((a[c] = manifest[c]), a), {} as any)\n\n  return manifest\n}\n\nexport class ReactLoadablePlugin {\n  private filename: string\n  private pagesDir: string\n\n  constructor(opts: { filename: string; pagesDir: string }) {\n    this.filename = opts.filename\n    this.pagesDir = opts.pagesDir\n  }\n\n  createAssets(compiler: any, compilation: any, assets: any) {\n    const manifest = buildManifest(compiler, compilation, this.pagesDir)\n    // @ts-ignore: TODO: remove when webpack 5 is stable\n    assets[this.filename] = new sources.RawSource(\n      JSON.stringify(manifest, null, 2)\n    )\n    return assets\n  }\n\n  apply(compiler: webpack.Compiler) {\n    if (isWebpack5) {\n      compiler.hooks.make.tap('ReactLoadableManifest', (compilation) => {\n        // @ts-ignore TODO: Remove ignore when webpack 5 is stable\n        compilation.hooks.processAssets.tap(\n          {\n            name: 'ReactLoadableManifest',\n            // @ts-ignore TODO: Remove ignore when webpack 5 is stable\n            stage: webpack.Compilation.PROCESS_ASSETS_STAGE_ADDITIONS,\n          },\n          (assets: any) => {\n            this.createAssets(compiler, compilation, assets)\n          }\n        )\n      })\n      return\n    }\n\n    compiler.hooks.emit.tap('ReactLoadableManifest', (compilation: any) => {\n      this.createAssets(compiler, compilation, compilation.assets)\n    })\n  }\n}\n"]}