{"version": 3, "sources": ["../../../../../build/webpack/plugins/wellknown-errors-plugin/parseBabel.ts"], "names": ["chalk", "Chalk", "constructor", "enabled", "getBabelError", "fileName", "err", "code", "loc", "lineNumber", "Math", "max", "line", "column", "message", "replace", "RegExp", "SimpleWebpackError", "cyan", "yellow", "toString", "red", "bold", "concat"], "mappings": "yEAAA,oDACA,wD,mFAEA,KAAMA,CAAAA,KAAK,CAAG,GAAIC,gBAAMC,WAAV,CAAsB,CAAEC,OAAO,CAAE,IAAX,CAAtB,CAAd,CAEO,QAASC,CAAAA,aAAT,CACLC,QADK,CAELC,GAFK,CAMuB,CAC5B,GAAIA,GAAG,CAACC,IAAJ,GAAa,mBAAjB,CAAsC,CACpC,MAAO,MAAP,CACD,CAED;AACA,GAAID,GAAG,CAACE,GAAR,CAAa,CACX,KAAMC,CAAAA,UAAU,CAAGC,IAAI,CAACC,GAAL,CAAS,CAAT,CAAYL,GAAG,CAACE,GAAJ,CAAQI,IAApB,CAAnB,CACA,KAAMC,CAAAA,MAAM,CAAGH,IAAI,CAACC,GAAL,CAAS,CAAT,CAAYL,GAAG,CAACE,GAAJ,CAAQK,MAApB,CAAf,CAEA,GAAIC,CAAAA,OAAO,CAAGR,GAAG,CAACQ,OAChB;AADY,CAEXC,OAFW,CAEH,QAFG,CAEO,EAFP,CAGZ;AAHY,CAIXA,OAJW,CAKV,GAAIC,CAAAA,MAAJ,CAAY,mBAAkBP,UAAW,IAAGI,MAAO,kBAAnD,CALU,CAMV,EANU,CAAd,CASA,MAAO,IAAII,uCAAJ,CACJ,GAAEjB,KAAK,CAACkB,IAAN,CAAWb,QAAX,CAAqB,IAAGL,KAAK,CAACmB,MAAN,CACzBV,UAAU,CAACW,QAAX,EADyB,CAEzB,IAAGpB,KAAK,CAACmB,MAAN,CAAaN,MAAM,CAACO,QAAP,EAAb,CAAgC,EAHhC,CAILpB,KAAK,CAACqB,GAAN,CAAUC,IAAV,CAAe,cAAf,EAA+BC,MAA/B,CAAuC,KAAIT,OAAQ,EAAnD,CAJK,CAAP,CAMD,CAED,MAAO,MAAP,CACD", "sourcesContent": ["import Chalk from 'chalk'\nimport { SimpleWebpackError } from './simpleWebpackError'\n\nconst chalk = new Chalk.constructor({ enabled: true })\n\nexport function getBabelError(\n  fileName: string,\n  err: Error & {\n    code?: 'BABEL_PARSE_ERROR'\n    loc?: { line: number; column: number }\n  }\n): SimpleWebpackError | false {\n  if (err.code !== 'BABEL_PARSE_ERROR') {\n    return false\n  }\n\n  // https://github.com/babel/babel/blob/34693d6024da3f026534dd8d569f97ac0109602e/packages/babel-core/src/parser/index.js\n  if (err.loc) {\n    const lineNumber = Math.max(1, err.loc.line)\n    const column = Math.max(1, err.loc.column)\n\n    let message = err.message\n      // Remove file information, which instead is provided by webpack.\n      .replace(/^.+?: /, '')\n      // Remove column information from message\n      .replace(\n        new RegExp(`[^\\\\S\\\\r\\\\n]*\\\\(${lineNumber}:${column}\\\\)[^\\\\S\\\\r\\\\n]*`),\n        ''\n      )\n\n    return new SimpleWebpackError(\n      `${chalk.cyan(fileName)}:${chalk.yellow(\n        lineNumber.toString()\n      )}:${chalk.yellow(column.toString())}`,\n      chalk.red.bold('Syntax error').concat(`: ${message}`)\n    )\n  }\n\n  return false\n}\n"]}