{"version": 3, "sources": ["../../../../../build/webpack/plugins/wellknown-errors-plugin/parseScss.ts"], "names": ["chalk", "Chalk", "constructor", "enabled", "regexScssError", "getScssError", "fileName", "fileContent", "err", "name", "res", "exec", "message", "reason", "_lineNumer", "<PERSON><PERSON><PERSON><PERSON>", "columnString", "lineNumber", "Math", "max", "parseInt", "column", "length", "frame", "start", "line", "forceColor", "SimpleWebpackError", "cyan", "yellow", "toString", "red", "bold", "concat"], "mappings": "uEAAA,8DACA,oDACA,wD,mFAEA,KAAMA,CAAAA,KAAK,CAAG,GAAIC,gBAAMC,WAAV,CAAsB,CAAEC,OAAO,CAAE,IAAX,CAAtB,CAAd,CACA,KAAMC,CAAAA,cAAc,CAAG,gEAAvB,CAEO,QAASC,CAAAA,YAAT,CACLC,QADK,CAELC,WAFK,CAGLC,GAHK,CAIuB,CAC5B,GAAIA,GAAG,CAACC,IAAJ,GAAa,WAAjB,CAA8B,CAC5B,MAAO,MAAP,CACD,CAED,KAAMC,CAAAA,GAAG,CAAGN,cAAc,CAACO,IAAf,CAAoBH,GAAG,CAACI,OAAxB,CAAZ,CACA,GAAIF,GAAJ,CAAS,iCACP,KAAM,EAAGG,MAAH,CAAWC,UAAX,CAAuBC,WAAvB,CAAoCC,YAApC,EAAoDN,GAA1D,CACA,KAAMO,CAAAA,UAAU,CAAGC,IAAI,CAACC,GAAL,CAAS,CAAT,CAAYC,QAAQ,CAACN,UAAD,CAAa,EAAb,CAApB,CAAnB,CACA,KAAMO,CAAAA,MAAM,uBAAGL,YAAH,cAAGA,YAAY,CAAEM,MAAjB,6BAA2B,CAAvC,CAEA,GAAIC,CAAAA,KAAJ,CACA,GAAIhB,WAAJ,CAAiB,CACf,GAAI,CACFgB,KAAK,CAAG,gCACNhB,WADM,CAEN,CAAEiB,KAAK,CAAE,CAAEC,IAAI,CAAER,UAAR,CAAoBI,MAApB,CAAT,CAFM,CAGN,CAAEK,UAAU,CAAE,IAAd,CAHM,CAAR,CAKD,CAAC,cAAM,CAAE,CACX,CAED,MAAO,IAAIC,uCAAJ,CACJ,GAAE3B,KAAK,CAAC4B,IAAN,CAAWtB,QAAX,CAAqB,IAAGN,KAAK,CAAC6B,MAAN,CACzBZ,UAAU,CAACa,QAAX,EADyB,CAEzB,IAAG9B,KAAK,CAAC6B,MAAN,CAAaR,MAAM,CAACS,QAAP,EAAb,CAAgC,EAHhC,CAIL9B,KAAK,CAAC+B,GAAN,CACGC,IADH,CACQ,cADR,EAEGC,MAFH,CAEW,KAAIpB,MAAO,OAAZ,QAAkBU,KAAlB,eAA2BR,WAAY,EAFjD,CAJK,CAAP,CAQD,CAED,MAAO,MAAP,CACD", "sourcesContent": ["import { codeFrameColumns } from 'next/dist/compiled/babel/code-frame'\nimport Chalk from 'chalk'\nimport { SimpleWebpackError } from './simpleWebpackError'\n\nconst chalk = new Chalk.constructor({ enabled: true })\nconst regexScssError = /SassError: (.+)\\n\\s+on line (\\d+) [\\s\\S]*?>> (.+)\\n\\s*(-+)\\^$/m\n\nexport function getScssError(\n  fileName: string,\n  fileContent: string | null,\n  err: Error\n): SimpleWebpackError | false {\n  if (err.name !== 'SassError') {\n    return false\n  }\n\n  const res = regexScssError.exec(err.message)\n  if (res) {\n    const [, reason, _lineNumer, backupFrame, columnString] = res\n    const lineNumber = Math.max(1, parseInt(_lineNumer, 10))\n    const column = columnString?.length ?? 1\n\n    let frame: string | undefined\n    if (fileContent) {\n      try {\n        frame = codeFrameColumns(\n          fileContent,\n          { start: { line: lineNumber, column } },\n          { forceColor: true }\n        ) as string\n      } catch {}\n    }\n\n    return new SimpleWebpackError(\n      `${chalk.cyan(fileName)}:${chalk.yellow(\n        lineNumber.toString()\n      )}:${chalk.yellow(column.toString())}`,\n      chalk.red\n        .bold('Syntax error')\n        .concat(`: ${reason}\\n\\n${frame ?? backupFrame}`)\n    )\n  }\n\n  return false\n}\n"]}