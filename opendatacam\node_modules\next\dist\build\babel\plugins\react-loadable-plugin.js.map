{"version": 3, "sources": ["../../../../build/babel/plugins/react-loadable-plugin.ts"], "names": ["types", "t", "visitor", "ImportDeclaration", "path", "state", "source", "node", "value", "defaultSpecifier", "get", "find", "specifier", "isImportDefaultSpecifier", "bindingName", "local", "name", "binding", "scope", "getBinding", "referencePaths", "for<PERSON>ach", "refPath", "callExpression", "parentPath", "isMemberExpression", "computed", "property", "Array", "isArray", "isIdentifier", "isCallExpression", "callExpression_", "args", "length", "buildCodeFrameError", "loader", "options", "isObjectExpression", "arguments", "push", "objectExpression", "options_", "properties", "propertiesMap", "key", "loadableGenerated", "modules", "dynamicImports", "dynamic<PERSON>eys", "traverse", "Import", "importPath", "importArguments", "binaryExpression", "stringLiteral", "file", "opts", "caller", "pagesDir", "filename", "objectProperty", "identifier", "arrowFunctionExpression", "arrayExpression", "map", "dynamicImport", "memberExpression", "arrowFunction", "replaceWith"], "mappings": "8DA+BA,0BA/BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,E,CACA;AACA;AACA;AACA;AAUe,kBAAU,CACvBA,KAAK,CAAEC,CADgB,CAAV,CAID,CACZ,MAAO,CACLC,OAAO,CAAE,CACPC,iBAAiB,CACfC,IADe,CAEfC,KAFe,CAGf,CACA,GAAIC,CAAAA,MAAM,CAAGF,IAAI,CAACG,IAAL,CAAUD,MAAV,CAAiBE,KAA9B,CACA,GAAIF,MAAM,GAAK,cAAf,CAA+B,OAE/B,GAAIG,CAAAA,gBAAgB,CAAGL,IAAI,CAACM,GAAL,CAAS,YAAT,EAAuBC,IAAvB,CAA6BC,SAAD,EAAe,CAChE,MAAOA,CAAAA,SAAS,CAACC,wBAAV,EAAP,CACD,CAFsB,CAAvB,CAIA,GAAI,CAACJ,gBAAL,CAAuB,OAEvB,KAAMK,CAAAA,WAAW,CAAGL,gBAAgB,CAACF,IAAjB,CAAsBQ,KAAtB,CAA4BC,IAAhD,CACA,KAAMC,CAAAA,OAAO,CAAGb,IAAI,CAACc,KAAL,CAAWC,UAAX,CAAsBL,WAAtB,CAAhB,CAEA,GAAI,CAACG,OAAL,CAAc,CACZ,OACD,CAEDA,OAAO,CAACG,cAAR,CAAuBC,OAAvB,CAAgCC,OAAD,EAAa,CAC1C,GAAIC,CAAAA,cAAc,CAAGD,OAAO,CAACE,UAA7B,CAEA,GACED,cAAc,CAACE,kBAAf,IACAF,cAAc,CAAChB,IAAf,CAAoBmB,QAApB,GAAiC,KAFnC,CAGE,CACA,KAAMC,CAAAA,QAAQ,CAAGJ,cAAc,CAACb,GAAf,CAAmB,UAAnB,CAAjB,CACA,GACE,CAACkB,KAAK,CAACC,OAAN,CAAcF,QAAd,CAAD,EACAA,QAAQ,CAACG,YAAT,CAAsB,CAAEd,IAAI,CAAE,KAAR,CAAtB,CAFF,CAGE,CACAO,cAAc,CAAGA,cAAc,CAACC,UAAhC,CACD,CACF,CAED,GAAI,CAACD,cAAc,CAACQ,gBAAf,EAAL,CAAwC,OAExC,KAAMC,CAAAA,eAAe,CAAGT,cAAxB,CAIA,GAAIU,CAAAA,IAAI,CAAGD,eAAe,CAACtB,GAAhB,CAAoB,WAApB,CAAX,CACA,GAAIuB,IAAI,CAACC,MAAL,CAAc,CAAlB,CAAqB,CACnB,KAAMF,CAAAA,eAAe,CAACG,mBAAhB,CACJ,uCADI,CAAN,CAGD,CAED,GAAI,CAACF,IAAI,CAAC,CAAD,CAAT,CAAc,CACZ,OACD,CAED,GAAIG,CAAAA,MAAJ,CACA,GAAIC,CAAAA,OAAJ,CAEA,GAAIJ,IAAI,CAAC,CAAD,CAAJ,CAAQK,kBAAR,EAAJ,CAAkC,CAChCD,OAAO,CAAGJ,IAAI,CAAC,CAAD,CAAd,CACD,CAFD,IAEO,CACL,GAAI,CAACA,IAAI,CAAC,CAAD,CAAT,CAAc,CACZD,eAAe,CAACzB,IAAhB,CAAqBgC,SAArB,CAA+BC,IAA/B,CAAoCvC,CAAC,CAACwC,gBAAF,CAAmB,EAAnB,CAApC,EACD,CACD;AACAR,IAAI,CAAGD,eAAe,CAACtB,GAAhB,CAAoB,WAApB,CAAP,CACA0B,MAAM,CAAGH,IAAI,CAAC,CAAD,CAAb,CACAI,OAAO,CAAGJ,IAAI,CAAC,CAAD,CAAd,CACD,CAED,GAAI,CAACI,OAAO,CAACC,kBAAR,EAAL,CAAmC,OACnC,KAAMI,CAAAA,QAAQ,CAAGL,OAAjB,CAEA,GAAIM,CAAAA,UAAU,CAAGD,QAAQ,CAAChC,GAAT,CAAa,YAAb,CAAjB,CACA,GAAIkC,CAAAA,aAMH,CAAG,EANJ,CAQAD,UAAU,CAACtB,OAAX,CAAoBM,QAAD,EAAc,CAC/B,KAAMkB,CAAAA,GAAQ,CAAGlB,QAAQ,CAACjB,GAAT,CAAa,KAAb,CAAjB,CACAkC,aAAa,CAACC,GAAG,CAACtC,IAAJ,CAASS,IAAV,CAAb,CAA+BW,QAA/B,CACD,CAHD,EAKA,GAAIiB,aAAa,CAACE,iBAAlB,CAAqC,CACnC,OACD,CAED,GAAIF,aAAa,CAACR,MAAlB,CAA0B,CACxBA,MAAM,CAAGQ,aAAa,CAACR,MAAd,CAAqB1B,GAArB,CAAyB,OAAzB,CAAT,CACD,CAED,GAAIkC,aAAa,CAACG,OAAlB,CAA2B,CACzBX,MAAM,CAAGQ,aAAa,CAACG,OAAd,CAAsBrC,GAAtB,CAA0B,OAA1B,CAAT,CACD,CAED,GAAI,CAAC0B,MAAD,EAAWR,KAAK,CAACC,OAAN,CAAcO,MAAd,CAAf,CAAsC,CACpC,OACD,CACD,KAAMY,CAAAA,cAAuC,CAAG,EAAhD,CACA,KAAMC,CAAAA,WAAoC,CAAG,EAA7C,CAEAb,MAAM,CAACc,QAAP,CAAgB,CACdC,MAAM,CAACC,UAAD,CAAa,2BACjB,KAAMC,CAAAA,eAAe,CAAGD,UAAU,CAAC5B,UAAX,CAAsBd,GAAtB,CAA0B,WAA1B,CAAxB,CACA,GAAI,CAACkB,KAAK,CAACC,OAAN,CAAcwB,eAAd,CAAL,CAAqC,OACrC,KAAM9C,CAAAA,IAAS,CAAG8C,eAAe,CAAC,CAAD,CAAf,CAAmB9C,IAArC,CACAyC,cAAc,CAACR,IAAf,CAAoBjC,IAApB,EACA0C,WAAW,CAACT,IAAZ,CACEvC,CAAC,CAACqD,gBAAF,CACE,GADF,CAEErD,CAAC,CAACsD,aAAF,CACE,CAAC,uBAAAlD,KAAK,CAACmD,IAAN,CAAWC,IAAX,CAAgBC,MAAhB,+BAAwBC,QAAxB,CACG,mBACEtD,KAAK,CAACmD,IAAN,CAAWC,IAAX,CAAgBC,MAAhB,CAAuBC,QADzB,CAEEtD,KAAK,CAACmD,IAAN,CAAWC,IAAX,CAAgBG,QAFlB,CADH,CAKGvD,KAAK,CAACmD,IAAN,CAAWC,IAAX,CAAgBG,QALpB,EAKgC,MANlC,CAFF,CAUErD,IAVF,CADF,EAcD,CApBa,CAAhB,EAuBA,GAAI,CAACyC,cAAc,CAACd,MAApB,CAA4B,OAE5BG,OAAO,CAAC9B,IAAR,CAAaoC,UAAb,CAAwBH,IAAxB,CACEvC,CAAC,CAAC4D,cAAF,CACE5D,CAAC,CAAC6D,UAAF,CAAa,mBAAb,CADF,CAEE7D,CAAC,CAACwC,gBAAF,CAAmB,CACjBxC,CAAC,CAAC4D,cAAF,CACE5D,CAAC,CAAC6D,UAAF,CAAa,SAAb,CADF,CAEE7D,CAAC,CAAC8D,uBAAF,CACE,EADF,CAEE9D,CAAC,CAAC+D,eAAF,CACEhB,cAAc,CAACiB,GAAf,CAAoBC,aAAD,EAAmB,CACpC,MAAOjE,CAAAA,CAAC,CAACsB,cAAF,CACLtB,CAAC,CAACkE,gBAAF,CACElE,CAAC,CAAC6D,UAAF,CAAa,SAAb,CADF,CAEE7D,CAAC,CAAC6D,UAAF,CAAa,aAAb,CAFF,CADK,CAKL,CAACI,aAAD,CALK,CAAP,CAOD,CARD,CADF,CAFF,CAFF,CADiB,CAkBjBjE,CAAC,CAAC4D,cAAF,CACE5D,CAAC,CAAC6D,UAAF,CAAa,SAAb,CADF,CAEE7D,CAAC,CAAC+D,eAAF,CAAkBf,WAAlB,CAFF,CAlBiB,CAAnB,CAFF,CADF,EA6BA;AACA;AACA;AACA,GAAIb,MAAM,CAACL,gBAAP,EAAJ,CAA+B,CAC7B,KAAMqC,CAAAA,aAAa,CAAGnE,CAAC,CAAC8D,uBAAF,CAA0B,EAA1B,CAA8B3B,MAAM,CAAC7B,IAArC,CAAtB,CACA6B,MAAM,CAACiC,WAAP,CAAmBD,aAAnB,EACD,CACF,CAhJD,EAiJD,CAtKM,CADJ,CAAP,CA0KD", "sourcesContent": ["/**\nCOPYRIGHT (c) 2017-present <PERSON> <<EMAIL>>\n MIT License\n Permission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n The above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWAR\n*/\n// This file is https://github.com/jamiebuilds/react-loadable/blob/master/src/babel.js\n// Modified to also look for `next/dynamic`\n// Modified to put `webpack` and `modules` under `loadableGenerated` to be backwards compatible with next/dynamic which has a `modules` key\n// Modified to support `dynamic(import('something'))` and `dynamic(import('something'), options)\n\nimport {\n  NodePath,\n  PluginObj,\n  types as BabelTypes,\n} from 'next/dist/compiled/babel/core'\n\nimport { relative as relativePath } from 'path'\n\nexport default function ({\n  types: t,\n}: {\n  types: typeof BabelTypes\n}): PluginObj {\n  return {\n    visitor: {\n      ImportDeclaration(\n        path: NodePath<BabelTypes.ImportDeclaration>,\n        state: any\n      ) {\n        let source = path.node.source.value\n        if (source !== 'next/dynamic') return\n\n        let defaultSpecifier = path.get('specifiers').find((specifier) => {\n          return specifier.isImportDefaultSpecifier()\n        })\n\n        if (!defaultSpecifier) return\n\n        const bindingName = defaultSpecifier.node.local.name\n        const binding = path.scope.getBinding(bindingName)\n\n        if (!binding) {\n          return\n        }\n\n        binding.referencePaths.forEach((refPath) => {\n          let callExpression = refPath.parentPath\n\n          if (\n            callExpression.isMemberExpression() &&\n            callExpression.node.computed === false\n          ) {\n            const property = callExpression.get('property')\n            if (\n              !Array.isArray(property) &&\n              property.isIdentifier({ name: 'Map' })\n            ) {\n              callExpression = callExpression.parentPath\n            }\n          }\n\n          if (!callExpression.isCallExpression()) return\n\n          const callExpression_ = callExpression as NodePath<\n            BabelTypes.CallExpression\n          >\n\n          let args = callExpression_.get('arguments')\n          if (args.length > 2) {\n            throw callExpression_.buildCodeFrameError(\n              'next/dynamic only accepts 2 arguments'\n            )\n          }\n\n          if (!args[0]) {\n            return\n          }\n\n          let loader\n          let options\n\n          if (args[0].isObjectExpression()) {\n            options = args[0]\n          } else {\n            if (!args[1]) {\n              callExpression_.node.arguments.push(t.objectExpression([]))\n            }\n            // This is needed as the code is modified above\n            args = callExpression_.get('arguments')\n            loader = args[0]\n            options = args[1]\n          }\n\n          if (!options.isObjectExpression()) return\n          const options_ = options as NodePath<BabelTypes.ObjectExpression>\n\n          let properties = options_.get('properties')\n          let propertiesMap: {\n            [key: string]: NodePath<\n              | BabelTypes.ObjectProperty\n              | BabelTypes.ObjectMethod\n              | BabelTypes.SpreadElement\n            >\n          } = {}\n\n          properties.forEach((property) => {\n            const key: any = property.get('key')\n            propertiesMap[key.node.name] = property\n          })\n\n          if (propertiesMap.loadableGenerated) {\n            return\n          }\n\n          if (propertiesMap.loader) {\n            loader = propertiesMap.loader.get('value')\n          }\n\n          if (propertiesMap.modules) {\n            loader = propertiesMap.modules.get('value')\n          }\n\n          if (!loader || Array.isArray(loader)) {\n            return\n          }\n          const dynamicImports: BabelTypes.Expression[] = []\n          const dynamicKeys: BabelTypes.Expression[] = []\n\n          loader.traverse({\n            Import(importPath) {\n              const importArguments = importPath.parentPath.get('arguments')\n              if (!Array.isArray(importArguments)) return\n              const node: any = importArguments[0].node\n              dynamicImports.push(node)\n              dynamicKeys.push(\n                t.binaryExpression(\n                  '+',\n                  t.stringLiteral(\n                    (state.file.opts.caller?.pagesDir\n                      ? relativePath(\n                          state.file.opts.caller.pagesDir,\n                          state.file.opts.filename\n                        )\n                      : state.file.opts.filename) + ' -> '\n                  ),\n                  node\n                )\n              )\n            },\n          })\n\n          if (!dynamicImports.length) return\n\n          options.node.properties.push(\n            t.objectProperty(\n              t.identifier('loadableGenerated'),\n              t.objectExpression([\n                t.objectProperty(\n                  t.identifier('webpack'),\n                  t.arrowFunctionExpression(\n                    [],\n                    t.arrayExpression(\n                      dynamicImports.map((dynamicImport) => {\n                        return t.callExpression(\n                          t.memberExpression(\n                            t.identifier('require'),\n                            t.identifier('resolveWeak')\n                          ),\n                          [dynamicImport]\n                        )\n                      })\n                    )\n                  )\n                ),\n                t.objectProperty(\n                  t.identifier('modules'),\n                  t.arrayExpression(dynamicKeys)\n                ),\n              ])\n            )\n          )\n\n          // Turns `dynamic(import('something'))` into `dynamic(() => import('something'))` for backwards compat.\n          // This is the replicate the behavior in versions below Next.js 7 where we magically handled not executing the `import()` too.\n          // We'll deprecate this behavior and provide a codemod for it in 7.1.\n          if (loader.isCallExpression()) {\n            const arrowFunction = t.arrowFunctionExpression([], loader.node)\n            loader.replaceWith(arrowFunction)\n          }\n        })\n      },\n    },\n  }\n}\n"]}