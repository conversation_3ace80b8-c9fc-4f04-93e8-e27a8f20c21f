import { PHASE_DEVELOPMENT_SERVER, PHASE_EXPORT, PHASE_PRODUCTION_BUILD } from '../../next-server/lib/constants';
declare type EventCliSessionStarted = {
    nextVersion: string;
    nodeVersion: string;
    cliCommand: string;
    isSrcDir: boolean | null;
    hasNowJson: boolean;
    isCustomServer: boolean | null;
    hasNextConfig: boolean;
    buildTarget: string;
    hasWebpackConfig: boolean;
    hasBabelConfig: boolean;
    basePathEnabled: boolean;
    i18nEnabled: boolean;
    imageEnabled: boolean;
    locales: string | null;
    localeDomainsCount: number | null;
    localeDetectionEnabled: boolean | null;
    imageDomainsCount: number | null;
    imageSizes: string | null;
    imageLoader: string | null;
    trailingSlashEnabled: boolean;
    reactStrictMode: boolean;
    webpackVersion: number | null;
};
declare type NextConfigurationPhase = typeof PHASE_DEVELOPMENT_SERVER | typeof PHASE_PRODUCTION_BUILD | typeof PHASE_EXPORT;
export declare function eventCliSession(phase: NextConfigurationPhase, dir: string, event: Omit<EventCliSessionStarted, 'nextVersion' | 'nodeVersion' | 'hasNextConfig' | 'buildTarget' | 'hasWebpackConfig' | 'hasBabelConfig' | 'basePathEnabled' | 'i18nEnabled' | 'imageEnabled' | 'locales' | 'localeDomainsCount' | 'localeDetectionEnabled' | 'imageDomainsCount' | 'imageSizes' | 'imageLoader' | 'trailingSlashEnabled' | 'reactStrictMode'>): {
    eventName: string;
    payload: EventCliSessionStarted;
}[];
export {};
