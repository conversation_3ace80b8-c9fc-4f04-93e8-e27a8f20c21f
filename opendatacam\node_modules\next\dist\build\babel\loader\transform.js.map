{"version": 3, "sources": ["../../../../build/babel/loader/transform.ts"], "names": ["getTraversalParams", "file", "pluginPairs", "passPairs", "passes", "visitors", "plugin", "concat", "pass", "Plug<PERSON><PERSON><PERSON>", "key", "options", "push", "visitor", "invokePluginPre", "pre", "call", "invokePluginPost", "post", "transformAstPass", "parentSpan", "traverse", "merge", "opts", "wrapPluginVisitorMethod", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "ast", "scope", "transformAst", "babelConfig", "transform", "source", "inputSourceMap", "loaderOptions", "filename", "target", "getConfigSpan", "getConfig", "stop", "normalizeSpan", "transformSpan", "generateSpan", "code", "map", "generatorOpts"], "mappings": "+DAIA,mFACA,qFACA,8GACA,8GACA,qHACA,wGAEA,+DACA,4B,mFAZA;AACA;AACA,GAcA,QAASA,CAAAA,kBAAT,CAA4BC,IAA5B,CAAuCC,WAAvC,CAA2D,CACzD,KAAMC,CAAAA,SAAS,CAAG,EAAlB,CACA,KAAMC,CAAAA,MAAM,CAAG,EAAf,CACA,KAAMC,CAAAA,QAAQ,CAAG,EAAjB,CAEA,IAAK,KAAMC,CAAAA,MAAX,GAAqBJ,CAAAA,WAAW,CAACK,MAAZ,CAAmB,sCAAnB,CAArB,CAAiE,CAC/D,KAAMC,CAAAA,IAAI,CAAG,GAAIC,2BAAJ,CAAeR,IAAf,CAAqBK,MAAM,CAACI,GAA5B,CAAiCJ,MAAM,CAACK,OAAxC,CAAb,CACAR,SAAS,CAACS,IAAV,CAAe,CAACN,MAAD,CAASE,IAAT,CAAf,EACAJ,MAAM,CAACQ,IAAP,CAAYJ,IAAZ,EACAH,QAAQ,CAACO,IAAT,CAAcN,MAAM,CAACO,OAArB,EACD,CAED,MAAO,CAAEV,SAAF,CAAaC,MAAb,CAAqBC,QAArB,CAAP,CACD,CAED,QAASS,CAAAA,eAAT,CAAyBb,IAAzB,CAAoCE,SAApC,CAAsD,CACpD,IAAK,KAAM,CAAC,CAAEY,GAAF,CAAD,CAAUP,IAAV,CAAX,EAA8BL,CAAAA,SAA9B,CAAyC,CACvC,GAAIY,GAAJ,CAAS,CACPA,GAAG,CAACC,IAAJ,CAASR,IAAT,CAAeP,IAAf,EACD,CACF,CACF,CAED,QAASgB,CAAAA,gBAAT,CAA0BhB,IAA1B,CAAqCE,SAArC,CAAuD,CACrD,IAAK,KAAM,CAAC,CAAEe,IAAF,CAAD,CAAWV,IAAX,CAAX,EAA+BL,CAAAA,SAA/B,CAA0C,CACxC,GAAIe,IAAJ,CAAU,CACRA,IAAI,CAACF,IAAL,CAAUR,IAAV,CAAgBP,IAAhB,EACD,CACF,CACF,CAED,QAASkB,CAAAA,gBAAT,CAA0BlB,IAA1B,CAAqCC,WAArC,CAAyDkB,UAAzD,CAA2E,CACzE,KAAM,CAAEjB,SAAF,CAAaC,MAAb,CAAqBC,QAArB,EAAkCL,kBAAkB,CAACC,IAAD,CAAOC,WAAP,CAA1D,CAEAY,eAAe,CAACb,IAAD,CAAOE,SAAP,CAAf,CACA,KAAMU,CAAAA,OAAO,CAAGQ,kBAAShB,QAAT,CAAkBiB,KAAlB,CACdjB,QADc,CAEdD,MAFc,CAGd;AACAH,IAAI,CAACsB,IAAL,CAAUC,uBAJI,CAAhB,CAOAJ,UAAU,CACPK,UADH,CACc,sBADd,EAEGC,OAFH,CAEW,IAAM,sBAASzB,IAAI,CAAC0B,GAAd,CAAmBd,OAAnB,CAA4BZ,IAAI,CAAC2B,KAAjC,CAFjB,EAIAX,gBAAgB,CAAChB,IAAD,CAAOE,SAAP,CAAhB,CACD,CAED,QAAS0B,CAAAA,YAAT,CAAsB5B,IAAtB,CAAiC6B,WAAjC,CAAmDV,UAAnD,CAAqE,CACnE,IAAK,KAAMlB,CAAAA,WAAX,GAA0B4B,CAAAA,WAAW,CAAC1B,MAAtC,CAA8C,CAC5Ce,gBAAgB,CAAClB,IAAD,CAAOC,WAAP,CAAoBkB,UAApB,CAAhB,CACD,CACF,CAEc,QAASW,CAAAA,SAAT,CAEbC,MAFa,CAGbC,cAHa,CAIbC,aAJa,CAKbC,QALa,CAMbC,MANa,CAObhB,UAPa,CAQb,CACA,KAAMiB,CAAAA,aAAa,CAAGjB,UAAU,CAACK,UAAX,CAAsB,wBAAtB,CAAtB,CACA,KAAMK,CAAAA,WAAW,CAAGQ,mBAAUtB,IAAV,CAAe,IAAf,CAAqB,CACvCgB,MADuC,CAEvCE,aAFuC,CAGvCD,cAHuC,CAIvCG,MAJuC,CAKvCD,QALuC,CAArB,CAApB,CAOAE,aAAa,CAACE,IAAd,GAEA,KAAMC,CAAAA,aAAa,CAAGpB,UAAU,CAACK,UAAX,CAAsB,4BAAtB,CAAtB,CACA,KAAMxB,CAAAA,IAAI,CAAG,0BACX,kCAAc6B,WAAW,CAAC1B,MAA1B,CAAkC,kCAAc0B,WAAd,CAAlC,CAA8DE,MAA9D,CADW,CAAb,CAGAQ,aAAa,CAACD,IAAd,GAEA,KAAME,CAAAA,aAAa,CAAGrB,UAAU,CAACK,UAAX,CAAsB,uBAAtB,CAAtB,CACAI,YAAY,CAAC5B,IAAD,CAAO6B,WAAP,CAAoBW,aAApB,CAAZ,CACAA,aAAa,CAACF,IAAd,GAEA,KAAMG,CAAAA,YAAY,CAAGtB,UAAU,CAACK,UAAX,CAAsB,sBAAtB,CAArB,CACA,KAAM,CAAEkB,IAAF,CAAQC,GAAR,EAAgB,uBAAS3C,IAAI,CAAC0B,GAAd,CAAmB1B,IAAI,CAACsB,IAAL,CAAUsB,aAA7B,CAA4C5C,IAAI,CAAC0C,IAAjD,CAAtB,CACAD,YAAY,CAACH,IAAb,GAEA,MAAO,CAAEI,IAAF,CAAQC,GAAR,CAAP,CACD", "sourcesContent": ["/*\n * Partially adapted from @babel/core (MIT license).\n */\n\nimport traverse from 'next/dist/compiled/babel/traverse'\nimport generate from 'next/dist/compiled/babel/generator'\nimport normalizeFile from 'next/dist/compiled/babel/core-lib-normalize-file'\nimport normalizeOpts from 'next/dist/compiled/babel/core-lib-normalize-opts'\nimport loadBlockHoistPlugin from 'next/dist/compiled/babel/core-lib-block-hoist-plugin'\nimport PluginPass from 'next/dist/compiled/babel/core-lib-plugin-pass'\n\nimport getConfig from './get-config'\nimport { consumeIterator } from './util'\nimport { Span } from '../../../telemetry/trace'\nimport { NextJsLoaderContext } from './types'\n\nfunction getTraversalParams(file: any, pluginPairs: any[]) {\n  const passPairs = []\n  const passes = []\n  const visitors = []\n\n  for (const plugin of pluginPairs.concat(loadBlockHoistPlugin())) {\n    const pass = new PluginPass(file, plugin.key, plugin.options)\n    passPairs.push([plugin, pass])\n    passes.push(pass)\n    visitors.push(plugin.visitor)\n  }\n\n  return { passPairs, passes, visitors }\n}\n\nfunction invokePluginPre(file: any, passPairs: any[]) {\n  for (const [{ pre }, pass] of passPairs) {\n    if (pre) {\n      pre.call(pass, file)\n    }\n  }\n}\n\nfunction invokePluginPost(file: any, passPairs: any[]) {\n  for (const [{ post }, pass] of passPairs) {\n    if (post) {\n      post.call(pass, file)\n    }\n  }\n}\n\nfunction transformAstPass(file: any, pluginPairs: any[], parentSpan: Span) {\n  const { passPairs, passes, visitors } = getTraversalParams(file, pluginPairs)\n\n  invokePluginPre(file, passPairs)\n  const visitor = traverse.visitors.merge(\n    visitors,\n    passes,\n    // @ts-ignore - the exported types are incorrect here\n    file.opts.wrapPluginVisitorMethod\n  )\n\n  parentSpan\n    .traceChild('babel-turbo-traverse')\n    .traceFn(() => traverse(file.ast, visitor, file.scope))\n\n  invokePluginPost(file, passPairs)\n}\n\nfunction transformAst(file: any, babelConfig: any, parentSpan: Span) {\n  for (const pluginPairs of babelConfig.passes) {\n    transformAstPass(file, pluginPairs, parentSpan)\n  }\n}\n\nexport default function transform(\n  this: NextJsLoaderContext,\n  source: string,\n  inputSourceMap: object | null | undefined,\n  loaderOptions: any,\n  filename: string,\n  target: string,\n  parentSpan: Span\n) {\n  const getConfigSpan = parentSpan.traceChild('babel-turbo-get-config')\n  const babelConfig = getConfig.call(this, {\n    source,\n    loaderOptions,\n    inputSourceMap,\n    target,\n    filename,\n  })\n  getConfigSpan.stop()\n\n  const normalizeSpan = parentSpan.traceChild('babel-turbo-normalize-file')\n  const file = consumeIterator(\n    normalizeFile(babelConfig.passes, normalizeOpts(babelConfig), source)\n  )\n  normalizeSpan.stop()\n\n  const transformSpan = parentSpan.traceChild('babel-turbo-transform')\n  transformAst(file, babelConfig, transformSpan)\n  transformSpan.stop()\n\n  const generateSpan = parentSpan.traceChild('babel-turbo-generate')\n  const { code, map } = generate(file.ast, file.opts.generatorOpts, file.code)\n  generateSpan.stop()\n\n  return { code, map }\n}\n"]}