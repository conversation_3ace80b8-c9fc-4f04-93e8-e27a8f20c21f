{"version": 3, "sources": ["../../lib/recursive-delete.ts"], "names": ["sleep", "setTimeout", "unlinkFile", "p", "t", "promises", "unlink", "e", "code", "recursiveDelete", "dir", "exclude", "previousPath", "result", "readdir", "Promise", "all", "map", "part", "absolutePath", "pathStat", "stat", "catch", "pp", "isDirectory", "test", "rmdir"], "mappings": "6EAAA,sBACA,0BACA,0BAEA,KAAMA,CAAAA,KAAK,CAAG,oBAAUC,UAAV,CAAd,CAEA,KAAMC,CAAAA,UAAU,CAAG,MAAOC,CAAP,CAAkBC,CAAC,CAAG,CAAtB,GAA2C,CAC5D,GAAI,CACF,KAAMC,cAASC,MAAT,CAAgBH,CAAhB,CAAN,CACD,CAAC,MAAOI,CAAP,CAAU,CACV,GACE,CAACA,CAAC,CAACC,IAAF,GAAW,OAAX,EACCD,CAAC,CAACC,IAAF,GAAW,WADZ,EAECD,CAAC,CAACC,IAAF,GAAW,OAFZ,EAGCD,CAAC,CAACC,IAAF,GAAW,QAHb,GAIAJ,CAAC,CAAG,CALN,CAME,CACA,KAAMJ,CAAAA,KAAK,CAACI,CAAC,CAAG,GAAL,CAAX,CACA,MAAOF,CAAAA,UAAU,CAACC,CAAD,CAAIC,CAAC,EAAL,CAAjB,CACD,CAED,GAAIG,CAAC,CAACC,IAAF,GAAW,QAAf,CAAyB,CACvB,OACD,CAED,KAAMD,CAAAA,CAAN,CACD,CACF,CArBD,CAuBA;AACA;AACA;AACA;AACA;AACA;AACA,GACO,cAAeE,CAAAA,eAAf,CACLC,GADK,CAELC,OAFK,CAGLC,YAAoB,CAAG,EAHlB,CAIU,CACf,GAAIC,CAAAA,MAAJ,CACA,GAAI,CACFA,MAAM,CAAG,KAAMR,cAASS,OAAT,CAAiBJ,GAAjB,CAAf,CACD,CAAC,MAAOH,CAAP,CAAU,CACV,GAAIA,CAAC,CAACC,IAAF,GAAW,QAAf,CAAyB,CACvB,OACD,CACD,KAAMD,CAAAA,CAAN,CACD,CAED,KAAMQ,CAAAA,OAAO,CAACC,GAAR,CACJH,MAAM,CAACI,GAAP,CAAW,KAAOC,CAAAA,IAAP,EAAwB,CACjC,KAAMC,CAAAA,YAAY,CAAG,eAAKT,GAAL,CAAUQ,IAAV,CAArB,CACA,KAAME,CAAAA,QAAQ,CAAG,KAAMf,cAASgB,IAAT,CAAcF,YAAd,EAA4BG,KAA5B,CAAmCf,CAAD,EAAO,CAC9D,GAAIA,CAAC,CAACC,IAAF,GAAW,QAAf,CAAyB,KAAMD,CAAAA,CAAN,CAC1B,CAFsB,CAAvB,CAGA,GAAI,CAACa,QAAL,CAAe,CACb,OACD,CAED,KAAMG,CAAAA,EAAE,CAAG,eAAKX,YAAL,CAAmBM,IAAnB,CAAX,CACA,GAAIE,QAAQ,CAACI,WAAT,KAA2B,CAACb,OAAD,EAAY,CAACA,OAAO,CAACc,IAAR,CAAaF,EAAb,CAAxC,CAAJ,CAA+D,CAC7D,KAAMd,CAAAA,eAAe,CAACU,YAAD,CAAeR,OAAf,CAAwBY,EAAxB,CAArB,CACA,MAAOlB,cAASqB,KAAT,CAAeP,YAAf,CAAP,CACD,CAED,GAAI,CAACR,OAAD,EAAY,CAACA,OAAO,CAACc,IAAR,CAAaF,EAAb,CAAjB,CAAmC,CACjC,MAAOrB,CAAAA,UAAU,CAACiB,YAAD,CAAjB,CACD,CACF,CAlBD,CADI,CAAN,CAqBD", "sourcesContent": ["import { promises } from 'fs'\nimport { join } from 'path'\nimport { promisify } from 'util'\n\nconst sleep = promisify(setTimeout)\n\nconst unlinkFile = async (p: string, t = 1): Promise<void> => {\n  try {\n    await promises.unlink(p)\n  } catch (e) {\n    if (\n      (e.code === 'EBUSY' ||\n        e.code === 'ENOTEMPTY' ||\n        e.code === 'EPERM' ||\n        e.code === 'EMFILE') &&\n      t < 3\n    ) {\n      await sleep(t * 100)\n      return unlinkFile(p, t++)\n    }\n\n    if (e.code === 'ENOENT') {\n      return\n    }\n\n    throw e\n  }\n}\n\n/**\n * Recursively delete directory contents\n * @param  {string} dir Directory to delete the contents of\n * @param  {RegExp} [exclude] Exclude based on relative file path\n * @param  {string} [previousPath] Ensures that parameter dir exists, this is not passed recursively\n * @returns Promise void\n */\nexport async function recursiveDelete(\n  dir: string,\n  exclude?: RegExp,\n  previousPath: string = ''\n): Promise<void> {\n  let result\n  try {\n    result = await promises.readdir(dir)\n  } catch (e) {\n    if (e.code === 'ENOENT') {\n      return\n    }\n    throw e\n  }\n\n  await Promise.all(\n    result.map(async (part: string) => {\n      const absolutePath = join(dir, part)\n      const pathStat = await promises.stat(absolutePath).catch((e) => {\n        if (e.code !== 'ENOENT') throw e\n      })\n      if (!pathStat) {\n        return\n      }\n\n      const pp = join(previousPath, part)\n      if (pathStat.isDirectory() && (!exclude || !exclude.test(pp))) {\n        await recursiveDelete(absolutePath, exclude, pp)\n        return promises.rmdir(absolutePath)\n      }\n\n      if (!exclude || !exclude.test(pp)) {\n        return unlinkFile(absolutePath)\n      }\n    })\n  )\n}\n"]}