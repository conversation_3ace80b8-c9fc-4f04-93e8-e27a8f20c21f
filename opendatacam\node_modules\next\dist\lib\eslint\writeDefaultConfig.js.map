{"version": 3, "sources": ["../../../lib/eslint/writeDefaultConfig.ts"], "names": ["writeDefaultConfig", "eslintrcFile", "pkgJsonPath", "defaultConfig", "extends", "ext", "path", "extname", "fileContent", "CommentJson", "stringify", "fs", "writeFile", "os", "EOL", "console", "log", "chalk", "green", "bold", "basename", "pkgJsonContent", "readFile", "encoding", "packageJsonConfig", "parse", "eslintConfig"], "mappings": "mFAAA,sBACA,oDACA,8CACA,kDAEA,oF,w4BAEO,cAAeA,CAAAA,kBAAf,CACLC,YADK,CAELC,WAFK,CAGL,CACA,KAAMC,CAAAA,aAAa,CAAG,CACpBC,OAAO,CAAE,MADW,CAAtB,CAIA,GAAIH,YAAJ,CAAkB,CAChB,KAAMI,CAAAA,GAAG,CAAGC,cAAKC,OAAL,CAAaN,YAAb,CAAZ,CAEA,GAAIO,CAAAA,WAAJ,CACA,GAAIH,GAAG,GAAK,OAAR,EAAmBA,GAAG,GAAK,MAA/B,CAAuC,CACrCG,WAAW,CAAG,iBAAd,CACD,CAFD,IAEO,CACLA,WAAW,CAAGC,WAAW,CAACC,SAAZ,CAAsBP,aAAtB,CAAqC,IAArC,CAA2C,CAA3C,CAAd,CAEA,GAAIE,GAAG,GAAK,KAAZ,CAAmB,CACjBG,WAAW,CAAG,oBAAsBA,WAApC,CACD,CACF,CAED,KAAMG,cAAGC,SAAH,CAAaX,YAAb,CAA2BO,WAAW,CAAGK,YAAGC,GAA5C,CAAN,CAEAC,OAAO,CAACC,GAAR,CACE,KACEC,eAAMC,KAAN,CACG,sDAAqDD,eAAME,IAAN,CACpDb,cAAKc,QAAL,CAAcnB,YAAd,CADoD,CAEpD,gBAHJ,CADF,CAME,IAPJ,EASD,CAzBD,IAyBO,IAAIC,WAAJ,CAAiB,CACtB,KAAMmB,CAAAA,cAAc,CAAG,KAAMV,cAAGW,QAAH,CAAYpB,WAAZ,CAAyB,CACpDqB,QAAQ,CAAE,MAD0C,CAAzB,CAA7B,CAGA,GAAIC,CAAAA,iBAAiB,CAAGf,WAAW,CAACgB,KAAZ,CAAkBJ,cAAlB,CAAxB,CAEAG,iBAAiB,CAACE,YAAlB,CAAiCvB,aAAjC,CAEA,KAAMQ,cAAGC,SAAH,CACJV,WADI,CAEJO,WAAW,CAACC,SAAZ,CAAsBc,iBAAtB,CAAyC,IAAzC,CAA+C,CAA/C,EAAoDX,YAAGC,GAFnD,CAAN,CAKAC,OAAO,CAACC,GAAR,CACE,KACEC,eAAMC,KAAN,CACG,sDAAqDD,eAAME,IAAN,CACpD,cADoD,CAEpD,mCAHJ,CADF,CAME,IAPJ,EASD,CACF", "sourcesContent": ["import { promises as fs } from 'fs'\nimport chalk from 'chalk'\nimport os from 'os'\nimport path from 'path'\n\nimport * as CommentJson from 'next/dist/compiled/comment-json'\n\nexport async function writeDefaultConfig(\n  eslintrcFile: string | null,\n  pkgJsonPath: string | null\n) {\n  const defaultConfig = {\n    extends: 'next',\n  }\n\n  if (eslintrcFile) {\n    const ext = path.extname(eslintrcFile)\n\n    let fileContent\n    if (ext === '.yaml' || ext === '.yml') {\n      fileContent = \"extends: 'next'\"\n    } else {\n      fileContent = CommentJson.stringify(defaultConfig, null, 2)\n\n      if (ext === '.js') {\n        fileContent = 'module.exports = ' + fileContent\n      }\n    }\n\n    await fs.writeFile(eslintrcFile, fileContent + os.EOL)\n\n    console.log(\n      '\\n' +\n        chalk.green(\n          `We detected ESLint in your project and updated the ${chalk.bold(\n            path.basename(eslintrcFile)\n          )} file for you.`\n        ) +\n        '\\n'\n    )\n  } else if (pkgJsonPath) {\n    const pkgJsonContent = await fs.readFile(pkgJsonPath, {\n      encoding: 'utf8',\n    })\n    let packageJsonConfig = CommentJson.parse(pkgJsonContent)\n\n    packageJsonConfig.eslintConfig = defaultConfig\n\n    await fs.writeFile(\n      pkgJsonPath,\n      CommentJson.stringify(packageJsonConfig, null, 2) + os.EOL\n    )\n\n    console.log(\n      '\\n' +\n        chalk.green(\n          `We detected ESLint in your project and updated the ${chalk.bold(\n            'eslintConfig'\n          )} field for you in package.json...`\n        ) +\n        '\\n'\n    )\n  }\n}\n"]}