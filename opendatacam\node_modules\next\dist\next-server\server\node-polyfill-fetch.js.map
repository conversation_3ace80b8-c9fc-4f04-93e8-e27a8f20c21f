{"version": 3, "sources": ["../../../next-server/server/node-polyfill-fetch.js"], "names": ["global", "fetch", "Headers", "Request", "Response"], "mappings": "aAAA,8D,qzBAEA;AACA,GAAI,CAACA,MAAM,CAACC,KAAZ,CAAmB,CACjBD,MAAM,CAACC,KAAP,CAAeA,kBAAf,CACAD,MAAM,CAACE,OAAP,CAAiBA,kBAAjB,CACAF,MAAM,CAACG,OAAP,CAAiBA,kBAAjB,CACAH,MAAM,CAACI,QAAP,CAAkBA,mBAAlB,CACD", "sourcesContent": ["import fetch, { Headers, Request, Response } from 'node-fetch'\n\n// Polyfill fetch() in the Node.js environment\nif (!global.fetch) {\n  global.fetch = fetch\n  global.Headers = Headers\n  global.Request = Request\n  global.Response = Response\n}\n"]}