{"version": 3, "sources": ["../../../../../build/webpack/plugins/wellknown-errors-plugin/parseNotFoundError.ts"], "names": ["chalk", "Chalk", "constructor", "enabled", "getNotFoundError", "compilation", "input", "fileName", "name", "loc", "dependencies", "map", "d", "filter", "Boolean", "originalSource", "module", "result", "line", "start", "column", "source", "rootDirectory", "options", "context", "frame", "errorMessage", "error", "message", "replace", "green", "red", "bold", "originalCodeFrame", "SimpleWebpackError", "cyan", "yellow", "originalStackFrame", "lineNumber", "toString", "err", "console", "log"], "mappings": "+EAAA,oDACA,wDACA,kE,mFAEA,KAAMA,CAAAA,KAAK,CAAG,GAAIC,gBAAMC,WAAV,CAAsB,CAAEC,OAAO,CAAE,IAAX,CAAtB,CAAd,CAEO,cAAeC,CAAAA,gBAAf,CACLC,WADK,CAELC,KAFK,CAGLC,QAHK,CAIL,CACA,GAAID,KAAK,CAACE,IAAN,GAAe,qBAAnB,CAA0C,CACxC,MAAO,MAAP,CACD,CAED,KAAMC,CAAAA,GAAG,CAAGH,KAAK,CAACG,GAAN,CACRH,KAAK,CAACG,GADE,CAERH,KAAK,CAACI,YAAN,CAAmBC,GAAnB,CAAwBC,CAAD,EAAYA,CAAC,CAACH,GAArC,EAA0CI,MAA1C,CAAiDC,OAAjD,EAA0D,CAA1D,CAFJ,CAGA,KAAMC,CAAAA,cAAc,CAAGT,KAAK,CAACU,MAAN,CAAaD,cAAb,EAAvB,CAEA,GAAI,gGACF,KAAME,CAAAA,MAAM,CAAG,KAAM,yCAAyB,CAC5CC,IAAI,CAAET,GAAG,CAACU,KAAJ,CAAUD,IAD4B,CAE5CE,MAAM,CAAEX,GAAG,CAACU,KAAJ,CAAUC,MAF0B,CAG5CC,MAAM,CAAEN,cAHoC,CAI5CO,aAAa,CAAEjB,WAAW,CAACkB,OAAZ,CAAoBC,OAJS,CAK5CC,KAAK,CAAE,EALqC,CAAzB,CAArB,CAQA;AACA,GAAI,CAACR,MAAL,CAAa,CACX,MAAOX,CAAAA,KAAP,CACD,CAED,KAAMoB,CAAAA,YAAY,CAAGpB,KAAK,CAACqB,KAAN,CAAYC,OAAZ,CAClBC,OADkB,CACV,WADU,CACG,EADH,EAElBA,OAFkB,CAEV,sBAFU,CAEe,kBAAiB7B,KAAK,CAAC8B,KAAN,CAAY,IAAZ,CAAkB,GAFlD,CAArB,CAIA,KAAMF,CAAAA,OAAO,CACX5B,KAAK,CAAC+B,GAAN,CAAUC,IAAV,CAAe,kBAAf,EACC,KAAIN,YAAa,EADlB,CAEA,IAFA,CAGAT,MAAM,CAACgB,iBAJT,CAMA,MAAO,IAAIC,uCAAJ,CACJ,GAAElC,KAAK,CAACmC,IAAN,CAAW5B,QAAX,CAAqB,IAAGP,KAAK,CAACoC,MAAN,gDACzBnB,MAAM,CAACoB,kBAAP,CAA0BC,UADD,eACzB,uBAAsCC,QAAtC,EADyB,8BAC2B,EAD3B,CAEzB,IAAGvC,KAAK,CAACoC,MAAN,iDAAanB,MAAM,CAACoB,kBAAP,CAA0BjB,MAAvC,eAAa,uBAAkCmB,QAAlC,EAAb,+BAA6D,EAA7D,CAAiE,EAHjE,CAILX,OAJK,CAAP,CAMD,CAAC,MAAOY,GAAP,CAAY,CACZC,OAAO,CAACC,GAAR,CAAY,6BAAZ,CAA2CF,GAA3C,EACA;AACA,MAAOlC,CAAAA,KAAP,CACD,CACF", "sourcesContent": ["import Chalk from 'chalk'\nimport { SimpleWebpackError } from './simpleWebpackError'\nimport { createOriginalStackFrame } from '@next/react-dev-overlay/lib/middleware'\n\nconst chalk = new Chalk.constructor({ enabled: true })\n\nexport async function getNotFoundError(\n  compilation: any,\n  input: any,\n  fileName: string\n) {\n  if (input.name !== 'ModuleNotFoundError') {\n    return false\n  }\n\n  const loc = input.loc\n    ? input.loc\n    : input.dependencies.map((d: any) => d.loc).filter(Boolean)[0]\n  const originalSource = input.module.originalSource()\n\n  try {\n    const result = await createOriginalStackFrame({\n      line: loc.start.line,\n      column: loc.start.column,\n      source: originalSource,\n      rootDirectory: compilation.options.context,\n      frame: {},\n    })\n\n    // If we could not result the original location we still need to show the existing error\n    if (!result) {\n      return input\n    }\n\n    const errorMessage = input.error.message\n      .replace(/ in '.*?'/, '')\n      .replace(/Can't resolve '(.*)'/, `Can't resolve '${chalk.green('$1')}'`)\n\n    const message =\n      chalk.red.bold('Module not found') +\n      `: ${errorMessage}` +\n      '\\n' +\n      result.originalCodeFrame\n\n    return new SimpleWebpackError(\n      `${chalk.cyan(fileName)}:${chalk.yellow(\n        result.originalStackFrame.lineNumber?.toString() ?? ''\n      )}:${chalk.yellow(result.originalStackFrame.column?.toString() ?? '')}`,\n      message\n    )\n  } catch (err) {\n    console.log('Failed to parse source map:', err)\n    // Don't fail on failure to resolve sourcemaps\n    return input\n  }\n}\n"]}