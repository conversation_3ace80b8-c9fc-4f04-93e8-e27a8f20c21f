{"version": 3, "sources": ["../../../server/lib/start-server.ts"], "names": ["start", "serverOptions", "port", "hostname", "app", "customServer", "srv", "http", "createServer", "getRequestHandler", "Promise", "resolve", "reject", "on", "listen"], "mappings": "2DAAA,kDACA,qD,mFAEe,cAAeA,CAAAA,KAAf,CACbC,aADa,CAEbC,IAFa,CAGbC,QAHa,CAIb,CACA,KAAMC,CAAAA,GAAG,CAAG,kBAAK,CACf,GAAGH,aADY,CAEfI,YAAY,CAAE,KAFC,CAAL,CAAZ,CAIA,KAAMC,CAAAA,GAAG,CAAGC,cAAKC,YAAL,CAAkBJ,GAAG,CAACK,iBAAJ,EAAlB,CAAZ,CACA,KAAM,IAAIC,CAAAA,OAAJ,CAAkB,CAACC,OAAD,CAAUC,MAAV,GAAqB,CAC3C;AACAN,GAAG,CAACO,EAAJ,CAAO,OAAP,CAAgBD,MAAhB,EACAN,GAAG,CAACO,EAAJ,CAAO,WAAP,CAAoB,IAAMF,OAAO,EAAjC,EACAL,GAAG,CAACQ,MAAJ,CAAWZ,IAAX,CAAiBC,QAAjB,EACD,CALK,CAAN,CAMA;AACA;AACA,MAAOC,CAAAA,GAAP,CACD", "sourcesContent": ["import http from 'http'\nimport next from '../next'\n\nexport default async function start(\n  serverOptions: any,\n  port?: number,\n  hostname?: string\n) {\n  const app = next({\n    ...serverOptions,\n    customServer: false,\n  })\n  const srv = http.createServer(app.getRequestHandler())\n  await new Promise<void>((resolve, reject) => {\n    // This code catches EADDRINUSE error if the port is already in use\n    srv.on('error', reject)\n    srv.on('listening', () => resolve())\n    srv.listen(port, hostname)\n  })\n  // It's up to caller to run `app.prepare()`, so it can notify that the server\n  // is listening before starting any intensive operations.\n  return app\n}\n"]}