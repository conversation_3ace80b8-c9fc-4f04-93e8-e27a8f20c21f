{"version": 3, "sources": ["../../../../build/webpack/plugins/pages-manifest-plugin.ts"], "names": ["PagesManifestPlugin", "constructor", "serverless", "dev", "createAssets", "compilation", "assets", "entrypoints", "pages", "entrypoint", "values", "pagePath", "name", "files", "getFiles", "filter", "file", "includes", "endsWith", "isWebpack5", "length", "console", "log", "slice", "replace", "PAGES_MANIFEST", "sources", "RawSource", "JSON", "stringify", "apply", "compiler", "hooks", "make", "tap", "processAssets", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS", "emit"], "mappings": "4DAAA,2DAKA,6DACA,qH,mFAIA;AACA;AACA;AACe,KAAMA,CAAAA,mBAA8C,CAIjEC,WAAW,CAAC,CAAEC,UAAF,CAAcC,GAAd,CAAD,CAA6D,MAHxED,UAGwE,aAFxEC,GAEwE,QACtE,KAAKD,UAAL,CAAkBA,UAAlB,CACA,KAAKC,GAAL,CAAWA,GAAX,CACD,CAEDC,YAAY,CAACC,WAAD,CAAmBC,MAAnB,CAAgC,CAC1C,KAAMC,CAAAA,WAAW,CAAGF,WAAW,CAACE,WAAhC,CACA,KAAMC,CAAAA,KAAoB,CAAG,EAA7B,CAEA,IAAK,KAAMC,CAAAA,UAAX,GAAyBF,CAAAA,WAAW,CAACG,MAAZ,EAAzB,CAA+C,CAC7C,KAAMC,CAAAA,QAAQ,CAAG,oCAAuBF,UAAU,CAACG,IAAlC,CAAjB,CAEA,GAAI,CAACD,QAAL,CAAe,CACb,SACD,CAED,KAAME,CAAAA,KAAK,CAAGJ,UAAU,CACrBK,QADW,GAEXC,MAFW,CAGTC,IAAD,EACE,CAACA,IAAI,CAACC,QAAL,CAAc,iBAAd,CAAD,EAAqCD,IAAI,CAACE,QAAL,CAAc,KAAd,CAJ7B,CAAd,CAOA,GAAI,CAACC,mBAAD,EAAeN,KAAK,CAACO,MAAN,CAAe,CAAlC,CAAqC,CACnCC,OAAO,CAACC,GAAR,CACG,iDAAgDb,UAAU,CAACG,IAAK,EADnE,CAEEC,KAFF,EAIA,SACD,CAED;AACAL,KAAK,CAACG,QAAD,CAAL,CAAkBE,KAAK,CAACA,KAAK,CAACO,MAAN,CAAe,CAAhB,CAAvB,CAEA,GAAID,qBAAc,CAAC,KAAKhB,GAAxB,CAA6B,CAC3BK,KAAK,CAACG,QAAD,CAAL,CAAkBH,KAAK,CAACG,QAAD,CAAL,CAAgBY,KAAhB,CAAsB,CAAtB,CAAlB,CACD,CACDf,KAAK,CAACG,QAAD,CAAL,CAAkBH,KAAK,CAACG,QAAD,CAAL,CAAgBa,OAAhB,CAAwB,KAAxB,CAA+B,GAA/B,CAAlB,CACD,CAEDlB,MAAM,CACH,GAAEa,qBAAc,CAAC,KAAKhB,GAApB,CAA0B,KAA1B,CAAkC,EAAG,EAAxC,CAA4CsB,yBADxC,CAAN,CAEI,GAAIC,kBAAQC,SAAZ,CAAsBC,IAAI,CAACC,SAAL,CAAerB,KAAf,CAAsB,IAAtB,CAA4B,CAA5B,CAAtB,CAFJ,CAGD,CAEDsB,KAAK,CAACC,QAAD,CAAmC,CACtC,GAAIZ,mBAAJ,CAAgB,CACdY,QAAQ,CAACC,KAAT,CAAeC,IAAf,CAAoBC,GAApB,CAAwB,qBAAxB,CAAgD7B,WAAD,EAAiB,CAC9D;AACAA,WAAW,CAAC2B,KAAZ,CAAkBG,aAAlB,CAAgCD,GAAhC,CACE,CACEtB,IAAI,CAAE,qBADR,CAEE;AACAwB,KAAK,CAAEC,iBAAQC,WAAR,CAAoBC,8BAH7B,CADF,CAMGjC,MAAD,EAAiB,CACf,KAAKF,YAAL,CAAkBC,WAAlB,CAA+BC,MAA/B,EACD,CARH,EAUD,CAZD,EAaA,OACD,CAEDyB,QAAQ,CAACC,KAAT,CAAeQ,IAAf,CAAoBN,GAApB,CAAwB,qBAAxB,CAAgD7B,WAAD,EAAsB,CACnE,KAAKD,YAAL,CAAkBC,WAAlB,CAA+BA,WAAW,CAACC,MAA3C,EACD,CAFD,EAGD,CAtEgE,C", "sourcesContent": ["import {\n  webpack,\n  isWebpack5,\n  sources,\n} from 'next/dist/compiled/webpack/webpack'\nimport { PAGES_MANIFEST } from '../../../next-server/lib/constants'\nimport getRouteFromEntrypoint from '../../../next-server/server/get-route-from-entrypoint'\n\nexport type PagesManifest = { [page: string]: string }\n\n// This plugin creates a pages-manifest.json from page entrypoints.\n// This is used for mapping paths like `/` to `.next/server/static/<buildid>/pages/index.js` when doing SSR\n// It's also used by next export to provide defaultPathMap\nexport default class PagesManifestPlugin implements webpack.Plugin {\n  serverless: boolean\n  dev: boolean\n\n  constructor({ serverless, dev }: { serverless: boolean; dev: boolean }) {\n    this.serverless = serverless\n    this.dev = dev\n  }\n\n  createAssets(compilation: any, assets: any) {\n    const entrypoints = compilation.entrypoints\n    const pages: PagesManifest = {}\n\n    for (const entrypoint of entrypoints.values()) {\n      const pagePath = getRouteFromEntrypoint(entrypoint.name)\n\n      if (!pagePath) {\n        continue\n      }\n\n      const files = entrypoint\n        .getFiles()\n        .filter(\n          (file: string) =>\n            !file.includes('webpack-runtime') && file.endsWith('.js')\n        )\n\n      if (!isWebpack5 && files.length > 1) {\n        console.log(\n          `Found more than one file in server entrypoint ${entrypoint.name}`,\n          files\n        )\n        continue\n      }\n\n      // Write filename, replace any backslashes in path (on windows) with forwardslashes for cross-platform consistency.\n      pages[pagePath] = files[files.length - 1]\n\n      if (isWebpack5 && !this.dev) {\n        pages[pagePath] = pages[pagePath].slice(3)\n      }\n      pages[pagePath] = pages[pagePath].replace(/\\\\/g, '/')\n    }\n\n    assets[\n      `${isWebpack5 && !this.dev ? '../' : ''}` + PAGES_MANIFEST\n    ] = new sources.RawSource(JSON.stringify(pages, null, 2))\n  }\n\n  apply(compiler: webpack.Compiler): void {\n    if (isWebpack5) {\n      compiler.hooks.make.tap('NextJsPagesManifest', (compilation) => {\n        // @ts-ignore TODO: Remove ignore when webpack 5 is stable\n        compilation.hooks.processAssets.tap(\n          {\n            name: 'NextJsPagesManifest',\n            // @ts-ignore TODO: Remove ignore when webpack 5 is stable\n            stage: webpack.Compilation.PROCESS_ASSETS_STAGE_ADDITIONS,\n          },\n          (assets: any) => {\n            this.createAssets(compilation, assets)\n          }\n        )\n      })\n      return\n    }\n\n    compiler.hooks.emit.tap('NextJsPagesManifest', (compilation: any) => {\n      this.createAssets(compilation, compilation.assets)\n    })\n  }\n}\n"]}