{"version": 3, "sources": ["../../../next-server/server/render.tsx"], "names": ["noRouter", "message", "Error", "ServerRouter", "constructor", "pathname", "query", "as", "<PERSON><PERSON><PERSON><PERSON>", "isReady", "basePath", "locale", "locales", "defaultLocale", "domainLocales", "isPreview", "isLocaleDomain", "route", "<PERSON><PERSON><PERSON>", "events", "replace", "push", "reload", "back", "prefetch", "beforePopState", "enhanceComponents", "options", "App", "Component", "enhanceApp", "enhanceComponent", "renderDocument", "Document", "buildManifest", "doc<PERSON><PERSON><PERSON><PERSON><PERSON>ed", "props", "docProps", "buildId", "canonicalBase", "assetPrefix", "runtimeConfig", "nextExport", "autoExport", "dynamicImportsIds", "dangerousAsPath", "err", "dev", "ampPath", "ampState", "inAmpMode", "hybridAmp", "dynamicImports", "headTags", "gsp", "gssp", "customServer", "gip", "appGip", "unstable_runtimeJS", "unstable_JsPreload", "devOnlyCacheBusterQueryString", "<PERSON><PERSON><PERSON><PERSON>", "disableOptimizedLoading", "__NEXT_DATA__", "page", "undefined", "dynamicIds", "length", "serializeError", "isDevelopment", "invalidKeysMsg", "methodName", "<PERSON><PERSON><PERSON><PERSON>", "join", "checkRedirectValues", "redirect", "req", "method", "destination", "permanent", "statusCode", "errors", "hasStatusCode", "hasPermanent", "allowedStatusCodes", "has", "destinationType", "basePathType", "url", "renderToHTML", "res", "renderOpts", "Date", "now", "Object", "assign", "pageConfig", "fontManifest", "reactLoadableManifest", "ErrorDebug", "getStaticProps", "getStaticPaths", "getServerSideProps", "isDataReq", "params", "previewProps", "getFontDefinition", "callMiddleware", "args", "results", "middlewareFunc", "default", "curResults", "result", "__<PERSON><PERSON><PERSON><PERSON>", "__next<PERSON><PERSON><PERSON>", "__nextDefaultLocale", "isSSG", "isBuildTimeSSG", "defaultAppGetInitialProps", "getInitialProps", "origGetInitialProps", "hasPageGetInitialProps", "pageIsDynamic", "isAutoExport", "GSSP_COMPONENT_MEMBER_ERROR", "SSG_GET_INITIAL_PROPS_CONFLICT", "SERVER_PROPS_GET_INIT_PROPS_CONFLICT", "SERVER_PROPS_SSG_CONFLICT", "resolvedAsPath", "isValidElementType", "require", "amp", "endsWith", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "STATIC_STATUS_PAGES", "includes", "Loadable", "preloadAll", "previewData", "routerIsReady", "router", "__nextIsLocaleDomain", "ctx", "AppTree", "ampFirs<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "hybrid", "reactLoadableModules", "head", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "updateHead", "state", "updateScripts", "scripts", "mountedInstances", "Set", "moduleName", "__N_PREVIEW", "STATIC_PROPS_ID", "data", "preview", "staticPropsError", "code", "GSP_NO_RETURNED_VALUE", "keys", "filter", "key", "UNSTABLE_REVALIDATE_RENAME_ERROR", "process", "env", "NODE_ENV", "notFound", "isNotFound", "__N_REDIRECT", "__N_REDIRECT_STATUS", "__N_REDIRECT_BASE_PATH", "isRedirect", "revalidate", "Number", "isInteger", "Math", "ceil", "console", "warn", "JSON", "stringify", "pageProps", "pageData", "SERVER_PROPS_ID", "resolvedUrl", "serverSidePropsError", "GSSP_NO_RETURNED_VALUE", "unstable_notFound", "unstable_redirect", "dataFetchError", "error", "filteredBuildManifest", "pages", "lowPriorityFiles", "f", "renderPage", "html", "EnhancedApp", "EnhancedComponent", "documentCtx", "mod", "manifestItem", "add", "id", "files", "for<PERSON>ach", "item", "__nextStrippedLocale", "Array", "from", "nonRenderedComponents", "expectedDocComponents", "comp", "plural", "missingComponentList", "map", "e", "ampRenderIndex", "indexOf", "AMP_RENDER_TARGET", "substring", "ampOptimizerConfig", "ampSkipValidation", "ampValidator", "__NEXT_OPTIMIZE_FONTS", "__NEXT_OPTIMIZE_IMAGES", "optimizeFonts", "optimizeImages", "optimizeCss", "Critters", "cssOptimizer", "ssrMode", "reduceInlineStyles", "path", "distDir", "publicPath", "preload", "fonts", "errorToJSON", "name", "stack"], "mappings": "uEAEA,oDACA,wCACA,2CAEA,8CAUA,oEAEA,+BA<PERSON>,8CACA,4CAMA,iCACA,+DACA,iEACA,wDACA,yDACA,wEACA,oDAEA,yDACA,mCAYA,qCAKA,4DACA,uCAEA,wDACA,mEACA,8D,mFAOA,QAASA,CAAAA,QAAT,EAAoB,CAClB,KAAMC,CAAAA,OAAO,CACX,qJADF,CAEA,KAAM,IAAIC,CAAAA,KAAJ,CAAUD,OAAV,CAAN,CACD,CAED,KAAME,CAAAA,YAAmC,CAevC;AAGAC,WAAW,CACTC,QADS,CAETC,KAFS,CAGTC,EAHS,CAIT,CAAEC,UAAF,CAJS,CAKTC,OALS,CAMTC,QANS,CAOTC,MAPS,CAQTC,OARS,CASTC,aATS,CAUTC,aAVS,CAWTC,SAXS,CAYTC,cAZS,CAaT,MA9BFC,KA8BE,aA7BFZ,QA6BE,aA5BFC,KA4BE,aA3BFY,MA2BE,aA1BFR,QA0BE,aAzBFS,MAyBE,aAxBFX,UAwBE,aAvBFG,MAuBE,aAtBFF,OAsBE,aArBFG,OAqBE,aApBFC,aAoBE,aAnBFC,aAmBE,aAlBFC,SAkBE,aAjBFC,cAiBE,QACA,KAAKC,KAAL,CAAaZ,QAAQ,CAACe,OAAT,CAAiB,KAAjB,CAAwB,EAAxB,GAA+B,GAA5C,CACA,KAAKf,QAAL,CAAgBA,QAAhB,CACA,KAAKC,KAAL,CAAaA,KAAb,CACA,KAAKY,MAAL,CAAcX,EAAd,CACA,KAAKC,UAAL,CAAkBA,UAAlB,CACA,KAAKE,QAAL,CAAgBA,QAAhB,CACA,KAAKC,MAAL,CAAcA,MAAd,CACA,KAAKC,OAAL,CAAeA,OAAf,CACA,KAAKC,aAAL,CAAqBA,aAArB,CACA,KAAKJ,OAAL,CAAeA,OAAf,CACA,KAAKK,aAAL,CAAqBA,aAArB,CACA,KAAKC,SAAL,CAAiB,CAAC,CAACA,SAAnB,CACA,KAAKC,cAAL,CAAsB,CAAC,CAACA,cAAxB,CACD,CAEDK,IAAI,EAAQ,CACVrB,QAAQ,GACT,CACDoB,OAAO,EAAQ,CACbpB,QAAQ,GACT,CACDsB,MAAM,EAAG,CACPtB,QAAQ,GACT,CACDuB,IAAI,EAAG,CACLvB,QAAQ,GACT,CACDwB,QAAQ,EAAQ,CACdxB,QAAQ,GACT,CACDyB,cAAc,EAAG,CACfzB,QAAQ,GACT,CAhEsC,CAAnCG,Y,CAgBGgB,M,CAAsB,mB,CAmD/B,QAASO,CAAAA,iBAAT,CACEC,OADF,CAEEC,GAFF,CAGEC,SAHF,CAOE,CACA;AACA,GAAI,MAAOF,CAAAA,OAAP,GAAmB,UAAvB,CAAmC,CACjC,MAAO,CACLC,GADK,CAELC,SAAS,CAAEF,OAAO,CAACE,SAAD,CAFb,CAAP,CAID,CAED,MAAO,CACLD,GAAG,CAAED,OAAO,CAACG,UAAR,CAAqBH,OAAO,CAACG,UAAR,CAAmBF,GAAnB,CAArB,CAA+CA,GAD/C,CAELC,SAAS,CAAEF,OAAO,CAACI,gBAAR,CACPJ,OAAO,CAACI,gBAAR,CAAyBF,SAAzB,CADO,CAEPA,SAJC,CAAP,CAMD,CAsCD,QAASG,CAAAA,cAAT,CACEC,QADF,CAEE,CACEC,aADF,CAEEC,qBAFF,CAGEC,KAHF,CAIEC,QAJF,CAKEhC,QALF,CAMEC,KANF,CAOEgC,OAPF,CAQEC,aARF,CASEC,WATF,CAUEC,aAVF,CAWEC,UAXF,CAYEC,UAZF,CAaEnC,UAbF,CAcEoC,iBAdF,CAeEC,eAfF,CAgBEC,GAhBF,CAiBEC,GAjBF,CAkBEC,OAlBF,CAmBEC,QAnBF,CAoBEC,SApBF,CAqBEC,SArBF,CAsBEC,cAtBF,CAuBEC,QAvBF,CAwBEC,GAxBF,CAyBEC,IAzBF,CA0BEC,YA1BF,CA2BEC,GA3BF,CA4BEC,MA5BF,CA6BEC,kBA7BF,CA8BEC,kBA9BF,CA+BEC,6BA/BF,CAgCEC,YAhCF,CAiCEnD,MAjCF,CAkCEC,OAlCF,CAmCEC,aAnCF,CAoCEC,aApCF,CAqCEC,SArCF,CAsCEgD,uBAtCF,CAFF,CAkEU,CACR,MACE,kBACA,8CACE,6BAAC,2BAAD,CAAiB,QAAjB,EAA0B,KAAK,CAAEd,QAAjC,EACGhB,QAAQ,CAACD,cAAT,CAAwBC,QAAxB,CAAkC,CACjC+B,aAAa,CAAE,CACb5B,KADa,CACN;AACP6B,IAAI,CAAE5D,QAFO,CAEG;AAChBC,KAHa,CAGN;AACPgC,OAJa,CAIJ;AACTE,WAAW,CAAEA,WAAW,GAAK,EAAhB,CAAqB0B,SAArB,CAAiC1B,WALjC,CAK8C;AAC3DC,aANa,CAME;AACfC,UAPa,CAOD;AACZC,UARa,CAQD;AACZnC,UATa,CAUb2D,UAAU,CACRvB,iBAAiB,CAACwB,MAAlB,GAA6B,CAA7B,CAAiCF,SAAjC,CAA6CtB,iBAXlC,CAYbE,GAAG,CAAEA,GAAG,CAAGuB,cAAc,CAACtB,GAAD,CAAMD,GAAN,CAAjB,CAA8BoB,SAZzB,CAYoC;AACjDZ,GAba,CAaR;AACLC,IAda,CAcP;AACNC,YAfa,CAeC;AACdC,GAhBa,CAgBR;AACLC,MAjBa,CAiBL;AACR/C,MAlBa,CAmBbC,OAnBa,CAoBbC,aApBa,CAqBbC,aArBa,CAsBbC,SAtBa,CADkB,CAyBjCmB,aAzBiC,CA0BjCC,qBA1BiC,CA2BjCU,eA3BiC,CA4BjCN,aA5BiC,CA6BjCS,OA7BiC,CA8BjCE,SA9BiC,CA+BjCoB,aAAa,CAAE,CAAC,CAACvB,GA/BgB,CAgCjCI,SAhCiC,CAiCjCC,cAjCiC,CAkCjCZ,WAlCiC,CAmCjCa,QAnCiC,CAoCjCM,kBApCiC,CAqCjCC,kBArCiC,CAsCjCC,6BAtCiC,CAuCjCC,YAvCiC,CAwCjCnD,MAxCiC,CAyCjCoD,uBAzCiC,CA0CjC,GAAG1B,QA1C8B,CAAlC,CADH,CADF,CAFF,CAmDD,CAED,KAAMkC,CAAAA,cAAc,CAAG,CAACC,UAAD,CAAqBC,WAArB,GAA+C,CACpE,MACG,wCAAuCD,UAAW,0FAAnD,CACC,+DADD,CAEC,mCAAkCC,WAAW,CAACC,IAAZ,CAAiB,IAAjB,CAAuB,GAF1D,CAGC,4EAJH,CAMD,CAPD,CASA,QAASC,CAAAA,mBAAT,CACEC,QADF,CAEEC,GAFF,CAGEC,MAHF,CAIE,CACA,KAAM,CAAEC,WAAF,CAAeC,SAAf,CAA0BC,UAA1B,CAAsCvE,QAAtC,EAAmDkE,QAAzD,CACA,GAAIM,CAAAA,MAAgB,CAAG,EAAvB,CAEA,KAAMC,CAAAA,aAAa,CAAG,MAAOF,CAAAA,UAAP,GAAsB,WAA5C,CACA,KAAMG,CAAAA,YAAY,CAAG,MAAOJ,CAAAA,SAAP,GAAqB,WAA1C,CAEA,GAAII,YAAY,EAAID,aAApB,CAAmC,CACjCD,MAAM,CAAC7D,IAAP,CAAa,2DAAb,EACD,CAFD,IAEO,IAAI+D,YAAY,EAAI,MAAOJ,CAAAA,SAAP,GAAqB,SAAzC,CAAoD,CACzDE,MAAM,CAAC7D,IAAP,CAAa,6CAAb,EACD,CAFM,IAEA,IAAI8D,aAAa,EAAI,CAACE,qCAAmBC,GAAnB,CAAuBL,UAAvB,CAAtB,CAA2D,CAChEC,MAAM,CAAC7D,IAAP,CACG,2CAA0C,CAAC,GAAGgE,oCAAJ,EAAwBX,IAAxB,CACzC,IADyC,CAEzC,EAHJ,EAKD,CACD,KAAMa,CAAAA,eAAe,CAAG,MAAOR,CAAAA,WAA/B,CAEA,GAAIQ,eAAe,GAAK,QAAxB,CAAkC,CAChCL,MAAM,CAAC7D,IAAP,CACG,iDAAgDkE,eAAgB,EADnE,EAGD,CAED,KAAMC,CAAAA,YAAY,CAAG,MAAO9E,CAAAA,QAA5B,CAEA,GAAI8E,YAAY,GAAK,WAAjB,EAAgCA,YAAY,GAAK,SAArD,CAAgE,CAC9DN,MAAM,CAAC7D,IAAP,CACG,yDAAwDmE,YAAa,EADxE,EAGD,CAED,GAAIN,MAAM,CAACd,MAAP,CAAgB,CAApB,CAAuB,CACrB,KAAM,IAAIlE,CAAAA,KAAJ,CACH,yCAAwC4E,MAAO,QAAOD,GAAG,CAACY,GAAI,IAA/D,CACEP,MAAM,CAACR,IAAP,CAAY,OAAZ,CADF,CAEE,IAFF,CAGG,4EAJC,CAAN,CAMD,CACF,CAEM,cAAegB,CAAAA,YAAf,CACLb,GADK,CAELc,GAFK,CAGLtF,QAHK,CAILC,KAJK,CAKLsF,UALK,CAMmB,YACxB;AACA;AACA;AACAA,UAAU,CAAC/B,6BAAX,CAA2C+B,UAAU,CAAC7C,GAAX,CACvC6C,UAAU,CAAC/B,6BAAX,EAA6C,OAAMgC,IAAI,CAACC,GAAL,EAAW,EADvB,CAEvC,EAFJ,CAIA;AACAxF,KAAK,CAAGyF,MAAM,CAACC,MAAP,CAAc,EAAd,CAAkB1F,KAAlB,CAAR,CAEA,KAAM,CACJwC,GADI,CAEJC,GAAG,CAAG,KAFF,CAGJC,OAAO,CAAG,EAHN,CAIJpB,GAJI,CAKJK,QALI,CAMJgE,UAAU,CAAG,EANT,CAOJpE,SAPI,CAQJK,aARI,CASJgE,YATI,CAUJC,qBAVI,CAWJC,UAXI,CAYJC,cAZI,CAaJC,cAbI,CAcJC,kBAdI,CAeJC,SAfI,CAgBJC,MAhBI,CAiBJC,YAjBI,CAkBJhG,QAlBI,CAmBJmD,6BAnBI,EAoBF+B,UApBJ,CAsBA,KAAMe,CAAAA,iBAAiB,CAAIlB,GAAD,EAAyB,CACjD,GAAIS,YAAJ,CAAkB,CAChB,MAAO,6CAA8BT,GAA9B,CAAmCS,YAAnC,CAAP,CACD,CACD,MAAO,EAAP,CACD,CALD,CAOA,KAAMU,CAAAA,cAAc,CAAG,MAAO9B,MAAP,CAAuB+B,IAAvB,CAAoCzE,KAAK,CAAG,KAA5C,GAAsD,CAC3E,GAAI0E,CAAAA,OAAY,CAAG1E,KAAK,CAAG,EAAH,CAAQ,EAAhC,CAEA,GAAKH,QAAD,CAAmB,GAAE6C,MAAO,YAA5B,CAAJ,CAA8C,CAC5C,GAAIiC,CAAAA,cAAc,CAAG,KAAO9E,CAAAA,QAAD,CAAmB,GAAE6C,MAAO,YAA5B,CAA3B,CACAiC,cAAc,CAAGA,cAAc,CAACC,OAAf,EAA0BD,cAA3C,CAEA,KAAME,CAAAA,UAAU,CAAG,KAAMF,CAAAA,cAAc,CAAC,GAAGF,IAAJ,CAAvC,CACA,GAAIzE,KAAJ,CAAW,CACT,IAAK,KAAM8E,CAAAA,MAAX,GAAqBD,CAAAA,UAArB,CAAiC,CAC/BH,OAAO,CAAG,CACR,GAAGA,OADK,CAER,GAAGI,MAFK,CAAV,CAID,CACF,CAPD,IAOO,CACLJ,OAAO,CAAGG,UAAV,CACD,CACF,CACD,MAAOH,CAAAA,OAAP,CACD,CApBD,CAsBA,KAAMzD,CAAAA,QAAQ,CAAG,CAAC,GAAGwD,IAAJ,GAAkBD,cAAc,CAAC,UAAD,CAAaC,IAAb,CAAjD,CAEA,KAAMrG,CAAAA,UAAU,CAAG,CAAC,CAACF,KAAK,CAAC6G,cAA3B,CACA,MAAO7G,CAAAA,KAAK,CAAC6G,cAAb,CACA,MAAO7G,CAAAA,KAAK,CAAC8G,YAAb,CACA,MAAO9G,CAAAA,KAAK,CAAC+G,mBAAb,CAEA,KAAMC,CAAAA,KAAK,CAAG,CAAC,CAACjB,cAAhB,CACA,KAAMkB,CAAAA,cAAc,CAAGD,KAAK,EAAI1B,UAAU,CAAClD,UAA3C,CACA,KAAM8E,CAAAA,yBAAyB,CAC7B5F,GAAG,CAAC6F,eAAJ,GAAyB7F,GAAD,CAAa8F,mBADvC,CAGA,KAAMC,CAAAA,sBAAsB,CAAG,CAAC,CAAE9F,SAAD,CAAmB4F,eAApD,CAEA,KAAMG,CAAAA,aAAa,CAAG,8BAAevH,QAAf,CAAtB,CAEA,KAAMwH,CAAAA,YAAY,CAChB,CAACF,sBAAD,EACAH,yBADA,EAEA,CAACF,KAFD,EAGA,CAACf,kBAJH,CAMA,IAAK,KAAM/B,CAAAA,UAAX,GAAyB,CACvB,gBADuB,CAEvB,oBAFuB,CAGvB,gBAHuB,CAAzB,CAIG,CACD,GAAK3C,SAAD,CAAmB2C,UAAnB,CAAJ,CAAoC,CAClC,KAAM,IAAItE,CAAAA,KAAJ,CACH,QAAOG,QAAS,IAAGmE,UAAW,IAAGsD,sCAA4B,EAD1D,CAAN,CAGD,CACF,CAED,GAAIH,sBAAsB,EAAIL,KAA9B,CAAqC,CACnC,KAAM,IAAIpH,CAAAA,KAAJ,CAAU6H,0CAAkC,IAAG1H,QAAS,EAAxD,CAAN,CACD,CAED,GAAIsH,sBAAsB,EAAIpB,kBAA9B,CAAkD,CAChD,KAAM,IAAIrG,CAAAA,KAAJ,CAAU8H,gDAAwC,IAAG3H,QAAS,EAA9D,CAAN,CACD,CAED,GAAIkG,kBAAkB,EAAIe,KAA1B,CAAiC,CAC/B,KAAM,IAAIpH,CAAAA,KAAJ,CAAU+H,qCAA6B,IAAG5H,QAAS,EAAnD,CAAN,CACD,CAED,GAAIiG,cAAc,EAAI,CAACsB,aAAvB,CAAsC,CACpC,KAAM,IAAI1H,CAAAA,KAAJ,CACH,0EAAyEG,QAAS,IAAnF,CACG,gFAFC,CAAN,CAID,CAED,GAAI,CAAC,CAACiG,cAAF,EAAoB,CAACgB,KAAzB,CAAgC,CAC9B,KAAM,IAAIpH,CAAAA,KAAJ,CACH,wDAAuDG,QAAS,uDAD7D,CAAN,CAGD,CAED,GAAIiH,KAAK,EAAIM,aAAT,EAA0B,CAACtB,cAA/B,CAA+C,CAC7C,KAAM,IAAIpG,CAAAA,KAAJ,CACH,wEAAuEG,QAAS,IAAjF,CACG,4EAFC,CAAN,CAID,CAED,GAAIa,CAAAA,MAAc,CAAG0E,UAAU,CAACsC,cAAX,EAA8BrD,GAAG,CAACY,GAAvD,CAEA,GAAI1C,GAAJ,CAAS,CACP,KAAM,CAAEoF,kBAAF,EAAyBC,OAAO,CAAC,UAAD,CAAtC,CACA,GAAI,CAACD,kBAAkB,CAACtG,SAAD,CAAvB,CAAoC,CAClC,KAAM,IAAI3B,CAAAA,KAAJ,CACH,yDAAwDG,QAAS,GAD9D,CAAN,CAGD,CAED,GAAI,CAAC8H,kBAAkB,CAACvG,GAAD,CAAvB,CAA8B,CAC5B,KAAM,IAAI1B,CAAAA,KAAJ,CACH,8DADG,CAAN,CAGD,CAED,GAAI,CAACiI,kBAAkB,CAAClG,QAAD,CAAvB,CAAmC,CACjC,KAAM,IAAI/B,CAAAA,KAAJ,CACH,mEADG,CAAN,CAGD,CAED,GAAI2H,YAAY,EAAIrH,UAApB,CAAgC,CAC9B;AACAF,KAAK,CAAG,CACN,IAAIA,KAAK,CAAC+H,GAAN,CACA,CACEA,GAAG,CAAE/H,KAAK,CAAC+H,GADb,CADA,CAIA,EAJJ,CADM,CAAR,CAOAnH,MAAM,CAAI,GAAEb,QAAS,GACnB;AACAwE,GAAG,CAACY,GAAJ,CAAS6C,QAAT,CAAkB,GAAlB,GAA0BjI,QAAQ,GAAK,GAAvC,EAA8C,CAACuH,aAA/C,CAA+D,GAA/D,CAAqE,EACtE,EAHD,CAIA/C,GAAG,CAACY,GAAJ,CAAUpF,QAAV,CACD,CAED,GAAIA,QAAQ,GAAK,MAAb,GAAwBsH,sBAAsB,EAAIpB,kBAAlD,CAAJ,CAA2E,CACzE,KAAM,IAAIrG,CAAAA,KAAJ,CACH,iBAAgBqI,qDAA2C,EADxD,CAAN,CAGD,CACD,GACEC,gCAAoBC,QAApB,CAA6BpI,QAA7B,IACCsH,sBAAsB,EAAIpB,kBAD3B,CADF,CAGE,CACA,KAAM,IAAIrG,CAAAA,KAAJ,CACH,UAASG,QAAS,MAAKkI,qDAA2C,EAD/D,CAAN,CAGD,CACF,CAED,KAAMG,mBAASC,UAAT,EAAN,CAA4B;AAE5B,GAAI5H,CAAAA,SAAJ,CACA,GAAI6H,CAAAA,WAAJ,CAEA,GAAI,CAACtB,KAAK,EAAIf,kBAAV,GAAiC,CAAC/F,UAAtC,CAAkD,CAChD;AACA;AACA;AACAoI,WAAW,CAAG,gCAAkB/D,GAAlB,CAAuBc,GAAvB,CAA4Be,YAA5B,CAAd,CACA3F,SAAS,CAAG6H,WAAW,GAAK,KAA5B,CACD,CAED;AACA,KAAMC,CAAAA,aAAa,CAAG,CAAC,EAAEtC,kBAAkB,EAAIoB,sBAAxB,CAAvB,CACA,KAAMmB,CAAAA,MAAM,CAAG,GAAI3I,CAAAA,YAAJ,CACbE,QADa,CAEbC,KAFa,CAGbY,MAHa,CAIb,CACEV,UAAU,CAAEA,UADd,CAJa,CAObqI,aAPa,CAQbnI,QARa,CASbkF,UAAU,CAACjF,MATE,CAUbiF,UAAU,CAAChF,OAVE,CAWbgF,UAAU,CAAC/E,aAXE,CAYb+E,UAAU,CAAC9E,aAZE,CAabC,SAba,CAcZ8D,GAAD,CAAakE,oBAdA,CAAf,CAgBA,KAAMC,CAAAA,GAAG,CAAG,CACVlG,GADU,CAEV+B,GAAG,CAAEgD,YAAY,CAAG3D,SAAH,CAAeW,GAFtB,CAGVc,GAAG,CAAEkC,YAAY,CAAG3D,SAAH,CAAeyB,GAHtB,CAIVtF,QAJU,CAKVC,KALU,CAMVY,MANU,CAOVP,MAAM,CAAEiF,UAAU,CAACjF,MAPT,CAQVC,OAAO,CAAEgF,UAAU,CAAChF,OARV,CASVC,aAAa,CAAE+E,UAAU,CAAC/E,aAThB,CAUVoI,OAAO,CAAG7G,KAAD,EAAgB,CACvB,mBACE,6BAAC,YAAD,mBACE,6BAAC,GAAD,kBAASA,KAAT,EAAgB,SAAS,CAAEP,SAA3B,CAAsC,MAAM,CAAEiH,MAA9C,GADF,CADF,CAKD,CAhBS,CAAZ,CAkBA,GAAI1G,CAAAA,KAAJ,CAEA,KAAMa,CAAAA,QAAQ,CAAG,CACfiG,QAAQ,CAAEjD,UAAU,CAACoC,GAAX,GAAmB,IADd,CAEfc,QAAQ,CAAEC,OAAO,CAAC9I,KAAK,CAAC+H,GAAP,CAFF,CAGfgB,MAAM,CAAEpD,UAAU,CAACoC,GAAX,GAAmB,QAHZ,CAAjB,CAMA,KAAMnF,CAAAA,SAAS,CAAG,qBAAYD,QAAZ,CAAlB,CAEA,KAAMqG,CAAAA,oBAA8B,CAAG,EAAvC,CAEA,GAAIC,CAAAA,IAAmB,CAAG,sBAAYrG,SAAZ,CAA1B,CAEA,GAAIY,CAAAA,YAAiB,CAAG,EAAxB,CAEA,KAAM0F,CAAAA,YAAY,CAAG,CAAC,CAAEC,QAAF,CAAD,gBACnB,6BAAC,4BAAD,CAAe,QAAf,EAAwB,KAAK,CAAEX,MAA/B,eACE,6BAAC,2BAAD,CAAiB,QAAjB,EAA0B,KAAK,CAAE7F,QAAjC,eACE,6BAAC,sCAAD,CAAoB,QAApB,EACE,KAAK,CAAE,CACLyG,UAAU,CAAGC,KAAD,EAAW,CACrBJ,IAAI,CAAGI,KAAP,CACD,CAHI,CAILC,aAAa,CAAGC,OAAD,EAAa,CAC1B/F,YAAY,CAAG+F,OAAf,CACD,CANI,CAOLA,OAAO,CAAE,EAPJ,CAQLC,gBAAgB,CAAE,GAAIC,CAAAA,GAAJ,EARb,CADT,eAYE,6BAAC,gCAAD,CAAiB,QAAjB,EACE,KAAK,CAAGC,UAAD,EAAgBV,oBAAoB,CAACjI,IAArB,CAA0B2I,UAA1B,CADzB,EAGGP,QAHH,CAZF,CADF,CADF,CADF,CAyBA,GAAI,CACFrH,KAAK,CAAG,KAAM,+BAAoBR,GAApB,CAAyB,CACrCqH,OAAO,CAAED,GAAG,CAACC,OADwB,CAErCpH,SAFqC,CAGrCiH,MAHqC,CAIrCE,GAJqC,CAAzB,CAAd,CAOA,GAAI,CAAC1B,KAAK,EAAIf,kBAAV,GAAiCxF,SAArC,CAAgD,CAC9CqB,KAAK,CAAC6H,WAAN,CAAoB,IAApB,CACD,CAED,GAAI3C,KAAJ,CAAW,CACTlF,KAAK,CAAC8H,2BAAD,CAAL,CAAyB,IAAzB,CACD,CAED,GAAI5C,KAAK,EAAI,CAAC9G,UAAd,CAA0B,CACxB,GAAI2J,CAAAA,IAAJ,CAEA,GAAI,CACFA,IAAI,CAAG,KAAM9D,CAAAA,cAAc,CAAE,CAC3B,IAAIuB,aAAa,CAAG,CAAEnB,MAAM,CAAEnG,KAAV,CAAH,CAAyC4D,SAA1D,CAD2B,CAE3B,IAAInD,SAAS,CACT,CAAEqJ,OAAO,CAAE,IAAX,CAAiBxB,WAAW,CAAEA,WAA9B,CADS,CAET1E,SAFJ,CAF2B,CAK3BtD,OAAO,CAAEgF,UAAU,CAAChF,OALO,CAM3BD,MAAM,CAAEiF,UAAU,CAACjF,MANQ,CAO3BE,aAAa,CAAE+E,UAAU,CAAC/E,aAPC,CAAF,CAA3B,CASD,CAAC,MAAOwJ,gBAAP,CAAyB,CACzB;AACA;AACA,GAAIA,gBAAgB,CAACC,IAAjB,GAA0B,QAA9B,CAAwC,CACtC,MAAOD,CAAAA,gBAAgB,CAACC,IAAxB,CACD,CACD,KAAMD,CAAAA,gBAAN,CACD,CAED,GAAIF,IAAI,EAAI,IAAZ,CAAkB,CAChB,KAAM,IAAIjK,CAAAA,KAAJ,CAAUqK,gCAAV,CAAN,CACD,CAED,KAAM9F,CAAAA,WAAW,CAAGsB,MAAM,CAACyE,IAAP,CAAYL,IAAZ,EAAkBM,MAAlB,CACjBC,GAAD,EACEA,GAAG,GAAK,YAAR,EACAA,GAAG,GAAK,OADR,EAEAA,GAAG,GAAK,UAFR,EAGAA,GAAG,GAAK,UALQ,CAApB,CAQA,GAAIjG,WAAW,CAACgE,QAAZ,CAAqB,qBAArB,CAAJ,CAAiD,CAC/C,KAAM,IAAIvI,CAAAA,KAAJ,CAAUyK,2CAAV,CAAN,CACD,CAED,GAAIlG,WAAW,CAACL,MAAhB,CAAwB,CACtB,KAAM,IAAIlE,CAAAA,KAAJ,CAAUqE,cAAc,CAAC,gBAAD,CAAmBE,WAAnB,CAAxB,CAAN,CACD,CAED,GAAImG,OAAO,CAACC,GAAR,CAAYC,QAAZ,GAAyB,YAA7B,CAA2C,CACzC,GACE,MAAQX,CAAAA,IAAD,CAAcY,QAArB,GAAkC,WAAlC,EACA,MAAQZ,CAAAA,IAAD,CAAcvF,QAArB,GAAkC,WAFpC,CAGE,CACA,KAAM,IAAI1E,CAAAA,KAAJ,CACH,+DACCoH,KAAK,CAAG,gBAAH,CAAsB,oBAC5B,4BAA2BjH,QAAS,sFAHjC,CAAN,CAKD,CACF,CAED,GAAI,YAAc8J,CAAAA,IAAd,EAAsBA,IAAI,CAACY,QAA/B,CAAyC,CACvC,GAAI1K,QAAQ,GAAK,MAAjB,CAAyB,CACvB,KAAM,IAAIH,CAAAA,KAAJ,CACH,0FADG,CAAN,CAGD,CAED,CAAE0F,UAAD,CAAoBoF,UAApB,CAAiC,IAAjC,CACF,CAED,GACE,YAAcb,CAAAA,IAAd,EACAA,IAAI,CAACvF,QADL,EAEA,MAAOuF,CAAAA,IAAI,CAACvF,QAAZ,GAAyB,QAH3B,CAIE,CACAD,mBAAmB,CAACwF,IAAI,CAACvF,QAAN,CAA4BC,GAA5B,CAAiC,gBAAjC,CAAnB,CAEA,GAAI0C,cAAJ,CAAoB,CAClB,KAAM,IAAIrH,CAAAA,KAAJ,CACH,6EAA4E2E,GAAG,CAACY,GAAI,KAArF,CACG,oFAFC,CAAN,CAID,CAED,CAAE0E,IAAD,CAAc/H,KAAd,CAAsB,CACrB6I,YAAY,CAAEd,IAAI,CAACvF,QAAL,CAAcG,WADP,CAErBmG,mBAAmB,CAAE,wCAAkBf,IAAI,CAACvF,QAAvB,CAFA,CAAtB,CAID,GAAI,MAAOuF,CAAAA,IAAI,CAACvF,QAAL,CAAclE,QAArB,GAAkC,WAAtC,CAAmD,CACjD,CAAEyJ,IAAD,CAAc/H,KAAd,CAAoB+I,sBAApB,CAA6ChB,IAAI,CAACvF,QAAL,CAAclE,QAA3D,CACF,CACD,CAAEkF,UAAD,CAAoBwF,UAApB,CAAiC,IAAjC,CACF,CAED,GACE,CAACrI,GAAG,EAAIwE,cAAR,GACA,CAAE3B,UAAD,CAAoBoF,UADrB,EAEA,CAAC,6CAAoB3K,QAApB,CAA8B,gBAA9B,CAAiD8J,IAAD,CAAc/H,KAA9D,CAHH,CAIE,CACA;AACA,KAAM,IAAIlC,CAAAA,KAAJ,CACJ,2EADI,CAAN,CAGD,CAED,GAAI,cAAgBiK,CAAAA,IAApB,CAA0B,CACxB,GAAI,MAAOA,CAAAA,IAAI,CAACkB,UAAZ,GAA2B,QAA/B,CAAyC,CACvC,GAAI,CAACC,MAAM,CAACC,SAAP,CAAiBpB,IAAI,CAACkB,UAAtB,CAAL,CAAwC,CACtC,KAAM,IAAInL,CAAAA,KAAJ,CACH,gFAA+E2E,GAAG,CAACY,GAAI,6BAA4B0E,IAAI,CAACkB,UAAW,oBAApI,CACG,gCAA+BG,IAAI,CAACC,IAAL,CAC9BtB,IAAI,CAACkB,UADyB,CAE9B,2DAJA,CAAN,CAMD,CAPD,IAOO,IAAIlB,IAAI,CAACkB,UAAL,EAAmB,CAAvB,CAA0B,CAC/B,KAAM,IAAInL,CAAAA,KAAJ,CACH,wEAAuE2E,GAAG,CAACY,GAAI,sHAAhF,CACG,6FADH,CAEG,sEAHC,CAAN,CAKD,CANM,IAMA,IAAI0E,IAAI,CAACkB,UAAL,CAAkB,QAAtB,CAAgC,CACrC;AACAK,OAAO,CAACC,IAAR,CACG,uEAAsE9G,GAAG,CAACY,GAAI,qCAA/E,CACG,oHAFL,EAID,CACF,CArBD,IAqBO,IAAI0E,IAAI,CAACkB,UAAL,GAAoB,IAAxB,CAA8B,CACnC;AACA;AACA;AACAlB,IAAI,CAACkB,UAAL,CAAkB,CAAlB,CACD,CALM,IAKA,IACLlB,IAAI,CAACkB,UAAL,GAAoB,KAApB,EACA,MAAOlB,CAAAA,IAAI,CAACkB,UAAZ,GAA2B,WAFtB,CAGL,CACA;AACAlB,IAAI,CAACkB,UAAL,CAAkB,KAAlB,CACD,CANM,IAMA,CACL,KAAM,IAAInL,CAAAA,KAAJ,CACH,iIAAgI0L,IAAI,CAACC,SAAL,CAC/H1B,IAAI,CAACkB,UAD0H,CAE/H,SAAQxG,GAAG,CAACY,GAAI,EAHd,CAAN,CAKD,CACF,CAxCD,IAwCO,CACL;AACA,CAAE0E,IAAD,CAAckB,UAAd,CAA2B,KAA3B,CACF,CAED;AACA,GAAKzF,UAAD,CAAoBoF,UAAxB,CAAoC,CAClC,MAAO,KAAP,CACD,CAED5I,KAAK,CAAC0J,SAAN,CAAkB/F,MAAM,CAACC,MAAP,CAChB,EADgB,CAEhB5D,KAAK,CAAC0J,SAFU,CAGhB,SAAW3B,CAAAA,IAAX,CAAkBA,IAAI,CAAC/H,KAAvB,CAA+B8B,SAHf,CAMlB;AACA;AAPA,CAQE0B,UAAD,CAAoByF,UAApB,CACC,cAAgBlB,CAAAA,IAAhB,CAAuBA,IAAI,CAACkB,UAA5B,CAAyCnH,SAD1C,CAEC0B,UAAD,CAAoBmG,QAApB,CAA+B3J,KAA/B,CACF,CAED,GAAImE,kBAAJ,CAAwB,CACtBnE,KAAK,CAAC4J,2BAAD,CAAL,CAAyB,IAAzB,CACD,CAED,GAAIzF,kBAAkB,EAAI,CAAC/F,UAA3B,CAAuC,CACrC,GAAI2J,CAAAA,IAAJ,CAEA,GAAI,CACFA,IAAI,CAAG,KAAM5D,CAAAA,kBAAkB,CAAC,CAC9B1B,GAAG,CAAEA,GADyB,CAI9Bc,GAJ8B,CAK9BrF,KAL8B,CAM9B2L,WAAW,CAAErG,UAAU,CAACqG,WANM,CAO9B,IAAIrE,aAAa,CAAG,CAAEnB,MAAM,CAAEA,MAAV,CAAH,CAA0CvC,SAA3D,CAP8B,CAQ9B,IAAI0E,WAAW,GAAK,KAAhB,CACA,CAAEwB,OAAO,CAAE,IAAX,CAAiBxB,WAAW,CAAEA,WAA9B,CADA,CAEA1E,SAFJ,CAR8B,CAW9BtD,OAAO,CAAEgF,UAAU,CAAChF,OAXU,CAY9BD,MAAM,CAAEiF,UAAU,CAACjF,MAZW,CAa9BE,aAAa,CAAE+E,UAAU,CAAC/E,aAbI,CAAD,CAA/B,CAeD,CAAC,MAAOqL,oBAAP,CAA6B,CAC7B;AACA;AACA,GAAIA,oBAAoB,CAAC5B,IAArB,GAA8B,QAAlC,CAA4C,CAC1C,MAAO4B,CAAAA,oBAAoB,CAAC5B,IAA5B,CACD,CACD,KAAM4B,CAAAA,oBAAN,CACD,CAED,GAAI/B,IAAI,EAAI,IAAZ,CAAkB,CAChB,KAAM,IAAIjK,CAAAA,KAAJ,CAAUiM,iCAAV,CAAN,CACD,CAED,KAAM1H,CAAAA,WAAW,CAAGsB,MAAM,CAACyE,IAAP,CAAYL,IAAZ,EAAkBM,MAAlB,CACjBC,GAAD,EAASA,GAAG,GAAK,OAAR,EAAmBA,GAAG,GAAK,UAA3B,EAAyCA,GAAG,GAAK,UADxC,CAApB,CAIA,GAAKP,IAAD,CAAciC,iBAAlB,CAAqC,CACnC,KAAM,IAAIlM,CAAAA,KAAJ,CACH,8FAA6FG,QAAS,EADnG,CAAN,CAGD,CACD,GAAK8J,IAAD,CAAckC,iBAAlB,CAAqC,CACnC,KAAM,IAAInM,CAAAA,KAAJ,CACH,8FAA6FG,QAAS,EADnG,CAAN,CAGD,CAED,GAAIoE,WAAW,CAACL,MAAhB,CAAwB,CACtB,KAAM,IAAIlE,CAAAA,KAAJ,CAAUqE,cAAc,CAAC,oBAAD,CAAuBE,WAAvB,CAAxB,CAAN,CACD,CAED,GAAI,YAAc0F,CAAAA,IAAd,EAAsBA,IAAI,CAACY,QAA/B,CAAyC,CACvC,GAAI1K,QAAQ,GAAK,MAAjB,CAAyB,CACvB,KAAM,IAAIH,CAAAA,KAAJ,CACH,0FADG,CAAN,CAGD,CAED,CAAE0F,UAAD,CAAoBoF,UAApB,CAAiC,IAAjC,CACD,MAAO,KAAP,CACD,CAED,GAAI,YAAcb,CAAAA,IAAd,EAAsB,MAAOA,CAAAA,IAAI,CAACvF,QAAZ,GAAyB,QAAnD,CAA6D,CAC3DD,mBAAmB,CACjBwF,IAAI,CAACvF,QADY,CAEjBC,GAFiB,CAGjB,oBAHiB,CAAnB,CAKEsF,IAAD,CAAc/H,KAAd,CAAsB,CACrB6I,YAAY,CAAEd,IAAI,CAACvF,QAAL,CAAcG,WADP,CAErBmG,mBAAmB,CAAE,wCAAkBf,IAAI,CAACvF,QAAvB,CAFA,CAAtB,CAID,GAAI,MAAOuF,CAAAA,IAAI,CAACvF,QAAL,CAAclE,QAArB,GAAkC,WAAtC,CAAmD,CACjD,CAAEyJ,IAAD,CAAc/H,KAAd,CAAoB+I,sBAApB,CAA6ChB,IAAI,CAACvF,QAAL,CAAclE,QAA3D,CACF,CACD,CAAEkF,UAAD,CAAoBwF,UAApB,CAAiC,IAAjC,CACF,CAED,GACE,CAACrI,GAAG,EAAIwE,cAAR,GACA,CAAC,6CACClH,QADD,CAEC,oBAFD,CAGE8J,IAAD,CAAc/H,KAHf,CAFH,CAOE,CACA;AACA,KAAM,IAAIlC,CAAAA,KAAJ,CACJ,+EADI,CAAN,CAGD,CAEDkC,KAAK,CAAC0J,SAAN,CAAkB/F,MAAM,CAACC,MAAP,CAAc,EAAd,CAAkB5D,KAAK,CAAC0J,SAAxB,CAAoC3B,IAAD,CAAc/H,KAAjD,CAAlB,CACEwD,UAAD,CAAoBmG,QAApB,CAA+B3J,KAA/B,CACF,CACF,CAAC,MAAOkK,cAAP,CAAuB,CACvB,GAAI9F,SAAS,EAAI,CAACzD,GAAd,EAAqB,CAACuJ,cAA1B,CAA0C,KAAMA,CAAAA,cAAN,CAC1CtD,GAAG,CAAClG,GAAJ,CAAUwJ,cAAV,CACA1G,UAAU,CAAC9C,GAAX,CAAiBwJ,cAAjB,CACAZ,OAAO,CAACa,KAAR,CAAcD,cAAd,EACD,CAED,GACE,CAAChF,KAAD,EAAU;AACV,CAACf,kBADD,EAEAqE,OAAO,CAACC,GAAR,CAAYC,QAAZ,GAAyB,YAFzB,EAGA/E,MAAM,CAACyE,IAAP,CAAY,SAAApI,KAAK,OAAL,eAAO0J,SAAP,GAAoB,EAAhC,EAAoCrD,QAApC,CAA6C,KAA7C,CAJF,CAKE,CACAiD,OAAO,CAACC,IAAR,CACG,oGAAmGtL,QAAS,IAA7G,CACG,yEAFL,EAID,CAED;AACA;AACA,GAAKmG,SAAS,EAAI,CAACc,KAAf,EAA0B1B,UAAD,CAAoBwF,UAAjD,CAA6D,CAC3D,MAAOhJ,CAAAA,KAAP,CACD,CAED;AACA;AACA,GAAI5B,UAAJ,CAAgB,CACd4B,KAAK,CAAC0J,SAAN,CAAkB,EAAlB,CACD,CAED;AACA,GAAI,qBAAUnG,GAAV,GAAkB,CAAC2B,KAAvB,CAA8B,MAAO,KAAP,CAE9B;AACA;AACA,GAAIkF,CAAAA,qBAAqB,CAAGtK,aAA5B,CACA,GAAI2F,YAAY,EAAID,aAApB,CAAmC,CACjC,KAAM3D,CAAAA,IAAI,CAAG,6CAAoB,yCAAkB5D,QAAlB,CAApB,CAAb,CACA;AACA;AACA;AACA,GAAI4D,IAAI,GAAIuI,CAAAA,qBAAqB,CAACC,KAAlC,CAAyC,CACvCD,qBAAqB,CAAG,CACtB,GAAGA,qBADmB,CAEtBC,KAAK,CAAE,CACL,GAAGD,qBAAqB,CAACC,KADpB,CAEL,CAACxI,IAAD,EAAQ,CACN,GAAGuI,qBAAqB,CAACC,KAAtB,CAA4BxI,IAA5B,CADG,CAEN,GAAGuI,qBAAqB,CAACE,gBAAtB,CAAuCjC,MAAvC,CAA+CkC,CAAD,EAC/CA,CAAC,CAAClE,QAAF,CAAW,gBAAX,CADC,CAFG,CAFH,CAFe,CAWtBiE,gBAAgB,CAAEF,qBAAqB,CAACE,gBAAtB,CAAuCjC,MAAvC,CACfkC,CAAD,EAAO,CAACA,CAAC,CAAClE,QAAF,CAAW,gBAAX,CADQ,CAXI,CAAxB,CAeD,CACF,CAED,KAAMmE,CAAAA,UAAsB,CAAG,CAC7BjL,OAA2B,CAAG,EADD,GAEG,CAChC,GAAIqH,GAAG,CAAClG,GAAJ,EAAWsD,UAAf,CAA2B,CACzB,MAAO,CAAEyG,IAAI,CAAE,wCAAe,6BAAC,UAAD,EAAY,KAAK,CAAE7D,GAAG,CAAClG,GAAvB,EAAf,CAAR,CAAwDyG,IAAxD,CAAP,CACD,CAED,GAAIxG,GAAG,GAAKX,KAAK,CAAC0G,MAAN,EAAgB1G,KAAK,CAACP,SAA3B,CAAP,CAA8C,CAC5C,KAAM,IAAI3B,CAAAA,KAAJ,CACH,wIADG,CAAN,CAGD,CAED,KAAM,CACJ0B,GAAG,CAAEkL,WADD,CAEJjL,SAAS,CAAEkL,iBAFP,EAGFrL,iBAAiB,CAACC,OAAD,CAAUC,GAAV,CAAeC,SAAf,CAHrB,CAKA,KAAMgL,CAAAA,IAAI,CAAG,wCACX,6BAAC,YAAD,mBACE,6BAAC,WAAD,gBAAa,SAAS,CAAEE,iBAAxB,CAA2C,MAAM,CAAEjE,MAAnD,EAA+D1G,KAA/D,EADF,CADW,CAAb,CAMA,MAAO,CAAEyK,IAAF,CAAQtD,IAAR,CAAP,CACD,CAzBD,CA0BA,KAAMyD,CAAAA,WAAW,CAAG,CAAE,GAAGhE,GAAL,CAAU4D,UAAV,CAApB,CACA,KAAMvK,CAAAA,QAA8B,CAAG,KAAM,+BAC3CJ,QAD2C,CAE3C+K,WAF2C,CAA7C,CAIA;AACA,GAAI,qBAAUrH,GAAV,GAAkB,CAAC2B,KAAvB,CAA8B,MAAO,KAAP,CAE9B,GAAI,CAACjF,QAAD,EAAa,MAAOA,CAAAA,QAAQ,CAACwK,IAAhB,GAAyB,QAA1C,CAAoD,CAClD,KAAM5M,CAAAA,OAAO,CAAI,IAAG,0BAClBgC,QADkB,CAElB,iGAFF,CAGA,KAAM,IAAI/B,CAAAA,KAAJ,CAAUD,OAAV,CAAN,CACD,CAED,KAAM2C,CAAAA,iBAAiB,CAAG,GAAImH,CAAAA,GAAJ,EAA1B,CACA,KAAM3G,CAAAA,cAAc,CAAG,GAAI2G,CAAAA,GAAJ,EAAvB,CAEA,IAAK,KAAMkD,CAAAA,GAAX,GAAkB3D,CAAAA,oBAAlB,CAAwC,CACtC,KAAM4D,CAAAA,YAA0B,CAAG/G,qBAAqB,CAAC8G,GAAD,CAAxD,CAEA,GAAIC,YAAJ,CAAkB,CAChBtK,iBAAiB,CAACuK,GAAlB,CAAsBD,YAAY,CAACE,EAAnC,EACAF,YAAY,CAACG,KAAb,CAAmBC,OAAnB,CAA4BC,IAAD,EAAU,CACnCnK,cAAc,CAAC+J,GAAf,CAAmBI,IAAnB,EACD,CAFD,EAGD,CACF,CAED,KAAMpK,CAAAA,SAAS,CAAGF,QAAQ,CAACoG,MAA3B,CAEA,KAAMlH,CAAAA,qBAA6D,CAAG,EAAtE,CACA,KAAMO,CAAAA,UAAU,CACd,CAAC4E,KAAD,GAAW1B,UAAU,CAAClD,UAAX,EAA0BK,GAAG,GAAK8E,YAAY,EAAIrH,UAArB,CAAxC,CADF,CAGA,GAAIqM,CAAAA,IAAI,CAAG7K,cAAc,CAACC,QAAD,CAAW,CAClC,GAAG2D,UAD+B,CAElCrD,aAAa,CACX,CAACqD,UAAU,CAAC5C,OAAZ,EAAwB6B,GAAD,CAAa2I,oBAApC,CACK,GAAE5H,UAAU,CAACrD,aAAX,EAA4B,EAAG,IAAGqD,UAAU,CAACjF,MAAO,EAD3D,CAEIiF,UAAU,CAACrD,aALiB,CAMlCJ,qBANkC,CAOlCD,aAAa,CAAEsK,qBAPmB,CAQlC;AACA7I,kBAAkB,CAChBiH,OAAO,CAACC,GAAR,CAAYC,QAAZ,GAAyB,YAAzB,CACI7E,UAAU,CAACtC,kBADf,CAEIO,SAZ4B,CAalCN,kBAAkB,CAAEqC,UAAU,CAACrC,kBAbG,CAclCf,eAAe,CAAEiG,MAAM,CAAC5H,MAdU,CAelC+B,QAfkC,CAgBlCb,KAhBkC,CAiBlCiB,QAAQ,CAAE,KAAMA,CAAAA,QAAQ,CAAC2J,WAAD,CAjBU,CAkBlCxM,UAlBkC,CAmBlC6B,QAnBkC,CAoBlChC,QApBkC,CAqBlC2C,OArBkC,CAsBlC1C,KAtBkC,CAuBlC4C,SAvBkC,CAwBlCC,SAxBkC,CAyBlCP,iBAAiB,CAAE6K,KAAK,CAACC,IAAN,CAAW9K,iBAAX,CAzBe,CA0BlCQ,cAAc,CAAEqK,KAAK,CAACC,IAAN,CAAWtK,cAAX,CA1BkB,CA2BlCE,GAAG,CAAE,CAAC,CAAC+C,cAAF,CAAmB,IAAnB,CAA0BnC,SA3BG,CA4BlCX,IAAI,CAAE,CAAC,CAACgD,kBAAF,CAAuB,IAAvB,CAA8BrC,SA5BF,CA6BlCT,GAAG,CAAEkE,sBAAsB,CAAG,IAAH,CAAUzD,SA7BH,CA8BlCR,MAAM,CAAE,CAAC8D,yBAAD,CAA6B,IAA7B,CAAoCtD,SA9BV,CA+BlCL,6BA/BkC,CAgClCC,YAhCkC,CAiClC/C,SAAS,CAAEA,SAAS,GAAK,IAAd,CAAqB,IAArB,CAA4BmD,SAjCL,CAkClCvB,UAAU,CAAEkF,YAAY,GAAK,IAAjB,CAAwB,IAAxB,CAA+B3D,SAlCT,CAmClCxB,UAAU,CAAEA,UAAU,GAAK,IAAf,CAAsB,IAAtB,CAA6BwB,SAnCP,CAAX,CAAzB,CAsCA,GAAI0G,OAAO,CAACC,GAAR,CAAYC,QAAZ,GAAyB,YAA7B,CAA2C,CACzC,KAAM6C,CAAAA,qBAAqB,CAAG,EAA9B,CACA,KAAMC,CAAAA,qBAAqB,CAAG,CAAC,MAAD,CAAS,MAAT,CAAiB,YAAjB,CAA+B,MAA/B,CAA9B,CAEA,IAAK,KAAMC,CAAAA,IAAX,GAAmBD,CAAAA,qBAAnB,CAA0C,CACxC,GAAI,CAAEzL,qBAAD,CAA+B0L,IAA/B,CAAL,CAA2C,CACzCF,qBAAqB,CAACtM,IAAtB,CAA2BwM,IAA3B,EACD,CACF,CACD,KAAMC,CAAAA,MAAM,CAAGH,qBAAqB,CAACvJ,MAAtB,GAAiC,CAAjC,CAAqC,GAArC,CAA2C,EAA1D,CAEA,GAAIuJ,qBAAqB,CAACvJ,MAA1B,CAAkC,CAChC,KAAM2J,CAAAA,oBAAoB,CAAGJ,qBAAqB,CAC/CK,GAD0B,CACrBC,CAAD,EAAQ,IAAGA,CAAE,KADS,EAE1BvJ,IAF0B,CAErB,IAFqB,CAA7B,CAGA,cACG,sFAAqFoJ,MAAO,KAA7F,CACG,oBAAmBA,MAAO,KAAIC,oBAAqB,IADtD,CAEE,mFAHJ,EAKD,CACF,CAED,GAAI7K,SAAS,EAAI2J,IAAjB,CAAuB,CACrB;AACA;AACA,KAAMqB,CAAAA,cAAc,CAAGrB,IAAI,CAACsB,OAAL,CAAaC,6BAAb,CAAvB,CACAvB,IAAI,CACFA,IAAI,CAACwB,SAAL,CAAe,CAAf,CAAkBH,cAAlB,EACC,yBAAwB7L,QAAQ,CAACwK,IAAK,EADvC,CAEAA,IAAI,CAACwB,SAAL,CAAeH,cAAc,CAAGE,8BAAkBhK,MAAlD,CAHF,CAIAyI,IAAI,CAAG,KAAM,yBAAYA,IAAZ,CAAkBjH,UAAU,CAAC0I,kBAA7B,CAAb,CAEA,GAAI,CAAC1I,UAAU,CAAC2I,iBAAZ,EAAiC3I,UAAU,CAAC4I,YAAhD,CAA8D,CAC5D,KAAM5I,CAAAA,UAAU,CAAC4I,YAAX,CAAwB3B,IAAxB,CAA8BxM,QAA9B,CAAN,CACD,CACF,CAED;AACA,GAAIuK,OAAO,CAACC,GAAR,CAAY4D,qBAAZ,EAAqC7D,OAAO,CAACC,GAAR,CAAY6D,sBAArD,CAA6E,CAC3E7B,IAAI,CAAG,KAAM,yBACXA,IADW,CAEX,CAAElG,iBAAF,CAFW,CAGX,CACEgI,aAAa,CAAE/I,UAAU,CAAC+I,aAD5B,CAEEC,cAAc,CAAEhJ,UAAU,CAACgJ,cAF7B,CAHW,CAAb,CAQD,CAED,GAAIhJ,UAAU,CAACiJ,WAAf,CAA4B,CAC1B;AACA,KAAMC,CAAAA,QAAQ,CAAG1G,OAAO,CAAC,UAAD,CAAxB,CACA,KAAM2G,CAAAA,YAAY,CAAG,GAAID,CAAAA,QAAJ,CAAa,CAChCE,OAAO,CAAE,IADuB,CAEhCC,kBAAkB,CAAE,KAFY,CAGhCC,IAAI,CAAEtJ,UAAU,CAACuJ,OAHe,CAIhCC,UAAU,CAAE,SAJoB,CAKhCC,OAAO,CAAE,OALuB,CAMhCC,KAAK,CAAE,KANyB,CAOhC,GAAG1J,UAAU,CAACiJ,WAPkB,CAAb,CAArB,CAUAhC,IAAI,CAAG,KAAMkC,CAAAA,YAAY,CAACnE,OAAb,CAAqBiC,IAArB,CAAb,CACD,CAED,GAAI3J,SAAS,EAAIC,SAAjB,CAA4B,CAC1B;AACA0J,IAAI,CAAGA,IAAI,CAACzL,OAAL,CAAa,aAAb,CAA4B,QAA5B,CAAP,CACD,CAED,MAAOyL,CAAAA,IAAP,CACD,CAED,QAAS0C,CAAAA,WAAT,CAAqBzM,GAArB,CAAwC,CACtC,KAAM,CAAE0M,IAAF,CAAQvP,OAAR,CAAiBwP,KAAjB,EAA2B3M,GAAjC,CACA,MAAO,CAAE0M,IAAF,CAAQvP,OAAR,CAAiBwP,KAAjB,CAAP,CACD,CAED,QAASpL,CAAAA,cAAT,CACEtB,GADF,CAEED,GAFF,CAGmC,CACjC,GAAIC,GAAJ,CAAS,CACP,MAAOwM,CAAAA,WAAW,CAACzM,GAAD,CAAlB,CACD,CAED,MAAO,CACL0M,IAAI,CAAE,wBADD,CAELvP,OAAO,CAAE,8BAFJ,CAGLgF,UAAU,CAAE,GAHP,CAAP,CAKD", "sourcesContent": ["import { IncomingMessage, ServerResponse } from 'http'\nimport { ParsedUrlQuery } from 'querystring'\nimport React from 'react'\nimport { renderToStaticMarkup, renderToString } from 'react-dom/server'\nimport { warn } from '../../build/output/log'\nimport { UnwrapPromise } from '../../lib/coalesced-function'\nimport {\n  GSP_NO_RETURNED_VALUE,\n  GSSP_COMPONENT_MEMBER_ERROR,\n  GSSP_NO_RETURNED_VALUE,\n  STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR,\n  SERVER_PROPS_GET_INIT_PROPS_CONFLICT,\n  SERVER_PROPS_SSG_CONFLICT,\n  SSG_GET_INITIAL_PROPS_CONFLICT,\n  UNSTABLE_REVALIDATE_RENAME_ERROR,\n} from '../../lib/constants'\nimport { isSerializableProps } from '../../lib/is-serializable-props'\nimport { GetServerSideProps, GetStaticProps, PreviewData } from '../../types'\nimport { isInAmpMode } from '../lib/amp'\nimport { AmpStateContext } from '../lib/amp-context'\nimport {\n  AMP_RENDER_TARGET,\n  SERVER_PROPS_ID,\n  STATIC_PROPS_ID,\n  STATIC_STATUS_PAGES,\n} from '../lib/constants'\nimport { defaultHead } from '../lib/head'\nimport { HeadManagerContext } from '../lib/head-manager-context'\nimport Loadable from '../lib/loadable'\nimport { LoadableContext } from '../lib/loadable-context'\nimport mitt, { MittEmitter } from '../lib/mitt'\nimport postProcess from '../lib/post-process'\nimport { RouterContext } from '../lib/router-context'\nimport { NextRouter } from '../lib/router/router'\nimport { isDynamicRoute } from '../lib/router/utils/is-dynamic'\nimport {\n  AppType,\n  ComponentsEnhancer,\n  DocumentInitialProps,\n  DocumentProps,\n  DocumentType,\n  getDisplayName,\n  isResSent,\n  loadGetInitialProps,\n  NextComponentType,\n  RenderPage,\n} from '../lib/utils'\nimport {\n  tryGetPreviewData,\n  NextApiRequestCookies,\n  __ApiPreviewProps,\n} from './api-utils'\nimport { denormalizePagePath } from './denormalize-page-path'\nimport { FontManifest, getFontDefinitionFromManifest } from './font-utils'\nimport { LoadComponentsReturnType, ManifestItem } from './load-components'\nimport { normalizePagePath } from './normalize-page-path'\nimport optimizeAmp from './optimize-amp'\nimport {\n  allowedStatusCodes,\n  getRedirectStatus,\n  Redirect,\n} from '../../lib/load-custom-routes'\nimport { DomainLocales } from './config'\n\nfunction noRouter() {\n  const message =\n    'No router instance found. you should only use \"next/router\" inside the client side of your app. https://nextjs.org/docs/messages/no-router-instance'\n  throw new Error(message)\n}\n\nclass ServerRouter implements NextRouter {\n  route: string\n  pathname: string\n  query: ParsedUrlQuery\n  asPath: string\n  basePath: string\n  events: any\n  isFallback: boolean\n  locale?: string\n  isReady: boolean\n  locales?: string[]\n  defaultLocale?: string\n  domainLocales?: DomainLocales\n  isPreview: boolean\n  isLocaleDomain: boolean\n  // TODO: Remove in the next major version, as this would mean the user is adding event listeners in server-side `render` method\n  static events: MittEmitter = mitt()\n\n  constructor(\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    { isFallback }: { isFallback: boolean },\n    isReady: boolean,\n    basePath: string,\n    locale?: string,\n    locales?: string[],\n    defaultLocale?: string,\n    domainLocales?: DomainLocales,\n    isPreview?: boolean,\n    isLocaleDomain?: boolean\n  ) {\n    this.route = pathname.replace(/\\/$/, '') || '/'\n    this.pathname = pathname\n    this.query = query\n    this.asPath = as\n    this.isFallback = isFallback\n    this.basePath = basePath\n    this.locale = locale\n    this.locales = locales\n    this.defaultLocale = defaultLocale\n    this.isReady = isReady\n    this.domainLocales = domainLocales\n    this.isPreview = !!isPreview\n    this.isLocaleDomain = !!isLocaleDomain\n  }\n\n  push(): any {\n    noRouter()\n  }\n  replace(): any {\n    noRouter()\n  }\n  reload() {\n    noRouter()\n  }\n  back() {\n    noRouter()\n  }\n  prefetch(): any {\n    noRouter()\n  }\n  beforePopState() {\n    noRouter()\n  }\n}\n\nfunction enhanceComponents(\n  options: ComponentsEnhancer,\n  App: AppType,\n  Component: NextComponentType\n): {\n  App: AppType\n  Component: NextComponentType\n} {\n  // For backwards compatibility\n  if (typeof options === 'function') {\n    return {\n      App,\n      Component: options(Component),\n    }\n  }\n\n  return {\n    App: options.enhanceApp ? options.enhanceApp(App) : App,\n    Component: options.enhanceComponent\n      ? options.enhanceComponent(Component)\n      : Component,\n  }\n}\n\nexport type RenderOptsPartial = {\n  buildId: string\n  canonicalBase: string\n  runtimeConfig?: { [key: string]: any }\n  assetPrefix?: string\n  err?: Error | null\n  nextExport?: boolean\n  dev?: boolean\n  ampPath?: string\n  ErrorDebug?: React.ComponentType<{ error: Error }>\n  ampValidator?: (html: string, pathname: string) => Promise<void>\n  ampSkipValidation?: boolean\n  ampOptimizerConfig?: { [key: string]: any }\n  isDataReq?: boolean\n  params?: ParsedUrlQuery\n  previewProps: __ApiPreviewProps\n  basePath: string\n  unstable_runtimeJS?: false\n  unstable_JsPreload?: false\n  optimizeFonts: boolean\n  fontManifest?: FontManifest\n  optimizeImages: boolean\n  optimizeCss: any\n  devOnlyCacheBusterQueryString?: string\n  resolvedUrl?: string\n  resolvedAsPath?: string\n  distDir?: string\n  locale?: string\n  locales?: string[]\n  defaultLocale?: string\n  domainLocales?: DomainLocales\n  disableOptimizedLoading?: boolean\n}\n\nexport type RenderOpts = LoadComponentsReturnType & RenderOptsPartial\n\nfunction renderDocument(\n  Document: DocumentType,\n  {\n    buildManifest,\n    docComponentsRendered,\n    props,\n    docProps,\n    pathname,\n    query,\n    buildId,\n    canonicalBase,\n    assetPrefix,\n    runtimeConfig,\n    nextExport,\n    autoExport,\n    isFallback,\n    dynamicImportsIds,\n    dangerousAsPath,\n    err,\n    dev,\n    ampPath,\n    ampState,\n    inAmpMode,\n    hybridAmp,\n    dynamicImports,\n    headTags,\n    gsp,\n    gssp,\n    customServer,\n    gip,\n    appGip,\n    unstable_runtimeJS,\n    unstable_JsPreload,\n    devOnlyCacheBusterQueryString,\n    scriptLoader,\n    locale,\n    locales,\n    defaultLocale,\n    domainLocales,\n    isPreview,\n    disableOptimizedLoading,\n  }: RenderOpts & {\n    props: any\n    docComponentsRendered: DocumentProps['docComponentsRendered']\n    docProps: DocumentInitialProps\n    pathname: string\n    query: ParsedUrlQuery\n    dangerousAsPath: string\n    ampState: any\n    ampPath: string\n    inAmpMode: boolean\n    hybridAmp: boolean\n    dynamicImportsIds: (string | number)[]\n    dynamicImports: string[]\n    headTags: any\n    isFallback?: boolean\n    gsp?: boolean\n    gssp?: boolean\n    customServer?: boolean\n    gip?: boolean\n    appGip?: boolean\n    devOnlyCacheBusterQueryString: string\n    scriptLoader: any\n    isPreview?: boolean\n    autoExport?: boolean\n  }\n): string {\n  return (\n    '<!DOCTYPE html>' +\n    renderToStaticMarkup(\n      <AmpStateContext.Provider value={ampState}>\n        {Document.renderDocument(Document, {\n          __NEXT_DATA__: {\n            props, // The result of getInitialProps\n            page: pathname, // The rendered page\n            query, // querystring parsed / passed by the user\n            buildId, // buildId is used to facilitate caching of page bundles, we send it to the client so that pageloader knows where to load bundles\n            assetPrefix: assetPrefix === '' ? undefined : assetPrefix, // send assetPrefix to the client side when configured, otherwise don't sent in the resulting HTML\n            runtimeConfig, // runtimeConfig if provided, otherwise don't sent in the resulting HTML\n            nextExport, // If this is a page exported by `next export`\n            autoExport, // If this is an auto exported page\n            isFallback,\n            dynamicIds:\n              dynamicImportsIds.length === 0 ? undefined : dynamicImportsIds,\n            err: err ? serializeError(dev, err) : undefined, // Error if one happened, otherwise don't sent in the resulting HTML\n            gsp, // whether the page is getStaticProps\n            gssp, // whether the page is getServerSideProps\n            customServer, // whether the user is using a custom server\n            gip, // whether the page has getInitialProps\n            appGip, // whether the _app has getInitialProps\n            locale,\n            locales,\n            defaultLocale,\n            domainLocales,\n            isPreview,\n          },\n          buildManifest,\n          docComponentsRendered,\n          dangerousAsPath,\n          canonicalBase,\n          ampPath,\n          inAmpMode,\n          isDevelopment: !!dev,\n          hybridAmp,\n          dynamicImports,\n          assetPrefix,\n          headTags,\n          unstable_runtimeJS,\n          unstable_JsPreload,\n          devOnlyCacheBusterQueryString,\n          scriptLoader,\n          locale,\n          disableOptimizedLoading,\n          ...docProps,\n        })}\n      </AmpStateContext.Provider>\n    )\n  )\n}\n\nconst invalidKeysMsg = (methodName: string, invalidKeys: string[]) => {\n  return (\n    `Additional keys were returned from \\`${methodName}\\`. Properties intended for your component must be nested under the \\`props\\` key, e.g.:` +\n    `\\n\\n\\treturn { props: { title: 'My Title', content: '...' } }` +\n    `\\n\\nKeys that need to be moved: ${invalidKeys.join(', ')}.` +\n    `\\nRead more: https://nextjs.org/docs/messages/invalid-getstaticprops-value`\n  )\n}\n\nfunction checkRedirectValues(\n  redirect: Redirect,\n  req: IncomingMessage,\n  method: 'getStaticProps' | 'getServerSideProps'\n) {\n  const { destination, permanent, statusCode, basePath } = redirect\n  let errors: string[] = []\n\n  const hasStatusCode = typeof statusCode !== 'undefined'\n  const hasPermanent = typeof permanent !== 'undefined'\n\n  if (hasPermanent && hasStatusCode) {\n    errors.push(`\\`permanent\\` and \\`statusCode\\` can not both be provided`)\n  } else if (hasPermanent && typeof permanent !== 'boolean') {\n    errors.push(`\\`permanent\\` must be \\`true\\` or \\`false\\``)\n  } else if (hasStatusCode && !allowedStatusCodes.has(statusCode!)) {\n    errors.push(\n      `\\`statusCode\\` must undefined or one of ${[...allowedStatusCodes].join(\n        ', '\n      )}`\n    )\n  }\n  const destinationType = typeof destination\n\n  if (destinationType !== 'string') {\n    errors.push(\n      `\\`destination\\` should be string but received ${destinationType}`\n    )\n  }\n\n  const basePathType = typeof basePath\n\n  if (basePathType !== 'undefined' && basePathType !== 'boolean') {\n    errors.push(\n      `\\`basePath\\` should be undefined or a false, received ${basePathType}`\n    )\n  }\n\n  if (errors.length > 0) {\n    throw new Error(\n      `Invalid redirect object returned from ${method} for ${req.url}\\n` +\n        errors.join(' and ') +\n        '\\n' +\n        `See more info here: https://nextjs.org/docs/messages/invalid-redirect-gssp`\n    )\n  }\n}\n\nexport async function renderToHTML(\n  req: IncomingMessage,\n  res: ServerResponse,\n  pathname: string,\n  query: ParsedUrlQuery,\n  renderOpts: RenderOpts\n): Promise<string | null> {\n  // In dev we invalidate the cache by appending a timestamp to the resource URL.\n  // This is a workaround to fix https://github.com/vercel/next.js/issues/5860\n  // TODO: remove this workaround when https://bugs.webkit.org/show_bug.cgi?id=187726 is fixed.\n  renderOpts.devOnlyCacheBusterQueryString = renderOpts.dev\n    ? renderOpts.devOnlyCacheBusterQueryString || `?ts=${Date.now()}`\n    : ''\n\n  // don't modify original query object\n  query = Object.assign({}, query)\n\n  const {\n    err,\n    dev = false,\n    ampPath = '',\n    App,\n    Document,\n    pageConfig = {},\n    Component,\n    buildManifest,\n    fontManifest,\n    reactLoadableManifest,\n    ErrorDebug,\n    getStaticProps,\n    getStaticPaths,\n    getServerSideProps,\n    isDataReq,\n    params,\n    previewProps,\n    basePath,\n    devOnlyCacheBusterQueryString,\n  } = renderOpts\n\n  const getFontDefinition = (url: string): string => {\n    if (fontManifest) {\n      return getFontDefinitionFromManifest(url, fontManifest)\n    }\n    return ''\n  }\n\n  const callMiddleware = async (method: string, args: any[], props = false) => {\n    let results: any = props ? {} : []\n\n    if ((Document as any)[`${method}Middleware`]) {\n      let middlewareFunc = await (Document as any)[`${method}Middleware`]\n      middlewareFunc = middlewareFunc.default || middlewareFunc\n\n      const curResults = await middlewareFunc(...args)\n      if (props) {\n        for (const result of curResults) {\n          results = {\n            ...results,\n            ...result,\n          }\n        }\n      } else {\n        results = curResults\n      }\n    }\n    return results\n  }\n\n  const headTags = (...args: any) => callMiddleware('headTags', args)\n\n  const isFallback = !!query.__nextFallback\n  delete query.__nextFallback\n  delete query.__nextLocale\n  delete query.__nextDefaultLocale\n\n  const isSSG = !!getStaticProps\n  const isBuildTimeSSG = isSSG && renderOpts.nextExport\n  const defaultAppGetInitialProps =\n    App.getInitialProps === (App as any).origGetInitialProps\n\n  const hasPageGetInitialProps = !!(Component as any).getInitialProps\n\n  const pageIsDynamic = isDynamicRoute(pathname)\n\n  const isAutoExport =\n    !hasPageGetInitialProps &&\n    defaultAppGetInitialProps &&\n    !isSSG &&\n    !getServerSideProps\n\n  for (const methodName of [\n    'getStaticProps',\n    'getServerSideProps',\n    'getStaticPaths',\n  ]) {\n    if ((Component as any)[methodName]) {\n      throw new Error(\n        `page ${pathname} ${methodName} ${GSSP_COMPONENT_MEMBER_ERROR}`\n      )\n    }\n  }\n\n  if (hasPageGetInitialProps && isSSG) {\n    throw new Error(SSG_GET_INITIAL_PROPS_CONFLICT + ` ${pathname}`)\n  }\n\n  if (hasPageGetInitialProps && getServerSideProps) {\n    throw new Error(SERVER_PROPS_GET_INIT_PROPS_CONFLICT + ` ${pathname}`)\n  }\n\n  if (getServerSideProps && isSSG) {\n    throw new Error(SERVER_PROPS_SSG_CONFLICT + ` ${pathname}`)\n  }\n\n  if (getStaticPaths && !pageIsDynamic) {\n    throw new Error(\n      `getStaticPaths is only allowed for dynamic SSG pages and was found on '${pathname}'.` +\n        `\\nRead more: https://nextjs.org/docs/messages/non-dynamic-getstaticpaths-usage`\n    )\n  }\n\n  if (!!getStaticPaths && !isSSG) {\n    throw new Error(\n      `getStaticPaths was added without a getStaticProps in ${pathname}. Without getStaticProps, getStaticPaths does nothing`\n    )\n  }\n\n  if (isSSG && pageIsDynamic && !getStaticPaths) {\n    throw new Error(\n      `getStaticPaths is required for dynamic SSG pages and is missing for '${pathname}'.` +\n        `\\nRead more: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`\n    )\n  }\n\n  let asPath: string = renderOpts.resolvedAsPath || (req.url as string)\n\n  if (dev) {\n    const { isValidElementType } = require('react-is')\n    if (!isValidElementType(Component)) {\n      throw new Error(\n        `The default export is not a React Component in page: \"${pathname}\"`\n      )\n    }\n\n    if (!isValidElementType(App)) {\n      throw new Error(\n        `The default export is not a React Component in page: \"/_app\"`\n      )\n    }\n\n    if (!isValidElementType(Document)) {\n      throw new Error(\n        `The default export is not a React Component in page: \"/_document\"`\n      )\n    }\n\n    if (isAutoExport || isFallback) {\n      // remove query values except ones that will be set during export\n      query = {\n        ...(query.amp\n          ? {\n              amp: query.amp,\n            }\n          : {}),\n      }\n      asPath = `${pathname}${\n        // ensure trailing slash is present for non-dynamic auto-export pages\n        req.url!.endsWith('/') && pathname !== '/' && !pageIsDynamic ? '/' : ''\n      }`\n      req.url = pathname\n    }\n\n    if (pathname === '/404' && (hasPageGetInitialProps || getServerSideProps)) {\n      throw new Error(\n        `\\`pages/404\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n      )\n    }\n    if (\n      STATIC_STATUS_PAGES.includes(pathname) &&\n      (hasPageGetInitialProps || getServerSideProps)\n    ) {\n      throw new Error(\n        `\\`pages${pathname}\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n      )\n    }\n  }\n\n  await Loadable.preloadAll() // Make sure all dynamic imports are loaded\n\n  let isPreview\n  let previewData: PreviewData\n\n  if ((isSSG || getServerSideProps) && !isFallback) {\n    // Reads of this are cached on the `req` object, so this should resolve\n    // instantly. There's no need to pass this data down from a previous\n    // invoke, where we'd have to consider server & serverless.\n    previewData = tryGetPreviewData(req, res, previewProps)\n    isPreview = previewData !== false\n  }\n\n  // url will always be set\n  const routerIsReady = !!(getServerSideProps || hasPageGetInitialProps)\n  const router = new ServerRouter(\n    pathname,\n    query,\n    asPath,\n    {\n      isFallback: isFallback,\n    },\n    routerIsReady,\n    basePath,\n    renderOpts.locale,\n    renderOpts.locales,\n    renderOpts.defaultLocale,\n    renderOpts.domainLocales,\n    isPreview,\n    (req as any).__nextIsLocaleDomain\n  )\n  const ctx = {\n    err,\n    req: isAutoExport ? undefined : req,\n    res: isAutoExport ? undefined : res,\n    pathname,\n    query,\n    asPath,\n    locale: renderOpts.locale,\n    locales: renderOpts.locales,\n    defaultLocale: renderOpts.defaultLocale,\n    AppTree: (props: any) => {\n      return (\n        <AppContainer>\n          <App {...props} Component={Component} router={router} />\n        </AppContainer>\n      )\n    },\n  }\n  let props: any\n\n  const ampState = {\n    ampFirst: pageConfig.amp === true,\n    hasQuery: Boolean(query.amp),\n    hybrid: pageConfig.amp === 'hybrid',\n  }\n\n  const inAmpMode = isInAmpMode(ampState)\n\n  const reactLoadableModules: string[] = []\n\n  let head: JSX.Element[] = defaultHead(inAmpMode)\n\n  let scriptLoader: any = {}\n\n  const AppContainer = ({ children }: any) => (\n    <RouterContext.Provider value={router}>\n      <AmpStateContext.Provider value={ampState}>\n        <HeadManagerContext.Provider\n          value={{\n            updateHead: (state) => {\n              head = state\n            },\n            updateScripts: (scripts) => {\n              scriptLoader = scripts\n            },\n            scripts: {},\n            mountedInstances: new Set(),\n          }}\n        >\n          <LoadableContext.Provider\n            value={(moduleName) => reactLoadableModules.push(moduleName)}\n          >\n            {children}\n          </LoadableContext.Provider>\n        </HeadManagerContext.Provider>\n      </AmpStateContext.Provider>\n    </RouterContext.Provider>\n  )\n\n  try {\n    props = await loadGetInitialProps(App, {\n      AppTree: ctx.AppTree,\n      Component,\n      router,\n      ctx,\n    })\n\n    if ((isSSG || getServerSideProps) && isPreview) {\n      props.__N_PREVIEW = true\n    }\n\n    if (isSSG) {\n      props[STATIC_PROPS_ID] = true\n    }\n\n    if (isSSG && !isFallback) {\n      let data: UnwrapPromise<ReturnType<GetStaticProps>>\n\n      try {\n        data = await getStaticProps!({\n          ...(pageIsDynamic ? { params: query as ParsedUrlQuery } : undefined),\n          ...(isPreview\n            ? { preview: true, previewData: previewData }\n            : undefined),\n          locales: renderOpts.locales,\n          locale: renderOpts.locale,\n          defaultLocale: renderOpts.defaultLocale,\n        })\n      } catch (staticPropsError) {\n        // remove not found error code to prevent triggering legacy\n        // 404 rendering\n        if (staticPropsError.code === 'ENOENT') {\n          delete staticPropsError.code\n        }\n        throw staticPropsError\n      }\n\n      if (data == null) {\n        throw new Error(GSP_NO_RETURNED_VALUE)\n      }\n\n      const invalidKeys = Object.keys(data).filter(\n        (key) =>\n          key !== 'revalidate' &&\n          key !== 'props' &&\n          key !== 'redirect' &&\n          key !== 'notFound'\n      )\n\n      if (invalidKeys.includes('unstable_revalidate')) {\n        throw new Error(UNSTABLE_REVALIDATE_RENAME_ERROR)\n      }\n\n      if (invalidKeys.length) {\n        throw new Error(invalidKeysMsg('getStaticProps', invalidKeys))\n      }\n\n      if (process.env.NODE_ENV !== 'production') {\n        if (\n          typeof (data as any).notFound !== 'undefined' &&\n          typeof (data as any).redirect !== 'undefined'\n        ) {\n          throw new Error(\n            `\\`redirect\\` and \\`notFound\\` can not both be returned from ${\n              isSSG ? 'getStaticProps' : 'getServerSideProps'\n            } at the same time. Page: ${pathname}\\nSee more info here: https://nextjs.org/docs/messages/gssp-mixed-not-found-redirect`\n          )\n        }\n      }\n\n      if ('notFound' in data && data.notFound) {\n        if (pathname === '/404') {\n          throw new Error(\n            `The /404 page can not return notFound in \"getStaticProps\", please remove it to continue!`\n          )\n        }\n\n        ;(renderOpts as any).isNotFound = true\n      }\n\n      if (\n        'redirect' in data &&\n        data.redirect &&\n        typeof data.redirect === 'object'\n      ) {\n        checkRedirectValues(data.redirect as Redirect, req, 'getStaticProps')\n\n        if (isBuildTimeSSG) {\n          throw new Error(\n            `\\`redirect\\` can not be returned from getStaticProps during prerendering (${req.url})\\n` +\n              `See more info here: https://nextjs.org/docs/messages/gsp-redirect-during-prerender`\n          )\n        }\n\n        ;(data as any).props = {\n          __N_REDIRECT: data.redirect.destination,\n          __N_REDIRECT_STATUS: getRedirectStatus(data.redirect),\n        }\n        if (typeof data.redirect.basePath !== 'undefined') {\n          ;(data as any).props.__N_REDIRECT_BASE_PATH = data.redirect.basePath\n        }\n        ;(renderOpts as any).isRedirect = true\n      }\n\n      if (\n        (dev || isBuildTimeSSG) &&\n        !(renderOpts as any).isNotFound &&\n        !isSerializableProps(pathname, 'getStaticProps', (data as any).props)\n      ) {\n        // this fn should throw an error instead of ever returning `false`\n        throw new Error(\n          'invariant: getStaticProps did not return valid props. Please report this.'\n        )\n      }\n\n      if ('revalidate' in data) {\n        if (typeof data.revalidate === 'number') {\n          if (!Number.isInteger(data.revalidate)) {\n            throw new Error(\n              `A page's revalidate option must be seconds expressed as a natural number for ${req.url}. Mixed numbers, such as '${data.revalidate}', cannot be used.` +\n                `\\nTry changing the value to '${Math.ceil(\n                  data.revalidate\n                )}' or using \\`Math.ceil()\\` if you're computing the value.`\n            )\n          } else if (data.revalidate <= 0) {\n            throw new Error(\n              `A page's revalidate option can not be less than or equal to zero for ${req.url}. A revalidate option of zero means to revalidate after _every_ request, and implies stale data cannot be tolerated.` +\n                `\\n\\nTo never revalidate, you can set revalidate to \\`false\\` (only ran once at build-time).` +\n                `\\nTo revalidate as soon as possible, you can set the value to \\`1\\`.`\n            )\n          } else if (data.revalidate > 31536000) {\n            // if it's greater than a year for some reason error\n            console.warn(\n              `Warning: A page's revalidate option was set to more than a year for ${req.url}. This may have been done in error.` +\n                `\\nTo only run getStaticProps at build-time and not revalidate at runtime, you can set \\`revalidate\\` to \\`false\\`!`\n            )\n          }\n        } else if (data.revalidate === true) {\n          // When enabled, revalidate after 1 second. This value is optimal for\n          // the most up-to-date page possible, but without a 1-to-1\n          // request-refresh ratio.\n          data.revalidate = 1\n        } else if (\n          data.revalidate === false ||\n          typeof data.revalidate === 'undefined'\n        ) {\n          // By default, we never revalidate.\n          data.revalidate = false\n        } else {\n          throw new Error(\n            `A page's revalidate option must be seconds expressed as a natural number. Mixed numbers and strings cannot be used. Received '${JSON.stringify(\n              data.revalidate\n            )}' for ${req.url}`\n          )\n        }\n      } else {\n        // By default, we never revalidate.\n        ;(data as any).revalidate = false\n      }\n\n      // this must come after revalidate is attached\n      if ((renderOpts as any).isNotFound) {\n        return null\n      }\n\n      props.pageProps = Object.assign(\n        {},\n        props.pageProps,\n        'props' in data ? data.props : undefined\n      )\n\n      // pass up revalidate and props for export\n      // TODO: change this to a different passing mechanism\n      ;(renderOpts as any).revalidate =\n        'revalidate' in data ? data.revalidate : undefined\n      ;(renderOpts as any).pageData = props\n    }\n\n    if (getServerSideProps) {\n      props[SERVER_PROPS_ID] = true\n    }\n\n    if (getServerSideProps && !isFallback) {\n      let data: UnwrapPromise<ReturnType<GetServerSideProps>>\n\n      try {\n        data = await getServerSideProps({\n          req: req as IncomingMessage & {\n            cookies: NextApiRequestCookies\n          },\n          res,\n          query,\n          resolvedUrl: renderOpts.resolvedUrl as string,\n          ...(pageIsDynamic ? { params: params as ParsedUrlQuery } : undefined),\n          ...(previewData !== false\n            ? { preview: true, previewData: previewData }\n            : undefined),\n          locales: renderOpts.locales,\n          locale: renderOpts.locale,\n          defaultLocale: renderOpts.defaultLocale,\n        })\n      } catch (serverSidePropsError) {\n        // remove not found error code to prevent triggering legacy\n        // 404 rendering\n        if (serverSidePropsError.code === 'ENOENT') {\n          delete serverSidePropsError.code\n        }\n        throw serverSidePropsError\n      }\n\n      if (data == null) {\n        throw new Error(GSSP_NO_RETURNED_VALUE)\n      }\n\n      const invalidKeys = Object.keys(data).filter(\n        (key) => key !== 'props' && key !== 'redirect' && key !== 'notFound'\n      )\n\n      if ((data as any).unstable_notFound) {\n        throw new Error(\n          `unstable_notFound has been renamed to notFound, please update the field to continue. Page: ${pathname}`\n        )\n      }\n      if ((data as any).unstable_redirect) {\n        throw new Error(\n          `unstable_redirect has been renamed to redirect, please update the field to continue. Page: ${pathname}`\n        )\n      }\n\n      if (invalidKeys.length) {\n        throw new Error(invalidKeysMsg('getServerSideProps', invalidKeys))\n      }\n\n      if ('notFound' in data && data.notFound) {\n        if (pathname === '/404') {\n          throw new Error(\n            `The /404 page can not return notFound in \"getStaticProps\", please remove it to continue!`\n          )\n        }\n\n        ;(renderOpts as any).isNotFound = true\n        return null\n      }\n\n      if ('redirect' in data && typeof data.redirect === 'object') {\n        checkRedirectValues(\n          data.redirect as Redirect,\n          req,\n          'getServerSideProps'\n        )\n        ;(data as any).props = {\n          __N_REDIRECT: data.redirect.destination,\n          __N_REDIRECT_STATUS: getRedirectStatus(data.redirect),\n        }\n        if (typeof data.redirect.basePath !== 'undefined') {\n          ;(data as any).props.__N_REDIRECT_BASE_PATH = data.redirect.basePath\n        }\n        ;(renderOpts as any).isRedirect = true\n      }\n\n      if (\n        (dev || isBuildTimeSSG) &&\n        !isSerializableProps(\n          pathname,\n          'getServerSideProps',\n          (data as any).props\n        )\n      ) {\n        // this fn should throw an error instead of ever returning `false`\n        throw new Error(\n          'invariant: getServerSideProps did not return valid props. Please report this.'\n        )\n      }\n\n      props.pageProps = Object.assign({}, props.pageProps, (data as any).props)\n      ;(renderOpts as any).pageData = props\n    }\n  } catch (dataFetchError) {\n    if (isDataReq || !dev || !dataFetchError) throw dataFetchError\n    ctx.err = dataFetchError\n    renderOpts.err = dataFetchError\n    console.error(dataFetchError)\n  }\n\n  if (\n    !isSSG && // we only show this warning for legacy pages\n    !getServerSideProps &&\n    process.env.NODE_ENV !== 'production' &&\n    Object.keys(props?.pageProps || {}).includes('url')\n  ) {\n    console.warn(\n      `The prop \\`url\\` is a reserved prop in Next.js for legacy reasons and will be overridden on page ${pathname}\\n` +\n        `See more info here: https://nextjs.org/docs/messages/reserved-page-prop`\n    )\n  }\n\n  // Avoid rendering page un-necessarily for getServerSideProps data request\n  // and getServerSideProps/getStaticProps redirects\n  if ((isDataReq && !isSSG) || (renderOpts as any).isRedirect) {\n    return props\n  }\n\n  // We don't call getStaticProps or getServerSideProps while generating\n  // the fallback so make sure to set pageProps to an empty object\n  if (isFallback) {\n    props.pageProps = {}\n  }\n\n  // the response might be finished on the getInitialProps call\n  if (isResSent(res) && !isSSG) return null\n\n  // we preload the buildManifest for auto-export dynamic pages\n  // to speed up hydrating query values\n  let filteredBuildManifest = buildManifest\n  if (isAutoExport && pageIsDynamic) {\n    const page = denormalizePagePath(normalizePagePath(pathname))\n    // This code would be much cleaner using `immer` and directly pushing into\n    // the result from `getPageFiles`, we could maybe consider that in the\n    // future.\n    if (page in filteredBuildManifest.pages) {\n      filteredBuildManifest = {\n        ...filteredBuildManifest,\n        pages: {\n          ...filteredBuildManifest.pages,\n          [page]: [\n            ...filteredBuildManifest.pages[page],\n            ...filteredBuildManifest.lowPriorityFiles.filter((f) =>\n              f.includes('_buildManifest')\n            ),\n          ],\n        },\n        lowPriorityFiles: filteredBuildManifest.lowPriorityFiles.filter(\n          (f) => !f.includes('_buildManifest')\n        ),\n      }\n    }\n  }\n\n  const renderPage: RenderPage = (\n    options: ComponentsEnhancer = {}\n  ): { html: string; head: any } => {\n    if (ctx.err && ErrorDebug) {\n      return { html: renderToString(<ErrorDebug error={ctx.err} />), head }\n    }\n\n    if (dev && (props.router || props.Component)) {\n      throw new Error(\n        `'router' and 'Component' can not be returned in getInitialProps from _app.js https://nextjs.org/docs/messages/cant-override-next-props`\n      )\n    }\n\n    const {\n      App: EnhancedApp,\n      Component: EnhancedComponent,\n    } = enhanceComponents(options, App, Component)\n\n    const html = renderToString(\n      <AppContainer>\n        <EnhancedApp Component={EnhancedComponent} router={router} {...props} />\n      </AppContainer>\n    )\n\n    return { html, head }\n  }\n  const documentCtx = { ...ctx, renderPage }\n  const docProps: DocumentInitialProps = await loadGetInitialProps(\n    Document,\n    documentCtx\n  )\n  // the response might be finished on the getInitialProps call\n  if (isResSent(res) && !isSSG) return null\n\n  if (!docProps || typeof docProps.html !== 'string') {\n    const message = `\"${getDisplayName(\n      Document\n    )}.getInitialProps()\" should resolve to an object with a \"html\" prop set with a valid html string`\n    throw new Error(message)\n  }\n\n  const dynamicImportsIds = new Set<string | number>()\n  const dynamicImports = new Set<string>()\n\n  for (const mod of reactLoadableModules) {\n    const manifestItem: ManifestItem = reactLoadableManifest[mod]\n\n    if (manifestItem) {\n      dynamicImportsIds.add(manifestItem.id)\n      manifestItem.files.forEach((item) => {\n        dynamicImports.add(item)\n      })\n    }\n  }\n\n  const hybridAmp = ampState.hybrid\n\n  const docComponentsRendered: DocumentProps['docComponentsRendered'] = {}\n  const nextExport =\n    !isSSG && (renderOpts.nextExport || (dev && (isAutoExport || isFallback)))\n\n  let html = renderDocument(Document, {\n    ...renderOpts,\n    canonicalBase:\n      !renderOpts.ampPath && (req as any).__nextStrippedLocale\n        ? `${renderOpts.canonicalBase || ''}/${renderOpts.locale}`\n        : renderOpts.canonicalBase,\n    docComponentsRendered,\n    buildManifest: filteredBuildManifest,\n    // Only enabled in production as development mode has features relying on HMR (style injection for example)\n    unstable_runtimeJS:\n      process.env.NODE_ENV === 'production'\n        ? pageConfig.unstable_runtimeJS\n        : undefined,\n    unstable_JsPreload: pageConfig.unstable_JsPreload,\n    dangerousAsPath: router.asPath,\n    ampState,\n    props,\n    headTags: await headTags(documentCtx),\n    isFallback,\n    docProps,\n    pathname,\n    ampPath,\n    query,\n    inAmpMode,\n    hybridAmp,\n    dynamicImportsIds: Array.from(dynamicImportsIds),\n    dynamicImports: Array.from(dynamicImports),\n    gsp: !!getStaticProps ? true : undefined,\n    gssp: !!getServerSideProps ? true : undefined,\n    gip: hasPageGetInitialProps ? true : undefined,\n    appGip: !defaultAppGetInitialProps ? true : undefined,\n    devOnlyCacheBusterQueryString,\n    scriptLoader,\n    isPreview: isPreview === true ? true : undefined,\n    autoExport: isAutoExport === true ? true : undefined,\n    nextExport: nextExport === true ? true : undefined,\n  })\n\n  if (process.env.NODE_ENV !== 'production') {\n    const nonRenderedComponents = []\n    const expectedDocComponents = ['Main', 'Head', 'NextScript', 'Html']\n\n    for (const comp of expectedDocComponents) {\n      if (!(docComponentsRendered as any)[comp]) {\n        nonRenderedComponents.push(comp)\n      }\n    }\n    const plural = nonRenderedComponents.length !== 1 ? 's' : ''\n\n    if (nonRenderedComponents.length) {\n      const missingComponentList = nonRenderedComponents\n        .map((e) => `<${e} />`)\n        .join(', ')\n      warn(\n        `Your custom Document (pages/_document) did not render all the required subcomponent${plural}.\\n` +\n          `Missing component${plural}: ${missingComponentList}\\n` +\n          'Read how to fix here: https://nextjs.org/docs/messages/missing-document-component'\n      )\n    }\n  }\n\n  if (inAmpMode && html) {\n    // inject HTML to AMP_RENDER_TARGET to allow rendering\n    // directly to body in AMP mode\n    const ampRenderIndex = html.indexOf(AMP_RENDER_TARGET)\n    html =\n      html.substring(0, ampRenderIndex) +\n      `<!-- __NEXT_DATA__ -->${docProps.html}` +\n      html.substring(ampRenderIndex + AMP_RENDER_TARGET.length)\n    html = await optimizeAmp(html, renderOpts.ampOptimizerConfig)\n\n    if (!renderOpts.ampSkipValidation && renderOpts.ampValidator) {\n      await renderOpts.ampValidator(html, pathname)\n    }\n  }\n\n  // Avoid postProcess if both flags are false\n  if (process.env.__NEXT_OPTIMIZE_FONTS || process.env.__NEXT_OPTIMIZE_IMAGES) {\n    html = await postProcess(\n      html,\n      { getFontDefinition },\n      {\n        optimizeFonts: renderOpts.optimizeFonts,\n        optimizeImages: renderOpts.optimizeImages,\n      }\n    )\n  }\n\n  if (renderOpts.optimizeCss) {\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    const Critters = require('critters')\n    const cssOptimizer = new Critters({\n      ssrMode: true,\n      reduceInlineStyles: false,\n      path: renderOpts.distDir,\n      publicPath: '/_next/',\n      preload: 'media',\n      fonts: false,\n      ...renderOpts.optimizeCss,\n    })\n\n    html = await cssOptimizer.process(html)\n  }\n\n  if (inAmpMode || hybridAmp) {\n    // fix &amp being escaped for amphtml rel link\n    html = html.replace(/&amp;amp=1/g, '&amp=1')\n  }\n\n  return html\n}\n\nfunction errorToJSON(err: Error): Error {\n  const { name, message, stack } = err\n  return { name, message, stack }\n}\n\nfunction serializeError(\n  dev: boolean | undefined,\n  err: Error\n): Error & { statusCode?: number } {\n  if (dev) {\n    return errorToJSON(err)\n  }\n\n  return {\n    name: 'Internal Server Error.',\n    message: '500 - Internal Server Error.',\n    statusCode: 500,\n  }\n}\n"]}