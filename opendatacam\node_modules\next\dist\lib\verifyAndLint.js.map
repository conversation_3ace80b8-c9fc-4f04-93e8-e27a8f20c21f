{"version": 3, "sources": ["../../lib/verifyAndLint.ts"], "names": ["verifyAndLint", "dir", "pagesDir", "numWorkers", "enableWorkerThreads", "lintWorkers", "Worker", "require", "resolve", "getStdout", "pipe", "process", "stdout", "getStderr", "stderr", "lintResults", "runLintCheck", "console", "log", "end", "err", "type", "error", "chalk", "red", "message", "exit"], "mappings": "yEAAA,oDACA,uC,mFAEO,cAAeA,CAAAA,aAAf,CACLC,GADK,CAELC,QAFK,CAGLC,UAHK,CAILC,mBAJK,CAKU,CACf,GAAI,CACF,KAAMC,CAAAA,WAAW,CAAG,GAAIC,mBAAJ,CAAWC,OAAO,CAACC,OAAR,CAAgB,uBAAhB,CAAX,CAAqD,CACvEL,UADuE,CAEvEC,mBAFuE,CAArD,CAApB,CAOAC,WAAW,CAACI,SAAZ,GAAwBC,IAAxB,CAA6BC,OAAO,CAACC,MAArC,EACAP,WAAW,CAACQ,SAAZ,GAAwBH,IAAxB,CAA6BC,OAAO,CAACG,MAArC,EAEA,KAAMC,CAAAA,WAAW,CAAG,KAAMV,CAAAA,WAAW,CAACW,YAAZ,CAAyBf,GAAzB,CAA8BC,QAA9B,CAA1B,CACA,GAAIa,WAAJ,CAAiB,CACfE,OAAO,CAACC,GAAR,CAAYH,WAAZ,EACD,CAEDV,WAAW,CAACc,GAAZ,GACD,CAAC,MAAOC,GAAP,CAAY,CACZ,GAAIA,GAAG,CAACC,IAAJ,GAAa,cAAjB,CAAiC,CAC/BJ,OAAO,CAACK,KAAR,CAAcC,eAAMC,GAAN,CAAU,sBAAV,CAAd,EACAP,OAAO,CAACK,KAAR,CAAcF,GAAG,CAACK,OAAlB,EACAd,OAAO,CAACe,IAAR,CAAa,CAAb,EACD,CAJD,IAIO,IAAIN,GAAG,CAACC,IAAJ,GAAa,YAAjB,CAA+B,CACpCJ,OAAO,CAACK,KAAR,CAAcF,GAAG,CAACK,OAAlB,EACAd,OAAO,CAACe,IAAR,CAAa,CAAb,EACD,CACD,KAAMN,CAAAA,GAAN,CACD,CACF", "sourcesContent": ["import chalk from 'chalk'\nimport { Worker } from 'jest-worker'\n\nexport async function verifyAndLint(\n  dir: string,\n  pagesDir: string,\n  numWorkers: number | undefined,\n  enableWorkerThreads: boolean | undefined\n): Promise<void> {\n  try {\n    const lintWorkers = new Worker(require.resolve('./eslint/runLintCheck'), {\n      numWorkers,\n      enableWorkerThreads,\n    }) as Worker & {\n      runLintCheck: typeof import('./eslint/runLintCheck').runLintCheck\n    }\n\n    lintWorkers.getStdout().pipe(process.stdout)\n    lintWorkers.getStderr().pipe(process.stderr)\n\n    const lintResults = await lintWorkers.runLintCheck(dir, pagesDir)\n    if (lintResults) {\n      console.log(lintResults)\n    }\n\n    lintWorkers.end()\n  } catch (err) {\n    if (err.type === 'CompileError') {\n      console.error(chalk.red('\\nFailed to compile.'))\n      console.error(err.message)\n      process.exit(1)\n    } else if (err.type === 'FatalError') {\n      console.error(err.message)\n      process.exit(1)\n    }\n    throw err\n  }\n}\n"]}