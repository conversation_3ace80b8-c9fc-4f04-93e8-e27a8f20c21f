{"version": 3, "sources": ["../../../telemetry/trace/trace.ts"], "names": ["NUM_OF_MICROSEC_IN_SEC", "BigInt", "getId", "toString", "SpanStatus", "Span", "constructor", "name", "parentId", "attrs", "id", "duration", "status", "_start", "Started", "process", "hrtime", "bigint", "stop", "end", "Stopped", "Number", "MAX_SAFE_INTEGER", "Error", "timestamp", "<PERSON><PERSON><PERSON><PERSON>", "setAttribute", "key", "value", "traceFn", "fn", "traceAsyncFn", "trace"], "mappings": "0FAAA,8BAEA,gCAEA,KAAMA,CAAAA,sBAAsB,CAAGC,MAAM,CAAC,MAAD,CAArC,CAEA,KAAMC,CAAAA,KAAK,CAAG,IAAM,wBAAY,CAAZ,EAAeC,QAAf,CAAwB,KAAxB,CAApB,CAEA;AACA,8B,GACYC,CAAAA,U,yCAAAA,U,EAAAA,U,CAAAA,U,yBAAAA,U,CAAAA,U,4BAAAA,U,sBAAAA,U,MAKL,KAAMC,CAAAA,IAAK,CAUhBC,WAAW,CAACC,IAAD,CAAeC,QAAf,CAAkCC,KAAlC,CAAkD,MAT7DF,IAS6D,aAR7DG,EAQ6D,aAP7DF,QAO6D,aAN7DG,QAM6D,aAL7DF,KAK6D,aAJ7DG,MAI6D,aAF7DC,MAE6D,QAC3D,KAAKN,IAAL,CAAYA,IAAZ,CACA,KAAKC,QAAL,CAAgBA,QAAhB,CACA,KAAKG,QAAL,CAAgB,IAAhB,CACA,KAAKF,KAAL,CAAaA,KAAK,CAAG,CAAE,GAAGA,KAAL,CAAH,CAAkB,EAApC,CACA,KAAKG,MAAL,CAAcR,UAAU,CAACU,OAAzB,CACA,KAAKJ,EAAL,CAAUR,KAAK,EAAf,CACA,KAAKW,MAAL,CAAcE,OAAO,CAACC,MAAR,CAAeC,MAAf,EAAd,CACD,CAED;AACA;AACA;AACA;AACAC,IAAI,EAAG,CACL,KAAMC,CAAAA,GAAW,CAAGJ,OAAO,CAACC,MAAR,CAAeC,MAAf,EAApB,CACA,KAAMN,CAAAA,QAAQ,CAAG,CAACQ,GAAG,CAAG,KAAKN,MAAZ,EAAsBb,sBAAvC,CACA,KAAKY,MAAL,CAAcR,UAAU,CAACgB,OAAzB,CACA,GAAIT,QAAQ,CAAGU,MAAM,CAACC,gBAAtB,CAAwC,CACtC,KAAM,IAAIC,CAAAA,KAAJ,CAAW,+CAA8CZ,QAAS,EAAlE,CAAN,CACD,CACD,KAAMa,CAAAA,SAAS,CAAG,KAAKX,MAAL,CAAcb,sBAAhC,CACA,mBACE,KAAKO,IADP,CAEEc,MAAM,CAACV,QAAD,CAFR,CAGEU,MAAM,CAACG,SAAD,CAHR,CAIE,KAAKd,EAJP,CAKE,KAAKF,QALP,CAME,KAAKC,KANP,EAQD,CAEDgB,UAAU,CAAClB,IAAD,CAAeE,KAAf,CAA+B,CACvC,MAAO,IAAIJ,CAAAA,IAAJ,CAASE,IAAT,CAAe,KAAKG,EAApB,CAAwBD,KAAxB,CAAP,CACD,CAEDiB,YAAY,CAACC,GAAD,CAAcC,KAAd,CAA0B,CACpC,KAAKnB,KAAL,CAAWkB,GAAX,EAAkBC,KAAlB,CACD,CAEDC,OAAO,CAACC,EAAD,CAAU,CACf,GAAI,CACF,MAAOA,CAAAA,EAAE,EAAT,CACD,CAFD,OAEU,CACR,KAAKZ,IAAL,GACD,CACF,CAED,KAAMa,CAAAA,YAAN,CAAsBD,EAAtB,CAA4D,CAC1D,GAAI,CACF,MAAO,MAAMA,CAAAA,EAAE,EAAf,CACD,CAFD,OAEU,CACR,KAAKZ,IAAL,GACD,CACF,CAhEe,C,kBAmEX,KAAMc,CAAAA,KAAK,CAAG,CAACzB,IAAD,CAAeC,QAAf,CAAkCC,KAAlC,GAAqD,CACxE,MAAO,IAAIJ,CAAAA,IAAJ,CAASE,IAAT,CAAeC,QAAf,CAAyBC,KAAzB,CAAP,CACD,CAFM,C", "sourcesContent": ["import { randomBytes } from 'crypto'\nimport { SpanId } from './shared'\nimport { report } from './report'\n\nconst NUM_OF_MICROSEC_IN_SEC = BigInt('1000')\n\nconst getId = () => randomBytes(8).toString('hex')\n\n// eslint typescript has a bug with TS enums\n/* eslint-disable no-shadow */\nexport enum SpanStatus {\n  Started,\n  Stopped,\n}\n\nexport class Span {\n  name: string\n  id: SpanId\n  parentId?: SpanId\n  duration: number | null\n  attrs: { [key: string]: any }\n  status: SpanStatus\n\n  _start: bigint\n\n  constructor(name: string, parentId?: SpanId, attrs?: Object) {\n    this.name = name\n    this.parentId = parentId\n    this.duration = null\n    this.attrs = attrs ? { ...attrs } : {}\n    this.status = SpanStatus.Started\n    this.id = getId()\n    this._start = process.hrtime.bigint()\n  }\n\n  // Durations are reported as microseconds. This gives 1000x the precision\n  // of something like Date.now(), which reports in milliseconds.\n  // Additionally, ~285 years can be safely represented as microseconds as\n  // a float64 in both JSON and JavaScript.\n  stop() {\n    const end: bigint = process.hrtime.bigint()\n    const duration = (end - this._start) / NUM_OF_MICROSEC_IN_SEC\n    this.status = SpanStatus.Stopped\n    if (duration > Number.MAX_SAFE_INTEGER) {\n      throw new Error(`Duration is too long to express as float64: ${duration}`)\n    }\n    const timestamp = this._start / NUM_OF_MICROSEC_IN_SEC\n    report(\n      this.name,\n      Number(duration),\n      Number(timestamp),\n      this.id,\n      this.parentId,\n      this.attrs\n    )\n  }\n\n  traceChild(name: string, attrs?: Object) {\n    return new Span(name, this.id, attrs)\n  }\n\n  setAttribute(key: string, value: any) {\n    this.attrs[key] = value\n  }\n\n  traceFn(fn: any) {\n    try {\n      return fn()\n    } finally {\n      this.stop()\n    }\n  }\n\n  async traceAsyncFn<T>(fn: () => T | Promise<T>): Promise<T> {\n    try {\n      return await fn()\n    } finally {\n      this.stop()\n    }\n  }\n}\n\nexport const trace = (name: string, parentId?: SpanId, attrs?: Object) => {\n  return new Span(name, parentId, attrs)\n}\n"]}