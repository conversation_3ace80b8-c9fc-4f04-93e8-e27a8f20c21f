(function() {
var exports = {};
exports.id = 888;
exports.ids = [888];
exports.modules = {

/***/ 809:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ _app; }
});

// EXTERNAL MODULE: external "react"
var external_react_ = __webpack_require__(297);
var external_react_default = /*#__PURE__*/__webpack_require__.n(external_react_);
// EXTERNAL MODULE: external "react-redux"
var external_react_redux_ = __webpack_require__(79);
// EXTERNAL MODULE: ./node_modules/next/app.js
var app = __webpack_require__(544);
;// CONCATENATED MODULE: external "next-redux-wrapper"
var external_next_redux_wrapper_namespaceObject = require("next-redux-wrapper");;
var external_next_redux_wrapper_default = /*#__PURE__*/__webpack_require__.n(external_next_redux_wrapper_namespaceObject);
;// CONCATENATED MODULE: external "redux"
var external_redux_namespaceObject = require("redux");;
;// CONCATENATED MODULE: external "redux-thunk"
var external_redux_thunk_namespaceObject = require("redux-thunk");;
var external_redux_thunk_default = /*#__PURE__*/__webpack_require__.n(external_redux_thunk_namespaceObject);
;// CONCATENATED MODULE: external "redux-devtools-extension"
var external_redux_devtools_extension_namespaceObject = require("redux-devtools-extension");;
// EXTERNAL MODULE: external "immutable"
var external_immutable_ = __webpack_require__(856);
// EXTERNAL MODULE: ./statemanagement/app/AppStateManagement.js
var AppStateManagement = __webpack_require__(855);
// EXTERNAL MODULE: ./statemanagement/app/CounterStateManagement.js
var CounterStateManagement = __webpack_require__(69);
// EXTERNAL MODULE: ./statemanagement/app/ViewportStateManagement.js
var ViewportStateManagement = __webpack_require__(198);
// EXTERNAL MODULE: ./statemanagement/app/TrackerStateManagement.js
var TrackerStateManagement = __webpack_require__(293);
// EXTERNAL MODULE: ./statemanagement/app/HistoryStateManagement.js
var HistoryStateManagement = __webpack_require__(861);
// EXTERNAL MODULE: ./statemanagement/app/UserSettingsStateManagement.js
var UserSettingsStateManagement = __webpack_require__(717);
;// CONCATENATED MODULE: ./statemanagement/reducers.js







/* harmony default export */ var reducers = ((0,external_redux_namespaceObject.combineReducers)({
  app: AppStateManagement/* default */.ZP,
  counter: CounterStateManagement/* default */.ZP,
  tracker: TrackerStateManagement/* default */.ZP,
  history: HistoryStateManagement/* default */.ZP,
  viewport: ViewportStateManagement/* default */.ZP,
  usersettings: UserSettingsStateManagement/* default */.ZP
}));
;// CONCATENATED MODULE: ./statemanagement/store.js





const makeStore = (initialState, {
  isServer
}) => {
  const middlewares = [(external_redux_thunk_default())];
  const middlewareEnhancer = (0,external_redux_namespaceObject.applyMiddleware)(...middlewares);
  const enhancers = [middlewareEnhancer];
  const composedEnhancers = (0,external_redux_devtools_extension_namespaceObject.composeWithDevTools)(...enhancers);

  if (isServer) {
    const store = (0,external_redux_namespaceObject.createStore)(reducers, initialState, composedEnhancers);
    return store;
  }

  if (!window.store) {
    // For each key of initialState, convert to Immutable object
    // Because SSR passed it as plain object
    Object.keys(initialState).map(key => {
      initialState[key] = (0,external_immutable_.fromJS)(initialState[key]);
    });
    window.store = (0,external_redux_namespaceObject.createStore)(reducers, initialState, composedEnhancers);
  }

  return window.store;
};
;// CONCATENATED MODULE: ./pages/_app.js
var __jsx = (external_react_default()).createElement;







class MyApp extends app.default {
  static async getInitialProps({
    Component,
    ctx
  }) {
    const pageProps = Component.getInitialProps ? await Component.getInitialProps(ctx) : {};
    return {
      pageProps
    };
  }

  render() {
    const {
      Component,
      pageProps,
      store
    } = this.props;
    return __jsx(external_react_redux_.Provider, {
      store: store
    }, __jsx(Component, pageProps));
  }

}

/* harmony default export */ var _app = (external_next_redux_wrapper_default()(makeStore)(MyApp));

/***/ }),

/***/ 376:
/***/ (function(module) {

"use strict";
module.exports = require("axios");;

/***/ }),

/***/ 856:
/***/ (function(module) {

"use strict";
module.exports = require("immutable");;

/***/ }),

/***/ 579:
/***/ (function(module) {

"use strict";
module.exports = require("next/dist/next-server/lib/utils.js");;

/***/ }),

/***/ 297:
/***/ (function(module) {

"use strict";
module.exports = require("react");;

/***/ }),

/***/ 79:
/***/ (function(module) {

"use strict";
module.exports = require("react-redux");;

/***/ }),

/***/ 231:
/***/ (function(module) {

"use strict";
module.exports = require("uuid");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
var __webpack_exports__ = __webpack_require__.X(0, [544,939], function() { return __webpack_exec__(809); });
module.exports = __webpack_exports__;

})();