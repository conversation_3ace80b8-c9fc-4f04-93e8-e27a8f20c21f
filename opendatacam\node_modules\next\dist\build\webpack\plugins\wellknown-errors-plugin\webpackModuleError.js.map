{"version": 3, "sources": ["../../../../../build/webpack/plugins/wellknown-errors-plugin/webpackModuleError.ts"], "names": ["getFileData", "compilation", "m", "resolved", "ctx", "compiler", "context", "resource", "res", "path", "relative", "replace", "posix", "sep", "startsWith", "requestShortener", "readableIdentifier", "request", "userRequest", "content", "resolve", "getModuleBuildError", "input", "name", "Boolean", "module", "error", "Error", "err", "sourceFilename", "sourceContent", "notFoundError", "babel", "css", "scss"], "mappings": "qFAAA,sBACA,kDAEA,wCACA,oCACA,sCACA,wD,qzBAGA,QAASA,CAAAA,WAAT,CACEC,WADF,CAEEC,CAFF,CAG2B,uDACzB,GAAIC,CAAAA,QAAJ,CACA,GAAIC,CAAAA,GAAkB,sDACpBH,WAAW,CAACI,QADQ,eACpB,uBAAsBC,OADF,8BACaL,WAAW,CAACK,OADzB,aACoC,IAD1D,CAEA,GAAIF,GAAG,GAAK,IAAR,EAAgB,MAAOF,CAAAA,CAAC,CAACK,QAAT,GAAsB,QAA1C,CAAoD,CAClD,KAAMC,CAAAA,GAAG,CAAGC,IAAI,CAACC,QAAL,CAAcN,GAAd,CAAmBF,CAAC,CAACK,QAArB,EAA+BI,OAA/B,CAAuC,KAAvC,CAA8CF,IAAI,CAACG,KAAL,CAAWC,GAAzD,CAAZ,CACAV,QAAQ,CAAGK,GAAG,CAACM,UAAJ,CAAe,GAAf,EAAsBN,GAAtB,CAA6B,IAAGC,IAAI,CAACG,KAAL,CAAWC,GAAI,GAAEL,GAAI,EAAhE,CACD,CAHD,IAGO,CACL,KAAMO,CAAAA,gBAAgB,CAAGd,WAAW,CAACc,gBAArC,CACA,GAAI,OAAOb,CAAP,cAAOA,CAAC,CAAEc,kBAAV,IAAiC,UAArC,CAAiD,CAC/Cb,QAAQ,CAAGD,CAAC,CAACc,kBAAF,CAAqBD,gBAArB,CAAX,CACD,CAFD,IAEO,gBACLZ,QAAQ,aAAGD,CAAC,CAACe,OAAL,mBAAgBf,CAAC,CAACgB,WAA1B,CACD,CACF,CAED,GAAIf,QAAJ,CAAc,CACZ,GAAIgB,CAAAA,OAAsB,CAAG,IAA7B,CACA,GAAI,CACFA,OAAO,CAAG,qBACRf,GAAG,CAAGK,IAAI,CAACW,OAAL,CAAahB,GAAb,CAAkBD,QAAlB,CAAH,CAAiCA,QAD5B,CAER,MAFQ,CAAV,CAID,CAAC,cAAM,CAAE,CACV,MAAO,CAACA,QAAD,CAAWgB,OAAX,CAAP,CACD,CAED,MAAO,CAAC,WAAD,CAAc,IAAd,CAAP,CACD,CAEM,cAAeE,CAAAA,mBAAf,CACLpB,WADK,CAELqB,KAFK,CAGgC,CACrC,GACE,EACE,MAAOA,CAAAA,KAAP,GAAiB,QAAjB,GACC,CAAAA,KAAK,MAAL,QAAAA,KAAK,CAAEC,IAAP,IAAgB,kBAAhB,EACC,CAAAD,KAAK,MAAL,QAAAA,KAAK,CAAEC,IAAP,IAAgB,qBAFlB,GAGAC,OAAO,CAACF,KAAK,CAACG,MAAP,CAHP,EAIAH,KAAK,CAACI,KAAN,WAAuBC,CAAAA,KALzB,CADF,CAQE,CACA,MAAO,MAAP,CACD,CAED,KAAMC,CAAAA,GAAU,CAAGN,KAAK,CAACI,KAAzB,CACA,KAAM,CAACG,cAAD,CAAiBC,aAAjB,EAAkC9B,WAAW,CAACC,WAAD,CAAcqB,KAAK,CAACG,MAApB,CAAnD,CAEA,KAAMM,CAAAA,aAAa,CAAG,KAAM,yCAC1B9B,WAD0B,CAE1BqB,KAF0B,CAG1BO,cAH0B,CAA5B,CAKA,GAAIE,aAAa,GAAK,KAAtB,CAA6B,CAC3B,MAAOA,CAAAA,aAAP,CACD,CAED,KAAMC,CAAAA,KAAK,CAAG,8BAAcH,cAAd,CAA8BD,GAA9B,CAAd,CACA,GAAII,KAAK,GAAK,KAAd,CAAqB,CACnB,MAAOA,CAAAA,KAAP,CACD,CAED,KAAMC,CAAAA,GAAG,CAAG,0BAAYJ,cAAZ,CAA4BD,GAA5B,CAAZ,CACA,GAAIK,GAAG,GAAK,KAAZ,CAAmB,CACjB,MAAOA,CAAAA,GAAP,CACD,CAED,KAAMC,CAAAA,IAAI,CAAG,4BAAaL,cAAb,CAA6BC,aAA7B,CAA4CF,GAA5C,CAAb,CACA,GAAIM,IAAI,GAAK,KAAb,CAAoB,CAClB,MAAOA,CAAAA,IAAP,CACD,CAED,MAAO,MAAP,CACD", "sourcesContent": ["import { readFileSync } from 'fs'\nimport * as path from 'path'\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { getBabelError } from './parseBabel'\nimport { getCssError } from './parseCss'\nimport { getScssError } from './parseScss'\nimport { getNotFoundError } from './parseNotFoundError'\nimport { SimpleWebpackError } from './simpleWebpackError'\n\nfunction getFileData(\n  compilation: webpack.compilation.Compilation,\n  m: any\n): [string, string | null] {\n  let resolved: string\n  let ctx: string | null =\n    compilation.compiler?.context ?? compilation.context ?? null\n  if (ctx !== null && typeof m.resource === 'string') {\n    const res = path.relative(ctx, m.resource).replace(/\\\\/g, path.posix.sep)\n    resolved = res.startsWith('.') ? res : `.${path.posix.sep}${res}`\n  } else {\n    const requestShortener = compilation.requestShortener\n    if (typeof m?.readableIdentifier === 'function') {\n      resolved = m.readableIdentifier(requestShortener)\n    } else {\n      resolved = m.request ?? m.userRequest\n    }\n  }\n\n  if (resolved) {\n    let content: string | null = null\n    try {\n      content = readFileSync(\n        ctx ? path.resolve(ctx, resolved) : resolved,\n        'utf8'\n      )\n    } catch {}\n    return [resolved, content]\n  }\n\n  return ['<unknown>', null]\n}\n\nexport async function getModuleBuildError(\n  compilation: webpack.compilation.Compilation,\n  input: any\n): Promise<SimpleWebpackError | false> {\n  if (\n    !(\n      typeof input === 'object' &&\n      (input?.name === 'ModuleBuildError' ||\n        input?.name === 'ModuleNotFoundError') &&\n      Boolean(input.module) &&\n      input.error instanceof Error\n    )\n  ) {\n    return false\n  }\n\n  const err: Error = input.error\n  const [sourceFilename, sourceContent] = getFileData(compilation, input.module)\n\n  const notFoundError = await getNotFoundError(\n    compilation,\n    input,\n    sourceFilename\n  )\n  if (notFoundError !== false) {\n    return notFoundError\n  }\n\n  const babel = getBabelError(sourceFilename, err)\n  if (babel !== false) {\n    return babel\n  }\n\n  const css = getCssError(sourceFilename, err)\n  if (css !== false) {\n    return css\n  }\n\n  const scss = getScssError(sourceFilename, sourceContent, err)\n  if (scss !== false) {\n    return scss\n  }\n\n  return false\n}\n"]}