{"version": 3, "sources": ["../../../next-server/server/require.ts"], "names": ["pageNotFoundError", "page", "err", "Error", "code", "getPagePath", "distDir", "serverless", "dev", "locales", "serverBuildPath", "SERVERLESS_DIRECTORY", "SERVER_DIRECTORY", "pagesManifest", "require", "PAGES_MANIFEST", "console", "error", "pagePath", "manifestNoLocales", "key", "Object", "keys", "pathname", "requirePage", "endsWith", "promises", "readFile", "requireFontManifest", "fontManifest", "FONT_MANIFEST"], "mappings": "iMAAA,sBACA,0BACA,2CAMA,wDAEA,sEAEO,QAASA,CAAAA,iBAAT,CAA2BC,IAA3B,CAAgD,CACrD,KAAMC,CAAAA,GAAQ,CAAG,GAAIC,CAAAA,KAAJ,CAAW,gCAA+BF,IAAK,EAA/C,CAAjB,CACAC,GAAG,CAACE,IAAJ,CAAW,QAAX,CACA,MAAOF,CAAAA,GAAP,CACD,CAEM,QAASG,CAAAA,WAAT,CACLJ,IADK,CAELK,OAFK,CAGLC,UAHK,CAILC,GAJK,CAKLC,OALK,CAMG,CACR,KAAMC,CAAAA,eAAe,CAAG,eACtBJ,OADsB,CAEtBC,UAAU,EAAI,CAACC,GAAf,CAAqBG,+BAArB,CAA4CC,2BAFtB,CAAxB,CAIA,KAAMC,CAAAA,aAAa,CAAGC,OAAO,CAAC,eAC5BJ,eAD4B,CAE5BK,yBAF4B,CAAD,CAA7B,CAKA,GAAI,CACFd,IAAI,CAAG,2CAAoB,yCAAkBA,IAAlB,CAApB,CAAP,CACD,CAAC,MAAOC,GAAP,CAAY,CACZc,OAAO,CAACC,KAAR,CAAcf,GAAd,EACA,KAAMF,CAAAA,iBAAiB,CAACC,IAAD,CAAvB,CACD,CACD,GAAIiB,CAAAA,QAAQ,CAAGL,aAAa,CAACZ,IAAD,CAA5B,CAEA,GAAI,CAACY,aAAa,CAACZ,IAAD,CAAd,EAAwBQ,OAA5B,CAAqC,CACnC,KAAMU,CAAAA,iBAAuC,CAAG,EAAhD,CAEA,IAAK,KAAMC,CAAAA,GAAX,GAAkBC,CAAAA,MAAM,CAACC,IAAP,CAAYT,aAAZ,CAAlB,CAA8C,CAC5CM,iBAAiB,CAAC,6CAAoBC,GAApB,CAAyBX,OAAzB,EAAkCc,QAAnC,CAAjB,CACEV,aAAa,CAACO,GAAD,CADf,CAED,CACDF,QAAQ,CAAGC,iBAAiB,CAAClB,IAAD,CAA5B,CACD,CAED,GAAI,CAACiB,QAAL,CAAe,CACb,KAAMlB,CAAAA,iBAAiB,CAACC,IAAD,CAAvB,CACD,CACD,MAAO,eAAKS,eAAL,CAAsBQ,QAAtB,CAAP,CACD,CAEM,QAASM,CAAAA,WAAT,CACLvB,IADK,CAELK,OAFK,CAGLC,UAHK,CAIA,CACL,KAAMW,CAAAA,QAAQ,CAAGb,WAAW,CAACJ,IAAD,CAAOK,OAAP,CAAgBC,UAAhB,CAA5B,CACA,GAAIW,QAAQ,CAACO,QAAT,CAAkB,OAAlB,CAAJ,CAAgC,CAC9B,MAAOC,cAASC,QAAT,CAAkBT,QAAlB,CAA4B,MAA5B,CAAP,CACD,CACD,MAAOJ,CAAAA,OAAO,CAACI,QAAD,CAAd,CACD,CAEM,QAASU,CAAAA,mBAAT,CAA6BtB,OAA7B,CAA8CC,UAA9C,CAAmE,CACxE,KAAMG,CAAAA,eAAe,CAAG,eACtBJ,OADsB,CAEtBC,UAAU,CAAGI,+BAAH,CAA0BC,2BAFd,CAAxB,CAIA,KAAMiB,CAAAA,YAAY,CAAGf,OAAO,CAAC,eAAKJ,eAAL,CAAsBoB,wBAAtB,CAAD,CAA5B,CACA,MAAOD,CAAAA,YAAP,CACD", "sourcesContent": ["import { promises } from 'fs'\nimport { join } from 'path'\nimport {\n  PAGES_MANIFEST,\n  SERVER_DIRECTORY,\n  SERVERLESS_DIRECTORY,\n  FONT_MANIFEST,\n} from '../lib/constants'\nimport { normalizePagePath, denormalizePagePath } from './normalize-page-path'\nimport { PagesManifest } from '../../build/webpack/plugins/pages-manifest-plugin'\nimport { normalizeLocalePath } from '../lib/i18n/normalize-locale-path'\n\nexport function pageNotFoundError(page: string): Error {\n  const err: any = new Error(`Cannot find module for page: ${page}`)\n  err.code = 'ENOENT'\n  return err\n}\n\nexport function getPagePath(\n  page: string,\n  distDir: string,\n  serverless: boolean,\n  dev?: boolean,\n  locales?: string[]\n): string {\n  const serverBuildPath = join(\n    distDir,\n    serverless && !dev ? SERVERLESS_DIRECTORY : SERVER_DIRECTORY\n  )\n  const pagesManifest = require(join(\n    serverBuildPath,\n    PAGES_MANIFEST\n  )) as PagesManifest\n\n  try {\n    page = denormalizePagePath(normalizePagePath(page))\n  } catch (err) {\n    console.error(err)\n    throw pageNotFoundError(page)\n  }\n  let pagePath = pagesManifest[page]\n\n  if (!pagesManifest[page] && locales) {\n    const manifestNoLocales: typeof pagesManifest = {}\n\n    for (const key of Object.keys(pagesManifest)) {\n      manifestNoLocales[normalizeLocalePath(key, locales).pathname] =\n        pagesManifest[key]\n    }\n    pagePath = manifestNoLocales[page]\n  }\n\n  if (!pagePath) {\n    throw pageNotFoundError(page)\n  }\n  return join(serverBuildPath, pagePath)\n}\n\nexport function requirePage(\n  page: string,\n  distDir: string,\n  serverless: boolean\n): any {\n  const pagePath = getPagePath(page, distDir, serverless)\n  if (pagePath.endsWith('.html')) {\n    return promises.readFile(pagePath, 'utf8')\n  }\n  return require(pagePath)\n}\n\nexport function requireFontManifest(distDir: string, serverless: boolean) {\n  const serverBuildPath = join(\n    distDir,\n    serverless ? SERVERLESS_DIRECTORY : SERVER_DIRECTORY\n  )\n  const fontManifest = require(join(serverBuildPath, FONT_MANIFEST))\n  return fontManifest\n}\n"]}