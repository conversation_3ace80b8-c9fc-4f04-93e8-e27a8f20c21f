{"version": 3, "sources": ["../../server/next.ts"], "names": ["ServerImpl", "getServerImpl", "undefined", "default", "NextServer", "constructor", "options", "serverPromise", "server", "reqHandlerPromise", "preparedAssetPrefix", "getRequestHandler", "req", "res", "parsedUrl", "requestHandler", "getServerRequestHandler", "setAssetPrefix", "assetPrefix", "logError", "args", "render", "getServer", "renderToHTML", "renderError", "renderErrorToHTML", "render404", "serveStatic", "prepare", "close", "createServer", "dev", "DevServer", "require", "loadConfig", "phase", "PHASE_DEVELOPMENT_SERVER", "PHASE_PRODUCTION_SERVER", "dir", "conf", "setTimeout", "then", "bind", "standardEnv", "Error", "isNextDevCommand", "process", "env", "NODE_ENV", "includes", "log", "warn", "NON_STANDARD_NODE_ENV", "console", "module", "exports"], "mappings": "+EAAA,qDAKA,2CACA,gEACA,4EACA,0BACA,wD,w4BAcA,GAAIA,CAAAA,UAAJ,CAEA,KAAMC,CAAAA,aAAa,CAAG,SAAY,CAChC,GAAID,UAAU,GAAKE,SAAnB,CACEF,UAAU,CAAG,CAAC,iEAAa,mCAAb,GAAD,EAAoDG,OAAjE,CACF,MAAOH,CAAAA,UAAP,CACD,CAJD,CAMO,KAAMI,CAAAA,UAAW,CAOtBC,WAAW,CAACC,OAAD,CAAiC,MANpCC,aAMoC,aALpCC,MAKoC,aAJpCC,iBAIoC,aAHpCC,mBAGoC,aAF5CJ,OAE4C,QAC1C,KAAKA,OAAL,CAAeA,OAAf,CACD,CAEDK,iBAAiB,EAAG,CAClB,MAAO,OACLC,GADK,CAELC,GAFK,CAGLC,SAHK,GAIF,CACH,KAAMC,CAAAA,cAAc,CAAG,KAAM,MAAKC,uBAAL,EAA7B,CACA,MAAOD,CAAAA,cAAc,CAACH,GAAD,CAAMC,GAAN,CAAWC,SAAX,CAArB,CACD,CAPD,CAQD,CAEDG,cAAc,CAACC,WAAD,CAAsB,CAClC,GAAI,KAAKV,MAAT,CAAiB,CACf,KAAKA,MAAL,CAAYS,cAAZ,CAA2BC,WAA3B,EACD,CAFD,IAEO,CACL,KAAKR,mBAAL,CAA2BQ,WAA3B,CACD,CACF,CAEDC,QAAQ,CAAC,GAAGC,IAAJ,CAA0C,CAChD,GAAI,KAAKZ,MAAT,CAAiB,CACf,KAAKA,MAAL,CAAYW,QAAZ,CAAqB,GAAGC,IAAxB,EACD,CACF,CAED,KAAMC,CAAAA,MAAN,CAAa,GAAGD,IAAhB,CAAoD,CAClD,KAAMZ,CAAAA,MAAM,CAAG,KAAM,MAAKc,SAAL,EAArB,CACA,MAAOd,CAAAA,MAAM,CAACa,MAAP,CAAc,GAAGD,IAAjB,CAAP,CACD,CAED,KAAMG,CAAAA,YAAN,CAAmB,GAAGH,IAAtB,CAAgE,CAC9D,KAAMZ,CAAAA,MAAM,CAAG,KAAM,MAAKc,SAAL,EAArB,CACA,MAAOd,CAAAA,MAAM,CAACe,YAAP,CAAoB,GAAGH,IAAvB,CAAP,CACD,CAED,KAAMI,CAAAA,WAAN,CAAkB,GAAGJ,IAArB,CAA8D,CAC5D,KAAMZ,CAAAA,MAAM,CAAG,KAAM,MAAKc,SAAL,EAArB,CACA,MAAOd,CAAAA,MAAM,CAACgB,WAAP,CAAmB,GAAGJ,IAAtB,CAAP,CACD,CAED,KAAMK,CAAAA,iBAAN,CAAwB,GAAGL,IAA3B,CAA0E,CACxE,KAAMZ,CAAAA,MAAM,CAAG,KAAM,MAAKc,SAAL,EAArB,CACA,MAAOd,CAAAA,MAAM,CAACiB,iBAAP,CAAyB,GAAGL,IAA5B,CAAP,CACD,CAED,KAAMM,CAAAA,SAAN,CAAgB,GAAGN,IAAnB,CAA0D,CACxD,KAAMZ,CAAAA,MAAM,CAAG,KAAM,MAAKc,SAAL,EAArB,CACA,MAAOd,CAAAA,MAAM,CAACkB,SAAP,CAAiB,GAAGN,IAApB,CAAP,CACD,CAED,KAAMO,CAAAA,WAAN,CAAkB,GAAGP,IAArB,CAA8D,CAC5D,KAAMZ,CAAAA,MAAM,CAAG,KAAM,MAAKc,SAAL,EAArB,CACA,MAAOd,CAAAA,MAAM,CAACmB,WAAP,CAAmB,GAAGP,IAAtB,CAAP,CACD,CAED,KAAMQ,CAAAA,OAAN,EAAgB,CACd,KAAMpB,CAAAA,MAAM,CAAG,KAAM,MAAKc,SAAL,EAArB,CACA,MAAOd,CAAAA,MAAM,CAACoB,OAAP,EAAP,CACD,CAED,KAAMC,CAAAA,KAAN,EAAc,CACZ,KAAMrB,CAAAA,MAAM,CAAG,KAAM,MAAKc,SAAL,EAArB,CACA,MAAQd,CAAAA,MAAD,CAAgBqB,KAAhB,EAAP,CACD,CAED,KAAcC,CAAAA,YAAd,CACExB,OADF,CAKmB,CACjB,GAAIA,OAAO,CAACyB,GAAZ,CAAiB,CACf,KAAMC,CAAAA,SAAS,CAAGC,OAAO,CAAC,mBAAD,CAAP,CAA6B9B,OAA/C,CACA,MAAO,IAAI6B,CAAAA,SAAJ,CAAc1B,OAAd,CAAP,CACD,CACD,MAAO,KAAK,KAAML,CAAAA,aAAa,EAAxB,EAA4BK,OAA5B,CAAP,CACD,CAED,KAAc4B,CAAAA,UAAd,EAA2B,CACzB,KAAMC,CAAAA,KAAK,CAAG,KAAK7B,OAAL,CAAayB,GAAb,CACVK,oCADU,CAEVC,mCAFJ,CAGA,KAAMC,CAAAA,GAAG,CAAG,kBAAQ,KAAKhC,OAAL,CAAagC,GAAb,EAAoB,GAA5B,CAAZ,CACA,KAAMC,CAAAA,IAAI,CAAG,KAAM,oBAAWJ,KAAX,CAAkBG,GAAlB,CAAuB,KAAKhC,OAAL,CAAaiC,IAApC,CAAnB,CACA,MAAOA,CAAAA,IAAP,CACD,CAED,KAAcjB,CAAAA,SAAd,EAA0B,CACxB,GAAI,CAAC,KAAKf,aAAV,CAAyB,CACvBiC,UAAU,CAACvC,aAAD,CAAgB,EAAhB,CAAV,CACA,KAAKM,aAAL,CAAqB,KAAK2B,UAAL,GAAkBO,IAAlB,CAAuB,KAAOF,CAAAA,IAAP,EAAgB,CAC1D,KAAK/B,MAAL,CAAc,KAAM,MAAKsB,YAAL,CAAkB,CACpC,GAAG,KAAKxB,OAD4B,CAEpCiC,IAFoC,CAAlB,CAApB,CAIA,GAAI,KAAK7B,mBAAT,CAA8B,CAC5B,KAAKF,MAAL,CAAYS,cAAZ,CAA2B,KAAKP,mBAAhC,EACD,CACD,MAAO,MAAKF,MAAZ,CACD,CAToB,CAArB,CAUD,CACD,MAAO,MAAKD,aAAZ,CACD,CAED,KAAcS,CAAAA,uBAAd,EAAwC,CACtC;AACA,GAAI,CAAC,KAAKP,iBAAV,CAA6B,CAC3B,KAAKA,iBAAL,CAAyB,KAAKa,SAAL,GAAiBmB,IAAjB,CAAuBjC,MAAD,EAC7CA,MAAM,CAACG,iBAAP,GAA2B+B,IAA3B,CAAgClC,MAAhC,CADuB,CAAzB,CAGD,CACD,MAAO,MAAKC,iBAAZ,CACD,CA3HqB,CA8HxB;8BACA,QAASqB,CAAAA,YAAT,CAAsBxB,OAAtB,CAAkE,CAChE,KAAMqC,CAAAA,WAAW,CAAG,CAAC,YAAD,CAAe,aAAf,CAA8B,MAA9B,CAApB,CAEA,GAAIrC,OAAO,EAAI,IAAf,CAAqB,CACnB,KAAM,IAAIsC,CAAAA,KAAJ,CACJ,wGADI,CAAN,CAGD,CAED,GACE,CAAEtC,OAAD,CAAiBuC,gBAAlB,EACAC,OAAO,CAACC,GAAR,CAAYC,QADZ,EAEA,CAACL,WAAW,CAACM,QAAZ,CAAqBH,OAAO,CAACC,GAAR,CAAYC,QAAjC,CAHH,CAIE,CACAE,GAAG,CAACC,IAAJ,CAASC,gCAAT,EACD,CAED,GAAI9C,OAAO,CAACyB,GAAZ,CAAiB,CACf,GAAI,MAAOzB,CAAAA,OAAO,CAACyB,GAAf,GAAuB,SAA3B,CAAsC,CACpCsB,OAAO,CAACF,IAAR,CACE,oIADF,EAGD,CACF,CAED,MAAO,IAAI/C,CAAAA,UAAJ,CAAeE,OAAf,CAAP,CACD,CAED;AACAgD,MAAM,CAACC,OAAP,CAAiBzB,YAAjB,CACAyB,OAAO,CAAGD,MAAM,CAACC,OAAjB,CAEA;aACezB,Y", "sourcesContent": ["import '../next-server/server/node-polyfill-fetch'\nimport {\n  default as Server,\n  ServerConstructor,\n} from '../next-server/server/next-server'\nimport { NON_STANDARD_NODE_ENV } from '../lib/constants'\nimport * as log from '../build/output/log'\nimport loadConfig, { NextConfig } from '../next-server/server/config'\nimport { resolve } from 'path'\nimport {\n  PHASE_DEVELOPMENT_SERVER,\n  PHASE_PRODUCTION_SERVER,\n} from '../next-server/lib/constants'\nimport { IncomingMessage, ServerResponse } from 'http'\nimport { UrlWithParsedQuery } from 'url'\n\ntype NextServerConstructor = ServerConstructor & {\n  /**\n   * Whether to launch Next.js in dev mode - @default false\n   */\n  dev?: boolean\n}\n\nlet ServerImpl: typeof Server\n\nconst getServerImpl = async () => {\n  if (ServerImpl === undefined)\n    ServerImpl = (await import('../next-server/server/next-server')).default\n  return ServerImpl\n}\n\nexport class NextServer {\n  private serverPromise?: Promise<Server>\n  private server?: Server\n  private reqHandlerPromise?: Promise<any>\n  private preparedAssetPrefix?: string\n  options: NextServerConstructor\n\n  constructor(options: NextServerConstructor) {\n    this.options = options\n  }\n\n  getRequestHandler() {\n    return async (\n      req: IncomingMessage,\n      res: ServerResponse,\n      parsedUrl?: UrlWithParsedQuery\n    ) => {\n      const requestHandler = await this.getServerRequestHandler()\n      return requestHandler(req, res, parsedUrl)\n    }\n  }\n\n  setAssetPrefix(assetPrefix: string) {\n    if (this.server) {\n      this.server.setAssetPrefix(assetPrefix)\n    } else {\n      this.preparedAssetPrefix = assetPrefix\n    }\n  }\n\n  logError(...args: Parameters<Server['logError']>) {\n    if (this.server) {\n      this.server.logError(...args)\n    }\n  }\n\n  async render(...args: Parameters<Server['render']>) {\n    const server = await this.getServer()\n    return server.render(...args)\n  }\n\n  async renderToHTML(...args: Parameters<Server['renderToHTML']>) {\n    const server = await this.getServer()\n    return server.renderToHTML(...args)\n  }\n\n  async renderError(...args: Parameters<Server['renderError']>) {\n    const server = await this.getServer()\n    return server.renderError(...args)\n  }\n\n  async renderErrorToHTML(...args: Parameters<Server['renderErrorToHTML']>) {\n    const server = await this.getServer()\n    return server.renderErrorToHTML(...args)\n  }\n\n  async render404(...args: Parameters<Server['render404']>) {\n    const server = await this.getServer()\n    return server.render404(...args)\n  }\n\n  async serveStatic(...args: Parameters<Server['serveStatic']>) {\n    const server = await this.getServer()\n    return server.serveStatic(...args)\n  }\n\n  async prepare() {\n    const server = await this.getServer()\n    return server.prepare()\n  }\n\n  async close() {\n    const server = await this.getServer()\n    return (server as any).close()\n  }\n\n  private async createServer(\n    options: NextServerConstructor & {\n      conf: NextConfig\n      isNextDevCommand?: boolean\n    }\n  ): Promise<Server> {\n    if (options.dev) {\n      const DevServer = require('./next-dev-server').default\n      return new DevServer(options)\n    }\n    return new (await getServerImpl())(options)\n  }\n\n  private async loadConfig() {\n    const phase = this.options.dev\n      ? PHASE_DEVELOPMENT_SERVER\n      : PHASE_PRODUCTION_SERVER\n    const dir = resolve(this.options.dir || '.')\n    const conf = await loadConfig(phase, dir, this.options.conf)\n    return conf\n  }\n\n  private async getServer() {\n    if (!this.serverPromise) {\n      setTimeout(getServerImpl, 10)\n      this.serverPromise = this.loadConfig().then(async (conf) => {\n        this.server = await this.createServer({\n          ...this.options,\n          conf,\n        })\n        if (this.preparedAssetPrefix) {\n          this.server.setAssetPrefix(this.preparedAssetPrefix)\n        }\n        return this.server\n      })\n    }\n    return this.serverPromise\n  }\n\n  private async getServerRequestHandler() {\n    // Memoize request handler creation\n    if (!this.reqHandlerPromise) {\n      this.reqHandlerPromise = this.getServer().then((server) =>\n        server.getRequestHandler().bind(server)\n      )\n    }\n    return this.reqHandlerPromise\n  }\n}\n\n// This file is used for when users run `require('next')`\nfunction createServer(options: NextServerConstructor): NextServer {\n  const standardEnv = ['production', 'development', 'test']\n\n  if (options == null) {\n    throw new Error(\n      'The server has not been instantiated properly. https://nextjs.org/docs/messages/invalid-server-options'\n    )\n  }\n\n  if (\n    !(options as any).isNextDevCommand &&\n    process.env.NODE_ENV &&\n    !standardEnv.includes(process.env.NODE_ENV)\n  ) {\n    log.warn(NON_STANDARD_NODE_ENV)\n  }\n\n  if (options.dev) {\n    if (typeof options.dev !== 'boolean') {\n      console.warn(\n        \"Warning: 'dev' is not a boolean which could introduce unexpected behavior. https://nextjs.org/docs/messages/invalid-server-options\"\n      )\n    }\n  }\n\n  return new NextServer(options)\n}\n\n// Support commonjs `require('next')`\nmodule.exports = createServer\nexports = module.exports\n\n// Support `import next from 'next'`\nexport default createServer\n"]}