{"version": 3, "sources": ["../../../server/lib/utils.ts"], "names": ["printAndExit", "message", "code", "console", "log", "error", "process", "exit", "getNodeOptionsWithoutInspect", "NODE_INSPECT_RE", "env", "NODE_OPTIONS", "replace"], "mappings": "yIAAO,QAASA,CAAAA,YAAT,CAAsBC,OAAtB,CAAuCC,IAAI,CAAG,CAA9C,CAAiD,CACtD,GAAIA,IAAI,GAAK,CAAb,CAAgB,CACdC,OAAO,CAACC,GAAR,CAAYH,OAAZ,EACD,CAFD,IAEO,CACLE,OAAO,CAACE,KAAR,CAAcJ,OAAd,EACD,CAEDK,OAAO,CAACC,IAAR,CAAaL,IAAb,EACD,CAEM,QAASM,CAAAA,4BAAT,EAAwC,CAC7C,KAAMC,CAAAA,eAAe,CAAG,8BAAxB,CACA,MAAO,CAACH,OAAO,CAACI,GAAR,CAAYC,YAAZ,EAA4B,EAA7B,EAAiCC,OAAjC,CAAyCH,eAAzC,CAA0D,EAA1D,CAAP,CACD", "sourcesContent": ["export function printAndExit(message: string, code = 1) {\n  if (code === 0) {\n    console.log(message)\n  } else {\n    console.error(message)\n  }\n\n  process.exit(code)\n}\n\nexport function getNodeOptionsWithoutInspect() {\n  const NODE_INSPECT_RE = /--inspect(-brk)?(=\\S+)?( |$)/\n  return (process.env.NODE_OPTIONS || '').replace(NODE_INSPECT_RE, '')\n}\n"]}