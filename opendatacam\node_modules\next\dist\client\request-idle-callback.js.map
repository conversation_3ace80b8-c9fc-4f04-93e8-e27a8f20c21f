{"version": 3, "sources": ["../../client/request-idle-callback.ts"], "names": ["requestIdleCallback", "self", "cb", "start", "Date", "now", "setTimeout", "didTimeout", "timeRemaining", "Math", "max", "cancelIdleCallback", "id", "clearTimeout"], "mappings": "mGAmBO,KAAMA,CAAAA,mBAAmB,CAC7B,MAAOC,CAAAA,IAAP,GAAgB,WAAhB,EAA+BA,IAAI,CAACD,mBAArC,EACA,SACEE,EADF,CAEkB,CAChB,GAAIC,CAAAA,KAAK,CAAGC,IAAI,CAACC,GAAL,EAAZ,CACA,MAAOC,CAAAA,UAAU,CAAC,UAAY,CAC5BJ,EAAE,CAAC,CACDK,UAAU,CAAE,KADX,CAEDC,aAAa,CAAE,UAAY,CACzB,MAAOC,CAAAA,IAAI,CAACC,GAAL,CAAS,CAAT,CAAY,IAAMN,IAAI,CAACC,GAAL,GAAaF,KAAnB,CAAZ,CAAP,CACD,CAJA,CAAD,CAAF,CAMD,CAPgB,CAOd,CAPc,CAAjB,CAQD,CAdI,C,gDAgBA,KAAMQ,CAAAA,kBAAkB,CAC5B,MAAOV,CAAAA,IAAP,GAAgB,WAAhB,EAA+BA,IAAI,CAACU,kBAArC,EACA,SAAUC,EAAV,CAAyC,CACvC,MAAOC,CAAAA,YAAY,CAACD,EAAD,CAAnB,CACD,CAJI,C", "sourcesContent": ["type RequestIdleCallbackHandle = any\ntype RequestIdleCallbackOptions = {\n  timeout: number\n}\ntype RequestIdleCallbackDeadline = {\n  readonly didTimeout: boolean\n  timeRemaining: () => number\n}\n\ndeclare global {\n  interface Window {\n    requestIdleCallback: (\n      callback: (deadline: RequestIdleCallbackDeadline) => void,\n      opts?: RequestIdleCallbackOptions\n    ) => RequestIdleCallbackHandle\n    cancelIdleCallback: (id: RequestIdleCallbackHandle) => void\n  }\n}\n\nexport const requestIdleCallback =\n  (typeof self !== 'undefined' && self.requestIdleCallback) ||\n  function (\n    cb: (deadline: RequestIdleCallbackDeadline) => void\n  ): NodeJS.Timeout {\n    let start = Date.now()\n    return setTimeout(function () {\n      cb({\n        didTimeout: false,\n        timeRemaining: function () {\n          return Math.max(0, 50 - (Date.now() - start))\n        },\n      })\n    }, 1)\n  }\n\nexport const cancelIdleCallback =\n  (typeof self !== 'undefined' && self.cancelIdleCallback) ||\n  function (id: RequestIdle<PERSON>allbackHandle) {\n    return clearTimeout(id)\n  }\n"]}