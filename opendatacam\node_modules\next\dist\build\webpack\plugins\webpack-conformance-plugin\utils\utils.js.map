{"version": 3, "sources": ["../../../../../../build/webpack/plugins/webpack-conformance-plugin/utils/utils.ts"], "names": ["assert", "require", "strict", "deepEqual", "a", "b", "deepStrictEqual", "_"], "mappings": "iEAAA,KAAMA,CAAAA,MAAM,CAAGC,OAAO,CAAC,QAAD,CAAP,CAAkBC,MAAjC,CAEO,QAASC,CAAAA,SAAT,CAAmBC,CAAnB,CAA2BC,CAA3B,CAAmC,CACxC,GAAI,CACFL,MAAM,CAACM,eAAP,CAAuBF,CAAvB,CAA0BC,CAA1B,EACA,MAAO,KAAP,CACD,CAAC,MAAOE,CAAP,CAAU,CACV,MAAO,MAAP,CACD,CACF", "sourcesContent": ["const assert = require('assert').strict\n\nexport function deepEqual(a: any, b: any) {\n  try {\n    assert.deepStrictEqual(a, b)\n    return true\n  } catch (_) {\n    return false\n  }\n}\n"]}