{"version": 3, "sources": ["../../../next-server/lib/router-context.ts"], "names": ["RouterContext", "React", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "kEAAA,oD,mFAGO,KAAMA,CAAAA,aAAa,cAAGC,eAAMC,aAAN,CAAgC,IAAhC,CAAtB,C,oCAEP,GAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,GAAyB,YAA7B,CAA2C,CACzCL,aAAa,CAACM,WAAd,CAA4B,eAA5B,CACD", "sourcesContent": ["import React from 'react'\nimport { NextRouter } from './router/router'\n\nexport const RouterContext = React.createContext<NextRouter>(null as any)\n\nif (process.env.NODE_ENV !== 'production') {\n  RouterContext.displayName = 'RouterContext'\n}\n"]}