{"version": 3, "sources": ["../../../../../../build/webpack/plugins/webpack-conformance-plugin/checks/react-sync-scripts-conformance-check.ts"], "names": ["ErrorMessage", "CONFORMANCE_ERROR_PREFIX", "WarningMessage", "CONFORMANCE_WARNING_PREFIX", "ErrorDescription", "EARLY_EXIT_SUCCESS_RESULT", "result", "IConformanceTestStatus", "SUCCESS", "ReactSyncScriptsConformanceCheck", "constructor", "AllowedSources", "allowedSources", "getAstNode", "visitor", "inspectNode", "path", "request", "node", "arguments", "length", "propsNode", "properties", "props", "reduce", "originalProps", "prop", "key", "name", "value", "includes", "src", "FAILED", "warnings", "message"], "mappings": "0JAAA,+CAOA,uCAQA,8CACA,4CACO,KAAMA,CAAAA,YAAoB,CAAI,GAAEC,mCAAyB,8CAAzD,C,kCACA,KAAMC,CAAAA,cAAsB,CAAI,GAAEC,qCAA2B,8CAA7D,C,sCACA,KAAMC,CAAAA,gBAAgB,CAAI,EAA1B,C,0CACP,KAAMC,CAAAA,yBAAiD,CAAG,CACxDC,MAAM,CAAEC,sCAAuBC,OADyB,CAA1D,CAOO,KAAMC,CAAAA,gCACwB,CAEnCC,WAAW,CAAC,CACVC,cADU,EAEiC,EAFlC,CAEsC,MAHzCC,cAGyC,CAHd,EAGc,CAC/C,GAAID,cAAJ,CAAoB,CAClB,KAAKC,cAAL,CAAsBD,cAAtB,CACD,CACF,CAEME,UAAP,EAAyC,CACvC,MAAO,CACL,CACEC,OAAO,CAAE,qBADX,CAEEC,WAAW,CAAE,CAACC,IAAD,CAAiB,CAAEC,OAAF,CAAjB,GAAuD,CAClE,KAAM,CAAEC,IAAF,EAAgDF,IAAtD,CACA,GAAI,CAACE,IAAI,CAACC,SAAN,EAAmBD,IAAI,CAACC,SAAL,CAAeC,MAAf,CAAwB,CAA/C,CAAkD,CAChD,MAAOf,CAAAA,yBAAP,CACD,CACD,GAAI,0CAA4Ba,IAA5B,CAAJ,CAAuC,CACrC,KAAMG,CAAAA,SAAS,CAAGH,IAAI,CAACC,SAAL,CAAe,CAAf,CAAlB,CACA,GAAI,CAACE,SAAS,CAACC,UAAf,CAA2B,CACzB,MAAOjB,CAAAA,yBAAP,CACD,CACD,KAAMkB,CAAAA,KAEL,CAAGF,SAAS,CAACC,UAAV,CAAqBE,MAArB,CAA4B,CAACC,aAAD,CAAgBC,IAAhB,GAA8B,CAC5D;AACAD,aAAa,CAACC,IAAI,CAACC,GAAL,CAASC,IAAV,CAAb,CAA+BF,IAAI,CAACG,KAAL,CAAWA,KAA1C,CACA,MAAOJ,CAAAA,aAAP,CACD,CAJG,CAID,EAJC,CAFJ,CAOA,GACE,SAAWF,CAAAA,KAAX,EACA,SAAWA,CAAAA,KADX,EAEA,EAAE,OAASA,CAAAA,KAAX,CAFA,EAGA,KAAKX,cAAL,CAAoBkB,QAApB,CAA6BP,KAAK,CAACQ,GAAnC,CAJF,CAKE,CACA,MAAO1B,CAAAA,yBAAP,CACD,CAED;AACA,MAAO,CACLC,MAAM,CAAEC,sCAAuByB,MAD1B,CAELC,QAAQ,CAAE,CACR,CACEC,OAAO,CAAG,GAAEhC,cAAe,IAAG,gCAC5Be,OAD4B,CAE5B,8CAHJ,CADQ,CAFL,CAAP,CAUD,CACD,MAAOZ,CAAAA,yBAAP,CACD,CAzCH,CADK,CAAP,CA6CD,CAxDkC,C", "sourcesContent": ["import {\n  IWebpackConformanceTest,\n  IGetAstNodeResult,\n  IParsedModuleDetails,\n  IConformanceTestResult,\n  IConformanceTestStatus,\n} from '../TestInterface'\nimport {\n  CONFORMANCE_ERROR_PREFIX,\n  CONFORMANCE_WARNING_PREFIX,\n} from '../constants'\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport { namedTypes } from 'ast-types/'\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport { NodePath } from 'ast-types/lib/node-path'\nimport { getLocalFileName } from '../utils/file-utils'\nimport { isNodeCreatingScriptElement } from '../utils/ast-utils'\nexport const ErrorMessage: string = `${CONFORMANCE_ERROR_PREFIX}: A sync script was found in a react module.`\nexport const WarningMessage: string = `${CONFORMANCE_WARNING_PREFIX}: A sync script was found in a react module.`\nexport const ErrorDescription = ``\nconst EARLY_EXIT_SUCCESS_RESULT: IConformanceTestResult = {\n  result: IConformanceTestStatus.SUCCESS,\n}\n\nexport interface ReactSyncScriptsConformanceCheckOptions {\n  AllowedSources?: String[]\n}\nexport class ReactSyncScriptsConformanceCheck\n  implements IWebpackConformanceTest {\n  private allowedSources: String[] = []\n  constructor({\n    AllowedSources,\n  }: ReactSyncScriptsConformanceCheckOptions = {}) {\n    if (AllowedSources) {\n      this.allowedSources = AllowedSources\n    }\n  }\n\n  public getAstNode(): IGetAstNodeResult[] {\n    return [\n      {\n        visitor: 'visitCallExpression',\n        inspectNode: (path: NodePath, { request }: IParsedModuleDetails) => {\n          const { node }: { node: namedTypes.CallExpression } = path\n          if (!node.arguments || node.arguments.length < 2) {\n            return EARLY_EXIT_SUCCESS_RESULT\n          }\n          if (isNodeCreatingScriptElement(node)) {\n            const propsNode = node.arguments[1] as namedTypes.ObjectExpression\n            if (!propsNode.properties) {\n              return EARLY_EXIT_SUCCESS_RESULT\n            }\n            const props: {\n              [key: string]: string\n            } = propsNode.properties.reduce((originalProps, prop: any) => {\n              // @ts-ignore\n              originalProps[prop.key.name] = prop.value.value\n              return originalProps\n            }, {})\n            if (\n              'defer' in props ||\n              'async' in props ||\n              !('src' in props) ||\n              this.allowedSources.includes(props.src)\n            ) {\n              return EARLY_EXIT_SUCCESS_RESULT\n            }\n\n            // Todo: Add an absolute error case for modern js when class is a subclass of next/head.\n            return {\n              result: IConformanceTestStatus.FAILED,\n              warnings: [\n                {\n                  message: `${WarningMessage} ${getLocalFileName(\n                    request\n                  )}. This can potentially delay FCP/FP metrics.`,\n                },\n              ],\n            }\n          }\n          return EARLY_EXIT_SUCCESS_RESULT\n        },\n      },\n    ]\n  }\n}\n"]}