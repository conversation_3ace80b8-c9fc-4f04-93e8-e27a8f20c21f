{"version": 3, "sources": ["../../../telemetry/events/plugins.ts"], "names": ["EVENT_PLUGIN_PRESENT", "eventNextPlugins", "dir", "packageJsonPath", "cwd", "dependencies", "devDependencies", "require", "deps", "Object", "keys", "reduce", "events", "plugin", "version", "push", "eventName", "payload", "packageName", "packageVersion", "_"], "mappings": "+EAAA,0E,mFAEA,KAAMA,CAAAA,oBAAoB,CAAG,uBAA7B,CASO,cAAeC,CAAAA,gBAAf,CACLC,GADK,CAE6B,CAClC,GAAI,CACF,KAAMC,CAAAA,eAAe,CAAG,KAAM,oBAAO,cAAP,CAAuB,CAAEC,GAAG,CAAEF,GAAP,CAAvB,CAA9B,CACA,GAAI,CAACC,eAAL,CAAsB,CACpB,MAAO,EAAP,CACD,CAED,KAAM,CAAEE,YAAY,CAAG,EAAjB,CAAqBC,eAAe,CAAG,EAAvC,EAA8CC,OAAO,CAACJ,eAAD,CAA3D,CAEA,KAAMK,CAAAA,IAAI,CAAG,CAAE,GAAGF,eAAL,CAAsB,GAAGD,YAAzB,CAAb,CAEA,MAAOI,CAAAA,MAAM,CAACC,IAAP,CAAYF,IAAZ,EAAkBG,MAAlB,CACL,CAACC,MAAD,CAA6BC,MAA7B,GAAoE,CAClE,KAAMC,CAAAA,OAAO,CAAGN,IAAI,CAACK,MAAD,CAApB,CACA;AACA,GAAI,CAACC,OAAL,CAAc,CACZ,MAAOF,CAAAA,MAAP,CACD,CAEDA,MAAM,CAACG,IAAP,CAAY,CACVC,SAAS,CAAEhB,oBADD,CAEViB,OAAO,CAAE,CACPC,WAAW,CAAEL,MADN,CAEPM,cAAc,CAAEL,OAFT,CAFC,CAAZ,EAQA,MAAOF,CAAAA,MAAP,CACD,CAjBI,CAkBL,EAlBK,CAAP,CAoBD,CAAC,MAAOQ,CAAP,CAAU,CACV,MAAO,EAAP,CACD,CACF", "sourcesContent": ["import findUp from 'next/dist/compiled/find-up'\n\nconst EVENT_PLUGIN_PRESENT = 'NEXT_PACKAGE_DETECTED'\ntype NextPluginsEvent = {\n  eventName: string\n  payload: {\n    packageName: string\n    packageVersion: string\n  }\n}\n\nexport async function eventNextPlugins(\n  dir: string\n): Promise<Array<NextPluginsEvent>> {\n  try {\n    const packageJsonPath = await findUp('package.json', { cwd: dir })\n    if (!packageJsonPath) {\n      return []\n    }\n\n    const { dependencies = {}, devDependencies = {} } = require(packageJsonPath)\n\n    const deps = { ...devDependencies, ...dependencies }\n\n    return Object.keys(deps).reduce(\n      (events: NextPluginsEvent[], plugin: string): NextPluginsEvent[] => {\n        const version = deps[plugin]\n        // Don't add deps without a version set\n        if (!version) {\n          return events\n        }\n\n        events.push({\n          eventName: EVENT_PLUGIN_PRESENT,\n          payload: {\n            packageName: plugin,\n            packageVersion: version,\n          },\n        })\n\n        return events\n      },\n      []\n    )\n  } catch (_) {\n    return []\n  }\n}\n"]}