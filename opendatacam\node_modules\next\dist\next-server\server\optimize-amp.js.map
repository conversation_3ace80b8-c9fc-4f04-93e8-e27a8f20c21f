{"version": 3, "sources": ["../../../next-server/server/optimize-amp.ts"], "names": ["optimize", "html", "config", "AmpOptimizer", "require", "_", "optimizer", "create", "transformHtml"], "mappings": "8DAAe,cAAeA,CAAAA,QAAf,CACbC,IADa,CAEbC,MAFa,CAGI,CACjB,GAAIC,CAAAA,YAAJ,CACA,GAAI,CACFA,YAAY,CAAGC,OAAO,CAAC,kDAAD,CAAtB,CACD,CAAC,MAAOC,CAAP,CAAU,CACV,MAAOJ,CAAAA,IAAP,CACD,CACD,KAAMK,CAAAA,SAAS,CAAGH,YAAY,CAACI,MAAb,CAAoBL,MAApB,CAAlB,CACA,MAAOI,CAAAA,SAAS,CAACE,aAAV,CAAwBP,IAAxB,CAA8BC,MAA9B,CAAP,CACD", "sourcesContent": ["export default async function optimize(\n  html: string,\n  config: any\n): Promise<string> {\n  let AmpOptimizer\n  try {\n    AmpOptimizer = require('next/dist/compiled/@ampproject/toolbox-optimizer')\n  } catch (_) {\n    return html\n  }\n  const optimizer = AmpOptimizer.create(config)\n  return optimizer.transformHtml(html, config)\n}\n"]}