{"version": 3, "sources": ["../../../../../next-server/lib/router/utils/sorted-routes.ts"], "names": ["UrlNode", "placeholder", "children", "Map", "slug<PERSON><PERSON>", "restSlugName", "optionalRestSlugName", "insert", "url<PERSON><PERSON>", "_insert", "split", "filter", "Boolean", "smoosh", "_smoosh", "prefix", "childrenPaths", "keys", "sort", "splice", "indexOf", "routes", "map", "c", "get", "reduce", "prev", "curr", "push", "r", "slice", "Error", "unshift", "url<PERSON><PERSON>s", "slug<PERSON><PERSON><PERSON>", "isCatchAll", "length", "nextSegment", "startsWith", "endsWith", "segmentName", "isOptional", "substring", "handleSlug", "previousSlug", "nextSlug", "for<PERSON>ach", "slug", "replace", "has", "set", "getSortedRoutes", "normalizedPages", "root", "pagePath"], "mappings": "6EAAA,KAAMA,CAAAA,OAAQ,oBACZC,WADY,CACW,IADX,MAEZC,QAFY,CAEqB,GAAIC,CAAAA,GAAJ,EAFrB,MAGZC,QAHY,CAGc,IAHd,MAIZC,YAJY,CAIkB,IAJlB,MAKZC,oBALY,CAK0B,IAL1B,EAOZC,MAAM,CAACC,OAAD,CAAwB,CAC5B,KAAKC,OAAL,CAAaD,OAAO,CAACE,KAAR,CAAc,GAAd,EAAmBC,MAAnB,CAA0BC,OAA1B,CAAb,CAAiD,EAAjD,CAAqD,KAArD,EACD,CAEDC,MAAM,EAAa,CACjB,MAAO,MAAKC,OAAL,EAAP,CACD,CAEOA,OAAR,CAAgBC,MAAc,CAAG,GAAjC,CAAgD,CAC9C,KAAMC,CAAAA,aAAa,CAAG,CAAC,GAAG,KAAKd,QAAL,CAAce,IAAd,EAAJ,EAA0BC,IAA1B,EAAtB,CACA,GAAI,KAAKd,QAAL,GAAkB,IAAtB,CAA4B,CAC1BY,aAAa,CAACG,MAAd,CAAqBH,aAAa,CAACI,OAAd,CAAsB,IAAtB,CAArB,CAAkD,CAAlD,EACD,CACD,GAAI,KAAKf,YAAL,GAAsB,IAA1B,CAAgC,CAC9BW,aAAa,CAACG,MAAd,CAAqBH,aAAa,CAACI,OAAd,CAAsB,OAAtB,CAArB,CAAqD,CAArD,EACD,CACD,GAAI,KAAKd,oBAAL,GAA8B,IAAlC,CAAwC,CACtCU,aAAa,CAACG,MAAd,CAAqBH,aAAa,CAACI,OAAd,CAAsB,SAAtB,CAArB,CAAuD,CAAvD,EACD,CAED,KAAMC,CAAAA,MAAM,CAAGL,aAAa,CACzBM,GADY,CACPC,CAAD,EAAO,KAAKrB,QAAL,CAAcsB,GAAd,CAAkBD,CAAlB,EAAsBT,OAAtB,CAA+B,GAAEC,MAAO,GAAEQ,CAAE,GAA5C,CADC,EAEZE,MAFY,CAEL,CAACC,IAAD,CAAOC,IAAP,GAAgB,CAAC,GAAGD,IAAJ,CAAU,GAAGC,IAAb,CAFX,CAE+B,EAF/B,CAAf,CAIA,GAAI,KAAKvB,QAAL,GAAkB,IAAtB,CAA4B,CAC1BiB,MAAM,CAACO,IAAP,CACE,GAAG,KAAK1B,QAAL,CAAcsB,GAAd,CAAkB,IAAlB,EAAyBV,OAAzB,CAAkC,GAAEC,MAAO,IAAG,KAAKX,QAAS,IAA5D,CADL,EAGD,CAED,GAAI,CAAC,KAAKH,WAAV,CAAuB,CACrB,KAAM4B,CAAAA,CAAC,CAAGd,MAAM,GAAK,GAAX,CAAiB,GAAjB,CAAuBA,MAAM,CAACe,KAAP,CAAa,CAAb,CAAgB,CAAC,CAAjB,CAAjC,CACA,GAAI,KAAKxB,oBAAL,EAA6B,IAAjC,CAAuC,CACrC,KAAM,IAAIyB,CAAAA,KAAJ,CACH,uFAAsFF,CAAE,UAASA,CAAE,QAAO,KAAKvB,oBAAqB,OADjI,CAAN,CAGD,CAEDe,MAAM,CAACW,OAAP,CAAeH,CAAf,EACD,CAED,GAAI,KAAKxB,YAAL,GAAsB,IAA1B,CAAgC,CAC9BgB,MAAM,CAACO,IAAP,CACE,GAAG,KAAK1B,QAAL,CACAsB,GADA,CACI,OADJ,EAEAV,OAFA,CAES,GAAEC,MAAO,OAAM,KAAKV,YAAa,IAF1C,CADL,EAKD,CAED,GAAI,KAAKC,oBAAL,GAA8B,IAAlC,CAAwC,CACtCe,MAAM,CAACO,IAAP,CACE,GAAG,KAAK1B,QAAL,CACAsB,GADA,CACI,SADJ,EAEAV,OAFA,CAES,GAAEC,MAAO,QAAO,KAAKT,oBAAqB,KAFnD,CADL,EAKD,CAED,MAAOe,CAAAA,MAAP,CACD,CAEOZ,OAAR,CACEwB,QADF,CAEEC,SAFF,CAGEC,UAHF,CAIQ,CACN,GAAIF,QAAQ,CAACG,MAAT,GAAoB,CAAxB,CAA2B,CACzB,KAAKnC,WAAL,CAAmB,KAAnB,CACA,OACD,CAED,GAAIkC,UAAJ,CAAgB,CACd,KAAM,IAAIJ,CAAAA,KAAJ,CAAW,6CAAX,CAAN,CACD,CAED;AACA,GAAIM,CAAAA,WAAW,CAAGJ,QAAQ,CAAC,CAAD,CAA1B,CAEA;AACA,GAAII,WAAW,CAACC,UAAZ,CAAuB,GAAvB,GAA+BD,WAAW,CAACE,QAAZ,CAAqB,GAArB,CAAnC,CAA8D,CAC5D;AACA,GAAIC,CAAAA,WAAW,CAAGH,WAAW,CAACP,KAAZ,CAAkB,CAAlB,CAAqB,CAAC,CAAtB,CAAlB,CAEA,GAAIW,CAAAA,UAAU,CAAG,KAAjB,CACA,GAAID,WAAW,CAACF,UAAZ,CAAuB,GAAvB,GAA+BE,WAAW,CAACD,QAAZ,CAAqB,GAArB,CAAnC,CAA8D,CAC5D;AACAC,WAAW,CAAGA,WAAW,CAACV,KAAZ,CAAkB,CAAlB,CAAqB,CAAC,CAAtB,CAAd,CACAW,UAAU,CAAG,IAAb,CACD,CAED,GAAID,WAAW,CAACF,UAAZ,CAAuB,KAAvB,CAAJ,CAAmC,CACjC;AACAE,WAAW,CAAGA,WAAW,CAACE,SAAZ,CAAsB,CAAtB,CAAd,CACAP,UAAU,CAAG,IAAb,CACD,CAED,GAAIK,WAAW,CAACF,UAAZ,CAAuB,GAAvB,GAA+BE,WAAW,CAACD,QAAZ,CAAqB,GAArB,CAAnC,CAA8D,CAC5D,KAAM,IAAIR,CAAAA,KAAJ,CACH,4DAA2DS,WAAY,KADpE,CAAN,CAGD,CAED,GAAIA,WAAW,CAACF,UAAZ,CAAuB,GAAvB,CAAJ,CAAiC,CAC/B,KAAM,IAAIP,CAAAA,KAAJ,CACH,wDAAuDS,WAAY,KADhE,CAAN,CAGD,CAED,QAASG,CAAAA,UAAT,CAAoBC,YAApB,CAAiDC,QAAjD,CAAmE,CACjE,GAAID,YAAY,GAAK,IAArB,CAA2B,CACzB;AACA;AACA;AACA;AACA;AACA,GAAIA,YAAY,GAAKC,QAArB,CAA+B,CAC7B;AACA,KAAM,IAAId,CAAAA,KAAJ,CACH,mEAAkEa,YAAa,UAASC,QAAS,KAD9F,CAAN,CAGD,CACF,CAEDX,SAAS,CAACY,OAAV,CAAmBC,IAAD,EAAU,CAC1B,GAAIA,IAAI,GAAKF,QAAb,CAAuB,CACrB,KAAM,IAAId,CAAAA,KAAJ,CACH,uCAAsCc,QAAS,uCAD5C,CAAN,CAGD,CAED,GAAIE,IAAI,CAACC,OAAL,CAAa,KAAb,CAAoB,EAApB,IAA4BX,WAAW,CAACW,OAAZ,CAAoB,KAApB,CAA2B,EAA3B,CAAhC,CAAgE,CAC9D,KAAM,IAAIjB,CAAAA,KAAJ,CACH,mCAAkCgB,IAAK,UAASF,QAAS,gEADtD,CAAN,CAGD,CACF,CAZD,EAcAX,SAAS,CAACN,IAAV,CAAeiB,QAAf,EACD,CAED,GAAIV,UAAJ,CAAgB,CACd,GAAIM,UAAJ,CAAgB,CACd,GAAI,KAAKpC,YAAL,EAAqB,IAAzB,CAA+B,CAC7B,KAAM,IAAI0B,CAAAA,KAAJ,CACH,wFAAuF,KAAK1B,YAAa,WAAU4B,QAAQ,CAAC,CAAD,CAAI,MAD5H,CAAN,CAGD,CAEDU,UAAU,CAAC,KAAKrC,oBAAN,CAA4BkC,WAA5B,CAAV,CACA;AACA,KAAKlC,oBAAL,CAA4BkC,WAA5B,CACA;AACAH,WAAW,CAAG,SAAd,CACD,CAZD,IAYO,CACL,GAAI,KAAK/B,oBAAL,EAA6B,IAAjC,CAAuC,CACrC,KAAM,IAAIyB,CAAAA,KAAJ,CACH,yFAAwF,KAAKzB,oBAAqB,YAAW2B,QAAQ,CAAC,CAAD,CAAI,KADtI,CAAN,CAGD,CAEDU,UAAU,CAAC,KAAKtC,YAAN,CAAoBmC,WAApB,CAAV,CACA;AACA,KAAKnC,YAAL,CAAoBmC,WAApB,CACA;AACAH,WAAW,CAAG,OAAd,CACD,CACF,CA1BD,IA0BO,CACL,GAAII,UAAJ,CAAgB,CACd,KAAM,IAAIV,CAAAA,KAAJ,CACH,qDAAoDE,QAAQ,CAAC,CAAD,CAAI,KAD7D,CAAN,CAGD,CACDU,UAAU,CAAC,KAAKvC,QAAN,CAAgBoC,WAAhB,CAAV,CACA;AACA,KAAKpC,QAAL,CAAgBoC,WAAhB,CACA;AACAH,WAAW,CAAG,IAAd,CACD,CACF,CAED;AACA,GAAI,CAAC,KAAKnC,QAAL,CAAc+C,GAAd,CAAkBZ,WAAlB,CAAL,CAAqC,CACnC,KAAKnC,QAAL,CAAcgD,GAAd,CAAkBb,WAAlB,CAA+B,GAAIrC,CAAAA,OAAJ,EAA/B,EACD,CAED,KAAKE,QAAL,CACGsB,GADH,CACOa,WADP,EAEG5B,OAFH,CAEWwB,QAAQ,CAACH,KAAT,CAAe,CAAf,CAFX,CAE8BI,SAF9B,CAEyCC,UAFzC,EAGD,CAlMW,CAqMP,QAASgB,CAAAA,eAAT,CAAyBC,eAAzB,CAA8D,CACnE;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAMC,CAAAA,IAAI,CAAG,GAAIrD,CAAAA,OAAJ,EAAb,CAEA;AACAoD,eAAe,CAACN,OAAhB,CAAyBQ,QAAD,EAAcD,IAAI,CAAC9C,MAAL,CAAY+C,QAAZ,CAAtC,EACA;AACA,MAAOD,CAAAA,IAAI,CAACxC,MAAL,EAAP,CACD", "sourcesContent": ["class UrlNode {\n  placeholder: boolean = true\n  children: Map<string, UrlNode> = new Map()\n  slugName: string | null = null\n  restSlugName: string | null = null\n  optionalRestSlugName: string | null = null\n\n  insert(urlPath: string): void {\n    this._insert(urlPath.split('/').filter(Boolean), [], false)\n  }\n\n  smoosh(): string[] {\n    return this._smoosh()\n  }\n\n  private _smoosh(prefix: string = '/'): string[] {\n    const childrenPaths = [...this.children.keys()].sort()\n    if (this.slugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[]'), 1)\n    }\n    if (this.restSlugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[...]'), 1)\n    }\n    if (this.optionalRestSlugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[[...]]'), 1)\n    }\n\n    const routes = childrenPaths\n      .map((c) => this.children.get(c)!._smoosh(`${prefix}${c}/`))\n      .reduce((prev, curr) => [...prev, ...curr], [])\n\n    if (this.slugName !== null) {\n      routes.push(\n        ...this.children.get('[]')!._smoosh(`${prefix}[${this.slugName}]/`)\n      )\n    }\n\n    if (!this.placeholder) {\n      const r = prefix === '/' ? '/' : prefix.slice(0, -1)\n      if (this.optionalRestSlugName != null) {\n        throw new Error(\n          `You cannot define a route with the same specificity as a optional catch-all route (\"${r}\" and \"${r}[[...${this.optionalRestSlugName}]]\").`\n        )\n      }\n\n      routes.unshift(r)\n    }\n\n    if (this.restSlugName !== null) {\n      routes.push(\n        ...this.children\n          .get('[...]')!\n          ._smoosh(`${prefix}[...${this.restSlugName}]/`)\n      )\n    }\n\n    if (this.optionalRestSlugName !== null) {\n      routes.push(\n        ...this.children\n          .get('[[...]]')!\n          ._smoosh(`${prefix}[[...${this.optionalRestSlugName}]]/`)\n      )\n    }\n\n    return routes\n  }\n\n  private _insert(\n    urlPaths: string[],\n    slugNames: string[],\n    isCatchAll: boolean\n  ): void {\n    if (urlPaths.length === 0) {\n      this.placeholder = false\n      return\n    }\n\n    if (isCatchAll) {\n      throw new Error(`Catch-all must be the last part of the URL.`)\n    }\n\n    // The next segment in the urlPaths list\n    let nextSegment = urlPaths[0]\n\n    // Check if the segment matches `[something]`\n    if (nextSegment.startsWith('[') && nextSegment.endsWith(']')) {\n      // Strip `[` and `]`, leaving only `something`\n      let segmentName = nextSegment.slice(1, -1)\n\n      let isOptional = false\n      if (segmentName.startsWith('[') && segmentName.endsWith(']')) {\n        // Strip optional `[` and `]`, leaving only `something`\n        segmentName = segmentName.slice(1, -1)\n        isOptional = true\n      }\n\n      if (segmentName.startsWith('...')) {\n        // Strip `...`, leaving only `something`\n        segmentName = segmentName.substring(3)\n        isCatchAll = true\n      }\n\n      if (segmentName.startsWith('[') || segmentName.endsWith(']')) {\n        throw new Error(\n          `Segment names may not start or end with extra brackets ('${segmentName}').`\n        )\n      }\n\n      if (segmentName.startsWith('.')) {\n        throw new Error(\n          `Segment names may not start with erroneous periods ('${segmentName}').`\n        )\n      }\n\n      function handleSlug(previousSlug: string | null, nextSlug: string) {\n        if (previousSlug !== null) {\n          // If the specific segment already has a slug but the slug is not `something`\n          // This prevents collisions like:\n          // pages/[post]/index.js\n          // pages/[id]/index.js\n          // Because currently multiple dynamic params on the same segment level are not supported\n          if (previousSlug !== nextSlug) {\n            // TODO: This error seems to be confusing for users, needs an error link, the description can be based on above comment.\n            throw new Error(\n              `You cannot use different slug names for the same dynamic path ('${previousSlug}' !== '${nextSlug}').`\n            )\n          }\n        }\n\n        slugNames.forEach((slug) => {\n          if (slug === nextSlug) {\n            throw new Error(\n              `You cannot have the same slug name \"${nextSlug}\" repeat within a single dynamic path`\n            )\n          }\n\n          if (slug.replace(/\\W/g, '') === nextSegment.replace(/\\W/g, '')) {\n            throw new Error(\n              `You cannot have the slug names \"${slug}\" and \"${nextSlug}\" differ only by non-word symbols within a single dynamic path`\n            )\n          }\n        })\n\n        slugNames.push(nextSlug)\n      }\n\n      if (isCatchAll) {\n        if (isOptional) {\n          if (this.restSlugName != null) {\n            throw new Error(\n              `You cannot use both an required and optional catch-all route at the same level (\"[...${this.restSlugName}]\" and \"${urlPaths[0]}\" ).`\n            )\n          }\n\n          handleSlug(this.optionalRestSlugName, segmentName)\n          // slugName is kept as it can only be one particular slugName\n          this.optionalRestSlugName = segmentName\n          // nextSegment is overwritten to [[...]] so that it can later be sorted specifically\n          nextSegment = '[[...]]'\n        } else {\n          if (this.optionalRestSlugName != null) {\n            throw new Error(\n              `You cannot use both an optional and required catch-all route at the same level (\"[[...${this.optionalRestSlugName}]]\" and \"${urlPaths[0]}\").`\n            )\n          }\n\n          handleSlug(this.restSlugName, segmentName)\n          // slugName is kept as it can only be one particular slugName\n          this.restSlugName = segmentName\n          // nextSegment is overwritten to [...] so that it can later be sorted specifically\n          nextSegment = '[...]'\n        }\n      } else {\n        if (isOptional) {\n          throw new Error(\n            `Optional route parameters are not yet supported (\"${urlPaths[0]}\").`\n          )\n        }\n        handleSlug(this.slugName, segmentName)\n        // slugName is kept as it can only be one particular slugName\n        this.slugName = segmentName\n        // nextSegment is overwritten to [] so that it can later be sorted specifically\n        nextSegment = '[]'\n      }\n    }\n\n    // If this UrlNode doesn't have the nextSegment yet we create a new child UrlNode\n    if (!this.children.has(nextSegment)) {\n      this.children.set(nextSegment, new UrlNode())\n    }\n\n    this.children\n      .get(nextSegment)!\n      ._insert(urlPaths.slice(1), slugNames, isCatchAll)\n  }\n}\n\nexport function getSortedRoutes(normalizedPages: string[]): string[] {\n  // First the UrlNode is created, and every UrlNode can have only 1 dynamic segment\n  // Eg you can't have pages/[post]/abc.js and pages/[hello]/something-else.js\n  // Only 1 dynamic segment per nesting level\n\n  // So in the case that is test/integration/dynamic-routing it'll be this:\n  // pages/[post]/comments.js\n  // pages/blog/[post]/comment/[id].js\n  // Both are fine because `pages/[post]` and `pages/blog` are on the same level\n  // So in this case `UrlNode` created here has `this.slugName === 'post'`\n  // And since your PR passed through `slugName` as an array basically it'd including it in too many possibilities\n  // Instead what has to be passed through is the upwards path's dynamic names\n  const root = new UrlNode()\n\n  // Here the `root` gets injected multiple paths, and insert will break them up into sublevels\n  normalizedPages.forEach((pagePath) => root.insert(pagePath))\n  // Smoosh will then sort those sublevels up to the point where you get the correct route definition priority\n  return root.smoosh()\n}\n"]}