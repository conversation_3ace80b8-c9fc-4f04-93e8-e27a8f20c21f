{"version": 3, "sources": ["../../../next-server/server/router.ts"], "names": ["route", "customRouteTypes", "Set", "replace<PERSON>ase<PERSON><PERSON>", "basePath", "pathname", "replace", "Router", "constructor", "headers", "fsRoutes", "rewrites", "beforeFiles", "afterFiles", "fallback", "redirects", "catchAllRoute", "dynamicRoutes", "page<PERSON><PERSON><PERSON>", "useFileSystemPublicRoutes", "locales", "setDynamicRoutes", "routes", "addFsRoute", "fsRoute", "unshift", "execute", "req", "res", "parsedUrl", "pageChecks", "memoizedPageChecker", "p", "result", "parsedUrlUpdated", "apply<PERSON>heckTrue", "checkParsedUrl", "originalFsPathname", "fsPathname", "fsParams", "match", "fsResult", "fn", "finished", "matchedPage", "normalizedFsPathname", "dynamicRoute", "pageParams", "query", "_nextBubbleNoFallback", "allRoutes", "type", "name", "requireBasePath", "checkerReq", "checkerRes", "params", "parsedCheckerUrl", "length", "_checkerReq", "_checkerRes", "_params", "originallyHadBasePath", "_nextHadBasePath", "testRoute", "currentPathname", "originalPathname", "isCustomRoute", "has", "isPublicFolderCatchall", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keepLocale", "currentPathnameNoBasePath", "localePathResult", "activeBasePath", "internal", "__next<PERSON><PERSON><PERSON>", "detectedLocale", "__nextHadTrailingSlash", "endsWith", "newParams", "hasParams", "Object", "assign", "_nextDidRewrite", "check"], "mappings": "0EAGA,iFACA,6EACA,sEAEA,2E,mFAEO,KAAMA,CAAAA,KAAK,CAAG,wBAAd,C,oBAiCP,KAAMC,CAAAA,gBAAgB,CAAG,GAAIC,CAAAA,GAAJ,CAAQ,CAAC,SAAD,CAAY,UAAZ,CAAwB,QAAxB,CAAR,CAAzB,CAEA,QAASC,CAAAA,eAAT,CAAyBC,QAAzB,CAA2CC,QAA3C,CAA6D,CAC3D;AACA,MAAOA,CAAAA,QAAQ,CAAEC,OAAV,CAAkBF,QAAlB,CAA4B,EAA5B,GAAmC,GAA1C,CACD,CAEc,KAAMG,CAAAA,MAAO,CAgB1BC,WAAW,CAAC,CACVJ,QAAQ,CAAG,EADD,CAEVK,OAAO,CAAG,EAFA,CAGVC,QAAQ,CAAG,EAHD,CAIVC,QAAQ,CAAG,CACTC,WAAW,CAAE,EADJ,CAETC,UAAU,CAAE,EAFH,CAGTC,QAAQ,CAAE,EAHD,CAJD,CASVC,SAAS,CAAG,EATF,CAUVC,aAVU,CAWVC,aAAa,CAAG,EAXN,CAYVC,WAZU,CAaVC,yBAbU,CAcVC,OAAO,CAAG,EAdA,CAAD,CA8BR,MA7CHhB,QA6CG,aA5CHK,OA4CG,aA3CHC,QA2CG,aA1CHK,SA0CG,aAzCHJ,QAyCG,aApCHK,aAoCG,aAnCHE,WAmCG,aAlCHD,aAkCG,aAjCHE,yBAiCG,aAhCHC,OAgCG,QACD,KAAKhB,QAAL,CAAgBA,QAAhB,CACA,KAAKK,OAAL,CAAeA,OAAf,CACA,KAAKC,QAAL,CAAgBA,QAAhB,CACA,KAAKC,QAAL,CAAgBA,QAAhB,CACA,KAAKI,SAAL,CAAiBA,SAAjB,CACA,KAAKG,WAAL,CAAmBA,WAAnB,CACA,KAAKF,aAAL,CAAqBA,aAArB,CACA,KAAKC,aAAL,CAAqBA,aAArB,CACA,KAAKE,yBAAL,CAAiCA,yBAAjC,CACA,KAAKC,OAAL,CAAeA,OAAf,CACD,CAEDC,gBAAgB,CAACC,MAAqB,CAAG,EAAzB,CAA6B,CAC3C,KAAKL,aAAL,CAAqBK,MAArB,CACD,CAEDC,UAAU,CAACC,OAAD,CAAiB,CACzB,KAAKd,QAAL,CAAce,OAAd,CAAsBD,OAAtB,EACD,CAED,KAAME,CAAAA,OAAN,CACEC,GADF,CAEEC,GAFF,CAGEC,SAHF,CAIoB,CAClB;AACA,KAAMC,CAAAA,UAAgD,CAAG,EAAzD,CACA,KAAMC,CAAAA,mBAAmB,CAAG,KAAOC,CAAAA,CAAP,EAAuC,CACjEA,CAAC,CAAG,6CAAoBA,CAApB,CAAuB,KAAKZ,OAA5B,EAAqCf,QAAzC,CAEA,GAAIyB,UAAU,CAACE,CAAD,CAAd,CAAmB,CACjB,MAAOF,CAAAA,UAAU,CAACE,CAAD,CAAjB,CACD,CACD,KAAMC,CAAAA,MAAM,CAAG,KAAKf,WAAL,CAAiBc,CAAjB,CAAf,CACAF,UAAU,CAACE,CAAD,CAAV,CAAgBC,MAAhB,CACA,MAAOA,CAAAA,MAAP,CACD,CATD,CAWA,GAAIC,CAAAA,gBAAgB,CAAGL,SAAvB,CAEA,KAAMM,CAAAA,cAAc,CAAG,KAAOC,CAAAA,cAAP,EAA8C,CACnE,KAAMC,CAAAA,kBAAkB,CAAGD,cAAc,CAAC/B,QAA1C,CACA,KAAMiC,CAAAA,UAAU,CAAGnC,eAAe,CAAC,KAAKC,QAAN,CAAgBiC,kBAAhB,CAAlC,CAEA,IAAK,KAAMb,CAAAA,OAAX,GAAsB,MAAKd,QAA3B,CAAqC,CACnC,KAAM6B,CAAAA,QAAQ,CAAGf,OAAO,CAACgB,KAAR,CAAcF,UAAd,CAAjB,CAEA,GAAIC,QAAJ,CAAc,CACZH,cAAc,CAAC/B,QAAf,CAA0BiC,UAA1B,CAEA,KAAMG,CAAAA,QAAQ,CAAG,KAAMjB,CAAAA,OAAO,CAACkB,EAAR,CAAWf,GAAX,CAAgBC,GAAhB,CAAqBW,QAArB,CAA+BH,cAA/B,CAAvB,CAEA,GAAIK,QAAQ,CAACE,QAAb,CAAuB,CACrB,MAAO,KAAP,CACD,CAEDP,cAAc,CAAC/B,QAAf,CAA0BgC,kBAA1B,CACD,CACF,CACD,GAAIO,CAAAA,WAAW,CAAG,KAAMb,CAAAA,mBAAmB,CAACO,UAAD,CAA3C,CAEA;AACA,GAAI,CAACM,WAAL,CAAkB,CAChB,KAAMC,CAAAA,oBAAoB,CAAG,6CAC3BP,UAD2B,CAE3B,KAAKlB,OAFsB,EAG3Bf,QAHF,CAKA,IAAK,KAAMyC,CAAAA,YAAX,GAA2B,MAAK7B,aAAhC,CAA+C,CAC7C,GAAI6B,YAAY,CAACN,KAAb,CAAmBK,oBAAnB,CAAJ,CAA8C,CAC5CD,WAAW,CAAG,IAAd,CACD,CACF,CACF,CAED;AACA,GAAIA,WAAJ,CAAiB,CACf,KAAMG,CAAAA,UAAU,CAAG,KAAK/B,aAAL,CAAmBwB,KAAnB,CAAyBJ,cAAc,CAAC/B,QAAxC,CAAnB,CACA+B,cAAc,CAAC/B,QAAf,CAA0BiC,UAA1B,CACAF,cAAc,CAACY,KAAf,CAAqBC,qBAArB,CAA6C,GAA7C,CAEA,KAAMhB,CAAAA,MAAM,CAAG,KAAM,MAAKjB,aAAL,CAAmB0B,EAAnB,CACnBf,GADmB,CAEnBC,GAFmB,CAGnBmB,UAHmB,CAInBX,cAJmB,CAArB,CAMA,MAAOH,CAAAA,MAAM,CAACU,QAAd,CACD,CACF,CAjDD,CAmDA;AACJ;AACA;AACA;AACA;AACA;AACA,MAEI,KAAMO,CAAAA,SAAS,CAAG,CAChB,GAAG,KAAKzC,OADQ,CAEhB,GAAG,KAAKM,SAFQ,CAGhB,GAAG,KAAKJ,QAAL,CAAcC,WAHD,CAIhB,GAAG,KAAKF,QAJQ,CAKhB;AACA;AACA,IAAI,KAAKS,yBAAL,CACA,CACE,CACEgC,IAAI,CAAE,OADR,CAEEC,IAAI,CAAE,cAFR,CAGEC,eAAe,CAAE,KAHnB,CAIEb,KAAK,CAAExC,KAAK,CAAC,SAAD,CAJd,CAKE0C,EAAE,CAAE,MAAOY,UAAP,CAAmBC,UAAnB,CAA+BC,MAA/B,CAAuCC,gBAAvC,GAA4D,CAC9D,GAAI,CAAEpD,QAAF,EAAeoD,gBAAnB,CACApD,QAAQ,CAAG,oDAAwBA,QAAQ,EAAI,GAApC,CAAX,CAEA,GAAI,CAACA,QAAL,CAAe,CACb,MAAO,CAAEsC,QAAQ,CAAE,KAAZ,CAAP,CACD,CAED,GAAI,KAAMZ,CAAAA,mBAAmB,CAAC1B,QAAD,CAA7B,CAAyC,CACvC,MAAO,MAAKW,aAAL,CAAmB0B,EAAnB,CACLY,UADK,CAELC,UAFK,CAGLC,MAHK,CAILC,gBAJK,CAAP,CAMD,CACD,MAAO,CAAEd,QAAQ,CAAE,KAAZ,CAAP,CACD,CAtBH,CADF,CADA,CA2BA,EA3BJ,CAPgB,CAmChB,GAAG,KAAKhC,QAAL,CAAcE,UAnCD,CAoChB,IAAI,KAAKF,QAAL,CAAcG,QAAd,CAAuB4C,MAAvB,CACA,CACE,CACEP,IAAI,CAAE,OADR,CAEEC,IAAI,CAAE,0BAFR,CAGEC,eAAe,CAAE,KAHnB,CAIEb,KAAK,CAAExC,KAAK,CAAC,SAAD,CAJd,CAKE0C,EAAE,CAAE,MACFiB,WADE,CAEFC,WAFE,CAGFC,OAHE,CAIFJ,gBAJE,GAKC,CACH,MAAO,CACLd,QAAQ,CAAE,KAAMR,CAAAA,cAAc,CAACsB,gBAAD,CADzB,CAAP,CAGD,CAdH,CADF,CAiBE,GAAG,KAAK9C,QAAL,CAAcG,QAjBnB,CADA,CAoBA,EApBJ,CApCgB,CA0DhB;AACA;AACA,IAAI,KAAKK,yBAAL,CAAiC,CAAC,KAAKH,aAAN,CAAjC,CAAwD,EAA5D,CA5DgB,CAAlB,CA8DA,KAAM8C,CAAAA,qBAAqB,CACzB,CAAC,KAAK1D,QAAN,EAAmBuB,GAAD,CAAaoC,gBADjC,CAGA,IAAK,KAAMC,CAAAA,SAAX,GAAwBd,CAAAA,SAAxB,CAAmC,CACjC;AACA;AACA;AACA;AACA,GAAIe,CAAAA,eAAe,CAAG/B,gBAAgB,CAAC7B,QAAvC,CACA,KAAM6D,CAAAA,gBAAgB,CAAGD,eAAzB,CACA,KAAMZ,CAAAA,eAAe,CAAGW,SAAS,CAACX,eAAV,GAA8B,KAAtD,CACA,KAAMc,CAAAA,aAAa,CAAGlE,gBAAgB,CAACmE,GAAjB,CAAqBJ,SAAS,CAACb,IAA/B,CAAtB,CACA,KAAMkB,CAAAA,sBAAsB,CAAGL,SAAS,CAACZ,IAAV,GAAmB,wBAAlD,CACA,KAAMkB,CAAAA,YAAY,CAAGH,aAAa,EAAIE,sBAAtC,CACA,KAAME,CAAAA,UAAU,CAAGJ,aAAnB,CAEA,KAAMK,CAAAA,yBAAyB,CAAGrE,eAAe,CAC/C,KAAKC,QAD0C,CAE/C6D,eAF+C,CAAjD,CAKA,GAAI,CAACK,YAAL,CAAmB,CACjBL,eAAe,CAAGO,yBAAlB,CACD,CAED,KAAMC,CAAAA,gBAAgB,CAAG,6CACvBD,yBADuB,CAEvB,KAAKpD,OAFkB,CAAzB,CAIA,KAAMsD,CAAAA,cAAc,CAAGJ,YAAY,CAAG,KAAKlE,QAAR,CAAmB,EAAtD,CAEA,GAAImE,UAAJ,CAAgB,CACd,GACE,CAACP,SAAS,CAACW,QAAX,EACA9C,SAAS,CAACmB,KAAV,CAAgB4B,YADhB,EAEA,CAACH,gBAAgB,CAACI,cAHpB,CAIE,CACAZ,eAAe,CAAI,GAAES,cAAe,IAAG7C,SAAS,CAACmB,KAAV,CAAgB4B,YAAa,GAClEJ,yBAAyB,GAAK,GAA9B,CAAoC,EAApC,CAAyCA,yBAC1C,EAFD,CAGD,CAED,GACG7C,GAAD,CAAamD,sBAAb,EACA,CAACb,eAAe,CAACc,QAAhB,CAAyB,GAAzB,CAFH,CAGE,CACAd,eAAe,EAAI,GAAnB,CACD,CACF,CAjBD,IAiBO,CACLA,eAAe,CAAI,GAChBtC,GAAD,CAAaoC,gBAAb,CAAgCW,cAAhC,CAAiD,EAClD,GACCA,cAAc,EAAID,gBAAgB,CAACpE,QAAjB,GAA8B,GAAhD,CACI,EADJ,CAEIoE,gBAAgB,CAACpE,QACtB,EAND,CAOD,CAED,GAAI2E,CAAAA,SAAS,CAAGhB,SAAS,CAACxB,KAAV,CAAgByB,eAAhB,CAAhB,CAEA,GAAID,SAAS,CAACI,GAAV,EAAiBY,SAArB,CAAgC,CAC9B,KAAMC,CAAAA,SAAS,CAAG,iCAAStD,GAAT,CAAcqC,SAAS,CAACI,GAAxB,CAA6BlC,gBAAgB,CAACc,KAA9C,CAAlB,CAEA,GAAIiC,SAAJ,CAAe,CACbC,MAAM,CAACC,MAAP,CAAcH,SAAd,CAAyBC,SAAzB,EACD,CAFD,IAEO,CACLD,SAAS,CAAG,KAAZ,CACD,CACF,CAED;AACA,GAAIA,SAAJ,CAAe,CACb;AACA;AACA,GAAI,CAACV,YAAL,CAAmB,CACjB,GAAI,CAACR,qBAAD,EAA0B,CAAEnC,GAAD,CAAayD,eAA5C,CAA6D,CAC3D,GAAI/B,eAAJ,CAAqB,CACnB;AACA,MAAO,MAAP,CACD,CACD;AACA;AACA,SACD,CAEDnB,gBAAgB,CAAC7B,QAAjB,CAA4B4D,eAA5B,CACD,CAED,KAAMhC,CAAAA,MAAM,CAAG,KAAM+B,CAAAA,SAAS,CAACtB,EAAV,CAAaf,GAAb,CAAkBC,GAAlB,CAAuBoD,SAAvB,CAAkC9C,gBAAlC,CAArB,CAEA;AACA,GAAID,MAAM,CAACU,QAAX,CAAqB,CACnB,MAAO,KAAP,CACD,CAED;AACA;AACA,GAAI,CAAC2B,YAAL,CAAmB,CACjBpC,gBAAgB,CAAC7B,QAAjB,CAA4B6D,gBAA5B,CACD,CAED,GAAIjC,MAAM,CAAC5B,QAAX,CAAqB,CACnB6B,gBAAgB,CAAC7B,QAAjB,CAA4B4B,MAAM,CAAC5B,QAAnC,CACD,CAED,GAAI4B,MAAM,CAACe,KAAX,CAAkB,CAChBd,gBAAgB,CAACc,KAAjB,CAAyB,CACvB,GAAGd,gBAAgB,CAACc,KADG,CAEvB,GAAGf,MAAM,CAACe,KAFa,CAAzB,CAID,CAED;AACA,GAAIgB,SAAS,CAACqB,KAAV,GAAoB,IAAxB,CAA8B,CAC5B,GAAI,KAAMlD,CAAAA,cAAc,CAACD,gBAAD,CAAxB,CAA4C,CAC1C,MAAO,KAAP,CACD,CACF,CACF,CACF,CACD,MAAO,MAAP,CACD,CAzUyB,C", "sourcesContent": ["import { IncomingMessage, ServerResponse } from 'http'\nimport { UrlWithParsedQuery } from 'url'\n\nimport pathMatch from '../lib/router/utils/path-match'\nimport { removePathTrailingSlash } from '../../client/normalize-trailing-slash'\nimport { normalizeLocalePath } from '../lib/i18n/normalize-locale-path'\nimport { RouteHas } from '../../lib/load-custom-routes'\nimport { matchHas } from '../lib/router/utils/prepare-destination'\n\nexport const route = pathMatch()\n\nexport type Params = { [param: string]: any }\n\nexport type RouteMatch = (pathname: string | null | undefined) => false | Params\n\ntype RouteResult = {\n  finished: boolean\n  pathname?: string\n  query?: { [k: string]: string }\n}\n\nexport type Route = {\n  match: RouteMatch\n  has?: RouteHas[]\n  type: string\n  check?: boolean\n  statusCode?: number\n  name: string\n  requireBasePath?: false\n  internal?: true\n  fn: (\n    req: IncomingMessage,\n    res: ServerResponse,\n    params: Params,\n    parsedUrl: UrlWithParsedQuery\n  ) => Promise<RouteResult> | RouteResult\n}\n\nexport type DynamicRoutes = Array<{ page: string; match: RouteMatch }>\n\nexport type PageChecker = (pathname: string) => Promise<boolean>\n\nconst customRouteTypes = new Set(['rewrite', 'redirect', 'header'])\n\nfunction replaceBasePath(basePath: string, pathname: string) {\n  // If replace ends up replacing the full url it'll be `undefined`, meaning we have to default it to `/`\n  return pathname!.replace(basePath, '') || '/'\n}\n\nexport default class Router {\n  basePath: string\n  headers: Route[]\n  fsRoutes: Route[]\n  redirects: Route[]\n  rewrites: {\n    beforeFiles: Route[]\n    afterFiles: Route[]\n    fallback: Route[]\n  }\n  catchAllRoute: Route\n  pageChecker: PageChecker\n  dynamicRoutes: DynamicRoutes\n  useFileSystemPublicRoutes: boolean\n  locales: string[]\n\n  constructor({\n    basePath = '',\n    headers = [],\n    fsRoutes = [],\n    rewrites = {\n      beforeFiles: [],\n      afterFiles: [],\n      fallback: [],\n    },\n    redirects = [],\n    catchAllRoute,\n    dynamicRoutes = [],\n    pageChecker,\n    useFileSystemPublicRoutes,\n    locales = [],\n  }: {\n    basePath: string\n    headers: Route[]\n    fsRoutes: Route[]\n    rewrites: {\n      beforeFiles: Route[]\n      afterFiles: Route[]\n      fallback: Route[]\n    }\n    redirects: Route[]\n    catchAllRoute: Route\n    dynamicRoutes: DynamicRoutes | undefined\n    pageChecker: PageChecker\n    useFileSystemPublicRoutes: boolean\n    locales: string[]\n  }) {\n    this.basePath = basePath\n    this.headers = headers\n    this.fsRoutes = fsRoutes\n    this.rewrites = rewrites\n    this.redirects = redirects\n    this.pageChecker = pageChecker\n    this.catchAllRoute = catchAllRoute\n    this.dynamicRoutes = dynamicRoutes\n    this.useFileSystemPublicRoutes = useFileSystemPublicRoutes\n    this.locales = locales\n  }\n\n  setDynamicRoutes(routes: DynamicRoutes = []) {\n    this.dynamicRoutes = routes\n  }\n\n  addFsRoute(fsRoute: Route) {\n    this.fsRoutes.unshift(fsRoute)\n  }\n\n  async execute(\n    req: IncomingMessage,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<boolean> {\n    // memoize page check calls so we don't duplicate checks for pages\n    const pageChecks: { [name: string]: Promise<boolean> } = {}\n    const memoizedPageChecker = async (p: string): Promise<boolean> => {\n      p = normalizeLocalePath(p, this.locales).pathname\n\n      if (pageChecks[p]) {\n        return pageChecks[p]\n      }\n      const result = this.pageChecker(p)\n      pageChecks[p] = result\n      return result\n    }\n\n    let parsedUrlUpdated = parsedUrl\n\n    const applyCheckTrue = async (checkParsedUrl: UrlWithParsedQuery) => {\n      const originalFsPathname = checkParsedUrl.pathname\n      const fsPathname = replaceBasePath(this.basePath, originalFsPathname!)\n\n      for (const fsRoute of this.fsRoutes) {\n        const fsParams = fsRoute.match(fsPathname)\n\n        if (fsParams) {\n          checkParsedUrl.pathname = fsPathname\n\n          const fsResult = await fsRoute.fn(req, res, fsParams, checkParsedUrl)\n\n          if (fsResult.finished) {\n            return true\n          }\n\n          checkParsedUrl.pathname = originalFsPathname\n        }\n      }\n      let matchedPage = await memoizedPageChecker(fsPathname)\n\n      // If we didn't match a page check dynamic routes\n      if (!matchedPage) {\n        const normalizedFsPathname = normalizeLocalePath(\n          fsPathname,\n          this.locales\n        ).pathname\n\n        for (const dynamicRoute of this.dynamicRoutes) {\n          if (dynamicRoute.match(normalizedFsPathname)) {\n            matchedPage = true\n          }\n        }\n      }\n\n      // Matched a page or dynamic route so render it using catchAllRoute\n      if (matchedPage) {\n        const pageParams = this.catchAllRoute.match(checkParsedUrl.pathname)\n        checkParsedUrl.pathname = fsPathname\n        checkParsedUrl.query._nextBubbleNoFallback = '1'\n\n        const result = await this.catchAllRoute.fn(\n          req,\n          res,\n          pageParams as Params,\n          checkParsedUrl\n        )\n        return result.finished\n      }\n    }\n\n    /*\n      Desired routes order\n      - headers\n      - redirects\n      - Check filesystem (including pages), if nothing found continue\n      - User rewrites (checking filesystem and pages each match)\n    */\n\n    const allRoutes = [\n      ...this.headers,\n      ...this.redirects,\n      ...this.rewrites.beforeFiles,\n      ...this.fsRoutes,\n      // We only check the catch-all route if public page routes hasn't been\n      // disabled\n      ...(this.useFileSystemPublicRoutes\n        ? [\n            {\n              type: 'route',\n              name: 'page checker',\n              requireBasePath: false,\n              match: route('/:path*'),\n              fn: async (checkerReq, checkerRes, params, parsedCheckerUrl) => {\n                let { pathname } = parsedCheckerUrl\n                pathname = removePathTrailingSlash(pathname || '/')\n\n                if (!pathname) {\n                  return { finished: false }\n                }\n\n                if (await memoizedPageChecker(pathname)) {\n                  return this.catchAllRoute.fn(\n                    checkerReq,\n                    checkerRes,\n                    params,\n                    parsedCheckerUrl\n                  )\n                }\n                return { finished: false }\n              },\n            } as Route,\n          ]\n        : []),\n      ...this.rewrites.afterFiles,\n      ...(this.rewrites.fallback.length\n        ? [\n            {\n              type: 'route',\n              name: 'dynamic route/page check',\n              requireBasePath: false,\n              match: route('/:path*'),\n              fn: async (\n                _checkerReq,\n                _checkerRes,\n                _params,\n                parsedCheckerUrl\n              ) => {\n                return {\n                  finished: await applyCheckTrue(parsedCheckerUrl),\n                }\n              },\n            } as Route,\n            ...this.rewrites.fallback,\n          ]\n        : []),\n\n      // We only check the catch-all route if public page routes hasn't been\n      // disabled\n      ...(this.useFileSystemPublicRoutes ? [this.catchAllRoute] : []),\n    ]\n    const originallyHadBasePath =\n      !this.basePath || (req as any)._nextHadBasePath\n\n    for (const testRoute of allRoutes) {\n      // if basePath is being used, the basePath will still be included\n      // in the pathname here to allow custom-routes to require containing\n      // it or not, filesystem routes and pages must always include the basePath\n      // if it is set\n      let currentPathname = parsedUrlUpdated.pathname as string\n      const originalPathname = currentPathname\n      const requireBasePath = testRoute.requireBasePath !== false\n      const isCustomRoute = customRouteTypes.has(testRoute.type)\n      const isPublicFolderCatchall = testRoute.name === 'public folder catchall'\n      const keepBasePath = isCustomRoute || isPublicFolderCatchall\n      const keepLocale = isCustomRoute\n\n      const currentPathnameNoBasePath = replaceBasePath(\n        this.basePath,\n        currentPathname\n      )\n\n      if (!keepBasePath) {\n        currentPathname = currentPathnameNoBasePath\n      }\n\n      const localePathResult = normalizeLocalePath(\n        currentPathnameNoBasePath,\n        this.locales\n      )\n      const activeBasePath = keepBasePath ? this.basePath : ''\n\n      if (keepLocale) {\n        if (\n          !testRoute.internal &&\n          parsedUrl.query.__nextLocale &&\n          !localePathResult.detectedLocale\n        ) {\n          currentPathname = `${activeBasePath}/${parsedUrl.query.__nextLocale}${\n            currentPathnameNoBasePath === '/' ? '' : currentPathnameNoBasePath\n          }`\n        }\n\n        if (\n          (req as any).__nextHadTrailingSlash &&\n          !currentPathname.endsWith('/')\n        ) {\n          currentPathname += '/'\n        }\n      } else {\n        currentPathname = `${\n          (req as any)._nextHadBasePath ? activeBasePath : ''\n        }${\n          activeBasePath && localePathResult.pathname === '/'\n            ? ''\n            : localePathResult.pathname\n        }`\n      }\n\n      let newParams = testRoute.match(currentPathname)\n\n      if (testRoute.has && newParams) {\n        const hasParams = matchHas(req, testRoute.has, parsedUrlUpdated.query)\n\n        if (hasParams) {\n          Object.assign(newParams, hasParams)\n        } else {\n          newParams = false\n        }\n      }\n\n      // Check if the match function matched\n      if (newParams) {\n        // since we require basePath be present for non-custom-routes we\n        // 404 here when we matched an fs route\n        if (!keepBasePath) {\n          if (!originallyHadBasePath && !(req as any)._nextDidRewrite) {\n            if (requireBasePath) {\n              // consider this a non-match so the 404 renders\n              return false\n            }\n            // page checker occurs before rewrites so we need to continue\n            // to check those since they don't always require basePath\n            continue\n          }\n\n          parsedUrlUpdated.pathname = currentPathname\n        }\n\n        const result = await testRoute.fn(req, res, newParams, parsedUrlUpdated)\n\n        // The response was handled\n        if (result.finished) {\n          return true\n        }\n\n        // since the fs route didn't match we need to re-add the basePath\n        // to continue checking rewrites with the basePath present\n        if (!keepBasePath) {\n          parsedUrlUpdated.pathname = originalPathname\n        }\n\n        if (result.pathname) {\n          parsedUrlUpdated.pathname = result.pathname\n        }\n\n        if (result.query) {\n          parsedUrlUpdated.query = {\n            ...parsedUrlUpdated.query,\n            ...result.query,\n          }\n        }\n\n        // check filesystem\n        if (testRoute.check === true) {\n          if (await applyCheckTrue(parsedUrlUpdated)) {\n            return true\n          }\n        }\n      }\n    }\n    return false\n  }\n}\n"]}