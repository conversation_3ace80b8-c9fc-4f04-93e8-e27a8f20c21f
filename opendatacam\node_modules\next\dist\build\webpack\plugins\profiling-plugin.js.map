{"version": 3, "sources": ["../../../../build/webpack/plugins/profiling-plugin.ts"], "names": ["pluginName", "spans", "WeakMap", "getNormalModuleLoaderHook", "compilation", "isWebpack5", "webpack", "NormalModule", "getCompilationHooks", "loader", "hooks", "normalModuleLoader", "Profiling<PERSON><PERSON><PERSON>", "compiler", "apply", "traceTopLevelHooks", "traceCompilationHooks", "traceHookPair", "spanName", "startHook", "stopHook", "attrs", "onSetSpan", "span", "tap", "traceLoopedHook", "compile", "done", "name", "set", "environment", "afterEnvironment", "options", "mode", "invalid", "beforeCompile", "afterCompile", "buildModule", "module", "compilerSpan", "get", "id", "setAttribute", "userRequest", "loaderContext", "parentSpan", "currentTraceSpan", "succeedModule", "stop", "beforeChunks", "after<PERSON><PERSON><PERSON>", "optimize", "reviveModules", "optimizeModules", "afterOptimizeModules", "optimizeChunks", "afterOptimizeChunks", "optimizeTree", "afterOptimizeTree", "beforeHash", "afterHash"], "mappings": "kFAAA,2DACA,+CAEA,KAAMA,CAAAA,UAAU,CAAG,iBAAnB,CACO,KAAMC,CAAAA,KAAK,CAAG,GAAIC,CAAAA,OAAJ,EAAd,C,oBAEP,QAASC,CAAAA,yBAAT,CAAmCC,WAAnC,CAAqD,CACnD,GAAIC,mBAAJ,CAAgB,CACd;AACA,MAAOC,kBAAQC,YAAR,CAAqBC,mBAArB,CAAyCJ,WAAzC,EAAsDK,MAA7D,CACD,CAED,MAAOL,CAAAA,WAAW,CAACM,KAAZ,CAAkBC,kBAAzB,CACD,CAEM,KAAMC,CAAAA,eAAgB,oBAC3BC,QAD2B,SAG3BC,KAAK,CAACD,QAAD,CAAgB,CACnB,KAAKE,kBAAL,CAAwBF,QAAxB,EACA,KAAKG,qBAAL,CAA2BH,QAA3B,EACA,KAAKA,QAAL,CAAgBA,QAAhB,CACD,CAEDI,aAAa,CACXC,QADW,CAEXC,SAFW,CAGXC,QAHW,CAIXC,KAJW,CAKXC,SALW,CAMX,CACA,GAAIC,CAAAA,IAAJ,CACAJ,SAAS,CAACK,GAAV,CAAcxB,UAAd,CAA0B,IAAM,CAC9BuB,IAAI,CAAG,qBAAU,KAAKV,QAAf,CAAyBK,QAAzB,CAAmCG,KAAnC,CAAP,CACAC,SAAS,MAAT,QAAAA,SAAS,CAAGC,IAAH,CAAT,CACD,CAHD,EAIAH,QAAQ,CAACI,GAAT,CAAaxB,UAAb,CAAyB,IAAM,CAC7B;AACA;AACA;AACA,GAAI,CAACuB,IAAL,CAAW,CACT,OACD,CACD,oBAAS,KAAKV,QAAd,CAAwBU,IAAxB,EACD,CARD,EASD,CAEDE,eAAe,CAACP,QAAD,CAAmBC,SAAnB,CAAmCC,QAAnC,CAAkD,CAC/D,GAAIG,CAAAA,IAAJ,CACAJ,SAAS,CAACK,GAAV,CAAcxB,UAAd,CAA0B,IAAM,CAC9B,GAAI,CAACuB,IAAL,CAAW,CACTA,IAAI,CAAG,qBAAU,KAAKV,QAAf,CAAyBK,QAAzB,CAAP,CACD,CACF,CAJD,EAKAE,QAAQ,CAACI,GAAT,CAAaxB,UAAb,CAAyB,IAAM,CAC7B,oBAAS,KAAKa,QAAd,CAAwBU,IAAxB,EACD,CAFD,EAGD,CAEDR,kBAAkB,CAACF,QAAD,CAAgB,CAChC,KAAKI,aAAL,CACE,iBADF,CAEEJ,QAAQ,CAACH,KAAT,CAAegB,OAFjB,CAGEb,QAAQ,CAACH,KAAT,CAAeiB,IAHjB,CAIE,IAAM,CACJ,MAAO,CAAEC,IAAI,CAAEf,QAAQ,CAACe,IAAjB,CAAP,CACD,CANH,CAOGL,IAAD,EAAUtB,KAAK,CAAC4B,GAAN,CAAUhB,QAAV,CAAoBU,IAApB,CAPZ,EASA,KAAKN,aAAL,CACE,qBADF,CAEEJ,QAAQ,CAACH,KAAT,CAAeoB,WAFjB,CAGEjB,QAAQ,CAACH,KAAT,CAAeqB,gBAHjB,EAKA,GAAIlB,QAAQ,CAACmB,OAAT,CAAiBC,IAAjB,GAA0B,aAA9B,CAA6C,CAC3C,KAAKhB,aAAL,CACE,qBADF,CAEEJ,QAAQ,CAACH,KAAT,CAAewB,OAFjB,CAGErB,QAAQ,CAACH,KAAT,CAAeiB,IAHjB,EAKD,CACF,CAEDX,qBAAqB,CAACH,QAAD,CAAgB,CACnC,GAAIR,mBAAJ,CAAgB,CACd,KAAKY,aAAL,CACE,qBADF,CAEEJ,QAAQ,CAACH,KAAT,CAAeyB,aAFjB,CAGEtB,QAAQ,CAACH,KAAT,CAAe0B,YAHjB,EAKD,CAEDvB,QAAQ,CAACH,KAAT,CAAeN,WAAf,CAA2BoB,GAA3B,CAA+BxB,UAA/B,CAA4CI,WAAD,EAAsB,CAC/DA,WAAW,CAACM,KAAZ,CAAkB2B,WAAlB,CAA8Bb,GAA9B,CAAkCxB,UAAlC,CAA+CsC,MAAD,EAAiB,CAC7D,KAAMC,CAAAA,YAAY,CAAGtC,KAAK,CAACuC,GAAN,CAAU3B,QAAV,CAArB,CACA,GAAI,CAAC0B,YAAL,CAAmB,CACjB,OACD,CAED,KAAMhB,CAAAA,IAAI,CAAG,iBAAM,cAAN,CAAsBgB,YAAY,CAACE,EAAnC,CAAb,CACAlB,IAAI,CAACmB,YAAL,CAAkB,MAAlB,CAA0BJ,MAAM,CAACK,WAAjC,EACA1C,KAAK,CAAC4B,GAAN,CAAUS,MAAV,CAAkBf,IAAlB,EACD,CATD,EAWApB,yBAAyB,CAACC,WAAD,CAAzB,CAAuCoB,GAAvC,CACExB,UADF,CAEE,CAAC4C,aAAD,CAAqBN,MAArB,GAAqC,CACnC,KAAMO,CAAAA,UAAU,CAAG5C,KAAK,CAACuC,GAAN,CAAUF,MAAV,CAAnB,CACAM,aAAa,CAACE,gBAAd,CAAiCD,UAAjC,CACD,CALH,EAQAzC,WAAW,CAACM,KAAZ,CAAkBqC,aAAlB,CAAgCvB,GAAhC,CAAoCxB,UAApC,CAAiDsC,MAAD,EAAiB,gBAC/D,YAAArC,KAAK,CAACuC,GAAN,CAAUF,MAAV,2BAAmBU,IAAnB,GACD,CAFD,EAIA,KAAK/B,aAAL,CACE,iCADF,CAEEb,WAAW,CAACM,KAAZ,CAAkBuC,YAFpB,CAGE7C,WAAW,CAACM,KAAZ,CAAkBwC,WAHpB,EAKA,KAAKjC,aAAL,CACE,8BADF,CAEEb,WAAW,CAACM,KAAZ,CAAkByC,QAFpB,CAGE/C,WAAW,CAACM,KAAZ,CAAkB0C,aAHpB,EAKA,KAAK3B,eAAL,CACE,sCADF,CAEErB,WAAW,CAACM,KAAZ,CAAkB2C,eAFpB,CAGEjD,WAAW,CAACM,KAAZ,CAAkB4C,oBAHpB,EAKA,KAAK7B,eAAL,CACE,qCADF,CAEErB,WAAW,CAACM,KAAZ,CAAkB6C,cAFpB,CAGEnD,WAAW,CAACM,KAAZ,CAAkB8C,mBAHpB,EAKA,KAAKvC,aAAL,CACE,mCADF,CAEEb,WAAW,CAACM,KAAZ,CAAkB+C,YAFpB,CAGErD,WAAW,CAACM,KAAZ,CAAkBgD,iBAHpB,EAKA,KAAKzC,aAAL,CACE,0BADF,CAEEb,WAAW,CAACM,KAAZ,CAAkBiD,UAFpB,CAGEvD,WAAW,CAACM,KAAZ,CAAkBkD,SAHpB,EAKD,CAtDD,EAuDD,CApI0B,C", "sourcesContent": ["import { webpack, isWebpack5 } from 'next/dist/compiled/webpack/webpack'\nimport { trace, stackPush, stackPop, Span } from '../../../telemetry/trace'\n\nconst pluginName = 'ProfilingPlugin'\nexport const spans = new WeakMap<any, Span>()\n\nfunction getNormalModuleLoaderHook(compilation: any) {\n  if (isWebpack5) {\n    // @ts-ignore TODO: Remove ignore when webpack 5 is stable\n    return webpack.NormalModule.getCompilationHooks(compilation).loader\n  }\n\n  return compilation.hooks.normalModuleLoader\n}\n\nexport class ProfilingPlugin {\n  compiler: any\n\n  apply(compiler: any) {\n    this.traceTopLevelHooks(compiler)\n    this.traceCompilationHooks(compiler)\n    this.compiler = compiler\n  }\n\n  traceHookPair(\n    spanName: string,\n    startHook: any,\n    stopHook: any,\n    attrs?: any,\n    onSetSpan?: (span: Span) => void\n  ) {\n    let span: Span | undefined\n    startHook.tap(pluginName, () => {\n      span = stackPush(this.compiler, spanName, attrs)\n      onSetSpan?.(span)\n    })\n    stopHook.tap(pluginName, () => {\n      // `stopHook` may be triggered when `startHook` has not in cases\n      // where `stopHook` is used as the terminating event for more\n      // than one pair of hooks.\n      if (!span) {\n        return\n      }\n      stackPop(this.compiler, span)\n    })\n  }\n\n  traceLoopedHook(spanName: string, startHook: any, stopHook: any) {\n    let span: Span | undefined\n    startHook.tap(pluginName, () => {\n      if (!span) {\n        span = stackPush(this.compiler, spanName)\n      }\n    })\n    stopHook.tap(pluginName, () => {\n      stackPop(this.compiler, span)\n    })\n  }\n\n  traceTopLevelHooks(compiler: any) {\n    this.traceHookPair(\n      'webpack-compile',\n      compiler.hooks.compile,\n      compiler.hooks.done,\n      () => {\n        return { name: compiler.name }\n      },\n      (span) => spans.set(compiler, span)\n    )\n    this.traceHookPair(\n      'webpack-prepare-env',\n      compiler.hooks.environment,\n      compiler.hooks.afterEnvironment\n    )\n    if (compiler.options.mode === 'development') {\n      this.traceHookPair(\n        'webpack-invalidated',\n        compiler.hooks.invalid,\n        compiler.hooks.done\n      )\n    }\n  }\n\n  traceCompilationHooks(compiler: any) {\n    if (isWebpack5) {\n      this.traceHookPair(\n        'webpack-compilation',\n        compiler.hooks.beforeCompile,\n        compiler.hooks.afterCompile\n      )\n    }\n\n    compiler.hooks.compilation.tap(pluginName, (compilation: any) => {\n      compilation.hooks.buildModule.tap(pluginName, (module: any) => {\n        const compilerSpan = spans.get(compiler)\n        if (!compilerSpan) {\n          return\n        }\n\n        const span = trace('build-module', compilerSpan.id)\n        span.setAttribute('name', module.userRequest)\n        spans.set(module, span)\n      })\n\n      getNormalModuleLoaderHook(compilation).tap(\n        pluginName,\n        (loaderContext: any, module: any) => {\n          const parentSpan = spans.get(module)\n          loaderContext.currentTraceSpan = parentSpan\n        }\n      )\n\n      compilation.hooks.succeedModule.tap(pluginName, (module: any) => {\n        spans.get(module)?.stop()\n      })\n\n      this.traceHookPair(\n        'webpack-compilation-chunk-graph',\n        compilation.hooks.beforeChunks,\n        compilation.hooks.afterChunks\n      )\n      this.traceHookPair(\n        'webpack-compilation-optimize',\n        compilation.hooks.optimize,\n        compilation.hooks.reviveModules\n      )\n      this.traceLoopedHook(\n        'webpack-compilation-optimize-modules',\n        compilation.hooks.optimizeModules,\n        compilation.hooks.afterOptimizeModules\n      )\n      this.traceLoopedHook(\n        'webpack-compilation-optimize-chunks',\n        compilation.hooks.optimizeChunks,\n        compilation.hooks.afterOptimizeChunks\n      )\n      this.traceHookPair(\n        'webpack-compilation-optimize-tree',\n        compilation.hooks.optimizeTree,\n        compilation.hooks.afterOptimizeTree\n      )\n      this.traceHookPair(\n        'webpack-compilation-hash',\n        compilation.hooks.beforeHash,\n        compilation.hooks.afterHash\n      )\n    })\n  }\n}\n"]}