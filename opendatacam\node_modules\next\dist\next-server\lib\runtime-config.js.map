{"version": 3, "sources": ["../../../next-server/lib/runtime-config.ts"], "names": ["runtimeConfig", "setConfig", "config<PERSON><PERSON><PERSON>"], "mappings": "wFAAA,GAAIA,CAAAA,aAAJ,C,aAEe,IAAM,CACnB,MAAOA,CAAAA,aAAP,CACD,C,0BAEM,QAASC,CAAAA,SAAT,CAAmBC,WAAnB,CAA2C,CAChDF,aAAa,CAAGE,WAAhB,CACD", "sourcesContent": ["let runtimeConfig: any\n\nexport default () => {\n  return runtimeConfig\n}\n\nexport function setConfig(configValue: any): void {\n  runtimeConfig = configValue\n}\n"]}