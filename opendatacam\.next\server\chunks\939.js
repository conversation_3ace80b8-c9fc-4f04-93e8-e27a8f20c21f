exports.id = 939;
exports.ids = [939];
exports.modules = {

/***/ 716:
/***/ (function(module) {

const self = module.exports = {
  isInsideArea(area, point) {
    const xMin = area.x;
    const xMax = area.x + area.w;
    const yMin = area.y;
    const yMax = area.y + area.h;

    if (point.x >= xMin && point.x <= xMax && point.y >= yMin && point.y <= yMax) {
      return true;
    }

    return false;
  },

  isInsideSomeAreas(areas, point, idDisplay) {
    const isInside = areas.some(area => self.isInsideArea(area, point));
    return isInside;
  },

  computeLineBearing(x1, y1, x2, y2) {
    const angle = Math.atan((x2 - x1) / (y2 - y1)) / (Math.PI / 180);

    if (angle > 0) {
      if (y1 < y2) return angle;
      return 180 + angle;
    }

    if (x1 < x2) return 180 + angle;
    return 360 + angle;
  },

  // Reference: https://jsfiddle.net/justin_c_rounds/Gd2S2/light/
  // Explanation: https://stackoverflow.com/a/24392281/1228937
  checkLineIntersection(line1StartX, line1StartY, line1EndX, line1EndY, line2StartX, line2StartY, line2EndX, line2EndY) {
    // if the lines intersect, the result contains the x and y of the intersection (treating the lines as infinite)
    // and booleans for whether line segment 1 or line segment 2 contain the point
    let denominator;
    let a;
    let b;
    let numerator1;
    let numerator2;
    const result = {
      x: null,
      y: null,
      angle: null,
      onLine1: false,
      onLine2: false
    };
    denominator = (line2EndY - line2StartY) * (line1EndX - line1StartX) - (line2EndX - line2StartX) * (line1EndY - line1StartY);

    if (denominator == 0) {
      return result;
    }

    a = line1StartY - line2StartY;
    b = line1StartX - line2StartX;
    numerator1 = (line2EndX - line2StartX) * a - (line2EndY - line2StartY) * b;
    numerator2 = (line1EndX - line1StartX) * a - (line1EndY - line1StartY) * b;
    a = numerator1 / denominator;
    b = numerator2 / denominator; // if we cast these lines infinitely in both directions, they intersect here:

    result.x = line1StartX + a * (line1EndX - line1StartX);
    result.y = line1StartY + a * (line1EndY - line1StartY);
    /*
      // it is worth noting that this should be the same as:
      x = line2StartX + (b * (line2EndX - line2StartX));
      y = line2StartX + (b * (line2EndY - line2StartY));
    */
    // if line1 is a segment and line2 is infinite, they intersect if:

    if (a > 0 && a < 1) {
      result.onLine1 = true;
    } // if line2 is a segment and line1 is infinite, they intersect if:


    if (b > 0 && b < 1) {
      result.onLine2 = true;
    } // if line1 and line2 are segments, they intersect if both of the above are true


    if (result.onLine1 && result.onLine2) {
      // compute angle of intersection
      // TODO ADD case one of the line is vertical, compute with an horizontal line and do alpha = 90 - gamma
      // So, we have two line formula, and a mutual interval. Your line formulas are:
      // f1(x) = A1*x + b1 = y
      // f2(x) = A2*x + b2 = y
      // As we got two points by segment, we are able to determine A1, A2, b1 and b2:
      const A1 = (line1StartY - line1EndY) / (line1StartX - line1EndX);
      const A2 = (line2StartY - line2EndY) / (line2StartX - line2EndX);
      const tanAlpha = Math.abs((A2 - A1) / (1 + A1 * A2));
      const angle = Math.atan(tanAlpha) * 180 / Math.PI;
      result.angle = angle;
    }

    return result;
  }

};

/***/ }),

/***/ 290:
/***/ (function(module) {

module.exports = {
  getURLData(req) {
    let protocol = 'http';

    if (req.headers['x-forwarded-proto'] === 'https') {
      protocol = 'https';
    }

    let ret;
    const parsedUrl = req.get('Host').split(':');

    if (parsedUrl.length > 1) {
      ret = {
        address: parsedUrl[0],
        port: parsedUrl[1],
        protocol
      };
    } else {
      ret = {
        address: req.get('Host'),
        port: 80,
        protocol: req.protocol
      };
    }

    return ret;
  }

};

/***/ }),

/***/ 855:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "PM": function() { return /* binding */ setMode; },
/* harmony export */   "Sy": function() { return /* binding */ startRecording; },
/* harmony export */   "kJ": function() { return /* binding */ stopRecording; },
/* harmony export */   "ME": function() { return /* binding */ loadConfig; },
/* harmony export */   "f$": function() { return /* binding */ restoreUiSettings; },
/* harmony export */   "AE": function() { return /* binding */ showMenu; },
/* harmony export */   "SC": function() { return /* binding */ hideMenu; },
/* harmony export */   "Cu": function() { return /* binding */ setUiSetting; },
/* harmony export */   "Fu": function() { return /* binding */ setURLData; },
/* harmony export */   "ZU": function() { return /* binding */ startListeningToServerData; },
/* harmony export */   "ZP": function() { return /* binding */ AppReducer; }
/* harmony export */ });
/* unused harmony export updateAppState */
/* harmony import */ var immutable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(856);
/* harmony import */ var immutable__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(immutable__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(376);
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(axios__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(526);
/* harmony import */ var _server_utils_urlHelper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(290);
/* harmony import */ var _server_utils_urlHelper__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_server_utils_urlHelper__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _TrackerStateManagement__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(293);
/* harmony import */ var _CounterStateManagement__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(69);
/* harmony import */ var _HistoryStateManagement__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(861);
/* harmony import */ var _ViewportStateManagement__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(198);







 // Initial state

const initialState = (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)({
  urlData: {},
  recordingStatus: {
    requestedFileRecording: false,
    isRecording: false,
    currentFPS: 0,
    recordingId: null,
    dateStarted: null,
    filename: ''
  },
  yoloStatus: {
    isStarted: false,
    isStarting: true
  },
  uiSettings: {
    counterEnabled: true,
    pathfinderEnabled: true,
    heatmapEnabled: false
  },
  isListeningToYOLO: false,
  mode: _utils_constants__WEBPACK_IMPORTED_MODULE_2__/* .MODE.LIVEVIEW */ .IK.LIVEVIEW,
  showMenu: false,
  isListeningToServerData: false,
  eventSourceServerData: null,
  config: {}
}); // Actions

const SET_URLDATA = 'App/SET_URLDATA';
const SET_MODE = 'App/SET_MODE';
const SHOW_MENU = 'App/SHOW_MENU';
const HIDE_MENU = 'App/HIDE_MENU';
const UPDATE_APPSTATE = 'App/UPDATE_APPSTATE';
const SET_UI_SETTING = 'App/SET_UI_SETTING';
const RESTORE_UI_SETTINGS = 'App/RESTORE_UI_SETTINGS';
const START_LISTENING_SERVERDATA = 'App/START_LISTENING_SERVERDATA'; // TODO LATER HANDLE STOP LISTENING ...

const LOAD_CONFIG = 'App/LOAD_CONFIG';
function setMode(mode) {
  return {
    type: SET_MODE,
    payload: mode
  };
}
function startRecording() {
  return (dispatch, getState) => {
    // Ping webservice to start storing data on server
    axios__WEBPACK_IMPORTED_MODULE_1___default().get('/recording/start');
    dispatch((0,_HistoryStateManagement__WEBPACK_IMPORTED_MODULE_6__/* .fetchHistory */ .eL)()); // If not counting areas defined, go to live view and remove counter button

    const isAtLeastOneCountingAreasDefined = getState().counter.get('countingAreas').size > 0;

    if (!isAtLeastOneCountingAreasDefined && getState().app.get('mode') === _utils_constants__WEBPACK_IMPORTED_MODULE_2__/* .MODE.COUNTERVIEW */ .IK.COUNTERVIEW) {
      // Go to Liveview
      dispatch(setMode(_utils_constants__WEBPACK_IMPORTED_MODULE_2__/* .MODE.LIVEVIEW */ .IK.LIVEVIEW));
    }
  };
}
function stopRecording() {
  return dispatch => {
    // Ping webservice to stop storing data on server
    axios__WEBPACK_IMPORTED_MODULE_1___default().get('/recording/stop');
    dispatch((0,_HistoryStateManagement__WEBPACK_IMPORTED_MODULE_6__/* .fetchHistory */ .eL)());
  };
}
function loadConfig(req) {
  return dispatch => new Promise((resolve, reject) => {
    const urlData = (0,_server_utils_urlHelper__WEBPACK_IMPORTED_MODULE_3__.getURLData)(req);
    const session = req && req.session ? req.session : null;
    const url = `${urlData.protocol}://${urlData.address}:${urlData.port}/config`;
    axios__WEBPACK_IMPORTED_MODULE_1___default()({
      method: 'get',
      url,
      credentials: 'same-origin',
      data: {
        session
      }
    }).then(response => {
      dispatch({
        type: LOAD_CONFIG,
        payload: response.data
      });
      resolve();
    }, error => {
      console.log(error);
      reject();
    }).catch(error => {
      console.log(error);
      reject();
    });
  });
}
function restoreUiSettings(req) {
  return dispatch => new Promise((resolve, reject) => {
    const urlData = (0,_server_utils_urlHelper__WEBPACK_IMPORTED_MODULE_3__.getURLData)(req);
    const session = req && req.session ? req.session : null;
    const url = `${urlData.protocol}://${urlData.address}:${urlData.port}/ui`;
    axios__WEBPACK_IMPORTED_MODULE_1___default()({
      method: 'get',
      url,
      credentials: 'same-origin',
      data: {
        session
      }
    }).then(response => {
      dispatch({
        type: RESTORE_UI_SETTINGS,
        payload: response.data
      });
      resolve();
    }, error => {
      console.log(error);
      reject();
    }).catch(error => {
      console.log(error);
      reject();
    });
  });
}
function updateAppState(data) {
  return {
    type: UPDATE_APPSTATE,
    payload: data
  };
}
function showMenu() {
  return {
    type: SHOW_MENU
  };
}
function hideMenu() {
  return {
    type: HIDE_MENU
  };
}
function setUiSetting(uiSetting, value) {
  return (dispatch, getState) => {
    // Side effects
    const currentMode = getState().app.get('mode'); // If on pathview and disable pathfinder, go to liveview

    if (uiSetting === 'pathfinderEnabled' && value === false && currentMode === _utils_constants__WEBPACK_IMPORTED_MODULE_2__/* .MODE.PATHVIEW */ .IK.PATHVIEW) {
      dispatch(setMode(_utils_constants__WEBPACK_IMPORTED_MODULE_2__/* .MODE.LIVEVIEW */ .IK.LIVEVIEW));
    } // If disabling counter while on counterview


    if (uiSetting === 'counterEnabled' && value === false && currentMode === _utils_constants__WEBPACK_IMPORTED_MODULE_2__/* .MODE.COUNTERVIEW */ .IK.COUNTERVIEW) {
      // Go to Liveview
      dispatch(setMode(_utils_constants__WEBPACK_IMPORTED_MODULE_2__/* .MODE.LIVEVIEW */ .IK.LIVEVIEW)); // If recording, stop recording

      if (getState().app.getIn(['recordingStatus', 'isRecording']) === true) {
        dispatch(stopRecording());
      } // Reset any counter areas defined


      dispatch((0,_CounterStateManagement__WEBPACK_IMPORTED_MODULE_5__/* .resetCountingAreas */ .P0)());
    }

    dispatch({
      type: SET_UI_SETTING,
      payload: {
        uiSetting,
        value
      }
    }); // Persist ui settings on server

    axios__WEBPACK_IMPORTED_MODULE_1___default().post('/ui', getState().app.get('uiSettings').toJS());
  };
}
function setURLData(req) {
  return {
    type: SET_URLDATA,
    payload: (0,_server_utils_urlHelper__WEBPACK_IMPORTED_MODULE_3__.getURLData)(req)
  };
}
function startListeningToServerData() {
  return dispatch => {
    const eventSource = new EventSource('/tracker/sse');
    dispatch({
      type: START_LISTENING_SERVERDATA,
      payload: eventSource
    }); // On new tracker data coming from server, update redux store

    eventSource.onmessage = msg => {
      // Parse JSON
      const message = JSON.parse(msg.data);

      if (message.videoResolution) {
        dispatch((0,_ViewportStateManagement__WEBPACK_IMPORTED_MODULE_7__/* .setOriginalResolution */ .hr)(message.videoResolution));
      }

      dispatch((0,_TrackerStateManagement__WEBPACK_IMPORTED_MODULE_4__/* .updateTrackerData */ .qL)(message.trackerDataForLastFrame));
      dispatch(updateAppState(message.appState));
      dispatch((0,_CounterStateManagement__WEBPACK_IMPORTED_MODULE_5__/* .updateCounterSummary */ .WU)(message.counterSummary));
      dispatch((0,_CounterStateManagement__WEBPACK_IMPORTED_MODULE_5__/* .updateTrackerSummary */ .ae)(message.trackerSummary));
    };
  };
} // Reducer

function AppReducer(state = initialState, action = {}) {
  switch (action.type) {
    case START_LISTENING_SERVERDATA:
      return state.set('isListeningToServerData', true).set('eventSourceServerData', action.payload);

    case SET_URLDATA:
      return state.set('urlData', (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)(action.payload));

    case SET_MODE:
      return state.set('mode', action.payload);

    case SHOW_MENU:
      return state.set('showMenu', true);

    case HIDE_MENU:
      return state.set('showMenu', false);

    case SET_UI_SETTING:
      return state.setIn(['uiSettings', action.payload.uiSetting], (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)(action.payload.value));

    case RESTORE_UI_SETTINGS:
      return state.set('uiSettings', (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)(action.payload));

    case LOAD_CONFIG:
      return state.set('config', (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)(action.payload));

    case UPDATE_APPSTATE:
      return state.set('yoloStatus', (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)(action.payload.yoloStatus)).set('isListeningToYOLO', action.payload.isListeningToYOLO).set('recordingStatus', (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)(action.payload.recordingStatus));

    default:
      return state;
  }
}

/***/ }),

/***/ 69:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Q4": function() { return /* binding */ EDITOR_MODE; },
/* harmony export */   "PM": function() { return /* binding */ setMode; },
/* harmony export */   "WU": function() { return /* binding */ updateCounterSummary; },
/* harmony export */   "ae": function() { return /* binding */ updateTrackerSummary; },
/* harmony export */   "P0": function() { return /* binding */ resetCountingAreas; },
/* harmony export */   "XO": function() { return /* binding */ deleteCountingArea; },
/* harmony export */   "_8": function() { return /* binding */ addCountingArea; },
/* harmony export */   "Co": function() { return /* binding */ saveCountingAreaLocation; },
/* harmony export */   "e3": function() { return /* binding */ toggleCountingAreaType; },
/* harmony export */   "WM": function() { return /* binding */ saveCountingAreaName; },
/* harmony export */   "GA": function() { return /* binding */ restoreCountingAreasFromJSON; },
/* harmony export */   "p6": function() { return /* binding */ restoreCountingAreas; },
/* harmony export */   "b3": function() { return /* binding */ computeCountingAreasCenters; },
/* harmony export */   "Qk": function() { return /* binding */ computeDistance; },
/* harmony export */   "ZP": function() { return /* binding */ CounterReducer; }
/* harmony export */ });
/* unused harmony exports selectCountingArea, registerCountingAreasOnServer */
/* harmony import */ var immutable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(856);
/* harmony import */ var immutable__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(immutable__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(376);
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(axios__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(231);
/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(uuid__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _utils_resolution__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(429);
/* harmony import */ var _server_utils_urlHelper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(290);
/* harmony import */ var _server_utils_urlHelper__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_server_utils_urlHelper__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _utils_colors__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(799);
/* harmony import */ var _server_tracker_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(716);
/* harmony import */ var _server_tracker_utils__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_server_tracker_utils__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(526);







 // Rename this to CounterEditorStateManagement

const EDITOR_MODE = {
  EDIT_LINE: 'edit_line',
  EDIT_POLYGON: 'edit_polygon',
  ASKNAME: 'askname',
  DELETE: 'delete',
  SHOW_INSTRUCTION: 'showinstruction'
}; // Initial state

const initialState = (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)({
  countingAreas: {},
  selectedCountingArea: null,
  mode: EDITOR_MODE.EDIT_LINE,
  // oneOf EDITOR_MODE
  lastEditingMode: EDITOR_MODE.EDIT_LINE,
  counterSummary: {},
  trackerSummary: {}
}); // Actions

const SELECT_COUNTING_AREA = 'Counter/SELECT_COUNTING_AREA';
const DELETE_COUNTING_AREA = 'Counter/DELETE_COUNTING_AREA';
const SAVE_COUNTING_AREA_LOCATION = 'Counter/SAVE_COUNTING_AREA_LOCATION';
const SAVE_COUNTING_AREA_BEARING = 'Counter/SAVE_COUNTING_AREA_BEARING';
const SAVE_COUNTING_AREA_TYPE = 'Counter/SAVE_COUNTING_AREA_TYPE';
const SET_MODE = 'Counter/SET_MODE';
const SET_LAST_EDITING_MODE = 'Counter/SET_LAST_EDITING_MODE';
const SAVE_COUNTING_AREA_NAME = 'Counter/SAVE_COUNTING_AREA_NAME';
const ADD_COUNTING_AREA = 'Counter/ADD_COUNTING_AREA';
const RESTORE_COUNTING_AREAS = 'Counter/RESTORE_COUNTING_AREAS';
const RESET_COUNTING_AREAS = 'Counter/RESET_COUNTING_AREAS';
const UPDATE_COUNTERSUMMARY = 'Counter/UPDATE_COUNTERSUMMARY';
const UPDATE_TRACKERSUMMARY = 'Counter/UPDATE_TRACKERSUMMARY';
function setMode(mode) {
  return (dispatch, getState) => {
    // If leaving editing mode, store last editing mode for when we go back
    const isEditLine = getState().counter.get('mode') === EDITOR_MODE.EDIT_LINE;
    const isEditPolygon = getState().counter.get('mode') === EDITOR_MODE.EDIT_POLYGON;

    if (isEditLine || isEditPolygon) {
      // If new mode is also editing, store new mode
      if (mode === EDITOR_MODE.EDIT_LINE || mode === EDITOR_MODE.EDIT_POLYGON) {
        dispatch({
          type: SET_LAST_EDITING_MODE,
          payload: mode
        });
      } else {
        dispatch({
          type: SET_LAST_EDITING_MODE,
          payload: getState().counter.get('mode')
        });
      }
    }

    dispatch({
      type: SET_MODE,
      payload: mode
    });
  };
}
function updateCounterSummary(data) {
  return {
    type: UPDATE_COUNTERSUMMARY,
    payload: data
  };
}
function updateTrackerSummary(data) {
  return {
    type: UPDATE_TRACKERSUMMARY,
    payload: data
  };
}
function selectCountingArea(id) {
  return {
    type: SELECT_COUNTING_AREA,
    payload: id
  };
} // TODO LATER , introduce Redux saga here to make it more explicit that this is triggered by
// => SAVE_COUNTING_AREA_LOCATION
// => SAVE_COUNTING_AREA_NAME
// => DELETE_COUNTING_AREA
// => SAVE_COUNTING_AREA_TYPE

function registerCountingAreasOnServer() {
  return (dispatch, getState) => {
    // Ping webservice to start storing data on server
    axios__WEBPACK_IMPORTED_MODULE_1___default().post('/counter/areas', {
      countingAreas: getState().counter.get('countingAreas').toJS()
    });
  };
}
function resetCountingAreas() {
  return dispatch => {
    dispatch({
      type: RESET_COUNTING_AREAS
    });
    dispatch(registerCountingAreasOnServer());
  };
}
function deleteCountingArea(id) {
  return (dispatch, getState) => {
    dispatch({
      type: DELETE_COUNTING_AREA,
      payload: id
    });

    if (getState().counter.get('countingAreas').size === 0) {
      dispatch(setMode(getState().counter.get('lastEditingMode')));
    }

    dispatch(registerCountingAreasOnServer());
  };
}
function addCountingArea(type = 'bidirectional') {
  return (dispatch, getState) => {
    // TODO Before adding a counting area, verify if selectedCountingArea is complete, otherwise
    // delete it
    const newCountingAreaId = (0,uuid__WEBPACK_IMPORTED_MODULE_2__.v4)();
    const AVAILABLE_COLORS = (0,_utils_colors__WEBPACK_IMPORTED_MODULE_6__/* .getAvailableCounterColors */ .el)();
    const DEFAULT_COLOR = (0,_utils_colors__WEBPACK_IMPORTED_MODULE_6__/* .getDefaultCounterColor */ .sV)();
    /* eslint-disable */
    // We disable eslint because it will inline the filters and make the code harder to read
    // Get a color unused

    let color = AVAILABLE_COLORS.find(potentialColor => {
      return getState().counter.get('countingAreas').findEntry(value => {
        return value.get('color') === potentialColor;
      }) === undefined;
    });
    /* eslint-enable */

    if (!color) {
      color = DEFAULT_COLOR;
    }

    dispatch({
      type: ADD_COUNTING_AREA,
      payload: {
        id: newCountingAreaId,
        color,
        type
      }
    });
    dispatch(selectCountingArea(newCountingAreaId));
  };
}
function saveCountingAreaLocation(id, location) {
  return (dispatch, getState) => {
    // Compute bearing of the line (if polygon of the first line)
    const lineBearing = (0,_server_tracker_utils__WEBPACK_IMPORTED_MODULE_4__.computeLineBearing)(location.points[0].x, -location.points[0].y, location.points[1].x, -location.points[1].y); // in both directions

    const lineBearings = [0, 0];

    if (lineBearing >= 180) {
      lineBearings[0] = lineBearing - 180;
      lineBearings[1] = lineBearing;
    } else {
      lineBearings[0] = lineBearing;
      lineBearings[1] = lineBearing + 180;
    }

    dispatch({
      type: SAVE_COUNTING_AREA_BEARING,
      payload: {
        lineBearings,
        id
      }
    });
    dispatch({
      type: SAVE_COUNTING_AREA_LOCATION,
      payload: {
        location,
        id
      }
    });

    if (!getState().counter.getIn(['countingAreas', id, 'name'])) {
      dispatch(setMode(EDITOR_MODE.ASKNAME));
    } // TODO UPDATE server side part to handle array of points instead of just point1, point2


    dispatch(registerCountingAreasOnServer());
  };
}
function toggleCountingAreaType(id, currentDirection) {
  return dispatch => {
    let newDirection = _utils_constants__WEBPACK_IMPORTED_MODULE_5__/* .COUNTING_AREA_TYPE.BIDIRECTIONAL */ .Og.BIDIRECTIONAL;

    if (currentDirection === _utils_constants__WEBPACK_IMPORTED_MODULE_5__/* .COUNTING_AREA_TYPE.BIDIRECTIONAL */ .Og.BIDIRECTIONAL) {
      newDirection = _utils_constants__WEBPACK_IMPORTED_MODULE_5__/* .COUNTING_AREA_TYPE.LEFTRIGHT_TOPBOTTOM */ .Og.LEFTRIGHT_TOPBOTTOM;
    } else if (currentDirection === _utils_constants__WEBPACK_IMPORTED_MODULE_5__/* .COUNTING_AREA_TYPE.LEFTRIGHT_TOPBOTTOM */ .Og.LEFTRIGHT_TOPBOTTOM) {
      newDirection = _utils_constants__WEBPACK_IMPORTED_MODULE_5__/* .COUNTING_AREA_TYPE.RIGHTLEFT_BOTTOMTOP */ .Og.RIGHTLEFT_BOTTOMTOP;
    }

    dispatch({
      type: SAVE_COUNTING_AREA_TYPE,
      payload: {
        type: newDirection,
        id
      }
    });
    dispatch(registerCountingAreasOnServer());
  };
}
function saveCountingAreaName(id, name) {
  return dispatch => {
    // console.log('saveName')
    dispatch({
      type: SAVE_COUNTING_AREA_NAME,
      payload: {
        id,
        name
      }
    });
    dispatch(registerCountingAreasOnServer());
  };
}
function restoreCountingAreasFromJSON(data) {
  return dispatch => {
    dispatch({
      type: RESTORE_COUNTING_AREAS,
      payload: data
    });
    dispatch(registerCountingAreasOnServer());
  };
}
function restoreCountingAreas(req) {
  return dispatch => new Promise((resolve, reject) => {
    if (req) {
      const urlData = (0,_server_utils_urlHelper__WEBPACK_IMPORTED_MODULE_3__.getURLData)(req);
      const session = req && req.session ? req.session : null;
      const url = `${urlData.protocol}://${urlData.address}:${urlData.port}/counter/areas`;
      axios__WEBPACK_IMPORTED_MODULE_1___default()({
        method: 'get',
        url,
        credentials: 'same-origin',
        data: {
          session
        }
      }).then(response => {
        dispatch({
          type: RESTORE_COUNTING_AREAS,
          payload: response.data
        });
        resolve();
      }, error => {
        console.log(error);
        reject();
      }).catch(error => {
        console.log(error);
        reject();
      });
    } else {
      axios__WEBPACK_IMPORTED_MODULE_1___default()({
        method: 'get',
        url: '/counter/areas'
      }).then(response => {
        dispatch({
          type: RESTORE_COUNTING_AREAS,
          payload: response.data
        });
        resolve();
      }, error => {
        console.log(error);
        reject();
      }).catch(error => {
        console.log(error);
        reject();
      });
    }
  });
}
function computeCountingAreasCenters(countingAreas, canvasResolution) {
  return countingAreas.map(data => {
    const location = data.get('location');

    if (location) {
      const points = location.get('points').toJS();
      const x1 = points[0].x;
      const y1 = points[0].y;
      const x2 = points[1].x;
      const y2 = points[1].y;
      return data.setIn(['location', 'center'], (0,_utils_resolution__WEBPACK_IMPORTED_MODULE_7__/* .scalePoint */ .q)({
        x: Math.abs(x2 - x1) / 2 + Math.min(x1, x2),
        y: Math.abs(y2 - y1) / 2 + Math.min(y1, y2)
      }, canvasResolution.toJS(), location.get('refResolution').toJS()));
    }

    return data;
  });
}
function computeDistance(point1, point2) {
  const xDist = (point2.x - point1.x) ** 2;
  const yDist = (point2.y - point1.y) ** 2;
  return Math.sqrt(xDist + yDist);
} // Reducer

function CounterReducer(state = initialState, action = {}) {
  switch (action.type) {
    case SELECT_COUNTING_AREA:
      return state.set('selectedCountingArea', action.payload);

    case DELETE_COUNTING_AREA:
      return state.deleteIn(['countingAreas', action.payload]);

    case SAVE_COUNTING_AREA_LOCATION:
      return state.setIn(['countingAreas', action.payload.id, 'location'], (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)(action.payload.location));

    case SAVE_COUNTING_AREA_BEARING:
      return state.setIn(['countingAreas', action.payload.id, 'computed', 'lineBearings'], (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)(action.payload.lineBearings));

    case SAVE_COUNTING_AREA_NAME:
      return state.setIn(['countingAreas', action.payload.id, 'name'], action.payload.name);

    case SAVE_COUNTING_AREA_TYPE:
      return state.setIn(['countingAreas', action.payload.id, 'type'], action.payload.type);

    case ADD_COUNTING_AREA:
      return state.setIn(['countingAreas', action.payload.id], (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)({
        color: action.payload.color,
        type: action.payload.type
      }));

    case RESET_COUNTING_AREAS:
      return state.set('countingAreas', (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)({}));

    case SET_MODE:
      return state.set('mode', action.payload);

    case SET_LAST_EDITING_MODE:
      return state.set('lastEditingMode', action.payload);

    case RESTORE_COUNTING_AREAS:
      return state.set('countingAreas', (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)(action.payload));

    case UPDATE_COUNTERSUMMARY:
      return state.setIn(['counterSummary'], (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)(action.payload));

    case UPDATE_TRACKERSUMMARY:
      return state.setIn(['trackerSummary'], (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)(action.payload));

    default:
      return state;
  }
}

/***/ }),

/***/ 861:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "eL": function() { return /* binding */ fetchHistory; },
/* harmony export */   "Mj": function() { return /* binding */ deleteRecording; },
/* harmony export */   "ZP": function() { return /* binding */ HistoryReducer; }
/* harmony export */ });
/* unused harmony exports fetchHistorySuccess, fetchHistoryError, updateRecordingsCursor */
/* harmony import */ var immutable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(856);
/* harmony import */ var immutable__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(immutable__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(376);
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(axios__WEBPACK_IMPORTED_MODULE_1__);


const DEFAULT_LIMIT = 20;
const DEFAULT_OFFSET = 0; // Initial state

const initialState = (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)({
  recordingHistory: [],
  isFetchingHistory: false,
  fetchHistoryError: null,
  recordingsCursor: {
    limit: DEFAULT_LIMIT,
    offset: DEFAULT_OFFSET,
    total: 0
  }
}); // Actions

const FETCH_HISTORY_SUCCESS = 'History/FETCH_HISTORY_SUCCESS';
const FETCH_HISTORY_START = 'History/FETCH_HISTORY_START';
const FETCH_HISTORY_ERROR = 'History/FETCH_HISTORY_ERROR';
const UPDATE_RECORDINGS_CURSOR = 'History/UPDATE_RECORDINGS_CURSOR';
const DELETE_RECORDING = 'History/DELETE_RECORDING';
function fetchHistorySuccess(data) {
  return {
    type: FETCH_HISTORY_SUCCESS,
    payload: data
  };
}
function fetchHistoryError() {
  return {
    type: FETCH_HISTORY_ERROR
  };
}
function updateRecordingsCursor(data) {
  return {
    type: UPDATE_RECORDINGS_CURSOR,
    payload: data
  };
}
function fetchHistory(offset = DEFAULT_OFFSET, limit = DEFAULT_LIMIT) {
  return dispatch => {
    dispatch({
      type: FETCH_HISTORY_START
    });
    axios__WEBPACK_IMPORTED_MODULE_1___default().get(`/recordings?offset=${offset}&limit=${limit}`).then(response => {
      dispatch(fetchHistorySuccess(response.data.recordings));
      dispatch(updateRecordingsCursor({
        total: response.data.total,
        offset: response.data.offset,
        limit: response.data.limit
      }));
    }, () => {
      dispatch(fetchHistoryError());
    });
  };
}
function deleteRecording(recordingId) {
  return dispatch => {
    dispatch({
      type: DELETE_RECORDING,
      payload: recordingId
    });
    axios__WEBPACK_IMPORTED_MODULE_1___default().delete(`/recording/${recordingId}`);
  };
} // Reducer

function HistoryReducer(state = initialState, action = {}) {
  switch (action.type) {
    case FETCH_HISTORY_START:
      return state.set('isFetchingHistory', true).set('fetchHistoryError', false);

    case FETCH_HISTORY_SUCCESS:
      return state.set('recordingHistory', (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)(action.payload)).set('isFetchingHistory', false).set('fetchHistoryError', false);

    case FETCH_HISTORY_ERROR:
      return state.set('isFetchingHistory', false).set('fetchHistoryError', false);

    case UPDATE_RECORDINGS_CURSOR:
      return state.set('recordingsCursor', (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)(action.payload));

    case DELETE_RECORDING:
      return state.updateIn(['recordingHistory'], recordingHistory => recordingHistory.delete(recordingHistory.findIndex(value => value.get('id') === action.payload)));

    default:
      return state;
  }
}

/***/ }),

/***/ 293:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "QJ": function() { return /* binding */ getTrackerAccuracyNbFrameBuffer; },
/* harmony export */   "_d": function() { return /* binding */ getTrackerAccuracySettings; },
/* harmony export */   "qL": function() { return /* binding */ updateTrackerData; },
/* harmony export */   "ZP": function() { return /* binding */ TrackerReducer; }
/* harmony export */ });
/* harmony import */ var immutable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(856);
/* harmony import */ var immutable__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(immutable__WEBPACK_IMPORTED_MODULE_0__);
 // Initial state

const initialState = (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)({
  trackerData: {
    frameIndex: 0,
    data: []
  }
}); // Actions

const UPDATE_DATA = 'Tracker/UPDATE_DATA';
function getTrackerAccuracyNbFrameBuffer() {
  return window.CONFIG.TRACKER_ACCURACY_DISPLAY.nbFrameBuffer;
}
function getTrackerAccuracySettings() {
  return window.CONFIG.TRACKER_ACCURACY_DISPLAY.settings;
}
function updateTrackerData(trackerDataLastFrame) {
  return dispatch => {
    // Update tracker raw data
    dispatch({
      type: UPDATE_DATA,
      payload: trackerDataLastFrame
    });
  };
} // Reducer

function TrackerReducer(state = initialState, action = {}) {
  switch (action.type) {
    case UPDATE_DATA:
      return state.set('trackerData', (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)(action.payload));

    default:
      return state;
  }
}

/***/ }),

/***/ 717:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "If": function() { return /* binding */ setUserSetting; },
/* harmony export */   "zJ": function() { return /* binding */ loadUserSettings; },
/* harmony export */   "ZP": function() { return /* binding */ ViewportStateManagement; }
/* harmony export */ });
/* harmony import */ var immutable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(856);
/* harmony import */ var immutable__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(immutable__WEBPACK_IMPORTED_MODULE_0__);
 // Initial state

const initialState = (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)({
  dimmerOpacity: 0.1,
  darkMode: false
});
const LOCALSTORAGE_KEY = 'opendatacam'; // Actions

const SET_USERSETTING = 'UserSettings/SET_USERSETTING';

function persistUserSettings(userSettings) {
  // Persist
  localStorage.setItem(LOCALSTORAGE_KEY, JSON.stringify(userSettings));
}

function setUserSetting(userSetting, value) {
  return (dispatch, getState) => {
    dispatch({
      type: SET_USERSETTING,
      payload: {
        userSetting,
        value
      }
    }); // Specific side effect for darkMode

    if (userSetting === 'darkMode' && value === true) {
      document.getElementsByTagName('body')[0].className = 'theme-dark';
    } else if (userSetting === 'darkMode' && value === false) {
      document.getElementsByTagName('body')[0].className = '';
    } // Persist


    persistUserSettings(getState().usersettings.toJS());
  };
}
function loadUserSettings() {
  return dispatch => {
    // Load localstorage content into reducer
    const persistedData = window.localStorage.getItem('opendatacam');

    if (persistedData) {
      const parsedData = JSON.parse(persistedData);
      Object.keys(parsedData).map(key => {
        dispatch(setUserSetting(key, parsedData[key]));
      }); // TODO for each key setUserSetting
    } else {
      // Nothing persisted yet, persist default
      persistUserSettings(initialState.toJS());
    }
  };
} // Reducer

function ViewportStateManagement(state = initialState, action = {}) {
  switch (action.type) {
    case SET_USERSETTING:
      return state.set(action.payload.userSetting, (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)(action.payload.value));

    default:
      return state;
  }
}

/***/ }),

/***/ 198:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "oE": function() { return /* binding */ initViewportListeners; },
/* harmony export */   "hr": function() { return /* binding */ setOriginalResolution; },
/* harmony export */   "ZP": function() { return /* binding */ ViewportStateManagement; }
/* harmony export */ });
/* unused harmony exports getCanvasResolution, setCanvasResolution, setLandscape, setPortrait, handleOrientationChange */
/* harmony import */ var immutable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(856);
/* harmony import */ var immutable__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(immutable__WEBPACK_IMPORTED_MODULE_0__);
 // Initial state

const initialState = (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)({
  listenersInitialized: false,
  deviceOrientation: 'none',
  canvasResolution: {
    w: 1280,
    h: 720
  },
  // TODO LATER
  // Maybe can rid of this by directly sending only the percentage value
  // This means original resolution of the YOLO detections & tracker
  // image field in opendatacam class on server
  // This should by set by the server
  originalResolution: {
    w: 0,
    h: 0
  }
}); // Actions

const SET_PORTRAIT = 'Viewport/SET_PORTRAIT';
const SET_LANDSCAPE = 'Viewport/SET_LANDSCAPE';
const INIT_LISTENERS = 'Viewport/INIT_LISTENERS';
const SET_CANVAS_RESOLUTION = 'Viewport/SET_CANVAS_RESOLUTION';
const SET_ORIGINAL_RESOLUTION = 'Viewport/SET_ORIGINAL_RESOLUTION';
function getCanvasResolution() {
  const {
    innerWidth
  } = window;
  const {
    innerHeight
  } = window; // if (innerWidth / innerHeight < 16 / 9) {
  //   // Height is 100% and there is a scroll on the width
  //   return {
  //     w: innerHeight * 16 / 9,
  //     h: innerHeight
  //   }
  // } else {
  //   // Width is 100% and there is a scroll on the height
  //   return {
  //     w: innerWidth,
  //     h: innerWidth * 9 / 16
  //   }
  // }

  return {
    w: innerWidth,
    h: innerHeight
  };
}
function setCanvasResolution(size) {
  return {
    type: SET_CANVAS_RESOLUTION,
    payload: size
  };
}
function setLandscape() {
  return {
    type: SET_LANDSCAPE
  };
}
function setPortrait() {
  return {
    type: SET_PORTRAIT
  };
}
function handleOrientationChange(dispatch) {
  // console.log(window.orientation)
  if (window.orientation === -90 || window.orientation === 90) {
    // console.log('landscape')
    dispatch(setLandscape());
  } else if (window.orientation !== undefined) {
    // console.log('portrait')
    dispatch(setPortrait());
  }

  dispatch(setCanvasResolution(getCanvasResolution()));
}
function initViewportListeners() {
  return (dispatch, getState) => {
    // Only if not initialized
    if (!getState().viewport.get('listenersInitialized')) {
      dispatch({
        type: INIT_LISTENERS
      }); // console.log('init orientation change listener')

      window.addEventListener('orientationchange', handleOrientationChange.bind(this, dispatch));
      handleOrientationChange(dispatch);
      dispatch(setCanvasResolution(getCanvasResolution()));
      let resizeDebounceTimeout = null;
      window.addEventListener('resize', () => {
        if (resizeDebounceTimeout) {
          // clear the timeout
          clearTimeout(resizeDebounceTimeout);
        } // start timing for event "completion"


        resizeDebounceTimeout = setTimeout(() => {
          dispatch(setCanvasResolution(getCanvasResolution()));
        }, 250);
      });
    }
  };
}
function setOriginalResolution(resolution) {
  return {
    type: SET_ORIGINAL_RESOLUTION,
    payload: resolution
  };
} // Reducer

function ViewportStateManagement(state = initialState, action = {}) {
  switch (action.type) {
    case SET_LANDSCAPE:
      return state.set('deviceOrientation', 'landscape');

    case SET_PORTRAIT:
      return state.set('deviceOrientation', 'portrait');

    case INIT_LISTENERS:
      return state.set('listenersInitialized', true);

    case SET_CANVAS_RESOLUTION:
      return state.set('canvasResolution', (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)(action.payload));

    case SET_ORIGINAL_RESOLUTION:
      return state.set('originalResolution', (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)(action.payload));

    default:
      return state;
  }
}

/***/ }),

/***/ 799:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "at": function() { return /* binding */ evaluateCSSVariable; },
/* harmony export */   "SD": function() { return /* binding */ getCounterColor; },
/* harmony export */   "el": function() { return /* binding */ getAvailableCounterColors; },
/* harmony export */   "sV": function() { return /* binding */ getDefaultCounterColor; },
/* harmony export */   "E5": function() { return /* binding */ getPathfinderColors; },
/* harmony export */   "b7": function() { return /* binding */ getDisplayClasses; }
/* harmony export */ });
// TODO memoize this for performance
function evaluateCSSVariable(color) {
  return window.getComputedStyle(document.body).getPropertyValue(color.match(/\((.*?)\)/)[1]);
}
function getCounterColor(colorLabel) {
  let color = null;
  color = window.CONFIG.COUNTER_COLORS[colorLabel];

  if (color) {
    return color;
  } // Maybe if colors have been modified and old recording are in DB, we need to render a default
  // color


  return '#AEAEAE';
}
function getAvailableCounterColors() {
  return Object.keys(window.CONFIG.COUNTER_COLORS);
}
function getDefaultCounterColor() {
  return Object.keys(window.CONFIG.COUNTER_COLORS[getAvailableCounterColors()[0]]);
}
function getPathfinderColors() {
  return window.CONFIG.PATHFINDER_COLORS;
}
function getDisplayClasses() {
  return window.CONFIG.DISPLAY_CLASSES;
}

/***/ }),

/***/ 526:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "IK": function() { return /* binding */ MODE; },
/* harmony export */   "qI": function() { return /* binding */ CANVAS_RENDERING_MODE; },
/* harmony export */   "Og": function() { return /* binding */ COUNTING_AREA_TYPE; },
/* harmony export */   "S7": function() { return /* binding */ CIRCLE_RADIUS; },
/* harmony export */   "a6": function() { return /* binding */ ICON_DIRECTION_SIZE; },
/* harmony export */   "SL": function() { return /* binding */ CIRCLE_DELETE_RADIUS; },
/* harmony export */   "Ys": function() { return /* binding */ POPOVER_HEIGHT; },
/* harmony export */   "qr": function() { return /* binding */ POPOVER_WIDTH; },
/* harmony export */   "fQ": function() { return /* binding */ POPOVER_ARROW_SIZE; }
/* harmony export */ });
const MODE = {
  LIVEVIEW: 'liveview',
  COUNTERVIEW: 'counterview',
  PATHVIEW: 'pathview',
  DATAVIEW: 'dataview',
  CONSOLEVIEW: 'consoleview'
};
const CANVAS_RENDERING_MODE = {
  LIVEVIEW: 'liveview',
  COUNTERVIEW: 'counterview',
  COUNTERVIEW_RECORDING: 'counterview_recording',
  PATHVIEW: 'pathview',
  COUNTING_AREAS: 'countingareas',
  TRACKER_ACCURACY: 'tracker_accuracy'
};
const COUNTING_AREA_TYPE = {
  BIDIRECTIONAL: 'bidirectional',
  LEFTRIGHT_TOPBOTTOM: 'leftright_topbottom',
  RIGHTLEFT_BOTTOMTOP: 'rightleft_bottomtop'
}; // TODO Make this resolution independant by specifying a percentage of the total

const CIRCLE_RADIUS = 30;
const ICON_DIRECTION_SIZE = 20;
const CIRCLE_DELETE_RADIUS = 40;
const POPOVER_HEIGHT = 140;
const POPOVER_WIDTH = 235;
const POPOVER_ARROW_SIZE = 10;

/***/ }),

/***/ 429:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "n": function() { return /* binding */ scaleDetection; },
/* harmony export */   "q": function() { return /* binding */ scalePoint; }
/* harmony export */ });
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

function scaleDetection(detection, canvasResolution, originalResolution) {
  return _objectSpread(_objectSpread({}, detection), {}, {
    x: detection.x * canvasResolution.w / originalResolution.w,
    y: detection.y * canvasResolution.h / originalResolution.h,
    w: detection.w * canvasResolution.w / originalResolution.w,
    h: detection.h * canvasResolution.h / originalResolution.h
  });
}
function scalePoint(point, canvasResolution, originalResolution) {
  return _objectSpread(_objectSpread({}, point), {}, {
    x: point.x * canvasResolution.w / originalResolution.w,
    y: point.y * canvasResolution.h / originalResolution.h
  });
}

/***/ })

};
;