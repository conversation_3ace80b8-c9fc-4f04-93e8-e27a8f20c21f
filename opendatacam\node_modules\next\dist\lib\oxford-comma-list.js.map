{"version": 3, "sources": ["../../lib/oxford-comma-list.ts"], "names": ["getOxfordCommaList", "items", "map", "v", "index", "length", "join"], "mappings": "mFAAO,QAASA,CAAAA,kBAAT,CAA4BC,KAA5B,CAAqD,CAC1D,MAAOA,CAAAA,KAAK,CACTC,GADI,CAEH,CAACC,CAAD,CAAIC,KAAJ,CAAW,CAAEC,MAAF,CAAX,GACE,CAACD,KAAK,CAAG,CAAR,CACGA,KAAK,GAAKC,MAAM,CAAG,CAAnB,CACEA,MAAM,CAAG,CAAT,CACE,QADF,CAEE,OAHJ,CAIE,IALL,CAMG,EANJ,EAMUF,CATT,EAWJG,IAXI,CAWC,EAXD,CAAP,CAYD", "sourcesContent": ["export function getOxfordCommaList(items: string[]): string {\n  return items\n    .map(\n      (v, index, { length }) =>\n        (index > 0\n          ? index === length - 1\n            ? length > 2\n              ? ', and '\n              : ' and '\n            : ', '\n          : '') + v\n    )\n    .join('')\n}\n"]}