{"version": 3, "sources": ["../../telemetry/post-payload.ts"], "names": ["_postPayload", "endpoint", "body", "method", "JSON", "stringify", "headers", "timeout", "then", "res", "ok", "err", "Error", "statusText", "response", "minTimeout", "retries", "factor", "catch"], "mappings": "uEAAA,kFACA,6D,mFAEO,QAASA,CAAAA,YAAT,CAAsBC,QAAtB,CAAwCC,IAAxC,CAAsD,CAC3D,MACE,wBACE,IACE,uBAAMD,QAAN,CAAgB,CACdE,MAAM,CAAE,MADM,CAEdD,IAAI,CAAEE,IAAI,CAACC,SAAL,CAAeH,IAAf,CAFQ,CAGdI,OAAO,CAAE,CAAE,eAAgB,kBAAlB,CAHK,CAIdC,OAAO,CAAE,IAJK,CAAhB,EAKGC,IALH,CAKSC,GAAD,EAAS,CACf,GAAI,CAACA,GAAG,CAACC,EAAT,CAAa,CACX,KAAMC,CAAAA,GAAG,CAAG,GAAIC,CAAAA,KAAJ,CAAUH,GAAG,CAACI,UAAd,CAAZ,CACEF,GAAD,CAAaG,QAAb,CAAwBL,GAAxB,CACD,KAAME,CAAAA,GAAN,CACD,CACF,CAXD,CAFJ,CAcE,CAAEI,UAAU,CAAE,GAAd,CAAmBC,OAAO,CAAE,CAA5B,CAA+BC,MAAM,CAAE,CAAvC,CAdF,EAgBGC,KAhBH,CAgBS,IAAM,CACX;AACD,CAlBH,CAmBE;AAnBF,CAoBGV,IApBH,CAqBI,IAAM,CAAE,CArBZ,CAsBI,IAAM,CAAE,CAtBZ,CADF,CA0BD", "sourcesContent": ["import retry from 'next/dist/compiled/async-retry'\nimport fetch from 'node-fetch'\n\nexport function _postPayload(endpoint: string, body: object) {\n  return (\n    retry(\n      () =>\n        fetch(endpoint, {\n          method: 'POST',\n          body: JSON.stringify(body),\n          headers: { 'content-type': 'application/json' },\n          timeout: 5000,\n        }).then((res) => {\n          if (!res.ok) {\n            const err = new Error(res.statusText)\n            ;(err as any).response = res\n            throw err\n          }\n        }),\n      { minTimeout: 500, retries: 1, factor: 1 }\n    )\n      .catch(() => {\n        // We swallow errors when telemetry cannot be sent\n      })\n      // Ensure promise is voided\n      .then(\n        () => {},\n        () => {}\n      )\n  )\n}\n"]}