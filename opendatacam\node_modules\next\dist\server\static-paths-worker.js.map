{"version": 3, "sources": ["../../server/static-paths-worker.ts"], "names": ["workerWasUsed", "loadStaticPaths", "distDir", "pathname", "serverless", "config", "locales", "defaultLocale", "process", "exit", "require", "setConfig", "components", "getStaticPaths", "Error"], "mappings": "6EAAA,qCACA,qEACA,qDAIA,GAAIA,CAAAA,aAAa,CAAG,KAApB,CAEA;AACA;AACA;AACO,cAAeC,CAAAA,eAAf,CACLC,OADK,CAELC,QAFK,CAGLC,UAHK,CAILC,MAJK,CAKLC,OALK,CAMLC,aANK,CAOL,CACA;AACA;AACA,GAAIP,aAAJ,CAAmB,CACjBQ,OAAO,CAACC,IAAR,CAAa,CAAb,EACD,CAED;AACAC,OAAO,CAAC,qCAAD,CAAP,CAA+CC,SAA/C,CAAyDN,MAAzD,EAEA,KAAMO,CAAAA,UAAU,CAAG,KAAM,mCAAeV,OAAf,CAAwBC,QAAxB,CAAkCC,UAAlC,CAAzB,CAEA,GAAI,CAACQ,UAAU,CAACC,cAAhB,CAAgC,CAC9B;AACA;AACA,KAAM,IAAIC,CAAAA,KAAJ,CACH,0DAAyDX,QAAS,EAD/D,CAAN,CAGD,CAEDH,aAAa,CAAG,IAAhB,CACA,MAAO,4BACLG,QADK,CAELS,UAAU,CAACC,cAFN,CAGLP,OAHK,CAILC,aAJK,CAAP,CAMD", "sourcesContent": ["import { buildStaticPaths } from '../build/utils'\nimport { loadComponents } from '../next-server/server/load-components'\nimport '../next-server/server/node-polyfill-fetch'\n\ntype RuntimeConfig = any\n\nlet workerWasUsed = false\n\n// we call getStaticPaths in a separate process to ensure\n// side-effects aren't relied on in dev that will break\n// during a production build\nexport async function loadStaticPaths(\n  distDir: string,\n  pathname: string,\n  serverless: boolean,\n  config: RuntimeConfig,\n  locales?: string[],\n  defaultLocale?: string\n) {\n  // we only want to use each worker once to prevent any invalid\n  // caches\n  if (workerWasUsed) {\n    process.exit(1)\n  }\n\n  // update work memory runtime-config\n  require('./../next-server/lib/runtime-config').setConfig(config)\n\n  const components = await loadComponents(distDir, pathname, serverless)\n\n  if (!components.getStaticPaths) {\n    // we shouldn't get to this point since the worker should\n    // only be called for SSG pages with getStaticPaths\n    throw new Error(\n      `Invariant: failed to load page with getStaticPaths for ${pathname}`\n    )\n  }\n\n  workerWasUsed = true\n  return buildStaticPaths(\n    pathname,\n    components.getStaticPaths,\n    locales,\n    defaultLocale\n  )\n}\n"]}