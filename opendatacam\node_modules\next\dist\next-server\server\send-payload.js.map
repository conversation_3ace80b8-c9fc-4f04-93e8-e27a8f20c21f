{"version": 3, "sources": ["../../../next-server/server/send-payload.ts"], "names": ["setRevalidateHeaders", "res", "options", "private", "stateful", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "revalidate", "Error", "sendPayload", "req", "payload", "type", "generateEtags", "poweredByHeader", "etag", "undefined", "sendEtagResponse", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "byteLength", "end", "method", "headers", "statusCode"], "mappings": "iKACA,mCACA,kDACA,uE,mFAOO,QAASA,CAAAA,oBAAT,CACLC,GADK,CAELC,OAFK,CAGL,CACA,GAAIA,OAAO,CAACC,OAAR,EAAmBD,OAAO,CAACE,QAA/B,CAAyC,CACvC,GAAIF,OAAO,CAACC,OAAR,EAAmB,CAACF,GAAG,CAACI,SAAJ,CAAc,eAAd,CAAxB,CAAwD,CACtDJ,GAAG,CAACK,SAAJ,CACE,eADF,CAEG,yDAFH,EAID,CACF,CAPD,IAOO,IAAI,MAAOJ,CAAAA,OAAO,CAACK,UAAf,GAA8B,QAAlC,CAA4C,CACjD,GAAIL,OAAO,CAACK,UAAR,CAAqB,CAAzB,CAA4B,CAC1B,KAAM,IAAIC,CAAAA,KAAJ,CACH,uDAAsDN,OAAO,CAACK,UAAW,MADtE,CAAN,CAGD,CAEDN,GAAG,CAACK,SAAJ,CACE,eADF,CAEG,YAAWJ,OAAO,CAACK,UAAW,0BAFjC,EAID,CAXM,IAWA,IAAIL,OAAO,CAACK,UAAR,GAAuB,KAA3B,CAAkC,CACvCN,GAAG,CAACK,SAAJ,CAAc,eAAd,CAAgC,2CAAhC,EACD,CACF,CAEM,QAASG,CAAAA,WAAT,CACLC,GADK,CAELT,GAFK,CAGLU,OAHK,CAILC,IAJK,CAKL,CACEC,aADF,CAEEC,eAFF,CALK,CASLZ,OATK,CAUC,CACN,GAAI,qBAAUD,GAAV,CAAJ,CAAoB,CAClB,OACD,CAED,GAAIa,eAAe,EAAIF,IAAI,GAAK,MAAhC,CAAwC,CACtCX,GAAG,CAACK,SAAJ,CAAc,cAAd,CAA8B,SAA9B,EACD,CAED,KAAMS,CAAAA,IAAI,CAAGF,aAAa,CAAG,kBAAaF,OAAb,CAAH,CAA2BK,SAArD,CACA,GAAIC,gBAAgB,CAACP,GAAD,CAAMT,GAAN,CAAWc,IAAX,CAApB,CAAsC,CACpC,OACD,CAED,GAAI,CAACd,GAAG,CAACiB,SAAJ,CAAc,cAAd,CAAL,CAAoC,CAClCjB,GAAG,CAACK,SAAJ,CACE,cADF,CAEEM,IAAI,GAAK,MAAT,CAAkB,kBAAlB,CAAuC,0BAFzC,EAID,CACDX,GAAG,CAACK,SAAJ,CAAc,gBAAd,CAAgCa,MAAM,CAACC,UAAP,CAAkBT,OAAlB,CAAhC,EACA,GAAIT,OAAO,EAAI,IAAf,CAAqB,CACnBF,oBAAoB,CAACC,GAAD,CAAMC,OAAN,CAApB,CACD,CACDD,GAAG,CAACoB,GAAJ,CAAQX,GAAG,CAACY,MAAJ,GAAe,MAAf,CAAwB,IAAxB,CAA+BX,OAAvC,EACD,CAEM,QAASM,CAAAA,gBAAT,CACLP,GADK,CAELT,GAFK,CAGLc,IAHK,CAII,CACT,GAAIA,IAAJ,CAAU,CACR;AACJ;AACA;AACA;AACA;AACA,OACId,GAAG,CAACK,SAAJ,CAAc,MAAd,CAAsBS,IAAtB,EACD,CAED,GAAI,mBAAML,GAAG,CAACa,OAAV,CAAmB,CAAER,IAAF,CAAnB,CAAJ,CAAkC,CAChCd,GAAG,CAACuB,UAAJ,CAAiB,GAAjB,CACAvB,GAAG,CAACoB,GAAJ,GACA,MAAO,KAAP,CACD,CAED,MAAO,MAAP,CACD", "sourcesContent": ["import { IncomingMessage, ServerResponse } from 'http'\nimport { isResSent } from '../lib/utils'\nimport generateETag from 'etag'\nimport fresh from 'next/dist/compiled/fresh'\n\ntype PayloadOptions =\n  | { private: true }\n  | { private: boolean; stateful: true }\n  | { private: boolean; stateful: false; revalidate: number | false }\n\nexport function setRevalidateHeaders(\n  res: ServerResponse,\n  options: PayloadOptions\n) {\n  if (options.private || options.stateful) {\n    if (options.private || !res.hasHeader('Cache-Control')) {\n      res.setHeader(\n        'Cache-Control',\n        `private, no-cache, no-store, max-age=0, must-revalidate`\n      )\n    }\n  } else if (typeof options.revalidate === 'number') {\n    if (options.revalidate < 1) {\n      throw new Error(\n        `invariant: invalid Cache-Control duration provided: ${options.revalidate} < 1`\n      )\n    }\n\n    res.setHeader(\n      'Cache-Control',\n      `s-maxage=${options.revalidate}, stale-while-revalidate`\n    )\n  } else if (options.revalidate === false) {\n    res.setHeader('Cache-Control', `s-maxage=31536000, stale-while-revalidate`)\n  }\n}\n\nexport function sendPayload(\n  req: IncomingMessage,\n  res: ServerResponse,\n  payload: any,\n  type: 'html' | 'json',\n  {\n    generateEtags,\n    poweredByHeader,\n  }: { generateEtags: boolean; poweredByHeader: boolean },\n  options?: PayloadOptions\n): void {\n  if (isResSent(res)) {\n    return\n  }\n\n  if (poweredByHeader && type === 'html') {\n    res.setHeader('X-Powered-By', 'Next.js')\n  }\n\n  const etag = generateEtags ? generateETag(payload) : undefined\n  if (sendEtagResponse(req, res, etag)) {\n    return\n  }\n\n  if (!res.getHeader('Content-Type')) {\n    res.setHeader(\n      'Content-Type',\n      type === 'json' ? 'application/json' : 'text/html; charset=utf-8'\n    )\n  }\n  res.setHeader('Content-Length', Buffer.byteLength(payload))\n  if (options != null) {\n    setRevalidateHeaders(res, options)\n  }\n  res.end(req.method === 'HEAD' ? null : payload)\n}\n\nexport function sendEtagResponse(\n  req: IncomingMessage,\n  res: ServerResponse,\n  etag: string | undefined\n): boolean {\n  if (etag) {\n    /**\n     * The server generating a 304 response MUST generate any of the\n     * following header fields that would have been sent in a 200 (OK)\n     * response to the same request: Cache-Control, Content-Location, Date,\n     * ETag, Expires, and Vary. https://tools.ietf.org/html/rfc7232#section-4.1\n     */\n    res.setHeader('ETag', etag)\n  }\n\n  if (fresh(req.headers, { etag })) {\n    res.statusCode = 304\n    res.end()\n    return true\n  }\n\n  return false\n}\n"]}