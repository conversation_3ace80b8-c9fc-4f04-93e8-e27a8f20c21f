(function() {
var exports = {};
exports.id = 405;
exports.ids = [405];
exports.modules = {

/***/ 110:
/***/ (function(module) {

if (true) module.exports = simpleheat;

function simpleheat(ctx, canvasResolution) {
  if (!(this instanceof simpleheat)) return new simpleheat(ctx, canvasResolution); // this._canvas = canvas = typeof canvas === 'string' ? document.getElementById(canvas) : canvas;

  this._ctx = ctx;
  this._canvasResolution = canvasResolution;
  this._width = canvasResolution.w;
  this._height = canvasResolution.h;
  this._max = 1;
  this._data = [];
}

simpleheat.prototype = {
  defaultRadius: 25,
  defaultGradient: {
    0.4: 'blue',
    0.6: 'cyan',
    0.7: 'lime',
    0.8: 'yellow',
    1.0: 'red'
  },

  data(data) {
    this._data = data;
    return this;
  },

  max(max) {
    this._max = max;
    return this;
  },

  add(point) {
    this._data.push(point);

    return this;
  },

  clear() {
    this._data = [];
    return this;
  },

  radius(r, blur) {
    blur = blur === undefined ? 15 : blur; // create a grayscale blurred circle image that we'll use for drawing points

    const circle = this._circle = this._createCanvas();

    const ctx = circle.getContext('2d');
    const r2 = this._r = r + blur;
    circle.width = circle.height = r2 * 2;
    ctx.shadowOffsetX = ctx.shadowOffsetY = r2 * 2;
    ctx.shadowBlur = blur;
    ctx.shadowColor = 'black';
    ctx.beginPath();
    ctx.arc(-r2, -r2, r, 0, Math.PI * 2, true);
    ctx.closePath();
    ctx.fill();
    return this;
  },

  resize() {
    this._width = this._canvasResolution.w;
    this._height = this._canvasResolution.h;
  },

  gradient(grad) {
    // create a 256x1 gradient that we'll use to turn a grayscale heatmap into a colored one
    const canvas = this._createCanvas();

    const ctx = canvas.getContext('2d');
    const gradient = ctx.createLinearGradient(0, 0, 0, 256);
    canvas.width = 1;
    canvas.height = 256;

    for (const i in grad) {
      gradient.addColorStop(+i, grad[i]);
    }

    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 1, 256);
    this._grad = ctx.getImageData(0, 0, 1, 256).data;
    return this;
  },

  draw(minOpacity) {
    if (!this._circle) this.radius(this.defaultRadius);
    if (!this._grad) this.gradient(this.defaultGradient);
    const ctx = this._ctx;
    ctx.clearRect(0, 0, this._width, this._height); // draw a grayscale heatmap by putting a blurred circle at each data point

    for (var i = 0, len = this._data.length, p; i < len; i++) {
      p = this._data[i];
      ctx.globalAlpha = Math.min(Math.max(p[2] / this._max, minOpacity === undefined ? 0.05 : minOpacity), 1);
      ctx.drawImage(this._circle, p[0] - this._r, p[1] - this._r);
    } // colorize the heatmap, using opacity value of each pixel to get the right color from our gradient


    const colored = ctx.getImageData(0, 0, this._width, this._height);

    this._colorize(colored.data, this._grad);

    ctx.putImageData(colored, 0, 0);
    return this;
  },

  _colorize(pixels, gradient) {
    for (var i = 0, len = pixels.length, j; i < len; i += 4) {
      j = pixels[i + 3] * 4; // get gradient color from opacity value

      if (j) {
        pixels[i] = gradient[j];
        pixels[i + 1] = gradient[j + 1];
        pixels[i + 2] = gradient[j + 2];
      }
    }
  },

  _createCanvas() {
    if (typeof document !== 'undefined') {
      return document.createElement('canvas');
    } // create a new canvas instance in node.js
    // the canvas class needs to have a default constructor without any parameter


    return new this._canvas.constructor();
  }

};

/***/ }),

/***/ 317:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ pages; }
});

// EXTERNAL MODULE: external "react"
var external_react_ = __webpack_require__(297);
var external_react_default = /*#__PURE__*/__webpack_require__.n(external_react_);
;// CONCATENATED MODULE: external "next/head"
var head_namespaceObject = require("next/head");;
var head_default = /*#__PURE__*/__webpack_require__.n(head_namespaceObject);
;// CONCATENATED MODULE: ./components/shared/Layout.js
var __jsx = (external_react_default()).createElement;



class Layout extends external_react_.Component {
  render() {
    return __jsx("div", null, __jsx((head_default()), null, __jsx("title", null, "OpenDataCam"), __jsx("meta", {
      charSet: "utf-8"
    }), __jsx("meta", {
      name: "viewport",
      content: "width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1,user-scalable=0,initial-scale=1"
    }), __jsx("link", {
      rel: "apple-touch-icon",
      sizes: "180x180",
      href: "/apple-touch-icon.png"
    }), __jsx("link", {
      rel: "icon",
      type: "image/png",
      sizes: "32x32",
      href: "/favicon-32x32.png"
    }), __jsx("link", {
      rel: "icon",
      type: "image/png",
      sizes: "16x16",
      href: "/favicon-16x16.png"
    }), __jsx("link", {
      rel: "manifest",
      href: "/site.webmanifest"
    }), __jsx("script", {
      type: "text/javascript",
      src: "/static/js/fabric-3.6.1.min.js"
    })), this.props.children);
  }

}

/* harmony default export */ var shared_Layout = (Layout);
;// CONCATENATED MODULE: external "styled-jsx/style"
var style_namespaceObject = require("styled-jsx/style");;
var style_default = /*#__PURE__*/__webpack_require__.n(style_namespaceObject);
// EXTERNAL MODULE: external "react-redux"
var external_react_redux_ = __webpack_require__(79);
// EXTERNAL MODULE: external "axios"
var external_axios_ = __webpack_require__(376);
var external_axios_default = /*#__PURE__*/__webpack_require__.n(external_axios_);
;// CONCATENATED MODULE: ./components/shared/AskLandscape.js

var AskLandscape_jsx = (external_react_default()).createElement;


class AskLandscape extends external_react_.Component {
  render() {
    return AskLandscape_jsx("div", {
      className: "jsx-808101764" + " " + "turnDevice"
    }, AskLandscape_jsx("div", {
      className: "jsx-808101764" + " " + "icon"
    }, "\uD83D\uDCF1"), AskLandscape_jsx("h1", {
      className: "jsx-808101764" + " " + "text-white text-2xl"
    }, "Please use your device in Landscape"), AskLandscape_jsx((style_default()), {
      id: "808101764"
    }, [".turnDevice.jsx-808101764{position:fixed;width:100%;height:100%;background-color:black;padding:2rem;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;text-align:center;z-index:10000000000000;}", ".turnDevice.jsx-808101764 h1.jsx-808101764{margin-top:1rem;}", ".icon.jsx-808101764{font-size:5rem;-webkit-animation-name:spin-jsx-808101764;animation-name:spin-jsx-808101764;-webkit-animation-duration:2s;animation-duration:2s;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;-webkit-animation-timing-function:linear;animation-timing-function:linear;}", "@-webkit-keyframes spin-jsx-808101764{0%{-webkit-transform:rotate(0deg);-ms-transform:rotate(0deg);transform:rotate(0deg);}30%{-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg);}100%{-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg);}}", "@keyframes spin-jsx-808101764{0%{-webkit-transform:rotate(0deg);-ms-transform:rotate(0deg);transform:rotate(0deg);}30%{-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg);}100%{-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg);}}"]));
  }

}

/* harmony default export */ var shared_AskLandscape = (AskLandscape);
;// CONCATENATED MODULE: ./components/shared/WebcamStream.js

var WebcamStream_jsx = (external_react_default()).createElement;


/*

  Solved by proxing the mjpeg stream from darknet on the server

  We are pulling the live view from a MJPEG HTTP Stream sent by the YOLO process

  Improvements ideas:
    -> Maybe the MJPEG stream is not well implemented on the YOLO process side
    -> Readable stream improve perfs : ( https://github.com/aruntj/mjpeg-readable-stream but I think not that useful , browser compat
    -> draw directly on canvas instead of having a <img> tag (https://gist.github.com/codebrainz/eeeeead894e8bdff059b)
    -> Support other resolution than 16/9
    -> Do not use the mjpeg HTTP Stream:  but launch a HLS stream with Gstreamer: https://stackoverflow.com/questions/34975851/i-want-to-perform-hls-http-live-streaming-using-gstreamer
    -  this will enable to launch stream with a <video> tag

*/

class WebcamStream extends external_react_.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      url: '/webcam/stream'
    };
  }

  componentDidMount() {}

  render() {
    return WebcamStream_jsx((external_react_default()).Fragment, null, WebcamStream_jsx("img", {
      width: this.props.resolution.get('w'),
      height: this.props.resolution.get('h'),
      src: this.state.url,
      className: "jsx-1451342145"
    }), WebcamStream_jsx((style_default()), {
      id: "1451342145"
    }, ["img.jsx-1451342145{height:inherit;}"]));
  }

}

/* harmony default export */ var shared_WebcamStream = ((0,external_react_redux_.connect)(state => ({
  resolution: state.viewport.get('canvasResolution')
}))(WebcamStream));
// EXTERNAL MODULE: ./statemanagement/app/ViewportStateManagement.js
var ViewportStateManagement = __webpack_require__(198);
// EXTERNAL MODULE: ./statemanagement/app/AppStateManagement.js
var AppStateManagement = __webpack_require__(855);
;// CONCATENATED MODULE: external "raf"
var external_raf_namespaceObject = require("raf");;
var external_raf_default = /*#__PURE__*/__webpack_require__.n(external_raf_namespaceObject);
// EXTERNAL MODULE: ./utils/constants.js
var constants = __webpack_require__(526);
// EXTERNAL MODULE: ./utils/resolution.js
var resolution = __webpack_require__(429);
// EXTERNAL MODULE: ./utils/colors.js
var colors = __webpack_require__(799);
// EXTERNAL MODULE: ./tailwind.config.js
var tailwind_config = __webpack_require__(263);
var tailwind_config_default = /*#__PURE__*/__webpack_require__.n(tailwind_config);
;// CONCATENATED MODULE: ./components/canvas/engines/LiveViewEngine.js



const {
  colors: LiveViewEngine_colors
} = (tailwind_config_default()).theme.extend;

class LiveViewEngine {
  drawTrackerData(context, objectTrackerData, canvasResolution, originalResolution) {
    context.globalAlpha = 1;
    context.lineWidth = 2;
    objectTrackerData.map(objectTracked => {
      context.globalAlpha = Math.max(Math.min(objectTracked.opacity, 1), 0);
      const objectTrackedScaled = (0,resolution/* scaleDetection */.n)(objectTracked, canvasResolution, originalResolution);
      const x = objectTrackedScaled.x - objectTrackedScaled.w / 2;
      const y = objectTrackedScaled.y - objectTrackedScaled.h / 2; // context.strokeStyle = 'black'
      // context.strokeRect(
      //   x + 5,
      //   y + 5,
      //   objectTrackedScaled.w - 10,
      //   objectTrackedScaled.h - 10
      // )

      context.setLineDash([10, 10]);
      context.strokeStyle = (0,colors/* evaluateCSSVariable */.at)(LiveViewEngine_colors.default);
      context.strokeRect(x + 5, y + 5, objectTrackedScaled.w - 10, objectTrackedScaled.h - 10);
      context.setLineDash([]);
      context.fillStyle = (0,colors/* evaluateCSSVariable */.at)(LiveViewEngine_colors.default);
      context.fillRect(x + 4, y - 10, objectTrackedScaled.w - 8, 17); // confidence -- text

      context.font = '10px';
      context.fillStyle = (0,colors/* evaluateCSSVariable */.at)(LiveViewEngine_colors.inverse);
      const rightName = x + 10 + context.measureText(`${objectTrackedScaled.name}`).width;
      const xConfidence = x + objectTrackedScaled.w - 30;

      if (rightName < xConfidence) {
        context.fillText(`${Math.round(objectTrackedScaled.confidence * 100)}%`, xConfidence, y);
      } // name -- background


      context.fillStyle = (0,colors/* evaluateCSSVariable */.at)(LiveViewEngine_colors.default);
      context.fillRect(x + 10, y - 10, context.measureText(`${objectTrackedScaled.name}`).width, 17); // name -- text

      context.fillStyle = (0,colors/* evaluateCSSVariable */.at)(LiveViewEngine_colors.inverse);
      context.fillText(`${objectTrackedScaled.name}`, x + 10, y);
    });
  }

  drawTrackerDataCounterEditor(context, objectTrackerData, countingAreas, canvasResolution, originalResolution, timeNow = new Date().getTime()) {
    context.globalAlpha = 1;
    context.lineWidth = 2;
    objectTrackerData.map(objectTracked => {
      const objectTrackedScaled = (0,resolution/* scaleDetection */.n)(objectTracked, canvasResolution, originalResolution);
      const x = objectTrackedScaled.x - objectTrackedScaled.w / 2;
      const y = objectTrackedScaled.y - objectTrackedScaled.h / 2; // Counted status

      let displayCountedArea = null; // get last counted event

      const countedEvent = objectTracked.counted && objectTracked.counted[objectTracked.counted.length - 1]; // For lines, display only during 1s after beeing counted

      if (countedEvent && countingAreas.getIn([countedEvent.areaKey, 'type']) !== 'polygon') {
        if (timeNow - countedEvent.timeMs < 1000) {
          displayCountedArea = true;
        }
      } // For polygon, as long as it is still inside the area


      if (countedEvent && countingAreas.getIn([countedEvent.areaKey, 'type']) === 'polygon') {
        if (objectTracked.areas.indexOf(countedEvent.areaKey) > -1) {
          displayCountedArea = true;
        }
      } // Display counted status for lines & polygon
      // => for lines : during 1s after beeing counted
      // => for polygons: as long as it remains inside the area


      if (displayCountedArea) {
        // displayCountedArea contain countingareakey : see Opendatacam.js on server side
        context.strokeStyle = (0,colors/* getCounterColor */.SD)(countingAreas.getIn([countedEvent.areaKey, 'color']));
        context.fillStyle = (0,colors/* getCounterColor */.SD)(countingAreas.getIn([countedEvent.areaKey, 'color']));
        context.strokeRect(x + 5, y + 5, objectTrackedScaled.w - 10, objectTrackedScaled.h - 10);
        context.globalAlpha = 0.1;
        context.fillRect(x + 5, y + 5, objectTrackedScaled.w - 10, objectTrackedScaled.h - 10);
        context.globalAlpha = 1;
      } else {
        context.setLineDash([10, 10]);
        context.strokeStyle = (0,colors/* evaluateCSSVariable */.at)(LiveViewEngine_colors.default);
        context.strokeRect(x + 5, y + 5, objectTrackedScaled.w - 10, objectTrackedScaled.h - 10);
        context.setLineDash([]);
      }
    });
  }

  drawCountingAreas(context, countingAreas, canvasResolution) {
    countingAreas.map((area, id) => {
      if (area.get('location') !== null) {
        const data = area.get('location').toJS();
        const color = area.get('color');
        context.strokeStyle = (0,colors/* getCounterColor */.SD)(color);
        context.fillStyle = (0,colors/* getCounterColor */.SD)(color);
        context.lineWidth = 5; // TODO Have those dynamic depending on canvas resolution

        const edgeCircleRadius = 5; // Rescale points

        const points = data.points.map(point => (0,resolution/* scalePoint */.q)(point, canvasResolution, data.refResolution));

        for (let index = 0; index < points.length; index++) {
          const point = points[index]; // Draw circle

          context.beginPath();
          context.arc(point.x, point.y, edgeCircleRadius, 0, 2 * Math.PI, false);
          context.fill(); // Draw line
          // Draw line connecting to previous point

          if (index > 0) {
            context.beginPath();
            context.moveTo(points[index - 1].x, points[index - 1].y);
            context.lineTo(points[index].x, points[index].y);
            context.stroke();
          }
        } // Draw polygon if length > 2


        if (points.length > 2) {
          context.globalAlpha = 0.3;
          context.beginPath();
          context.moveTo(points[0].x, points[0].y);
          points.map(point => {
            context.lineTo(point.x, point.y);
            context.lineTo(point.x, point.y);
          });
          context.fill();
          context.globalAlpha = 1;
        }
      }
    });
  } // drawRawDetections (context, detections, canvasResolution, originalResolution) {
  //   context.strokeStyle = '#f00'
  //   context.lineWidth = 5
  //   context.font = '15px Arial'
  //   context.fillStyle = '#f00'
  //   detections.map(detection => {
  //     let scaledDetection = scaleDetection(
  //       detection,
  //       canvasResolution,
  //       originalResolution
  //     )
  //     let x = scaledDetection.x - scaledDetection.w / 2
  //     let y = scaledDetection.y - scaledDetection.h / 2
  //     context.strokeRect(x, y, scaledDetection.w, scaledDetection.h)
  //     context.fillText(scaledDetection.name, x, y - 10)
  //   })
  // }


}

const LiveViewEngineInstance = new LiveViewEngine();
/* harmony default export */ var engines_LiveViewEngine = (LiveViewEngineInstance);
;// CONCATENATED MODULE: ./components/canvas/engines/PathViewEngine.js



class PathViewEngine {
  constructor() {
    this.lastFrameData = [];
    this.pathsColors = (0,colors/* getPathfinderColors */.E5)();
  }

  drawLine(context, line, color = 'green') {
    context.strokeStyle = color;
    context.lineWidth = 5;
    context.lineCap = 'round';
    context.beginPath();
    context.moveTo(line.pointA.x, line.pointA.y);
    context.lineTo(line.pointB.x, line.pointB.y);
    context.stroke();
  }

  drawPaths(context, currentFrameData, canvasResolution, originalResolution) {
    this.lastFrameData = currentFrameData.map(objectTracked => {
      const trackedItemScaled = (0,resolution/* scaleDetection */.n)(objectTracked, canvasResolution, originalResolution); // If this tracked Item was already there in last frame

      const lastFrameTrackedItem = this.lastFrameData.find(lastFrameItemTracked => trackedItemScaled.id === lastFrameItemTracked.id);

      if (lastFrameTrackedItem) {
        const color = lastFrameTrackedItem.color ? lastFrameTrackedItem.color : this.pathsColors[lastFrameTrackedItem.id % this.pathsColors.length];
        trackedItemScaled.color = color;
        this.drawLine(context, {
          pointA: {
            x: lastFrameTrackedItem.x,
            y: lastFrameTrackedItem.y
          },
          pointB: {
            x: trackedItemScaled.x,
            y: trackedItemScaled.y
          }
        }, color);
      }

      return trackedItemScaled;
    });
  }

  resetLastFrameData() {
    this.lastFrameData = [];
  }

}

/* harmony default export */ var engines_PathViewEngine = (PathViewEngine);
// EXTERNAL MODULE: ./components/canvas/engines/simpleheat.js
var simpleheat = __webpack_require__(110);
var simpleheat_default = /*#__PURE__*/__webpack_require__.n(simpleheat);
// EXTERNAL MODULE: ./statemanagement/app/TrackerStateManagement.js
var TrackerStateManagement = __webpack_require__(293);
;// CONCATENATED MODULE: ./components/canvas/engines/TrackerAccuracyEngine.js





class TrackerAccuracyEngine {
  constructor() {
    this.lastFrameData = [];
    this.pathsColors = (0,colors/* getPathfinderColors */.E5)();
    this.simpleheat = null;
    this.heatmapData = [];
  }

  drawAccuracyHeatmap(canvasCtx, currentFrameData, canvasResolution, originalResolution) {
    // if simpleheat engine not initialiaze
    if (!this.simpleheat) {
      this.simpleheat = simpleheat_default()(canvasCtx, canvasResolution);
      this.simpleheat.radius(canvasResolution.w * (0,TrackerStateManagement/* getTrackerAccuracySettings */._d)().radius / 100, canvasResolution.w * (0,TrackerStateManagement/* getTrackerAccuracySettings */._d)().blur / 100);
      this.simpleheat.gradient((0,TrackerStateManagement/* getTrackerAccuracySettings */._d)().gradient);
    }

    if (this.heatmapData.length > (0,TrackerStateManagement/* getTrackerAccuracyNbFrameBuffer */.QJ)()) {
      // remove first item
      this.heatmapData.shift();
    }

    this.heatmapData.push(currentFrameData.filter(trackedItem => trackedItem.isZombie === true).map(trackedItem => {
      const trackedItemScaled = (0,resolution/* scaleDetection */.n)(trackedItem, canvasResolution, originalResolution);
      return [trackedItemScaled.x, trackedItemScaled.y, (0,TrackerStateManagement/* getTrackerAccuracySettings */._d)().step];
    }));
    this.simpleheat.data(this.heatmapData.flat()).draw();
  }

}

/* harmony default export */ var engines_TrackerAccuracyEngine = (TrackerAccuracyEngine);
;// CONCATENATED MODULE: ./components/canvas/CanvasEngine.js

var CanvasEngine_jsx = (external_react_default()).createElement;









class CanvasEngine extends external_react_.PureComponent {
  constructor(props) {
    super(props);
    this.lastFrameDrawn = -1;
    this.loopUpdateCanvas = this.loopUpdateCanvas.bind(this);
    this.clearCanvas = this.clearCanvas.bind(this);
    this.rafHandle = null;
    this.PathViewEngine = new engines_PathViewEngine();
    this.TrackerAccuracyEngine = new engines_TrackerAccuracyEngine();
  }

  componentDidMount() {
    this.loopUpdateCanvas();
  }

  componentDidUpdate(prevProps) {
    if (this.props.canvasResolution !== prevProps.canvasResolution) {
      this.PathViewEngine.resetLastFrameData();
      this.clearCanvas();
    }
  }

  clearCanvas() {
    // console.log('clearCanvas')
    this.canvasContext.clearRect(0, 0, this.props.fixedResolution && this.props.fixedResolution.w || this.props.canvasResolution.get('w'), this.props.fixedResolution && this.props.fixedResolution.h || this.props.canvasResolution.get('h'));

    if (this.props.mode === constants/* CANVAS_RENDERING_MODE.PATHVIEW */.qI.PATHVIEW) {
      this.PathViewEngine.resetLastFrameData();
    }
  }

  loopUpdateCanvas() {
    if (this.lastFrameDrawn !== this.props.trackerData.frameIndex) {
      // Clear previous frame
      if (this.props.mode !== constants/* CANVAS_RENDERING_MODE.PATHVIEW */.qI.PATHVIEW) {
        this.clearCanvas();
      }
      /*
        Draw things for this frame
      */


      if (this.props.mode === constants/* CANVAS_RENDERING_MODE.LIVEVIEW */.qI.LIVEVIEW) {
        engines_LiveViewEngine.drawTrackerData(this.canvasContext, this.props.trackerData.data, this.props.canvasResolution.toJS(), this.props.originalResolution);
      }

      if (this.props.mode === constants/* CANVAS_RENDERING_MODE.COUNTERVIEW */.qI.COUNTERVIEW) {
        engines_LiveViewEngine.drawTrackerDataCounterEditor(this.canvasContext, this.props.trackerData.data, this.props.countingAreas, this.props.canvasResolution.toJS(), this.props.originalResolution);
      }

      if (this.props.mode === constants/* CANVAS_RENDERING_MODE.COUNTERVIEW_RECORDING */.qI.COUNTERVIEW_RECORDING) {
        engines_LiveViewEngine.drawCountingAreas(this.canvasContext, this.props.countingAreas, this.props.canvasResolution.toJS());
        engines_LiveViewEngine.drawTrackerDataCounterEditor(this.canvasContext, this.props.trackerData.data, this.props.countingAreas, this.props.canvasResolution.toJS(), this.props.originalResolution);
      }

      if (this.props.mode === constants/* CANVAS_RENDERING_MODE.COUNTING_AREAS */.qI.COUNTING_AREAS) {
        engines_LiveViewEngine.drawCountingAreas(this.canvasContext, this.props.countingAreas, this.props.canvasResolution.toJS());
      }

      if (this.props.mode === constants/* CANVAS_RENDERING_MODE.PATHVIEW */.qI.PATHVIEW) {
        this.PathViewEngine.drawPaths(this.canvasContext, this.props.trackerData.data, this.props.fixedResolution || this.props.canvasResolution.toJS(), this.props.originalResolution);
      }

      if (this.props.mode === constants/* CANVAS_RENDERING_MODE.TRACKER_ACCURACY */.qI.TRACKER_ACCURACY) {
        this.TrackerAccuracyEngine.drawAccuracyHeatmap(this.canvasContext, this.props.trackerData.data, this.props.fixedResolution || this.props.canvasResolution.toJS(), this.props.originalResolution);
      }

      this.lastFrameDrawn = this.props.trackerData.frameIndex;
    }

    this.rafHandle = external_raf_default()(this.loopUpdateCanvas.bind(this));
  }

  componentWillUnmount() {
    if (this.rafHandle) {
      external_raf_default().cancel(this.rafHandle);
    }
  }

  render() {
    return CanvasEngine_jsx("div", {
      className: style_default().dynamic([["382447905", [this.props.hidden ? 'hidden' : 'visible', this.props.userSettings.get('dimmerOpacity')]]]) + " " + "canvas-container"
    }, CanvasEngine_jsx("canvas", {
      ref: el => {
        this.canvasEl = el;

        if (this.canvasEl) {
          this.canvasContext = el.getContext('2d');

          if (this.props.onDomReady) {
            this.props.onDomReady(this.canvasEl);
          }

          if (this.props.registerClearCanvas) {
            this.props.registerClearCanvas(this.clearCanvas);
          }
        }
      },
      width: this.props.fixedResolution && this.props.fixedResolution.w || this.props.canvasResolution.get('w'),
      height: this.props.fixedResolution && this.props.fixedResolution.h || this.props.canvasResolution.get('h'),
      className: style_default().dynamic([["382447905", [this.props.hidden ? 'hidden' : 'visible', this.props.userSettings.get('dimmerOpacity')]]]) + " " + "canvas"
    }), CanvasEngine_jsx((style_default()), {
      id: "382447905",
      dynamic: [this.props.hidden ? 'hidden' : 'visible', this.props.userSettings.get('dimmerOpacity')]
    }, [`.canvas-container.__jsx-style-dynamic-selector{width:100%;height:100%;position:absolute;top:0;left:0;pointer-events:none;visibility:${this.props.hidden ? 'hidden' : 'visible'};}`, `.canvas.__jsx-style-dynamic-selector{display:block;position:absolute;top:0;left:0;z-index:1;width:100%;height:100%;background-color:rgba(0,0,0,${this.props.userSettings.get('dimmerOpacity')});}`]));
  }

}

/* harmony default export */ var canvas_CanvasEngine = ((0,external_react_redux_.connect)(state => ({
  trackerData: state.tracker.get('trackerData').toJS(),
  originalResolution: state.viewport.get('originalResolution').toJS(),
  canvasResolution: state.viewport.get('canvasResolution'),
  countingAreas: state.counter.get('countingAreas'),
  userSettings: state.usersettings
}))(CanvasEngine));
;// CONCATENATED MODULE: ./components/shared/BtnRecording.js

var BtnRecording_jsx = (external_react_default()).createElement;




class BtnRecording extends external_react_.Component {
  handleClick() {
    if (this.props.recordingStatus.isRecording) {
      this.props.dispatch((0,AppStateManagement/* stopRecording */.kJ)());
    } else {
      this.props.dispatch((0,AppStateManagement/* startRecording */.Sy)());
    }
  }

  render() {
    return BtnRecording_jsx("div", {
      className: "jsx-3341985503" + " " + "btn-record-container"
    }, BtnRecording_jsx("div", {
      onClick: () => this.handleClick(),
      className: "jsx-3341985503" + " " + "btn-record"
    }, this.props.recordingStatus.isRecording && BtnRecording_jsx((external_react_default()).Fragment, null, BtnRecording_jsx("img", {
      src: "/static/icons/ui/stop-recording.svg",
      className: "jsx-3341985503" + " " + "inline"
    }), BtnRecording_jsx("h3", {
      className: "jsx-3341985503" + " " + "btn-record-label text-default text-xl font-bold"
    }, "Stop recording")), !this.props.recordingStatus.isRecording && BtnRecording_jsx((external_react_default()).Fragment, null, BtnRecording_jsx("img", {
      src: "/static/icons/ui/start-recording.svg",
      className: "jsx-3341985503" + " " + "inline"
    }), BtnRecording_jsx("h3", {
      className: "jsx-3341985503" + " " + "btn-record-label text-default text-xl font-bold"
    }, "Start recording"))), BtnRecording_jsx((style_default()), {
      id: "3341985503"
    }, [".btn-record-container.jsx-3341985503{position:fixed;bottom:0.62rem;left:50%;-webkit-transform:translateX(-50%);-ms-transform:translateX(-50%);transform:translateX(-50%);z-index:5;}", ".btn-record.jsx-3341985503{position:relative;text-align:center;z-index:2;cursor:pointer;}", ".btn-record.jsx-3341985503 img.jsx-3341985503{width:3.12rem;}", ".btn-record-label.jsx-3341985503{margin-top:0.5rem;text-shadow:0px 2px 4px rgba(73,73,73,0.5);}"]));
  }

}

/* harmony default export */ var shared_BtnRecording = ((0,external_react_redux_.connect)(state => ({
  recordingStatus: state.app.get('recordingStatus').toJS(),
  mode: state.app.get('mode')
}))(BtnRecording));
;// CONCATENATED MODULE: ./components/main/LiveView.js
var LiveView_jsx = (external_react_default()).createElement;






class LiveView extends external_react_.PureComponent {
  constructor(props) {
    super(props);
  }

  componentDidMount() {}

  componentWillUnmount() {}

  render() {
    return LiveView_jsx((external_react_default()).Fragment, null, LiveView_jsx(canvas_CanvasEngine, {
      mode: constants/* CANVAS_RENDERING_MODE.LIVEVIEW */.qI.LIVEVIEW
    }), LiveView_jsx(shared_BtnRecording, null));
  }

}

/* harmony default export */ var main_LiveView = (LiveView);
;// CONCATENATED MODULE: external "react-inlinesvg"
var external_react_inlinesvg_namespaceObject = require("react-inlinesvg");;
var external_react_inlinesvg_default = /*#__PURE__*/__webpack_require__.n(external_react_inlinesvg_namespaceObject);
// EXTERNAL MODULE: ./statemanagement/app/CounterStateManagement.js
var CounterStateManagement = __webpack_require__(69);
;// CONCATENATED MODULE: ./components/shared/MenuCountingAreasEditor.js

var MenuCountingAreasEditor_jsx = (external_react_default()).createElement;





class MenuCountingAreasEditor extends external_react_.Component {
  handleDelete() {
    if (this.props.countingAreas.size > 1) {
      this.props.dispatch((0,CounterStateManagement/* setMode */.PM)(CounterStateManagement/* EDITOR_MODE.DELETE */.Q4.DELETE));
    } else {
      this.props.dispatch((0,CounterStateManagement/* deleteCountingArea */.XO)(this.props.countingAreas.keySeq().first()));
    }
  }

  loadFile() {
    console.log('loadFile');
    let input;
    let file;
    let fr;

    if (typeof window.FileReader !== 'function') {
      alert("The file API isn't supported on this browser yet.");
      return;
    }

    input = document.getElementById('upload');

    if (!input) {
      alert("Um, couldn't find the fileinput element.");
    } else if (!input.files) {
      alert("This browser doesn't seem to support the `files` property of file inputs.");
    } else if (!input.files[0]) {
      alert("Please select a file before clicking 'Load'");
    } else {
      file = input.files[0];
      fr = new FileReader();

      fr.onload = e => {
        const lines = e.target.result;
        const json = JSON.parse(lines);
        this.props.dispatch((0,CounterStateManagement/* restoreCountingAreasFromJSON */.GA)(json));
      };

      fr.readAsText(file);
    }
  }

  render() {
    return MenuCountingAreasEditor_jsx("div", {
      className: "jsx-1890780646" + " " + "menu-active-areas flex fixed bottom-0 left-0 mb-2 ml-2"
    }, this.props.mode !== CounterStateManagement/* EDITOR_MODE.DELETE */.Q4.DELETE && MenuCountingAreasEditor_jsx((external_react_default()).Fragment, null, MenuCountingAreasEditor_jsx("button", {
      onClick: () => this.handleDelete(),
      className: "jsx-1890780646" + " " + "btn btn-default p-0 rounded-l shadow"
    }, MenuCountingAreasEditor_jsx((external_react_inlinesvg_default()), {
      className: "w-10 h-10 svg-icon flex items-center",
      cacheRequests: true,
      src: "/static/icons/ui/delete.svg",
      "aria-label": "icon delete"
    })), MenuCountingAreasEditor_jsx("button", {
      onClick: () => this.props.dispatch((0,CounterStateManagement/* setMode */.PM)(CounterStateManagement/* EDITOR_MODE.EDIT_LINE */.Q4.EDIT_LINE)),
      className: "jsx-1890780646" + " " + `btn btn-default p-0 shadow ${this.props.mode === CounterStateManagement/* EDITOR_MODE.EDIT_LINE */.Q4.EDIT_LINE ? 'btn-default--active' : ''}`
    }, MenuCountingAreasEditor_jsx((external_react_inlinesvg_default()), {
      className: "w-10 h-10 svg-icon flex items-center",
      cacheRequests: true,
      src: "/static/icons/ui/addline.svg",
      "aria-label": "icon addline"
    })), MenuCountingAreasEditor_jsx("button", {
      onClick: () => this.props.dispatch((0,CounterStateManagement/* setMode */.PM)(CounterStateManagement/* EDITOR_MODE.EDIT_POLYGON */.Q4.EDIT_POLYGON)),
      className: "jsx-1890780646" + " " + `btn btn-default p-0 shadow rounded-r ${this.props.mode === CounterStateManagement/* EDITOR_MODE.EDIT_POLYGON */.Q4.EDIT_POLYGON ? 'btn-default--active' : ''}`
    }, MenuCountingAreasEditor_jsx((external_react_inlinesvg_default()), {
      className: "w-10 h-10 svg-icon flex items-center",
      cacheRequests: true,
      src: "/static/icons/ui/addpolygon.svg",
      "aria-label": "icon addpolygon"
    })), MenuCountingAreasEditor_jsx("a", {
      href: "/counter/areas",
      target: "_blank",
      download: true,
      className: "jsx-1890780646" + " " + "btn btn-default p-0 ml-4 rounded-l shadow"
    }, MenuCountingAreasEditor_jsx((external_react_inlinesvg_default()), {
      className: "w-10 h-10 svg-icon flex items-center",
      cacheRequests: true,
      src: "/static/icons/ui/download.svg",
      "aria-label": "icon download"
    })), MenuCountingAreasEditor_jsx("label", {
      htmlFor: "upload",
      className: "jsx-1890780646" + " " + "btn btn-default p-0 rounded-r shadow cursor-pointer	"
    }, MenuCountingAreasEditor_jsx((external_react_inlinesvg_default()), {
      className: "w-10 h-10 svg-icon flex items-center",
      cacheRequests: true,
      src: "/static/icons/ui/upload.svg",
      "aria-label": "icon upload"
    }), MenuCountingAreasEditor_jsx("input", {
      type: "file",
      id: "upload",
      onChange: () => this.loadFile(),
      style: {
        display: 'none'
      },
      className: "jsx-1890780646"
    }))), this.props.mode === CounterStateManagement/* EDITOR_MODE.DELETE */.Q4.DELETE && MenuCountingAreasEditor_jsx("button", {
      onClick: () => this.props.dispatch((0,CounterStateManagement/* setMode */.PM)(this.props.lastEditingMode)),
      className: "jsx-1890780646" + " " + "btn btn-default p-0 rounded shadow"
    }, MenuCountingAreasEditor_jsx((external_react_inlinesvg_default()), {
      className: "w-10 h-10 svg-icon flex items-center",
      cacheRequests: true,
      src: "/static/icons/ui/close.svg",
      "aria-label": "icon edit"
    })), MenuCountingAreasEditor_jsx((style_default()), {
      id: "1890780646"
    }, [".menu-active-areas.jsx-1890780646{z-index:8;}"]));
  }

}

/* harmony default export */ var shared_MenuCountingAreasEditor = ((0,external_react_redux_.connect)(state => ({
  countingAreas: state.counter.get('countingAreas'),
  selectedCountingArea: state.counter.get('selectedCountingArea'),
  mode: state.counter.get('mode'),
  lastEditingMode: state.counter.get('lastEditingMode')
}))(MenuCountingAreasEditor));
;// CONCATENATED MODULE: ./components/shared/AskNameModal.js

var AskNameModal_jsx = (external_react_default()).createElement;





class AskNameModal extends external_react_.Component {
  constructor(props) {
    super(props);
    this.state = {
      name: props.name || ''
    };
    this.handleChange = this.handleChange.bind(this);
    this.escFunction = this.escFunction.bind(this);
  }

  handleChange(event) {
    this.setState({
      name: event.target.value
    });
  }

  escFunction(event) {
    if (event.keyCode === 27) {
      this.props.cancel();
    }
  }

  componentDidMount() {
    document.addEventListener('keydown', this.escFunction, false);
  }

  componentWillUnmount() {
    document.removeEventListener('keydown', this.escFunction, false);
  }

  render() {
    return AskNameModal_jsx("div", {
      className: "jsx-74240079" + " " + "overlay"
    }, AskNameModal_jsx("form", {
      onSubmit: e => {
        e.preventDefault();

        if (this.state.name !== '') {
          this.props.save(this.state.name);
        }
      },
      className: "jsx-74240079" + " " + "ask-name flex"
    }, AskNameModal_jsx("input", {
      type: "text",
      value: this.state.name,
      onChange: this.handleChange,
      placeholder: "Counter name",
      autoFocus: true,
      className: "jsx-74240079" + " " + "appearance-none rounded-l py-2 px-3"
    }), AskNameModal_jsx("input", {
      type: "submit",
      value: "OK",
      className: "jsx-74240079" + " " + "btn btn-default cursor-pointer"
    }), AskNameModal_jsx("button", {
      onClick: () => this.props.cancel(),
      className: "jsx-74240079" + " " + "btn btn-default p-0 rounded-r"
    }, AskNameModal_jsx((external_react_inlinesvg_default()), {
      className: "w-10 h-10 svg-icon flex items-center",
      cacheRequests: true,
      src: "/static/icons/ui/close.svg",
      "aria-label": "icon close"
    }))), AskNameModal_jsx(canvas_CanvasEngine, {
      mode: constants/* CANVAS_RENDERING_MODE.COUNTING_AREAS */.qI.COUNTING_AREAS
    }), AskNameModal_jsx((style_default()), {
      id: "74240079"
    }, [".overlay.jsx-74240079{position:fixed;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,0.8);z-index:8;}", ".ask-name.jsx-74240079{text-align:center;position:fixed;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);transform:translate(-50%,-50%);z-index:6;}"]));
  }

}

/* harmony default export */ var shared_AskNameModal = (AskNameModal);
;// CONCATENATED MODULE: ./components/shared/DeleteModal.js

var DeleteModal_jsx = (external_react_default()).createElement;






class DeleteModal extends external_react_.Component {
  constructor(props) {
    super(props);
    this.escFunction = this.escFunction.bind(this);
  }

  escFunction(event) {
    event.stopPropagation();

    if (event.keyCode === 27) {
      this.props.cancel();
    }
  }

  componentDidMount() {
    document.addEventListener('keydown', this.escFunction, false);
  }

  componentWillUnmount() {
    document.removeEventListener('keydown', this.escFunction, false);
  }

  render() {
    return DeleteModal_jsx("div", {
      className: style_default().dynamic([["135469577", [constants/* CIRCLE_DELETE_RADIUS */.SL, constants/* CIRCLE_DELETE_RADIUS */.SL, constants/* CIRCLE_DELETE_RADIUS */.SL, constants/* CIRCLE_DELETE_RADIUS */.SL]]]) + " " + "overlay"
    }, this.props.countingAreasWithCenters.entrySeq().map(([id, countingArea]) => DeleteModal_jsx("div", {
      key: id,
      onClick: () => this.props.delete(id),
      style: {
        top: countingArea.getIn(['location', 'center', 'y']) - constants/* CIRCLE_DELETE_RADIUS */.SL / 2,
        left: countingArea.getIn(['location', 'center', 'x']) - constants/* CIRCLE_DELETE_RADIUS */.SL / 2,
        backgroundColor: (0,colors/* getCounterColor */.SD)(countingArea.get('color'))
      },
      className: style_default().dynamic([["135469577", [constants/* CIRCLE_DELETE_RADIUS */.SL, constants/* CIRCLE_DELETE_RADIUS */.SL, constants/* CIRCLE_DELETE_RADIUS */.SL, constants/* CIRCLE_DELETE_RADIUS */.SL]]]) + " " + "circle"
    }, DeleteModal_jsx((external_react_inlinesvg_default()), {
      className: "w-8 h-8 svg-icon flex items-center",
      cacheRequests: true,
      src: "/static/icons/ui/delete.svg",
      "aria-label": "icon close"
    }))), DeleteModal_jsx(canvas_CanvasEngine, {
      mode: constants/* CANVAS_RENDERING_MODE.COUNTING_AREAS */.qI.COUNTING_AREAS
    }), DeleteModal_jsx((style_default()), {
      id: "135469577",
      dynamic: [constants/* CIRCLE_DELETE_RADIUS */.SL, constants/* CIRCLE_DELETE_RADIUS */.SL, constants/* CIRCLE_DELETE_RADIUS */.SL, constants/* CIRCLE_DELETE_RADIUS */.SL]
    }, [".overlay.__jsx-style-dynamic-selector{position:fixed;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,0.8);z-index:8;}", `.circle.__jsx-style-dynamic-selector{position:absolute;border-radius:${constants/* CIRCLE_DELETE_RADIUS */.SL}px;z-index:2;min-width:${constants/* CIRCLE_DELETE_RADIUS */.SL}px;height:${constants/* CIRCLE_DELETE_RADIUS */.SL}px;line-height:${constants/* CIRCLE_DELETE_RADIUS */.SL}px;font-size:16px;font-weight:bold;color:black;text-align:center;cursor:pointer;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;}`]));
  }

}

/* harmony default export */ var shared_DeleteModal = (DeleteModal);
;// CONCATENATED MODULE: ./components/shared/InstructionsModal.js

var InstructionsModal_jsx = (external_react_default()).createElement;


class InstructionsModal extends external_react_.Component {
  render() {
    return InstructionsModal_jsx("div", {
      onClick: () => this.props.close(),
      className: "jsx-3225436558" + " " + "overlay"
    }, InstructionsModal_jsx("div", {
      className: "jsx-3225436558" + " " + "modal rounded p-10 shadow text-inverse bg-default border-inverse"
    }, InstructionsModal_jsx("h3", {
      className: "jsx-3225436558" + " " + "text-center text-xl font-bold"
    }, "Draw to define the counting zones"), InstructionsModal_jsx("div", {
      className: "jsx-3225436558" + " " + "text-center mt-2"
    }, "(crossing vehicles increase counter by 1)"), InstructionsModal_jsx("button", {
      onClick: () => this.props.close(),
      className: "jsx-3225436558" + " " + "btn btn-primary btn-rounded min-w-100 mt-5 pl-10 pr-10"
    }, "OK")), InstructionsModal_jsx((style_default()), {
      id: "3225436558"
    }, [".overlay.jsx-3225436558{position:fixed;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,0.8);z-index:8;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;}", ".modal.jsx-3225436558{border:1px solid black;width:300px;height:auto;padding:1rem;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;}"]));
  }

}

/* harmony default export */ var shared_InstructionsModal = (InstructionsModal);
;// CONCATENATED MODULE: ./components/shared/SingleCounterDirection.js

var SingleCounterDirection_jsx = (external_react_default()).createElement;




class SingleCounterDirection extends external_react_.Component {
  render() {
    return SingleCounterDirection_jsx((external_react_default()).Fragment, null, SingleCounterDirection_jsx("div", {
      onClick: () => this.props.toggleDirection(),
      style: {
        top: this.props.area.location.center.y - constants/* CIRCLE_RADIUS */.S7 / 2,
        left: this.props.area.location.center.x - constants/* CIRCLE_RADIUS */.S7 / 2,
        backgroundColor: (0,colors/* getCounterColor */.SD)(this.props.area.color)
      },
      className: style_default().dynamic([["729206982", [constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* ICON_DIRECTION_SIZE */.a6, constants/* ICON_DIRECTION_SIZE */.a6, this.props.area.computed.lineBearings[0] + 90]]]) + " " + "circle"
    }, this.props.area.type === constants/* COUNTING_AREA_TYPE.BIDIRECTIONAL */.Og.BIDIRECTIONAL && SingleCounterDirection_jsx("img", {
      src: "/static/icons/ui/arrow-double.svg",
      className: style_default().dynamic([["729206982", [constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* ICON_DIRECTION_SIZE */.a6, constants/* ICON_DIRECTION_SIZE */.a6, this.props.area.computed.lineBearings[0] + 90]]]) + " " + "icon-direction"
    }), this.props.area.type === constants/* COUNTING_AREA_TYPE.LEFTRIGHT_TOPBOTTOM */.Og.LEFTRIGHT_TOPBOTTOM && SingleCounterDirection_jsx("img", {
      src: "/static/icons/ui/arrow-up.svg",
      className: style_default().dynamic([["729206982", [constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* ICON_DIRECTION_SIZE */.a6, constants/* ICON_DIRECTION_SIZE */.a6, this.props.area.computed.lineBearings[0] + 90]]]) + " " + "icon-direction"
    }), this.props.area.type === constants/* COUNTING_AREA_TYPE.RIGHTLEFT_BOTTOMTOP */.Og.RIGHTLEFT_BOTTOMTOP && SingleCounterDirection_jsx("img", {
      src: "/static/icons/ui/arrow-down.svg",
      className: style_default().dynamic([["729206982", [constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* ICON_DIRECTION_SIZE */.a6, constants/* ICON_DIRECTION_SIZE */.a6, this.props.area.computed.lineBearings[0] + 90]]]) + " " + "icon-direction"
    })), SingleCounterDirection_jsx((style_default()), {
      id: "729206982",
      dynamic: [constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* ICON_DIRECTION_SIZE */.a6, constants/* ICON_DIRECTION_SIZE */.a6, this.props.area.computed.lineBearings[0] + 90]
    }, [`.circle.__jsx-style-dynamic-selector{position:absolute;border-radius:${constants/* CIRCLE_RADIUS */.S7}px;z-index:5;min-width:${constants/* CIRCLE_RADIUS */.S7}px;height:${constants/* CIRCLE_RADIUS */.S7}px;line-height:${constants/* CIRCLE_RADIUS */.S7}px;font-size:16px;font-weight:bold;padding-left:5px;padding-right:5px;color:black;text-align:center;cursor:pointer;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;}`, `.icon-direction.__jsx-style-dynamic-selector{width:${constants/* ICON_DIRECTION_SIZE */.a6}px;height:${constants/* ICON_DIRECTION_SIZE */.a6}px;-webkit-transform:rotate(${this.props.area.computed.lineBearings[0] + 90}deg);-ms-transform:rotate(${this.props.area.computed.lineBearings[0] + 90}deg);transform:rotate(${this.props.area.computed.lineBearings[0] + 90}deg);}`]));
  }

}

/* harmony default export */ var shared_SingleCounterDirection = (SingleCounterDirection);
;// CONCATENATED MODULE: ./components/shared/CounterAreasEditor.js

var CounterAreasEditor_jsx = (external_react_default()).createElement;










class CounterAreasEditor extends external_react_.Component {
  constructor(props) {
    super(props);
    this.state = {
      editorInitialized: false
    };
    this.escFunction = this.escFunction.bind(this); // Fabric.js state

    this.currentLine = null;
    this.currentPolygon = null;
    this.isDrawing = false;
    this.points = [];
  }

  checkIfClosedPolygon(point) {
    let radius = 15;

    if (this.points.length > 2) {
      const circleCenter = this.points[0];
      const dist_points = (point.x - circleCenter.x) * (point.x - circleCenter.x) + (point.y - circleCenter.y) * (point.y - circleCenter.y);
      radius *= radius;

      if (dist_points < radius) {
        return true;
      }

      return false;
    }
  }

  resetDrawing() {
    this.isDrawing = false;
    this.points = [];
    this.currentPolygon = null;
    this.currentLine = null;
  }

  escFunction(event) {
    // Prevent catching esc key press from delete or ask name modal
    if (this.props.mode === CounterStateManagement/* EDITOR_MODE.EDIT_LINE */.Q4.EDIT_LINE || this.props.mode === CounterStateManagement/* EDITOR_MODE.EDIT_POLYGON */.Q4.EDIT_POLYGON) {
      if (event.keyCode === 27) {
        this.props.dispatch((0,CounterStateManagement/* deleteCountingArea */.XO)(this.props.selectedCountingArea));
        this.resetDrawing();
      }
    }
  }

  initListeners() {
    this.editorCanvas.on('mouse:down', o => {
      if (!this.isDrawing) {
        const areaType = this.props.mode === CounterStateManagement/* EDITOR_MODE.EDIT_LINE */.Q4.EDIT_LINE ? 'bidirectional' : 'polygon';
        this.props.dispatch((0,CounterStateManagement/* addCountingArea */._8)(areaType));
      }

      this.isDrawing = true;
      const pointer = this.editorCanvas.getPointer(o.e);

      if (this.props.mode === CounterStateManagement/* EDITOR_MODE.EDIT_POLYGON */.Q4.EDIT_POLYGON) {
        if (this.checkIfClosedPolygon(pointer)) {
          // Close polygon
          this.points.push(this.points[0]); // Save polygon

          this.props.dispatch((0,CounterStateManagement/* saveCountingAreaLocation */.Co)(this.props.selectedCountingArea, {
            points: this.points,
            refResolution: {
              w: this.editorCanvas.width,
              h: this.editorCanvas.height
            }
          })); // Reset editor

          this.isDrawing = false;
          this.points = [];
          return;
        }

        this.points.push(pointer);
        this.editorCanvas.remove(this.currentPolygon);
        this.currentPolygon = new fabric.Polygon(this.points, {
          strokeWidth: 5,
          fill: (0,colors/* getCounterColor */.SD)(this.props.countingAreas.getIn([this.props.selectedCountingArea, 'color'])),
          stroke: (0,colors/* getCounterColor */.SD)(this.props.countingAreas.getIn([this.props.selectedCountingArea, 'color'])),
          opacity: 0.3,
          selectable: false,
          hasBorders: false,
          hasControls: false
        });
        this.editorCanvas.add(this.currentPolygon);
      }

      if (this.props.mode === CounterStateManagement/* EDITOR_MODE.EDIT_LINE */.Q4.EDIT_LINE) {
        this.points.push(pointer); // We finished the line

        if (this.points.length > 1) {
          this.isDrawing = false;
          const point1 = {
            x: this.points[0].x,
            y: this.points[0].y
          };
          const point2 = {
            x: this.points[1].x,
            y: this.points[1].y
          }; // Only record if line distance if superior to some threshold to avoid single clicks

          if ((0,CounterStateManagement/* computeDistance */.Qk)(point1, point2) > 50) {
            // Maybe use getCenterPoint to persist center
            this.props.dispatch((0,CounterStateManagement/* saveCountingAreaLocation */.Co)(this.props.selectedCountingArea, {
              points: this.points,
              refResolution: {
                w: this.editorCanvas.width,
                h: this.editorCanvas.height
              }
            }));
          } else {
            // Cancel line, not long enough
            this.props.dispatch((0,CounterStateManagement/* deleteCountingArea */.XO)(this.props.selectedCountingArea));
          }

          this.points = [];
          this.isDrawing = false;
          return;
        }
      } // Potential cause of bug if this.props.selectedCountingArea isn't
      // defined when we reach here
      // Init line of last two points


      const lineCoord = [pointer.x, pointer.y, pointer.x, pointer.y]; // For touch devices, draw previous line

      if (this.points.length > 1) {
        this.currentLine.set({
          x2: pointer.x,
          y2: pointer.y
        });
      }

      this.currentLine = new fabric.Line(lineCoord, {
        strokeWidth: 5,
        fill: (0,colors/* getCounterColor */.SD)(this.props.countingAreas.getIn([this.props.selectedCountingArea, 'color'])),
        stroke: (0,colors/* getCounterColor */.SD)(this.props.countingAreas.getIn([this.props.selectedCountingArea, 'color'])),
        originX: 'center',
        originY: 'center'
      });
      this.editorCanvas.add(this.currentLine);
      this.editorCanvas.add(new fabric.Circle({
        radius: 5,
        fill: (0,colors/* getCounterColor */.SD)(this.props.countingAreas.getIn([this.props.selectedCountingArea, 'color'])),
        top: pointer.y,
        left: pointer.x,
        originX: 'center',
        originY: 'center'
      }));
    });
    this.editorCanvas.on('mouse:move', o => {
      if (!this.isDrawing) return;
      const pointer = this.editorCanvas.getPointer(o.e);
      this.currentLine.set({
        x2: pointer.x,
        y2: pointer.y
      }); // TODO STORE LINE DATA POINTS

      this.editorCanvas.renderAll();
    });
  }

  componentDidUpdate(prevProps) {
    // We may have to delete some lines
    if (prevProps.countingAreas !== this.props.countingAreas) {
      this.reRenderCountingAreasInEditor(this.props.countingAreas);
    } // TODO later in order to fix bug if resizing windows while in counter editing mode
    // if(newProps.canvasResolution !== this.props.canvasResolution) {
    //   this.editorCanvas.setDimensions({
    //     width: newProps.canvasResolution.get('w'),
    //     height: newProps.canvasResolution.get('h')
    //   });
    //   // TODO Update counting areas with new refResolution
    // }

  }

  componentDidMount() {
    if (this.elCanvas) {
      const {
        width,
        height
      } = this.elCanvas.getBoundingClientRect();
      this.editorCanvas = new fabric.Canvas(this.elCanvas, {
        selection: false,
        width,
        height
      }); // If no countingAreas exists already

      if (this.props.countingAreas.size === 0) {
        this.props.dispatch((0,CounterStateManagement/* setMode */.PM)(CounterStateManagement/* EDITOR_MODE.SHOW_INSTRUCTION */.Q4.SHOW_INSTRUCTION));
      } else {
        this.reRenderCountingAreasInEditor(this.props.countingAreas);
      }

      this.initListeners();
    }

    document.addEventListener('keydown', this.escFunction, false);
  }

  componentWillUnmount() {
    document.removeEventListener('keydown', this.escFunction, false);
  }

  reRenderCountingAreasInEditor(countingAreas) {
    // Clear canvas
    this.editorCanvas.clear();
    this.resetDrawing();
    const {
      width,
      height
    } = this.elCanvas.getBoundingClientRect();
    countingAreas.map((area, id) => {
      if (area.get('location') !== undefined) {
        const data = area.get('location').toJS();
        const color = area.get('color');
        const reScalingFactorX = width / data.refResolution.w;
        const reScalingFactorY = height / data.refResolution.h; // Rescale points

        const points = data.points.map(point => ({
          x: point.x * reScalingFactorX,
          y: point.y * reScalingFactorY
        }));

        for (let index = 0; index < points.length; index++) {
          const point = points[index]; // Draw circle

          this.editorCanvas.add(new fabric.Circle({
            radius: 5,
            fill: (0,colors/* getCounterColor */.SD)(color),
            top: point.y,
            left: point.x,
            originX: 'center',
            originY: 'center'
          })); // Draw line connecting to previous point

          if (index > 0) {
            this.editorCanvas.add(new fabric.Line([points[index - 1].x, points[index - 1].y, points[index].x, points[index].y], {
              strokeWidth: 5,
              fill: (0,colors/* getCounterColor */.SD)(color),
              stroke: (0,colors/* getCounterColor */.SD)(color),
              originX: 'center',
              originY: 'center'
            }));
          }
        } // Draw polygon if length > 2


        if (points.length > 2) {
          this.editorCanvas.add(new fabric.Polygon(points, {
            strokeWidth: 5,
            fill: (0,colors/* getCounterColor */.SD)(color),
            stroke: (0,colors/* getCounterColor */.SD)(color),
            opacity: 0.3,
            selectable: false,
            hasBorders: false,
            hasControls: false
          }));
        }
      }
    });
  }

  render() {
    return CounterAreasEditor_jsx("div", {
      className: style_default().dynamic([["1873663113", [this.props.mode === CounterStateManagement/* EDITOR_MODE.ASKNAME */.Q4.ASKNAME || this.props.mode === CounterStateManagement/* EDITOR_MODE.SHOW_INSTRUCTION */.Q4.SHOW_INSTRUCTION || this.props.mode === CounterStateManagement/* EDITOR_MODE.DELETE */.Q4.DELETE ? '7' : '2']]]) + " " + "counting-areas-editor"
    }, this.props.mode === CounterStateManagement/* EDITOR_MODE.SHOW_INSTRUCTION */.Q4.SHOW_INSTRUCTION && CounterAreasEditor_jsx(shared_InstructionsModal, {
      close: () => this.props.dispatch((0,CounterStateManagement/* setMode */.PM)(CounterStateManagement/* EDITOR_MODE.EDIT_LINE */.Q4.EDIT_LINE))
    }), this.props.mode === CounterStateManagement/* EDITOR_MODE.ASKNAME */.Q4.ASKNAME && CounterAreasEditor_jsx(shared_AskNameModal, {
      save: name => {
        this.props.dispatch((0,CounterStateManagement/* saveCountingAreaName */.WM)(this.props.selectedCountingArea, name));
        this.props.dispatch((0,CounterStateManagement/* setMode */.PM)(this.props.lastEditingMode));
      },
      cancel: name => {
        this.props.dispatch((0,CounterStateManagement/* deleteCountingArea */.XO)(this.props.selectedCountingArea));
        this.props.dispatch((0,CounterStateManagement/* setMode */.PM)(this.props.lastEditingMode));
      }
    }), this.props.mode === CounterStateManagement/* EDITOR_MODE.DELETE */.Q4.DELETE && CounterAreasEditor_jsx(shared_DeleteModal, {
      countingAreasWithCenters: this.props.countingAreasWithCenters,
      delete: id => this.props.dispatch((0,CounterStateManagement/* deleteCountingArea */.XO)(id)),
      cancel: () => this.props.dispatch((0,CounterStateManagement/* setMode */.PM)(this.props.lastEditingMode))
    }), this.props.countingAreasWithCenters.entrySeq().map(([id, countingArea]) => CounterAreasEditor_jsx((external_react_default()).Fragment, {
      key: id
    }, countingArea.get('type') !== 'polygon' && countingArea.get('computed') && countingArea.get('location') && CounterAreasEditor_jsx(shared_SingleCounterDirection, {
      key: id,
      area: countingArea.toJS(),
      toggleDirection: () => this.props.dispatch((0,CounterStateManagement/* toggleCountingAreaType */.e3)(id, countingArea.get('type')))
    }))), CounterAreasEditor_jsx(shared_MenuCountingAreasEditor, null), CounterAreasEditor_jsx("canvas", {
      ref: el => this.elCanvas = el,
      width: this.props.canvasResolution.get('w'),
      height: this.props.canvasResolution.get('h'),
      className: style_default().dynamic([["1873663113", [this.props.mode === CounterStateManagement/* EDITOR_MODE.ASKNAME */.Q4.ASKNAME || this.props.mode === CounterStateManagement/* EDITOR_MODE.SHOW_INSTRUCTION */.Q4.SHOW_INSTRUCTION || this.props.mode === CounterStateManagement/* EDITOR_MODE.DELETE */.Q4.DELETE ? '7' : '2']]]) + " " + "editor-canvas"
    }), CounterAreasEditor_jsx((style_default()), {
      id: "1873663113",
      dynamic: [this.props.mode === CounterStateManagement/* EDITOR_MODE.ASKNAME */.Q4.ASKNAME || this.props.mode === CounterStateManagement/* EDITOR_MODE.SHOW_INSTRUCTION */.Q4.SHOW_INSTRUCTION || this.props.mode === CounterStateManagement/* EDITOR_MODE.DELETE */.Q4.DELETE ? '7' : '2']
    }, [`.counting-areas-editor.__jsx-style-dynamic-selector,.editor-canvas.__jsx-style-dynamic-selector{position:absolute;top:0;right:0;left:0;bottom:0;z-index:${this.props.mode === CounterStateManagement/* EDITOR_MODE.ASKNAME */.Q4.ASKNAME || this.props.mode === CounterStateManagement/* EDITOR_MODE.SHOW_INSTRUCTION */.Q4.SHOW_INSTRUCTION || this.props.mode === CounterStateManagement/* EDITOR_MODE.DELETE */.Q4.DELETE ? '7' : '2'};}`]));
  }

}

/* harmony default export */ var shared_CounterAreasEditor = ((0,external_react_redux_.connect)(state => {
  const countingAreasWithCenters = (0,CounterStateManagement/* computeCountingAreasCenters */.b3)(state.counter.get('countingAreas'), state.viewport.get('canvasResolution'));
  return {
    countingAreas: state.counter.get('countingAreas'),
    // Need to inject this as is it for componentDidUpdate comparison
    countingAreasWithCenters,
    selectedCountingArea: state.counter.get('selectedCountingArea'),
    canvasResolution: state.viewport.get('canvasResolution'),
    mode: state.counter.get('mode'),
    lastEditingMode: state.counter.get('lastEditingMode')
  };
})(CounterAreasEditor));
// EXTERNAL MODULE: external "immutable"
var external_immutable_ = __webpack_require__(856);
;// CONCATENATED MODULE: ./components/shared/OpenMoji.js
var OpenMoji_jsx = (external_react_default()).createElement;



class OpenMoji extends external_react_.Component {
  render() {
    return OpenMoji_jsx((external_react_inlinesvg_default()), {
      className: "svg-openmoji w-10",
      cacheRequests: true,
      src: `/static/icons/openmojis/${this.props.hexcode}.svg`,
      "aria-label": this.props.label
    });
  }

}

/* harmony default export */ var shared_OpenMoji = (OpenMoji);
;// CONCATENATED MODULE: ./components/shared/SingleCounterArea.js

var SingleCounterArea_jsx = (external_react_default()).createElement;







class SingleCounterArea extends external_react_.Component {
  constructor(props) {
    super(props);
    this.state = {
      showPopover: false
    };
    this.togglePopover = this.togglePopover.bind(this);
    this.DISPLAY_CLASSES = (0,colors/* getDisplayClasses */.b7)();
  }

  componentDidMount() {}

  componentWillUnmount() {}

  togglePopover() {
    if (this.state.showPopover) {
      this.setState({
        showPopover: false
      });
    } else {
      this.setState({
        showPopover: true
      });
    }
  }

  render() {
    // TODO POSITION BOTTOM IN CASE NO SPACE ON TOP
    return SingleCounterArea_jsx((external_react_default()).Fragment, null, this.props.counterData && this.state.showPopover && SingleCounterArea_jsx("div", {
      style: {
        top: this.props.area.location.center.y - constants/* POPOVER_HEIGHT */.Ys - constants/* CIRCLE_RADIUS */.S7 / 2 - constants/* POPOVER_ARROW_SIZE */.fQ - 5,
        left: this.props.area.location.center.x - constants/* POPOVER_WIDTH */.qr / 2
      },
      className: style_default().dynamic([["2957555364", [constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7 + constants/* ICON_DIRECTION_SIZE */.a6 + 5, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* ICON_DIRECTION_SIZE */.a6, constants/* ICON_DIRECTION_SIZE */.a6, this.props.area.computed.lineBearings[0] + 90, constants/* CIRCLE_RADIUS */.S7 - constants/* ICON_DIRECTION_SIZE */.a6 - 3, constants/* POPOVER_WIDTH */.qr, constants/* POPOVER_HEIGHT */.Ys, constants/* POPOVER_ARROW_SIZE */.fQ, constants/* POPOVER_ARROW_SIZE */.fQ]]]) + " " + "area-popover bg-default text-inverse"
    }, SingleCounterArea_jsx("h4", {
      className: style_default().dynamic([["2957555364", [constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7 + constants/* ICON_DIRECTION_SIZE */.a6 + 5, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* ICON_DIRECTION_SIZE */.a6, constants/* ICON_DIRECTION_SIZE */.a6, this.props.area.computed.lineBearings[0] + 90, constants/* CIRCLE_RADIUS */.S7 - constants/* ICON_DIRECTION_SIZE */.a6 - 3, constants/* POPOVER_WIDTH */.qr, constants/* POPOVER_HEIGHT */.Ys, constants/* POPOVER_ARROW_SIZE */.fQ, constants/* POPOVER_ARROW_SIZE */.fQ]]]) + " " + "area-popover-title border-b border-default-soft text-center py-2"
    }, this.props.area.name), SingleCounterArea_jsx("div", {
      className: style_default().dynamic([["2957555364", [constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7 + constants/* ICON_DIRECTION_SIZE */.a6 + 5, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* ICON_DIRECTION_SIZE */.a6, constants/* ICON_DIRECTION_SIZE */.a6, this.props.area.computed.lineBearings[0] + 90, constants/* CIRCLE_RADIUS */.S7 - constants/* ICON_DIRECTION_SIZE */.a6 - 3, constants/* POPOVER_WIDTH */.qr, constants/* POPOVER_HEIGHT */.Ys, constants/* POPOVER_ARROW_SIZE */.fQ, constants/* POPOVER_ARROW_SIZE */.fQ]]]) + " " + "area-popover-content"
    }, this.DISPLAY_CLASSES.slice(0, Math.min(this.DISPLAY_CLASSES.length, 6)).map(counterClass => SingleCounterArea_jsx("div", {
      key: counterClass.class,
      className: style_default().dynamic([["2957555364", [constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7 + constants/* ICON_DIRECTION_SIZE */.a6 + 5, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* ICON_DIRECTION_SIZE */.a6, constants/* ICON_DIRECTION_SIZE */.a6, this.props.area.computed.lineBearings[0] + 90, constants/* CIRCLE_RADIUS */.S7 - constants/* ICON_DIRECTION_SIZE */.a6 - 3, constants/* POPOVER_WIDTH */.qr, constants/* POPOVER_HEIGHT */.Ys, constants/* POPOVER_ARROW_SIZE */.fQ, constants/* POPOVER_ARROW_SIZE */.fQ]]]) + " " + "area-popover-item mb-1"
    }, SingleCounterArea_jsx("div", {
      className: style_default().dynamic([["2957555364", [constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7 + constants/* ICON_DIRECTION_SIZE */.a6 + 5, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* ICON_DIRECTION_SIZE */.a6, constants/* ICON_DIRECTION_SIZE */.a6, this.props.area.computed.lineBearings[0] + 90, constants/* CIRCLE_RADIUS */.S7 - constants/* ICON_DIRECTION_SIZE */.a6 - 3, constants/* POPOVER_WIDTH */.qr, constants/* POPOVER_HEIGHT */.Ys, constants/* POPOVER_ARROW_SIZE */.fQ, constants/* POPOVER_ARROW_SIZE */.fQ]]]) + " " + "area-popover-count mr-2"
    }, this.props.counterData.get(counterClass.class) || 0), SingleCounterArea_jsx(shared_OpenMoji, {
      hexcode: counterClass.hexcode,
      class: counterClass.class
    }))))), SingleCounterArea_jsx("div", {
      onClick: this.togglePopover,
      style: {
        top: this.props.area.location.center.y - constants/* CIRCLE_RADIUS */.S7 / 2,
        left: this.props.area.location.center.x - constants/* CIRCLE_RADIUS */.S7 / 2 - (constants/* ICON_DIRECTION_SIZE */.a6 + 5) / 2,
        backgroundColor: (0,colors/* getCounterColor */.SD)(this.props.area.color)
      },
      className: style_default().dynamic([["2957555364", [constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7 + constants/* ICON_DIRECTION_SIZE */.a6 + 5, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* ICON_DIRECTION_SIZE */.a6, constants/* ICON_DIRECTION_SIZE */.a6, this.props.area.computed.lineBearings[0] + 90, constants/* CIRCLE_RADIUS */.S7 - constants/* ICON_DIRECTION_SIZE */.a6 - 3, constants/* POPOVER_WIDTH */.qr, constants/* POPOVER_HEIGHT */.Ys, constants/* POPOVER_ARROW_SIZE */.fQ, constants/* POPOVER_ARROW_SIZE */.fQ]]]) + " " + "circle"
    }, this.props.area.type === constants/* COUNTING_AREA_TYPE.BIDIRECTIONAL */.Og.BIDIRECTIONAL && SingleCounterArea_jsx("img", {
      src: "/static/icons/ui/arrow-double.svg",
      className: style_default().dynamic([["2957555364", [constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7 + constants/* ICON_DIRECTION_SIZE */.a6 + 5, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* ICON_DIRECTION_SIZE */.a6, constants/* ICON_DIRECTION_SIZE */.a6, this.props.area.computed.lineBearings[0] + 90, constants/* CIRCLE_RADIUS */.S7 - constants/* ICON_DIRECTION_SIZE */.a6 - 3, constants/* POPOVER_WIDTH */.qr, constants/* POPOVER_HEIGHT */.Ys, constants/* POPOVER_ARROW_SIZE */.fQ, constants/* POPOVER_ARROW_SIZE */.fQ]]]) + " " + "icon-direction"
    }), this.props.area.type === constants/* COUNTING_AREA_TYPE.LEFTRIGHT_TOPBOTTOM */.Og.LEFTRIGHT_TOPBOTTOM && SingleCounterArea_jsx("img", {
      src: "/static/icons/ui/arrow-up.svg",
      className: style_default().dynamic([["2957555364", [constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7 + constants/* ICON_DIRECTION_SIZE */.a6 + 5, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* ICON_DIRECTION_SIZE */.a6, constants/* ICON_DIRECTION_SIZE */.a6, this.props.area.computed.lineBearings[0] + 90, constants/* CIRCLE_RADIUS */.S7 - constants/* ICON_DIRECTION_SIZE */.a6 - 3, constants/* POPOVER_WIDTH */.qr, constants/* POPOVER_HEIGHT */.Ys, constants/* POPOVER_ARROW_SIZE */.fQ, constants/* POPOVER_ARROW_SIZE */.fQ]]]) + " " + "icon-direction"
    }), this.props.area.type === constants/* COUNTING_AREA_TYPE.RIGHTLEFT_BOTTOMTOP */.Og.RIGHTLEFT_BOTTOMTOP && SingleCounterArea_jsx("img", {
      src: "/static/icons/ui/arrow-down.svg",
      className: style_default().dynamic([["2957555364", [constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7 + constants/* ICON_DIRECTION_SIZE */.a6 + 5, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* ICON_DIRECTION_SIZE */.a6, constants/* ICON_DIRECTION_SIZE */.a6, this.props.area.computed.lineBearings[0] + 90, constants/* CIRCLE_RADIUS */.S7 - constants/* ICON_DIRECTION_SIZE */.a6 - 3, constants/* POPOVER_WIDTH */.qr, constants/* POPOVER_HEIGHT */.Ys, constants/* POPOVER_ARROW_SIZE */.fQ, constants/* POPOVER_ARROW_SIZE */.fQ]]]) + " " + "icon-direction"
    }), SingleCounterArea_jsx("div", {
      className: style_default().dynamic([["2957555364", [constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7 + constants/* ICON_DIRECTION_SIZE */.a6 + 5, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* ICON_DIRECTION_SIZE */.a6, constants/* ICON_DIRECTION_SIZE */.a6, this.props.area.computed.lineBearings[0] + 90, constants/* CIRCLE_RADIUS */.S7 - constants/* ICON_DIRECTION_SIZE */.a6 - 3, constants/* POPOVER_WIDTH */.qr, constants/* POPOVER_HEIGHT */.Ys, constants/* POPOVER_ARROW_SIZE */.fQ, constants/* POPOVER_ARROW_SIZE */.fQ]]]) + " " + "counter-value"
    }, this.props.counterData && this.props.counterData.get('_total') || 0)), SingleCounterArea_jsx((style_default()), {
      id: "2957555364",
      dynamic: [constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7 + constants/* ICON_DIRECTION_SIZE */.a6 + 5, constants/* CIRCLE_RADIUS */.S7, constants/* CIRCLE_RADIUS */.S7, constants/* ICON_DIRECTION_SIZE */.a6, constants/* ICON_DIRECTION_SIZE */.a6, this.props.area.computed.lineBearings[0] + 90, constants/* CIRCLE_RADIUS */.S7 - constants/* ICON_DIRECTION_SIZE */.a6 - 3, constants/* POPOVER_WIDTH */.qr, constants/* POPOVER_HEIGHT */.Ys, constants/* POPOVER_ARROW_SIZE */.fQ, constants/* POPOVER_ARROW_SIZE */.fQ]
    }, [`.circle.__jsx-style-dynamic-selector{position:absolute;border-radius:${constants/* CIRCLE_RADIUS */.S7}px;z-index:2;min-width:${constants/* CIRCLE_RADIUS */.S7 + constants/* ICON_DIRECTION_SIZE */.a6 + 5}px;height:${constants/* CIRCLE_RADIUS */.S7}px;line-height:${constants/* CIRCLE_RADIUS */.S7}px;font-size:16px;font-weight:bold;padding-left:5px;padding-right:5px;color:black;text-align:center;cursor:pointer;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;}`, `.icon-direction.__jsx-style-dynamic-selector{width:${constants/* ICON_DIRECTION_SIZE */.a6}px;height:${constants/* ICON_DIRECTION_SIZE */.a6}px;-webkit-transform:rotate(${this.props.area.computed.lineBearings[0] + 90}deg);-ms-transform:rotate(${this.props.area.computed.lineBearings[0] + 90}deg);transform:rotate(${this.props.area.computed.lineBearings[0] + 90}deg);}`, `.counter-value.__jsx-style-dynamic-selector{min-width:${constants/* CIRCLE_RADIUS */.S7 - constants/* ICON_DIRECTION_SIZE */.a6 - 3}px;margin-left:3px;}`, `.area-popover.__jsx-style-dynamic-selector{position:absolute;z-index:3;width:${constants/* POPOVER_WIDTH */.qr}px;height:${constants/* POPOVER_HEIGHT */.Ys}px;}`, ".area-popover-title.__jsx-style-dynamic-selector{width:100%;}", ".area-popover-content.__jsx-style-dynamic-selector{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;padding:5px;overflow:hidden;}", ".area-popover-item.__jsx-style-dynamic-selector{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;width:75px;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;}", `.area-popover.__jsx-style-dynamic-selector:after{top:100%;left:50%;border:solid transparent;content:" ";height:0;width:0;position:absolute;pointer-events:none;border-color:rgba(0,0,0,0);border-top-color:var(--color-default);border-width:${constants/* POPOVER_ARROW_SIZE */.fQ}px;margin-left:-${constants/* POPOVER_ARROW_SIZE */.fQ}px;}`]));
  }

}

/* harmony default export */ var shared_SingleCounterArea = (SingleCounterArea);
;// CONCATENATED MODULE: ./components/shared/CounterAreasVisualizer.js
var CounterAreasVisualizer_jsx = (external_react_default()).createElement;





/*
  Here we suppose that the dom parent element is positionned the same as the canvas
*/

class CounterAreasVisualizer extends external_react_.Component {
  constructor(props) {
    super(props);
  }

  componentDidMount() {}

  componentWillUnmount() {}

  render() {
    const countingAreasIds = Object.keys(this.props.countingAreas);
    return CounterAreasVisualizer_jsx((external_react_default()).Fragment, null, Object.values(this.props.countingAreas).map((area, index) =>
    /* TODO Create a CounterArea component */
    CounterAreasVisualizer_jsx(shared_SingleCounterArea, {
      key: countingAreasIds[index],
      area: area,
      counterData: this.props.counterSummary.get(countingAreasIds[index]) || (0,external_immutable_.Map)()
    })));
  }

}

/* harmony default export */ var shared_CounterAreasVisualizer = ((0,external_react_redux_.connect)(state => {
  // Enrich countingAreas with more data
  // Maybe persist directly this data so we can reuse here and in the canvas engine
  const countingAreasWithCenters = (0,CounterStateManagement/* computeCountingAreasCenters */.b3)(state.counter.get('countingAreas'), state.viewport.get('canvasResolution'));
  return {
    countingAreas: countingAreasWithCenters.toJS(),
    counterSummary: state.counter.get('counterSummary')
  };
})(CounterAreasVisualizer));
;// CONCATENATED MODULE: ./components/main/CounterView.js

var CounterView_jsx = (external_react_default()).createElement;









class CounterView extends (external_react_default()).PureComponent {
  componentDidMount() {
    this.props.dispatch((0,CounterStateManagement/* restoreCountingAreas */.p6)());
  }

  render() {
    return CounterView_jsx("div", {
      className: "jsx-1522581866"
    }, !this.props.isRecording && CounterView_jsx((external_react_default()).Fragment, null, CounterView_jsx(shared_CounterAreasEditor, null), CounterView_jsx(canvas_CanvasEngine, {
      mode: constants/* CANVAS_RENDERING_MODE.COUNTERVIEW */.qI.COUNTERVIEW
    })), this.props.isRecording && this.props.isAtLeastOneCountingAreasDefined && CounterView_jsx((external_react_default()).Fragment, null, CounterView_jsx(shared_CounterAreasVisualizer, null), CounterView_jsx(canvas_CanvasEngine, {
      mode: constants/* CANVAS_RENDERING_MODE.COUNTERVIEW_RECORDING */.qI.COUNTERVIEW_RECORDING
    })), this.props.isRecording && !this.props.isAtLeastOneCountingAreasDefined && CounterView_jsx((external_react_default()).Fragment, null, CounterView_jsx("div", {
      className: "jsx-1522581866" + " " + "modal"
    }, "Not counting lines defined , Blablabalbla Define counting lines before start recording")), CounterView_jsx(shared_BtnRecording, null), CounterView_jsx((style_default()), {
      id: "1522581866"
    }, [".modal.jsx-1522581866{position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);transform:translate(-50%,-50%);}"]));
  }

}

/* harmony default export */ var main_CounterView = ((0,external_react_redux_.connect)(state => {
  return {
    isAtLeastOneCountingAreasDefined: state.counter.get('countingAreas').size > 0,
    isRecording: state.app.getIn(['recordingStatus', 'isRecording'])
  };
})(CounterView));
;// CONCATENATED MODULE: ./components/main/PathView.js
var PathView_jsx = (external_react_default()).createElement;






class PathView extends external_react_.PureComponent {
  constructor(props) {
    super(props);
  }

  componentDidMount() {}

  componentWillUnmount() {}

  registerHiddenCanvas(el) {
    this.hiddenCanvas = el;
  }

  downloadFrame() {
    this.downloadBtn.setAttribute('href', this.hiddenCanvas.toDataURL());
    this.downloadBtn.setAttribute('download', 'pathfinder.png');
  }

  clearCanvas() {
    // Clear both hidden and visible canvas
    this.clearVisibleCanvas();
    this.clearHiddenCanvas();
  }

  render() {
    return PathView_jsx((external_react_default()).Fragment, null, PathView_jsx(canvas_CanvasEngine, {
      mode: constants/* CANVAS_RENDERING_MODE.PATHVIEW */.qI.PATHVIEW,
      hidden: this.props.hidden,
      registerClearCanvas: clearCanvas => this.clearVisibleCanvas = clearCanvas
    }), PathView_jsx(canvas_CanvasEngine, {
      fixedResolution: {
        w: 1280,
        h: 720
      },
      mode: constants/* CANVAS_RENDERING_MODE.PATHVIEW */.qI.PATHVIEW,
      onDomReady: el => this.hiddenCanvas = el,
      registerClearCanvas: clearCanvas => this.clearHiddenCanvas = clearCanvas,
      hidden: true
    }), !this.props.hidden && PathView_jsx(shared_BtnRecording, null), !this.props.hidden && PathView_jsx("div", {
      className: "flex fixed bottom-0 left-0 mb-2 ml-2 z-10"
    }, PathView_jsx("button", {
      className: "btn btn-default p-0 rounded-l shadow",
      onClick: () => this.clearCanvas()
    }, PathView_jsx((external_react_inlinesvg_default()), {
      className: "w-10 h-10 svg-icon flex items-center",
      cacheRequests: true,
      src: "/static/icons/ui/delete.svg",
      "aria-label": "icon delete"
    })), PathView_jsx("a", {
      id: "downloadFrame",
      ref: el => {
        this.downloadBtn = el;
      },
      className: "btn btn-default p-0 rounded-r shadow cursor-pointer",
      onClick: () => this.downloadFrame()
    }, PathView_jsx((external_react_inlinesvg_default()), {
      className: "w-10 h-10 svg-icon flex items-center",
      cacheRequests: true,
      src: "/static/icons/ui/download.svg",
      "aria-label": "icon take screenshot"
    }))));
  }

}

/* harmony default export */ var main_PathView = (PathView);
;// CONCATENATED MODULE: ./components/shared/Console.js
var Console_jsx = (external_react_default()).createElement;


let LazyLog;
let ScrollFollow;

class Console extends external_react_.Component {
  constructor(props) {
    super(props);
    this.state = {
      onClient: false
    };
  }

  componentDidMount() {
    this.setState({
      onClient: true
    });
    LazyLog = __webpack_require__(469).LazyLog;
    ScrollFollow = __webpack_require__(469).ScrollFollow;
  }

  render() {
    return Console_jsx((external_react_default()).Fragment, null, this.state.onClient && Console_jsx(ScrollFollow, {
      startFollowing: true,
      render: ({
        follow,
        onScroll
      }) => Console_jsx(LazyLog, {
        url: "/console",
        stream: true,
        follow: follow,
        onScroll: onScroll,
        overscanRowCount: 300
      })
    }));
  }

}

/* harmony default export */ var shared_Console = (Console);
;// CONCATENATED MODULE: ./components/main/ConsoleView.js

var ConsoleView_jsx = (external_react_default()).createElement;




class ConsoleView extends external_react_.Component {
  constructor(props) {
    super(props);
  }

  render() {
    return ConsoleView_jsx("div", {
      className: "jsx-3129463923" + " " + "console-view bg-default-soft"
    }, ConsoleView_jsx("div", {
      className: "jsx-3129463923" + " " + "flex justify-end pl-5 pt-5 pr-5"
    }, ConsoleView_jsx("a", {
      target: "_blank",
      href: "/console",
      className: "jsx-3129463923" + " " + "btn btn-light rounded"
    }, "Download logs")), ConsoleView_jsx("div", {
      className: "jsx-3129463923" + " " + "w-full h-full p-5"
    }, ConsoleView_jsx(shared_Console, null)), ConsoleView_jsx((style_default()), {
      id: "3129463923"
    }, [".console-view.jsx-3129463923{width:100%;height:100%;position:fixed;will-change:transform;overflow:scroll;padding-top:3.1rem;top:0;left:0;bottom:0;right:0;}"]));
  }

}

/* harmony default export */ var main_ConsoleView = (ConsoleView);
;// CONCATENATED MODULE: external "dayjs"
var external_dayjs_namespaceObject = require("dayjs");;
var external_dayjs_default = /*#__PURE__*/__webpack_require__.n(external_dayjs_namespaceObject);
// EXTERNAL MODULE: ./statemanagement/app/HistoryStateManagement.js
var HistoryStateManagement = __webpack_require__(861);
;// CONCATENATED MODULE: ./components/shared/RecordingDeleteConfirmationModal.js

var RecordingDeleteConfirmationModal_jsx = (external_react_default()).createElement;


class RecordingDeleteConfirmationModal extends external_react_.Component {
  render() {
    return RecordingDeleteConfirmationModal_jsx("div", {
      onClick: () => this.props.onCancel(),
      className: "jsx-3225436558" + " " + "overlay"
    }, RecordingDeleteConfirmationModal_jsx("div", {
      className: "jsx-3225436558" + " " + "modal rounded p-10 shadow text-inverse bg-default border-inverse"
    }, RecordingDeleteConfirmationModal_jsx("h3", {
      className: "jsx-3225436558" + " " + "text-center text-xl font-bold"
    }, "Delete Recording"), RecordingDeleteConfirmationModal_jsx("div", {
      className: "jsx-3225436558" + " " + "text-center mt-2"
    }, "Are you sure you want to delete this recording ?"), RecordingDeleteConfirmationModal_jsx("div", {
      className: "jsx-3225436558" + " " + "flex items-center"
    }, RecordingDeleteConfirmationModal_jsx("button", {
      onClick: () => this.props.onCancel(),
      className: "jsx-3225436558" + " " + "btn btn-secondary btn-rounded min-w-100 mt-5 pl-10 pr-10"
    }, "Cancel"), RecordingDeleteConfirmationModal_jsx("button", {
      onClick: () => this.props.onConfirm(),
      className: "jsx-3225436558" + " " + "btn btn-primary btn-rounded min-w-100 mt-5 pl-10 pr-10"
    }, "Confirm"))), RecordingDeleteConfirmationModal_jsx((style_default()), {
      id: "3225436558"
    }, [".overlay.jsx-3225436558{position:fixed;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,0.8);z-index:8;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;}", ".modal.jsx-3225436558{border:1px solid black;width:300px;height:auto;padding:1rem;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;}"]));
  }

}

/* harmony default export */ var shared_RecordingDeleteConfirmationModal = (RecordingDeleteConfirmationModal);
;// CONCATENATED MODULE: ./components/dataview/Recording.js

var Recording_jsx = (external_react_default()).createElement;










class Recording extends external_react_.PureComponent {
  constructor(props) {
    super(props);
    this.DISPLAY_CLASSES = (0,colors/* getDisplayClasses */.b7)();
    this.state = {
      showDeleteConfirmationModal: false
    };
  }

  componentDidMount() {}

  componentWillUnmount() {}

  renderDateEnd(dateEnd, active = false) {
    if (!active) {
      return external_dayjs_default()(dateEnd).format('hh:mm a');
    }

    return Recording_jsx("span", {
      className: "font-bold",
      style: {
        color: '#FF0000'
      }
    }, "Ongoing");
  }

  render() {
    return Recording_jsx("div", {
      className: "jsx-2750094696" + " " + "flex flex-initial flex-col recording pl-2 mb-10"
    }, Recording_jsx("div", {
      className: "jsx-2750094696" + " " + "text-inverse flex flex-initial items-center pl-6"
    }, Recording_jsx("div", {
      className: "jsx-2750094696"
    }, external_dayjs_default()(this.props.dateStart).format('MMM DD, YYYY')), Recording_jsx("div", {
      className: "jsx-2750094696" + " " + "ml-10"
    }, external_dayjs_default()(this.props.dateStart).format('hh:mm a'), ' ', "-", this.renderDateEnd(this.props.dateEnd, this.props.active)), this.props.filename && Recording_jsx("div", {
      className: "jsx-2750094696" + " " + "ml-10"
    }, this.props.filename), !this.props.active && Recording_jsx("button", {
      onClick: () => this.setState({
        showDeleteConfirmationModal: true
      }),
      className: "jsx-2750094696" + " " + "btn btn-default p-0 ml-2 shadow rounded"
    }, Recording_jsx((external_react_inlinesvg_default()), {
      className: "w-6 h-6 svg-icon flex items-center",
      cacheRequests: true,
      src: "/static/icons/ui/delete.svg",
      "aria-label": "icon close"
    }))), this.state.showDeleteConfirmationModal && Recording_jsx(shared_RecordingDeleteConfirmationModal, {
      onCancel: () => this.setState({
        showDeleteConfirmationModal: false
      }),
      onConfirm: () => this.props.dispatch((0,HistoryStateManagement/* deleteRecording */.Mj)(this.props.id))
    }), Recording_jsx("div", {
      className: "jsx-2750094696" + " " + "flex flex-initial flex-wrap pb-2 pl-1 m-2"
    }, this.props.countingAreas.size > 0 && Recording_jsx("div", {
      className: "jsx-2750094696" + " " + "flex flex-initial flex-col rounded bg-white text-black shadow m-2 p-4"
    }, Recording_jsx("div", {
      className: "jsx-2750094696" + " " + "flex items-end justify-between"
    }, Recording_jsx("h3", {
      className: "jsx-2750094696" + " " + "mr-3 text-xl font-bold"
    }, "Counter"), Recording_jsx("div", {
      className: "jsx-2750094696"
    }, Recording_jsx("div", {
      className: "jsx-2750094696" + " " + "font-medium mr-2 inline-block"
    }, "Download:"), Recording_jsx("a", {
      href: `/recording/${this.props.id}/counter`,
      target: "_blank",
      download: true,
      className: "jsx-2750094696" + " " + "btn-text mr-2"
    }, "JSON"), Recording_jsx("a", {
      href: `/recording/${this.props.id}/counter/csv`,
      target: "_blank",
      download: true,
      className: "jsx-2750094696" + " " + "btn-text"
    }, "CSV"))), Recording_jsx("div", {
      className: "jsx-2750094696" + " " + "mt-4 flex flex-wrap"
    }, this.props.countingAreas && this.props.countingAreas.entrySeq().map(([countingAreaId, countingAreaData], index) => Recording_jsx("div", {
      key: countingAreaId,
      className: "jsx-2750094696" + " " + "flex flex-col counter-area bg-gray-200 m-2 rounded p-4"
    }, Recording_jsx("div", {
      className: "jsx-2750094696" + " " + "flex items-center"
    }, Recording_jsx("h4", {
      className: "jsx-2750094696" + " " + "font-medium"
    }, countingAreaData.get('name')), Recording_jsx("div", {
      style: {
        backgroundColor: (0,colors/* getCounterColor */.SD)(countingAreaData.get('color'))
      },
      className: "jsx-2750094696" + " " + "w-4 h-4 ml-2 rounded-full"
    }), countingAreaData.get('type') === constants/* COUNTING_AREA_TYPE.BIDIRECTIONAL */.Og.BIDIRECTIONAL && Recording_jsx("img", {
      style: {
        transform: `rotate(${countingAreaData.getIn(['computed', 'lineBearings']).first() + 90}deg)`
      },
      src: "/static/icons/ui/arrow-double.svg",
      className: "jsx-2750094696" + " " + "icon-direction"
    }), countingAreaData.get('type') === constants/* COUNTING_AREA_TYPE.LEFTRIGHT_TOPBOTTOM */.Og.LEFTRIGHT_TOPBOTTOM && Recording_jsx("img", {
      style: {
        transform: `rotate(${countingAreaData.getIn(['computed', 'lineBearings']).first() + 90}deg)`
      },
      src: "/static/icons/ui/arrow-up.svg",
      className: "jsx-2750094696" + " " + "icon-direction"
    }), countingAreaData.get('type') === constants/* COUNTING_AREA_TYPE.RIGHTLEFT_BOTTOMTOP */.Og.RIGHTLEFT_BOTTOMTOP && Recording_jsx("img", {
      style: {
        transform: `rotate(${countingAreaData.getIn(['computed', 'lineBearings']).first() + 90}deg)`
      },
      src: "/static/icons/ui/arrow-down.svg",
      className: "jsx-2750094696" + " " + "icon-direction"
    })), Recording_jsx("div", {
      className: "jsx-2750094696" + " " + "flex flex-initial flex-wrap mt-5 w-64"
    }, this.DISPLAY_CLASSES.slice(0, Math.min(this.DISPLAY_CLASSES.length, 6)).map(counterClass => Recording_jsx("div", {
      key: counterClass.class,
      className: "jsx-2750094696" + " " + "flex w-16 m-1 items-center justify-center"
    }, Recording_jsx("h4", {
      className: "jsx-2750094696" + " " + "mr-2"
    }, this.props.counterData && this.props.counterData.getIn([countingAreaId, counterClass.class]) || 0), Recording_jsx(shared_OpenMoji, {
      hexcode: counterClass.hexcode,
      class: counterClass.class
    })))))))), Recording_jsx("div", {
      className: "jsx-2750094696" + " " + "flex flex-initial flex-col rounded bg-white text-black shadow m-2 p-4"
    }, Recording_jsx("div", {
      className: "jsx-2750094696" + " " + "flex items-end justify-between"
    }, Recording_jsx("h3", {
      className: "jsx-2750094696" + " " + "mr-3 text-xl font-bold"
    }, "Tracker"), Recording_jsx("div", {
      className: "jsx-2750094696"
    }, Recording_jsx("div", {
      className: "jsx-2750094696" + " " + "font-medium mr-2 inline-block"
    }, "Download:"), Recording_jsx("a", {
      href: `/recording/${this.props.id}/tracker`,
      target: "_blank",
      download: true,
      className: "jsx-2750094696" + " " + "btn-text mr-2"
    }, "JSON"))), Recording_jsx("div", {
      className: "jsx-2750094696" + " " + "mt-6 rounded relative"
    }, Recording_jsx("div", {
      style: {
        bottom: 10,
        left: 10
      },
      className: "jsx-2750094696" + " " + "text-white absolute"
    }, Recording_jsx("h2", {
      className: "jsx-2750094696" + " " + "inline text-4xl font-bold"
    }, this.props.nbPaths), ' ', "objects tracked"), Recording_jsx("img", {
      src: "/static/placeholder/pathview.jpg",
      className: "jsx-2750094696"
    })))), Recording_jsx((style_default()), {
      id: "2750094696"
    }, [".counter-area.jsx-2750094696{max-width:350px;-webkit-flex:1;-ms-flex:1;flex:1;}", ".icon-direction.jsx-2750094696{margin-left:5px;width:20px;height:20px;}"]));
  }

}

/* harmony default export */ var dataview_Recording = ((0,external_react_redux_.connect)()(Recording));
;// CONCATENATED MODULE: ./components/main/DataView.js

var DataView_jsx = (external_react_default()).createElement;





/**
 * TODO SPLIT THIS INTO TWO COMPONENT TO HAVE ONE THAT RENDERS ALL THE TIME (current recording)
 * AND THE HISTORY THAT JUST RENDERS ONCE
 */

class DataView extends external_react_.PureComponent {
  constructor(props) {
    super(props);
  }

  componentDidMount() {
    this.props.dispatch((0,HistoryStateManagement/* fetchHistory */.eL)());
  }

  componentWillUnmount() {}

  render() {
    const pagination = this.props.recordingsCursor.toJS();
    const needPagination = pagination.total > pagination.limit;
    const nbPages = Math.ceil(pagination.total / pagination.limit);
    const pagesArray = new Array(nbPages).fill(0);
    const currentPage = Math.floor(pagination.offset / pagination.limit);
    return DataView_jsx("div", {
      className: "jsx-1239669181" + " " + "data-view bg-default-soft"
    }, this.props.recordingStatus.get('isRecording') && DataView_jsx(dataview_Recording, {
      id: this.props.recordingStatus.get('recordingId'),
      dateStart: this.props.recordingStatus.get('dateStarted'),
      counterData: this.props.counterSummary,
      countingAreas: this.props.countingAreas,
      nbPaths: this.props.totalItemsTracked,
      filename: this.props.recordingStatus.get('filename'),
      active: true
    }), this.props.recordingHistory.map(recording => DataView_jsx(dataview_Recording, {
      key: recording.get('id'),
      id: recording.get('id'),
      dateStart: recording.get('dateStart'),
      dateEnd: recording.get('dateEnd'),
      counterData: recording.get('counterSummary'),
      countingAreas: recording.get('areas'),
      filename: recording.get('filename'),
      nbPaths: recording.getIn(['trackerSummary', 'totalItemsTracked'])
    })), needPagination && DataView_jsx("div", {
      className: "jsx-1239669181" + " " + "flex justify-center mb-8"
    }, pagesArray.map((value, index) => DataView_jsx("button", {
      key: index,
      onClick: () => {
        this.props.dispatch((0,HistoryStateManagement/* fetchHistory */.eL)(index * pagination.limit, pagination.limit));
      },
      className: "jsx-1239669181" + " " + `btn btn-default ${index === currentPage ? 'btn-default--active' : ''}`
    }, index))), DataView_jsx((style_default()), {
      id: "1239669181"
    }, [".data-view.jsx-1239669181{width:100%;height:100%;overflow:scroll;padding-top:100px;}"]));
  }

}

/* harmony default export */ var main_DataView = ((0,external_react_redux_.connect)(state => ({
  recordingHistory: state.app.getIn(['recordingStatus', 'isRecording']) ? state.history.get('recordingHistory').skip(1) : state.history.get('recordingHistory'),
  recordingStatus: state.app.get('recordingStatus'),
  recordingsCursor: state.history.get('recordingsCursor'),
  counterSummary: state.counter.get('counterSummary'),
  countingAreas: state.counter.get('countingAreas'),
  totalItemsTracked: state.counter.getIn(['trackerSummary', 'totalItemsTracked'])
}))(DataView));
;// CONCATENATED MODULE: ./components/main/UIControls.js

var UIControls_jsx = (external_react_default()).createElement;







class UIControls extends external_react_.Component {
  constructor(props) {
    super(props);
  } // handleStartRecording() {
  //   this.props.dispatch(startCounting());
  // }


  render() {
    if (this.props.recordingStatus.isRecording) {
      const diff = Math.abs(new Date(this.props.recordingStatus.dateStarted) - new Date());
      var seconds = Math.floor(diff / 1000) % 60;
      var minutes = Math.floor(diff / 1000 / 60);
    }

    return UIControls_jsx((external_react_default()).Fragment, null, UIControls_jsx("div", {
      className: "jsx-2283970313" + " " + "nav"
    }, this.props.recordingStatus.isRecording && UIControls_jsx("div", {
      className: "jsx-2283970313" + " " + "recording-bar"
    }), UIControls_jsx("div", {
      className: "jsx-2283970313" + " " + "recording-status"
    }, this.props.recordingStatus.isRecording && UIControls_jsx("div", {
      className: "jsx-2283970313" + " " + "time text-lg mb-1 font-bold"
    }, minutes.toString().padStart(2, '0'), ":", seconds.toString().padStart(2, '0')), UIControls_jsx("div", {
      className: "jsx-2283970313" + " " + "fps"
    }, this.props.recordingStatus.currentFPS, ' ', "FPS")), UIControls_jsx("div", {
      className: "jsx-2283970313" + " " + "flex"
    }, UIControls_jsx("div", {
      className: "jsx-2283970313" + " " + "nav-left mt-2 ml-2 shadow flex"
    }, UIControls_jsx("button", {
      onClick: () => this.props.dispatch((0,AppStateManagement/* setMode */.PM)(constants/* MODE.LIVEVIEW */.IK.LIVEVIEW)),
      className: "jsx-2283970313" + " " + `btn btn-default rounded-l ${this.props.mode === constants/* MODE.LIVEVIEW */.IK.LIVEVIEW ? 'btn-default--active' : ''} ${!this.props.uiSettings.get('pathfinderEnabled') && !this.props.uiSettings.get('counterEnabled') ? 'rounded-r' : ''}`
    }, "Live view"), this.props.uiSettings.get('counterEnabled') && (!this.props.recordingStatus.isRecording || this.props.isAtLeastOneCountingAreasDefined) && UIControls_jsx("button", {
      onClick: () => this.props.dispatch((0,AppStateManagement/* setMode */.PM)(constants/* MODE.COUNTERVIEW */.IK.COUNTERVIEW)),
      className: "jsx-2283970313" + " " + `btn btn-default border-r border-l border-default-soft border-solid ${this.props.mode === constants/* MODE.COUNTERVIEW */.IK.COUNTERVIEW ? 'btn-default--active' : ''} ${this.props.uiSettings.get('pathfinderEnabled') ? '' : 'rounded-r'}`
    }, "Counter"), this.props.uiSettings.get('pathfinderEnabled') && UIControls_jsx("button", {
      onClick: () => this.props.dispatch((0,AppStateManagement/* setMode */.PM)(constants/* MODE.PATHVIEW */.IK.PATHVIEW)),
      className: "jsx-2283970313" + " " + `btn btn-default rounded-r ${this.props.mode === constants/* MODE.PATHVIEW */.IK.PATHVIEW ? 'btn-default--active' : ''}`
    }, "Pathfinder")), UIControls_jsx("div", {
      className: "jsx-2283970313" + " " + "nav-right mt-2 mr-2 flex"
    }, UIControls_jsx("button", {
      onClick: () => this.props.dispatch((0,AppStateManagement/* setMode */.PM)(constants/* MODE.DATAVIEW */.IK.DATAVIEW)),
      className: "jsx-2283970313" + " " + `btn btn-default shadow rounded-l ${this.props.mode === constants/* MODE.DATAVIEW */.IK.DATAVIEW ? 'btn-default--active' : ''}`
    }, "Data"), UIControls_jsx("button", {
      onClick: () => this.props.dispatch((0,AppStateManagement/* setMode */.PM)(constants/* MODE.CONSOLEVIEW */.IK.CONSOLEVIEW)),
      className: "jsx-2283970313" + " " + `btn btn-default shadow rounded-r border-l border-default-soft border-solid ${this.props.mode === constants/* MODE.CONSOLEVIEW */.IK.CONSOLEVIEW ? 'btn-default--active' : ''}`
    }, "Console"), UIControls_jsx("button", {
      onClick: () => this.props.dispatch((0,AppStateManagement/* showMenu */.AE)()),
      className: "jsx-2283970313" + " " + "btn btn-default shadow ml-2 py-0 px-3 rounded border border-default-soft border-solid"
    }, UIControls_jsx((external_react_inlinesvg_default()), {
      className: "w-5 h-5 svg-icon flex items-center",
      cacheRequests: true,
      src: "/static/icons/ui/menu.svg",
      "aria-label": "icon menu"
    }))))), UIControls_jsx((style_default()), {
      id: "2283970313"
    }, [".nav.jsx-2283970313{position:fixed;top:0;left:0;right:0;z-index:3;}", ".nav-right.jsx-2283970313{position:absolute;right:0;}", ".recording-bar.jsx-2283970313{background-color:#FF0000;text-align:center;width:100%;z-index:3;height:0.32rem;}", ".recording-status.jsx-2283970313{position:absolute;-webkit-transform:translateX(-50%);-ms-transform:translateX(-50%);transform:translateX(-50%);margin-left:50%;text-align:center;color:#FF0000;text-shadow:0 2px 4px rgba(0,0,0,0.1);top:1rem;}"]));
  }

}

/* harmony default export */ var main_UIControls = ((0,external_react_redux_.connect)(state => ({
  recordingStatus: state.app.get('recordingStatus').toJS(),
  uiSettings: state.app.get('uiSettings'),
  mode: state.app.get('mode'),
  isAtLeastOneCountingAreasDefined: state.counter.get('countingAreas').size > 0
}))(UIControls));
// EXTERNAL MODULE: ./statemanagement/app/UserSettingsStateManagement.js
var UserSettingsStateManagement = __webpack_require__(717);
// EXTERNAL MODULE: external "uuid"
var external_uuid_ = __webpack_require__(231);
;// CONCATENATED MODULE: ./components/shared/Toggle.js
var Toggle_jsx = (external_react_default()).createElement;



class Toggle extends external_react_.Component {
  constructor(props) {
    super(props);
    this.state = {
      id: (0,external_uuid_.v4)()
    };
  }

  render() {
    return Toggle_jsx("div", {
      className: "mb-4 mt-4 flex items-center justify-between"
    }, Toggle_jsx("label", {
      htmlFor: this.state.id,
      className: "mr-3"
    }, Toggle_jsx("h4", {
      className: "text-xl font-bold"
    }, this.props.label), Toggle_jsx("p", {
      className: "text-xs"
    }, this.props.description)), Toggle_jsx("div", {
      className: "form-switch inline-block align-middle shadow"
    }, Toggle_jsx("input", {
      type: "checkbox",
      name: this.state.id,
      id: this.state.id,
      checked: this.props.enabled,
      className: "form-switch-checkbox",
      onChange: e => this.props.onChange(e.target.checked)
    }), Toggle_jsx("label", {
      className: "form-switch-label",
      htmlFor: this.state.id
    })));
  }

}

/* harmony default export */ var shared_Toggle = (Toggle);
;// CONCATENATED MODULE: ./components/main/Menu.js

var Menu_jsx = (external_react_default()).createElement;







class Menu extends external_react_.Component {
  constructor(props) {
    super(props);
    this.escFunction = this.escFunction.bind(this);
    this.handleClick = this.handleClick.bind(this);
  }

  escFunction(event) {
    if (event.keyCode === 27) {
      this.props.dispatch((0,AppStateManagement/* hideMenu */.SC)());
    }
  }

  componentDidMount() {
    document.addEventListener('keydown', this.escFunction, false);
    document.addEventListener('click', this.handleClick, false);
  }

  componentWillUnmount() {
    document.removeEventListener('keydown', this.escFunction, false);
    document.removeEventListener('click', this.handleClick, false);
  }

  handleClick(e) {
    if (this.node.contains(e.target)) {
      // click inside menu, do nothing
      return;
    } // Click outside, hide menu


    this.props.dispatch((0,AppStateManagement/* hideMenu */.SC)());
  }

  render() {
    return Menu_jsx((external_react_default()).Fragment, null, Menu_jsx("div", {
      ref: node => this.node = node,
      className: "jsx-3272078245" + " " + "menu text-inverse bg-default shadow"
    }, Menu_jsx("button", {
      onClick: () => this.props.dispatch((0,AppStateManagement/* hideMenu */.SC)()),
      className: "jsx-3272078245" + " " + "btn btn-default btn-close p-0 flex items-center shadow rounded"
    }, Menu_jsx((external_react_inlinesvg_default()), {
      className: "w-12 h-12 svg-icon flex items-center",
      cacheRequests: true,
      src: "/static/icons/ui/close.svg",
      "aria-label": "icon close"
    })), Menu_jsx("div", {
      className: "jsx-3272078245" + " " + "p-5 w-full overflow-y-scroll"
    }, Menu_jsx("h3", {
      className: "jsx-3272078245" + " " + "mb-4 text-2xl font-bold"
    }, Menu_jsx("a", {
      href: "https://github.com/opendatacam/opendatacam",
      target: "_blank",
      className: "jsx-3272078245" + " " + "mt-2"
    }, "OpenDataCam")), Menu_jsx(shared_Toggle, {
      label: "Counter",
      description: "Count objects on active areas",
      enabled: this.props.uiSettings.get('counterEnabled'),
      onChange: value => this.props.dispatch((0,AppStateManagement/* setUiSetting */.Cu)('counterEnabled', value))
    }), Menu_jsx(shared_Toggle, {
      label: "Pathfinder",
      description: "Display paths and positions",
      enabled: this.props.uiSettings.get('pathfinderEnabled'),
      onChange: value => this.props.dispatch((0,AppStateManagement/* setUiSetting */.Cu)('pathfinderEnabled', value))
    }), Menu_jsx(shared_Toggle, {
      label: "Tracker accuracy",
      description: "Display tracker accuracy",
      enabled: this.props.uiSettings.get('heatmapEnabled'),
      onChange: value => this.props.dispatch((0,AppStateManagement/* setUiSetting */.Cu)('heatmapEnabled', value))
    }), Menu_jsx("div", {
      className: "jsx-3272078245" + " " + "mt-16"
    }), Menu_jsx(shared_Toggle, {
      label: "Dark mode",
      description: "Turn dark UI elements on",
      enabled: this.props.userSettings.get('darkMode'),
      onChange: darkMode => {
        this.props.dispatch((0,UserSettingsStateManagement/* setUserSetting */.If)('darkMode', darkMode));
      }
    }), Menu_jsx("div", {
      className: "jsx-3272078245" + " " + "mb-4 mt-4 flex items-center justify-between"
    }, Menu_jsx("div", {
      className: "jsx-3272078245" + " " + "mr-3"
    }, Menu_jsx("h4", {
      className: "jsx-3272078245" + " " + "text-xl font-bold"
    }, "Dimmer"), Menu_jsx("p", {
      className: "jsx-3272078245" + " " + "text-xs"
    }, "Opacity of camera image")), Menu_jsx("div", {
      className: "jsx-3272078245" + " " + "flex"
    }, Menu_jsx("button", {
      onClick: () => this.props.dispatch((0,UserSettingsStateManagement/* setUserSetting */.If)('dimmerOpacity', Math.max(this.props.userSettings.get('dimmerOpacity') - 0.1, 0))),
      className: "jsx-3272078245" + " " + "btn btn-light py-1 px-3 rounded-l border border-gray-100 border-solid flex items-center text-xl font-bold shadow"
    }, "-"), Menu_jsx("button", {
      onClick: () => this.props.dispatch((0,UserSettingsStateManagement/* setUserSetting */.If)('dimmerOpacity', Math.min(this.props.userSettings.get('dimmerOpacity') + 0.1, 1))),
      className: "jsx-3272078245" + " " + "btn btn-light py-1 px-3 rounded-r border border-gray-100 border-solid flex items-center text-xl font-bold shadow"
    }, "+")))), Menu_jsx("div", {
      className: "jsx-3272078245" + " " + "menu-footer bg-black text-white p-5 w-full"
    }, Menu_jsx("div", {
      className: "jsx-3272078245" + " " + "flex flex-col"
    }, Menu_jsx("p", {
      className: "jsx-3272078245"
    }, "Version", this.props.version), Menu_jsx("a", {
      target: "_blank",
      href: "/api/doc",
      className: "jsx-3272078245" + " " + "mt-2"
    }, "API documentation"), Menu_jsx("a", {
      href: "https://github.com/opendatacam/opendatacam",
      target: "_blank",
      className: "jsx-3272078245" + " " + "mt-2"
    }, "About")))), Menu_jsx((style_default()), {
      id: "3272078245"
    }, [".menu.jsx-3272078245{position:absolute;top:0;right:0;bottom:0;z-index:10;min-width:250px;max-width:320px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;}", ".menu-footer.jsx-3272078245{box-shadow:0 -1px 3px 0 rgba(0,0,0,0.1);}", ".btn-close.jsx-3272078245{position:absolute;top:1rem;left:-4rem;}"]));
  }

}

/* harmony default export */ var main_Menu = ((0,external_react_redux_.connect)(state => ({
  mode: state.app.get('mode'),
  userSettings: state.usersettings,
  uiSettings: state.app.get('uiSettings'),
  version: state.app.getIn(['config', 'OPENDATACAM_VERSION'])
}))(Menu));
;// CONCATENATED MODULE: ./components/shared/InitializingView.js

var InitializingView_jsx = (external_react_default()).createElement;





class InitializingView extends external_react_.Component {
  constructor(props) {
    super(props);
    this.estimatedDuration = 30;
    this.timeStarted = null;
    this.updateProgress = this.updateProgress.bind(this);
    this.state = {
      showConsole: false
    };
  }

  updateProgress() {
    // Time since started
    const timeSinceBeg = (new Date().getTime() - this.timeStarted) / 1000;

    if (this.progressBar) {
      const progress = Math.min(timeSinceBeg / this.estimatedDuration, 1);
      this.progressBar.style = `transform:scaleX(${progress});`;
    }

    external_raf_default()(this.updateProgress);
  }

  componentDidMount() {
    this.timeStarted = new Date().getTime();
    this.updateProgress();
  }

  render() {
    return InitializingView_jsx("div", {
      className: "jsx-194310094" + " " + "initializing-view pt-20 pb-20 pr-12 pl-12"
    }, !this.props.requestedFileRecording && !this.props.droppedFile && InitializingView_jsx("h2", {
      className: "jsx-194310094" + " " + "text-white text-3xl font-bold"
    }, "Initializing OpenDataCam"), this.props.requestedFileRecording && InitializingView_jsx("h2", {
      className: "jsx-194310094" + " " + "text-white text-3xl font-bold"
    }, "Restarting to process video file", this.props.fileName.split('/').pop()), this.props.droppedFile && InitializingView_jsx("h2", {
      className: "jsx-194310094" + " " + "text-white text-3xl font-bold"
    }, "Uploading and restarting on dropped video file"), InitializingView_jsx("div", {
      className: "jsx-194310094" + " " + "w-1/5 mt-5 h-5 progress-bar rounded overflow-hidden"
    }, InitializingView_jsx("div", {
      className: "jsx-194310094" + " " + "shadow w-full h-full bg-gray-900"
    }, InitializingView_jsx("div", {
      ref: el => this.progressBar = el,
      className: "jsx-194310094" + " " + "bg-white py-2 progress-bar-content"
    }))), !this.state.showConsole && InitializingView_jsx("button", {
      onClick: () => this.setState({
        showConsole: true
      }),
      className: "jsx-194310094" + " " + "btn btn-light mt-10 rounded"
    }, "Show details"), this.state.showConsole && InitializingView_jsx("div", {
      className: "jsx-194310094" + " " + "mt-10"
    }, InitializingView_jsx("a", {
      onClick: () => this.setState({
        showConsole: false
      }),
      className: "jsx-194310094" + " " + "btn btn-light rounded cursor-pointer"
    }, "Hide details"), InitializingView_jsx("a", {
      target: "_blank",
      href: "/console",
      className: "jsx-194310094" + " " + "ml-2 btn btn-light rounded"
    }, "Download logs")), this.state.showConsole && InitializingView_jsx("div", {
      className: "jsx-194310094" + " " + "console mt-10"
    }, InitializingView_jsx(shared_Console, null)), InitializingView_jsx((style_default()), {
      id: "194310094"
    }, [".initializing-view.jsx-194310094{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;background-color:black;}", ".console.jsx-194310094{width:100%;-webkit-flex:1;-ms-flex:1;flex:1;}", ".progress-bar.jsx-194310094{min-width:200px;position:relative;}", ".progress-bar-content.jsx-194310094{content:'';position:absolute;top:0;left:0;width:100%;height:100%;-webkit-transform-origin:0 0;-ms-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scaleX(0);-ms-transform:scaleX(0);transform:scaleX(0);}"]));
  }

}

/* harmony default export */ var shared_InitializingView = ((0,external_react_redux_.connect)(state => ({
  fileName: state.app.getIn(['recordingStatus', 'filename'])
}))(InitializingView));
;// CONCATENATED MODULE: ./components/shared/TrackerAccuracyModal.js

var TrackerAccuracyModal_jsx = (external_react_default()).createElement;


class TrackerAccuracyModal extends external_react_.Component {
  render() {
    return TrackerAccuracyModal_jsx("div", {
      onClick: () => this.props.close(),
      className: "jsx-472049130" + " " + "overlay"
    }, TrackerAccuracyModal_jsx("div", {
      className: "jsx-472049130" + " " + "modal rounded p-10 shadow text-inverse bg-default border-inverse"
    }, TrackerAccuracyModal_jsx("h3", {
      className: "jsx-472049130" + " " + "text-center text-xl font-bold"
    }, "Tracker accuracy"), TrackerAccuracyModal_jsx("div", {
      className: "jsx-472049130" + " " + "mt-4"
    }, TrackerAccuracyModal_jsx("p", {
      className: "jsx-472049130"
    }, "The heatmap highlights the areas where the tracker accuracy", TrackerAccuracyModal_jsx("strong", {
      className: "jsx-472049130"
    }, "isn't good"), ' ', "to help you:"), TrackerAccuracyModal_jsx("ul", {
      className: "jsx-472049130" + " " + "list-disc mt-2 ml-6"
    }, TrackerAccuracyModal_jsx("li", {
      className: "jsx-472049130"
    }, "Set counter lines where things are well tracked"), TrackerAccuracyModal_jsx("li", {
      className: "jsx-472049130"
    }, "Decide if you should eventually change the camera viewpoint"))), TrackerAccuracyModal_jsx("button", {
      onClick: () => this.props.close(),
      className: "jsx-472049130" + " " + "btn btn-primary btn-rounded min-w-100 mt-5 pl-10 pr-10"
    }, "OK")), TrackerAccuracyModal_jsx((style_default()), {
      id: "472049130"
    }, [".overlay.jsx-472049130{position:fixed;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,0.8);z-index:8;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;}", ".modal.jsx-472049130{border:1px solid black;max-width:90%;height:auto;padding:1rem;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;}"]));
  }

}

/* harmony default export */ var shared_TrackerAccuracyModal = (TrackerAccuracyModal);
;// CONCATENATED MODULE: ./components/shared/TrackerAccuracyView.js
var TrackerAccuracyView_jsx = (external_react_default()).createElement;







class TrackerAccuracyView extends external_react_.Component {
  constructor(props) {
    super(props);
    this.state = {
      showHelp: true
    };
  }

  componentDidMount() {}

  componentWillUnmount() {}

  clearCanvas() {
    // Clear both hidden and visible canvas
    this.clearVisibleCanvas();
  }

  render() {
    return TrackerAccuracyView_jsx((external_react_default()).Fragment, null, this.state.showHelp && TrackerAccuracyView_jsx(shared_TrackerAccuracyModal, {
      close: () => this.setState({
        showHelp: false
      })
    }), TrackerAccuracyView_jsx(canvas_CanvasEngine, {
      mode: constants/* CANVAS_RENDERING_MODE.TRACKER_ACCURACY */.qI.TRACKER_ACCURACY,
      fixedResolution: {
        w: this.props.canvasResolution.get('w') * (0,TrackerStateManagement/* getTrackerAccuracySettings */._d)().canvasResolutionFactor,
        h: this.props.canvasResolution.get('h') * (0,TrackerStateManagement/* getTrackerAccuracySettings */._d)().canvasResolutionFactor
      },
      hidden: this.props.hidden,
      registerClearCanvas: clearCanvas => this.clearVisibleCanvas = clearCanvas
    }));
  }

}

/* harmony default export */ var shared_TrackerAccuracyView = ((0,external_react_redux_.connect)(state => ({
  canvasResolution: state.viewport.get('canvasResolution')
}))(TrackerAccuracyView));
;// CONCATENATED MODULE: ./components/MainPage.js

var MainPage_jsx = (external_react_default()).createElement;



















class MainPage extends (external_react_default()).PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      droppedFile: false
    };
  }

  componentDidMount() {
    this.props.dispatch((0,ViewportStateManagement/* initViewportListeners */.oE)()); // TODO Handle specifying canvas size + resizing here, copy from beatthetraffic

    this.props.dispatch((0,UserSettingsStateManagement/* loadUserSettings */.zJ)()); // TODO See how we handle the YOLO on / off situation

    this.props.dispatch((0,AppStateManagement/* startListeningToServerData */.ZU)()); // Make config available on window global

    window.CONFIG = this.props.config.toJS();
  }

  onDrop(event) {
    event.preventDefault();
    this.setState({
      droppedFile: true
    });
    const formData = new FormData();
    formData.append('video', event.dataTransfer.files[0]);
    external_axios_default().post('/files', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }).then(() => {
      console.log('success');
      this.setState({
        droppedFile: false
      }); // Todo here
      // Ping API endpoint to restart YOLO on this file
    }, error => {
      console.log('error');
      this.setState({
        droppedFile: false
      });
    });
  }

  render() {
    return MainPage_jsx("div", {
      onDragOver: event => event.preventDefault(),
      onDragStart: event => event.preventDefault(),
      onDrop: event => this.onDrop(event),
      className: "jsx-1468007328" + " " + "main-page"
    }, this.props.deviceOrientation === 'portrait' && MainPage_jsx(shared_AskLandscape, null), !this.props.isListeningToYOLO && MainPage_jsx(shared_InitializingView, {
      requestedFileRecording: this.props.requestedFileRecording,
      droppedFile: this.state.droppedFile
    }), this.props.isListeningToYOLO && this.state.droppedFile && MainPage_jsx(shared_InitializingView, {
      requestedFileRecording: this.props.requestedFileRecording,
      droppedFile: this.state.droppedFile
    }), this.props.isListeningToYOLO && !this.state.droppedFile && MainPage_jsx((external_react_default()).Fragment, null, MainPage_jsx(main_UIControls, null), this.props.showMenu && MainPage_jsx(main_Menu, null), this.props.mode === constants/* MODE.DATAVIEW */.IK.DATAVIEW && MainPage_jsx(main_DataView, null), this.props.mode === constants/* MODE.CONSOLEVIEW */.IK.CONSOLEVIEW && MainPage_jsx(main_ConsoleView, null), this.props.mode === constants/* MODE.LIVEVIEW */.IK.LIVEVIEW && MainPage_jsx(main_LiveView, null), this.props.uiSettings.get('counterEnabled') && this.props.mode === constants/* MODE.COUNTERVIEW */.IK.COUNTERVIEW && MainPage_jsx(main_CounterView, null), this.props.uiSettings.get('pathfinderEnabled') && MainPage_jsx(main_PathView, {
      hidden: this.props.mode !== constants/* MODE.PATHVIEW */.IK.PATHVIEW
    }), this.props.uiSettings.get('heatmapEnabled') && MainPage_jsx(shared_TrackerAccuracyView, {
      hidden: this.props.mode === constants/* MODE.PATHVIEW */.IK.PATHVIEW
    }), MainPage_jsx(shared_WebcamStream, null)), MainPage_jsx((style_default()), {
      id: "1468007328"
    }, [".main-page.jsx-1468007328{width:100%;height:100%;position:absolute;top:0;left:0;z-index:1;overflow:hidden;}"]));
  }

}

/* harmony default export */ var components_MainPage = ((0,external_react_redux_.connect)(state => ({
  deviceOrientation: state.viewport.get('deviceOrientation'),
  mode: state.app.get('mode'),
  isListeningToYOLO: state.app.get('isListeningToYOLO'),
  requestedFileRecording: state.app.getIn(['recordingStatus', 'requestedFileRecording']),
  showMenu: state.app.get('showMenu'),
  uiSettings: state.app.get('uiSettings'),
  config: state.app.get('config')
}))(MainPage));
;// CONCATENATED MODULE: ./pages/index.js
var pages_jsx = (external_react_default()).createElement;






class Index extends (external_react_default()).Component {
  static async getInitialProps(params) {
    const {
      store,
      isServer,
      req,
      query
    } = params;

    if (isServer) {
      await store.dispatch((0,CounterStateManagement/* restoreCountingAreas */.p6)(req));
      await store.dispatch((0,AppStateManagement/* restoreUiSettings */.f$)(req));
      await store.dispatch((0,AppStateManagement/* setURLData */.Fu)(req));
      await store.dispatch((0,AppStateManagement/* loadConfig */.ME)(req));
    }
  }

  render() {
    return pages_jsx(shared_Layout, null, pages_jsx(components_MainPage, null));
  }

}

/* harmony default export */ var pages = (Index);

/***/ }),

/***/ 263:
/***/ (function(module) {

module.exports = {
  theme: {
    extend: {
      colors: {
        default: 'var(--color-default)',
        'default-soft': 'var(--color-default-soft)',
        inverse: 'var(--color-inverse)',
        'inverse-soft': 'var(--color-inverse-soft)',
        primary: '#006CFF'
      }
    }
  },
  variants: {},
  plugins: [],
  purge: false
};

/***/ }),

/***/ 376:
/***/ (function(module) {

"use strict";
module.exports = require("axios");;

/***/ }),

/***/ 856:
/***/ (function(module) {

"use strict";
module.exports = require("immutable");;

/***/ }),

/***/ 297:
/***/ (function(module) {

"use strict";
module.exports = require("react");;

/***/ }),

/***/ 469:
/***/ (function(module) {

"use strict";
module.exports = require("react-lazylog");;

/***/ }),

/***/ 79:
/***/ (function(module) {

"use strict";
module.exports = require("react-redux");;

/***/ }),

/***/ 231:
/***/ (function(module) {

"use strict";
module.exports = require("uuid");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
var __webpack_exports__ = __webpack_require__.X(0, [939], function() { return __webpack_exec__(317); });
module.exports = __webpack_exports__;

})();