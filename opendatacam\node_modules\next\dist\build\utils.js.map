{"version": 3, "sources": ["../../build/utils.ts"], "names": ["fileGzipStats", "fsStatGzip", "file", "cached", "getGzipSize", "fileSize", "fs", "stat", "size", "fileStats", "fsStat", "collectPages", "directory", "pageExtensions", "RegExp", "join", "printTreeView", "list", "pageInfos", "serverless", "distPath", "buildId", "pagesDir", "buildManifest", "useStatic404", "gzipSize", "getPrettySize", "_size", "chalk", "green", "yellow", "red", "bold", "getCleanName", "fileName", "replace", "messages", "map", "entry", "underline", "hasCustomApp", "set", "get", "static", "includes", "sizeData", "computeFromManifest", "pageList", "slice", "filter", "e", "sort", "a", "b", "localeCompare", "for<PERSON>ach", "item", "i", "arr", "symbol", "length", "pageInfo", "ampFirs<PERSON>", "ampFirstPages", "push", "isSsg", "initialRevalidateSeconds", "cyan", "totalSize", "uniqueCssFiles", "pages", "endsWith", "uniqueFiles", "contSymbol", "index", "innerSymbol", "sizeUniqueFiles", "ssgPageRoutes", "totalRoutes", "previewPages", "routes", "remaining", "slug", "sharedFilesSize", "sizeCommonFiles", "sharedFiles", "sizeCommonFile", "shared<PERSON><PERSON><PERSON><PERSON>s", "Object", "keys", "sharedCssFiles", "originalName", "cleanName", "console", "log", "align", "stringLength", "str", "printCustomRoutes", "redirects", "rewrites", "headers", "printRoutes", "type", "isRedirects", "isHeaders", "routesStr", "route", "routeStr", "source", "r", "destination", "statusCode", "permanent", "header", "last", "key", "value", "combinedRewrites", "beforeFiles", "afterFiles", "fallback", "cachedBuildManifest", "lastCompute", "lastComputePageInfo", "manifest", "is", "expected", "files", "Map", "isHybridAmp", "Infinity", "has", "getSize", "commonFiles", "entries", "len", "f", "stats", "Promise", "all", "path", "_", "uniqueStats", "reduce", "obj", "n", "assign", "difference", "main", "sub", "Set", "x", "intersect", "sum", "getJsPageSizeInKb", "page", "computedManifestData", "data", "fnFilterJs", "pageFiles", "appFiles", "fnMapRealPath", "dep", "allFilesReal", "selfFilesReal", "allFilesSize", "selfFilesSize", "buildStaticPaths", "getStaticPaths", "locales", "defaultLocale", "prerenderPaths", "encoded<PERSON>rerenderPaths", "_routeRegex", "_routeMatcher", "_validParamKeys", "staticPathsResult", "expectedReturnVal", "Array", "isArray", "Error", "invalidStatic<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "paths", "localePathResult", "cleanedEntry", "detectedLocale", "substr", "result", "add", "split", "segment", "decodeURIComponent", "<PERSON><PERSON><PERSON><PERSON>", "k", "params", "builtPage", "encodedBuiltPage", "validParamKey", "repeat", "optional", "groups", "paramValue", "hasOwnProperty", "undefined", "replaced", "encodeURIComponent", "locale", "cur<PERSON><PERSON><PERSON>", "encodedPaths", "isPageStatic", "distDir", "runtimeEnvConfig", "parentId", "isPageStaticSpan", "traceAsyncFn", "require", "setConfig", "components", "mod", "ComponentMod", "Comp", "default", "hasGetInitialProps", "getInitialProps", "hasStaticProps", "getStaticProps", "hasStaticPaths", "hasServerProps", "getServerSideProps", "hasLegacyServerProps", "unstable_getServerProps", "hasLegacyStaticProps", "unstable_getStaticProps", "hasLegacyStaticPaths", "unstable_getStaticPaths", "hasLegacyStaticParams", "unstable_getStaticParams", "SSG_GET_INITIAL_PROPS_CONFLICT", "SERVER_PROPS_GET_INIT_PROPS_CONFLICT", "SERVER_PROPS_SSG_CONFLICT", "pageIsDynamic", "prerenderRoutes", "encodedPrerenderRoutes", "prerenderFallback", "isNextImageImported", "global", "__NEXT_IMAGE_IMPORTED", "config", "isStatic", "amp", "isAmpOnly", "err", "code", "hasCustomGetInitialProps", "isLikeServerless", "checkingApp", "_app", "origGetInitialProps", "getNamedExports", "detectConflictingPaths", "combinedPages", "ssgPages", "additionalSsgPaths", "conflictingPaths", "dynamicSsgPages", "pathsPage", "curPath", "lowerPath", "toLowerCase", "conflictingPage", "find", "conflicting<PERSON><PERSON>", "compPath", "conflictingPathsOutput", "pathItems", "pathItem", "idx", "isDynamic", "Log", "error", "process", "exit", "getCssFilePaths", "cssFiles", "values"], "mappings": "6hBAAA,qDACA,oDACA,8EACA,gFACA,kDACA,sBACA,iCACA,gFAOA,2CAKA,wEACA,0DACA,sDACA,qEACA,oHACA,0DAEA,4EAEA,0EAEA,kFACA,yDACA,qEACA,yC,w4BAEA,KAAMA,CAAAA,aAA2D,CAAG,EAApE,CACA,KAAMC,CAAAA,UAAU,CAAIC,IAAD,EAAkB,CACnC,KAAMC,CAAAA,MAAM,CAAGH,aAAa,CAACE,IAAD,CAA5B,CACA,GAAIC,MAAJ,CAAY,MAAOA,CAAAA,MAAP,CACZ,MAAQH,CAAAA,aAAa,CAACE,IAAD,CAAb,CAAsBE,kBAAYF,IAAZ,CAAiBA,IAAjB,CAA9B,CACD,CAJD,CAMA,KAAMG,CAAAA,QAAQ,CAAG,KAAOH,CAAAA,IAAP,EAAwB,CAAC,KAAMI,cAAGC,IAAH,CAAQL,IAAR,CAAP,EAAsBM,IAA/D,CAEA,KAAMC,CAAAA,SAAuD,CAAG,EAAhE,CACA,KAAMC,CAAAA,MAAM,CAAIR,IAAD,EAAkB,CAC/B,KAAMC,CAAAA,MAAM,CAAGM,SAAS,CAACP,IAAD,CAAxB,CACA,GAAIC,MAAJ,CAAY,MAAOA,CAAAA,MAAP,CACZ,MAAQM,CAAAA,SAAS,CAACP,IAAD,CAAT,CAAkBG,QAAQ,CAACH,IAAD,CAAlC,CACD,CAJD,CAMO,QAASS,CAAAA,YAAT,CACLC,SADK,CAELC,cAFK,CAGc,CACnB,MAAO,uCACLD,SADK,CAEL,GAAIE,CAAAA,MAAJ,CAAY,SAAQD,cAAc,CAACE,IAAf,CAAoB,GAApB,CAAyB,IAA7C,CAFK,CAAP,CAID,CAYM,cAAeC,CAAAA,aAAf,CACLC,IADK,CAELC,SAFK,CAGLC,UAHK,CAIL,CACEC,QADF,CAEEC,OAFF,CAGEC,QAHF,CAIET,cAJF,CAKEU,aALF,CAMEC,YANF,CAOEC,QAAQ,CAAG,IAPb,CAJK,CAqBL,CACA,KAAMC,CAAAA,aAAa,CAAIC,KAAD,EAA2B,CAC/C,KAAMnB,CAAAA,IAAI,CAAG,yBAAYmB,KAAZ,CAAb,CACA;AACA,GAAIA,KAAK,CAAG,IAAM,IAAlB,CAAwB,MAAOC,gBAAMC,KAAN,CAAYrB,IAAZ,CAAP,CACxB;AACA,GAAImB,KAAK,CAAG,IAAM,IAAlB,CAAwB,MAAOC,gBAAME,MAAN,CAAatB,IAAb,CAAP,CACxB;AACA,MAAOoB,gBAAMG,GAAN,CAAUC,IAAV,CAAexB,IAAf,CAAP,CACD,CARD,CAUA,KAAMyB,CAAAA,YAAY,CAAIC,QAAD,EACnBA,QACE;AADM,CAELC,OAFH,CAEW,WAFX,CAEwB,EAFxB,CAGE;AAHF,CAIGA,OAJH,CAIW,YAJX,CAIyB,QAJzB,CAKE;AALF,CAMGA,OANH,CAMW,2CANX,CAMwD,KANxD,CADF,CASA,KAAMC,CAAAA,QAAoC,CAAG,CAC3C,CAAC,MAAD,CAAS,MAAT,CAAiB,eAAjB,EAAkCC,GAAlC,CAAuCC,KAAD,EACpCV,eAAMW,SAAN,CAAgBD,KAAhB,CADF,CAD2C,CAA7C,CAMA,KAAME,CAAAA,YAAY,CAAG,KAAM,+BAAalB,QAAb,CAAuB,OAAvB,CAAgCT,cAAhC,CAA3B,CAEAK,SAAS,CAACuB,GAAV,CAAc,MAAd,CAAsB,CACpB,IAAIvB,SAAS,CAACwB,GAAV,CAAc,MAAd,GAAyBxB,SAAS,CAACwB,GAAV,CAAc,SAAd,CAA7B,CADoB,CAEpBC,MAAM,CAAEnB,YAFY,CAAtB,EAKA,GAAI,CAACP,IAAI,CAAC2B,QAAL,CAAc,MAAd,CAAL,CAA4B,CAC1B3B,IAAI,CAAG,CAAC,GAAGA,IAAJ,CAAU,MAAV,CAAP,CACD,CAED,KAAM4B,CAAAA,QAAQ,CAAG,KAAMC,CAAAA,mBAAmB,CACxCvB,aADwC,CAExCH,QAFwC,CAGxCK,QAHwC,CAIxCP,SAJwC,CAA1C,CAOA,KAAM6B,CAAAA,QAAQ,CAAG9B,IAAI,CAClB+B,KADc,GAEdC,MAFc,CAGZC,CAAD,EACE,EACEA,CAAC,GAAK,YAAN,EACAA,CAAC,GAAK,SADN,EAEC,CAACV,YAAD,EAAiBU,CAAC,GAAK,OAH1B,CAJW,EAUdC,IAVc,CAUT,CAACC,CAAD,CAAIC,CAAJ,GAAUD,CAAC,CAACE,aAAF,CAAgBD,CAAhB,CAVD,CAAjB,CAYAN,QAAQ,CAACQ,OAAT,CAAiB,CAACC,IAAD,CAAOC,CAAP,CAAUC,GAAV,GAAkB,iDACjC,KAAMC,CAAAA,MAAM,CACVF,CAAC,GAAK,CAAN,CACIC,GAAG,CAACE,MAAJ,GAAe,CAAf,CACE,GADF,CAEE,GAHN,CAIIH,CAAC,GAAKC,GAAG,CAACE,MAAJ,CAAa,CAAnB,CACA,GADA,CAEA,GAPN,CASA,KAAMC,CAAAA,QAAQ,CAAG3C,SAAS,CAACwB,GAAV,CAAcc,IAAd,CAAjB,CACA,KAAMM,CAAAA,QAAQ,CAAGvC,aAAa,CAACwC,aAAd,CAA4BnB,QAA5B,CAAqCY,IAArC,CAAjB,CAEApB,QAAQ,CAAC4B,IAAT,CAAc,CACX,GAAEL,MAAO,IACRH,IAAI,GAAK,OAAT,CACI,GADJ,CAEIK,QAAQ,MAAR,EAAAA,QAAQ,CAAElB,MAAV,CACA,GADA,CAEAkB,QAAQ,MAAR,EAAAA,QAAQ,CAAEI,KAAV,CACA,GADA,CAEA,GACL,IACCJ,QAAQ,MAAR,EAAAA,QAAQ,CAAEK,wBAAV,CACK,GAAEV,IAAK,UAASK,QAAjB,cAAiBA,QAAQ,CAAEK,wBAAyB,WADxD,CAEIV,IACL,EAbW,CAcZK,QAAQ,CACJC,QAAQ,CACNlC,eAAMuC,IAAN,CAAW,KAAX,CADM,CAENN,QAAQ,CAACrD,IAAT,EAAiB,CAAjB,CACA,yBAAYqD,QAAQ,CAACrD,IAArB,CADA,CAEA,EALE,CAMJ,EApBQ,CAqBZqD,QAAQ,CACJC,QAAQ,CACNlC,eAAMuC,IAAN,CAAW,KAAX,CADM,CAENN,QAAQ,CAACrD,IAAT,EAAiB,CAAjB,CACAkB,aAAa,CAACmC,QAAQ,CAACO,SAAV,CADb,CAEA,EALE,CAMJ,EA3BQ,CAAd,EA8BA,KAAMC,CAAAA,cAAc,CAClB,wBAAA9C,aAAa,CAAC+C,KAAd,CAAoBd,IAApB,sCAA2BP,MAA3B,CACG/C,IAAD,EAAUA,IAAI,CAACqE,QAAL,CAAc,MAAd,GAAyB1B,QAAQ,CAAC2B,WAAT,CAAqB5B,QAArB,CAA8B1C,IAA9B,CADrC,IAEK,EAHP,CAKA,GAAImE,cAAc,CAACT,MAAf,CAAwB,CAA5B,CAA+B,CAC7B,KAAMa,CAAAA,UAAU,CAAGhB,CAAC,GAAKC,GAAG,CAACE,MAAJ,CAAa,CAAnB,CAAuB,GAAvB,CAA6B,GAAhD,CAEAS,cAAc,CAACd,OAAf,CAAuB,CAACrD,IAAD,CAAOwE,KAAP,CAAc,CAAEd,MAAF,CAAd,GAA6B,CAClD,KAAMe,CAAAA,WAAW,CAAGD,KAAK,GAAKd,MAAM,CAAG,CAAnB,CAAuB,GAAvB,CAA6B,GAAjD,CACAxB,QAAQ,CAAC4B,IAAT,CAAc,CACX,GAAES,UAAW,MAAKE,WAAY,IAAG1C,YAAY,CAAC/B,IAAD,CAAO,EADzC,CAEZ,yBAAY2C,QAAQ,CAAC+B,eAAT,CAAyB1E,IAAzB,CAAZ,CAFY,CAGZ,EAHY,CAAd,EAKD,CAPD,EAQD,CAED,GAAI2D,QAAJ,+BAAIA,QAAQ,CAAEgB,aAAd,SAAI,sBAAyBjB,MAA7B,CAAqC,CACnC,KAAMkB,CAAAA,WAAW,CAAGjB,QAAQ,CAACgB,aAAT,CAAuBjB,MAA3C,CACA,KAAMmB,CAAAA,YAAY,CAAGD,WAAW,GAAK,CAAhB,CAAoB,CAApB,CAAwB,CAA7C,CACA,KAAML,CAAAA,UAAU,CAAGhB,CAAC,GAAKC,GAAG,CAACE,MAAJ,CAAa,CAAnB,CAAuB,GAAvB,CAA6B,GAAhD,CAEA,KAAMoB,CAAAA,MAAM,CAAGnB,QAAQ,CAACgB,aAAT,CAAuB7B,KAAvB,CAA6B,CAA7B,CAAgC+B,YAAhC,CAAf,CACA,GAAID,WAAW,CAAGC,YAAlB,CAAgC,CAC9B,KAAME,CAAAA,SAAS,CAAGH,WAAW,CAAGC,YAAhC,CACAC,MAAM,CAAChB,IAAP,CAAa,KAAIiB,SAAU,cAA3B,EACD,CAEDD,MAAM,CAACzB,OAAP,CAAe,CAAC2B,IAAD,CAAOR,KAAP,CAAc,CAAEd,MAAF,CAAd,GAA6B,CAC1C,KAAMe,CAAAA,WAAW,CAAGD,KAAK,GAAKd,MAAM,CAAG,CAAnB,CAAuB,GAAvB,CAA6B,GAAjD,CACAxB,QAAQ,CAAC4B,IAAT,CAAc,CAAE,GAAES,UAAW,MAAKE,WAAY,IAAGO,IAAK,EAAxC,CAA2C,EAA3C,CAA+C,EAA/C,CAAd,EACD,CAHD,EAID,CACF,CA7ED,EA+EA,KAAMC,CAAAA,eAAe,CAAGtC,QAAQ,CAACuC,eAAjC,CACA,KAAMC,CAAAA,WAAW,CAAGxC,QAAQ,CAACyC,cAA7B,CAEAlD,QAAQ,CAAC4B,IAAT,CAAc,CACZ,+BADY,CAEZtC,aAAa,CAACyD,eAAD,CAFD,CAGZ,EAHY,CAAd,EAKA,KAAMI,CAAAA,cAAc,CAAGC,MAAM,CAACC,IAAP,CAAYJ,WAAZ,CAAvB,CACA,KAAMK,CAAAA,cAAwB,CAAG,EAAjC,CACC,CACC,GAAGH,cAAc,CACdtC,MADA,CACQ/C,IAAD,EAAU,CAChB,GAAIA,IAAI,CAACqE,QAAL,CAAc,MAAd,CAAJ,CAA2B,CACzBmB,cAAc,CAAC1B,IAAf,CAAoB9D,IAApB,EACA,MAAO,MAAP,CACD,CACD,MAAO,KAAP,CACD,CAPA,EAQAmC,GARA,CAQKa,CAAD,EAAOA,CAAC,CAACf,OAAF,CAAUd,OAAV,CAAmB,WAAnB,CARX,EASA8B,IATA,EADJ,CAWC,GAAGuC,cAAc,CAACrD,GAAf,CAAoBa,CAAD,EAAOA,CAAC,CAACf,OAAF,CAAUd,OAAV,CAAmB,WAAnB,CAA1B,EAA2D8B,IAA3D,EAXJ,EAYCI,OAZD,CAYS,CAACrB,QAAD,CAAWwC,KAAX,CAAkB,CAAEd,MAAF,CAAlB,GAAiC,CACzC,KAAMe,CAAAA,WAAW,CAAGD,KAAK,GAAKd,MAAM,CAAG,CAAnB,CAAuB,GAAvB,CAA6B,GAAjD,CAEA,KAAM+B,CAAAA,YAAY,CAAGzD,QAAQ,CAACC,OAAT,CAAiB,WAAjB,CAA8Bd,OAA9B,CAArB,CACA,KAAMuE,CAAAA,SAAS,CAAG3D,YAAY,CAACC,QAAD,CAA9B,CAEAE,QAAQ,CAAC4B,IAAT,CAAc,CACX,KAAIW,WAAY,IAAGiB,SAAU,EADlB,CAEZ,yBAAYP,WAAW,CAACM,YAAD,CAAvB,CAFY,CAGZ,EAHY,CAAd,EAKD,CAvBA,EAyBDE,OAAO,CAACC,GAAR,CACE,uBAAU1D,QAAV,CAAoB,CAClB2D,KAAK,CAAE,CAAC,GAAD,CAAM,GAAN,CAAW,GAAX,CADW,CAElBC,YAAY,CAAGC,GAAD,EAAS,uBAAUA,GAAV,EAAerC,MAFpB,CAApB,CADF,EAOAiC,OAAO,CAACC,GAAR,GACAD,OAAO,CAACC,GAAR,CACE,uBACE,CACE,CACE,GADF,CAEE3E,UAAU,CAAG,UAAH,CAAgB,UAF5B,CAGG,wCAAuCS,eAAMuC,IAAN,CACtC,iBADsC,CAEtC,OAAMvC,eAAMuC,IAAN,CAAW,oBAAX,CAAiC,GAL3C,CADF,CAQE,CACE,GADF,CAEE,UAFF,CAGE,+DAHF,CARF,CAaE,CACE,GADF,CAEE,OAFF,CAGG,uDAAsDvC,eAAMuC,IAAN,CACrD,gBADqD,CAErD,GALJ,CAbF,CAoBE,CACE,EADF,CAEE,OAFF,CAGG,uDAAsDvC,eAAMuC,IAAN,CACrD,gBADqD,CAErD,GALJ,CApBF,CADF,CA6BE,CACE4B,KAAK,CAAE,CAAC,GAAD,CAAM,GAAN,CAAW,GAAX,CADT,CAEEC,YAAY,CAAGC,GAAD,EAAS,uBAAUA,GAAV,EAAerC,MAFxC,CA7BF,CADF,EAqCAiC,OAAO,CAACC,GAAR,GACD,CAEM,QAASI,CAAAA,iBAAT,CAA2B,CAChCC,SADgC,CAEhCC,QAFgC,CAGhCC,OAHgC,CAA3B,CAIU,CACf,KAAMC,CAAAA,WAAW,CAAG,CAClBtB,MADkB,CAElBuB,IAFkB,GAGf,CACH,KAAMC,CAAAA,WAAW,CAAGD,IAAI,GAAK,WAA7B,CACA,KAAME,CAAAA,SAAS,CAAGF,IAAI,GAAK,SAA3B,CACAV,OAAO,CAACC,GAAR,CAAYlE,eAAMW,SAAN,CAAgBgE,IAAhB,CAAZ,EACAV,OAAO,CAACC,GAAR,GAEA;AACJ;AACA;AACA;AACA,OACI,KAAMY,CAAAA,SAAS,CAAI1B,MAAD,CACf3C,GADe,CACVsE,KAAD,EAA+B,CAClC,GAAIC,CAAAA,QAAQ,CAAI,aAAYD,KAAK,CAACE,MAAO,IAAzC,CAEA,GAAI,CAACJ,SAAL,CAAgB,CACd,KAAMK,CAAAA,CAAC,CAAGH,KAAV,CACAC,QAAQ,EAAK,GAAEJ,WAAW,CAAG,GAAH,CAAS,GAAI,iBACrCM,CAAC,CAACC,WACH,IAFD,CAGD,CACD,GAAIP,WAAJ,CAAiB,CACf,KAAMM,CAAAA,CAAC,CAAGH,KAAV,CACAC,QAAQ,EAAK,KACXE,CAAC,CAACE,UAAF,CACK,WAAUF,CAAC,CAACE,UAAW,EAD5B,CAEK,cAAaF,CAAC,CAACG,SAAU,EAC/B,IAJD,CAKD,CAED,GAAIR,SAAJ,CAAe,CACb,KAAMK,CAAAA,CAAC,CAAGH,KAAV,CACAC,QAAQ,EAAK,cAAb,CAEA,IAAK,GAAInD,CAAAA,CAAC,CAAG,CAAb,CAAgBA,CAAC,CAAGqD,CAAC,CAACT,OAAF,CAAUzC,MAA9B,CAAsCH,CAAC,EAAvC,CAA2C,CACzC,KAAMyD,CAAAA,MAAM,CAAGJ,CAAC,CAACT,OAAF,CAAU5C,CAAV,CAAf,CACA,KAAM0D,CAAAA,IAAI,CAAG1D,CAAC,GAAK4C,OAAO,CAACzC,MAAR,CAAiB,CAApC,CAEAgD,QAAQ,EAAK,KAAIO,IAAI,CAAG,GAAH,CAAS,GAAI,IAAGD,MAAM,CAACE,GAAI,KAAIF,MAAM,CAACG,KAAM,IAAjE,CACD,CACF,CAED,MAAOT,CAAAA,QAAP,CACD,CAhCe,EAiCf7F,IAjCe,CAiCV,IAjCU,CAAlB,CAmCA8E,OAAO,CAACC,GAAR,CAAYY,SAAZ,CAAuB,IAAvB,EACD,CAlDD,CAoDA,GAAIP,SAAS,CAACvC,MAAd,CAAsB,CACpB0C,WAAW,CAACH,SAAD,CAAY,WAAZ,CAAX,CACD,CACD,GAAIE,OAAO,CAACzC,MAAZ,CAAoB,CAClB0C,WAAW,CAACD,OAAD,CAAU,SAAV,CAAX,CACD,CAED,KAAMiB,CAAAA,gBAAgB,CAAG,CACvB,GAAGlB,QAAQ,CAACmB,WADW,CAEvB,GAAGnB,QAAQ,CAACoB,UAFW,CAGvB,GAAGpB,QAAQ,CAACqB,QAHW,CAAzB,CAKA,GAAIH,gBAAgB,CAAC1D,MAArB,CAA6B,CAC3B0C,WAAW,CAACgB,gBAAD,CAAmB,UAAnB,CAAX,CACD,CACF,CAUD,GAAII,CAAAA,mBAAJ,CAEA,GAAIC,CAAAA,WAAJ,CACA,GAAIC,CAAAA,mBAAJ,CAEO,cAAe9E,CAAAA,mBAAf,CACL+E,QADK,CAELzG,QAFK,CAGLK,QAAiB,CAAG,IAHf,CAILP,SAJK,CAK0B,CAC/B,GACEsE,MAAM,CAACsC,EAAP,CAAUJ,mBAAV,CAA+BG,QAA/B,GACAD,mBAAmB,GAAK,CAAC,CAAC1G,SAF5B,CAGE,CACA,MAAOyG,CAAAA,WAAP,CACD,CAED,GAAII,CAAAA,QAAQ,CAAG,CAAf,CACA,KAAMC,CAAAA,KAAK,CAAG,GAAIC,CAAAA,GAAJ,EAAd,CACAzC,MAAM,CAACC,IAAP,CAAYoC,QAAQ,CAACvD,KAArB,EAA4Bf,OAA5B,CAAqC6D,GAAD,EAAS,CAC3C,GAAIlG,SAAJ,CAAe,CACb,KAAM2C,CAAAA,QAAQ,CAAG3C,SAAS,CAACwB,GAAV,CAAc0E,GAAd,CAAjB,CACA;AACA;AACA,GAAIvD,QAAJ,QAAIA,QAAQ,CAAEqE,WAAd,CAA2B,CACzB,OACD,CACF,CAED,EAAEH,QAAF,CACAF,QAAQ,CAACvD,KAAT,CAAe8C,GAAf,EAAoB7D,OAApB,CAA6BrD,IAAD,EAAU,CACpC,GAAIkH,GAAG,GAAK,OAAZ,CAAqB,CACnBY,KAAK,CAACvF,GAAN,CAAUvC,IAAV,CAAgBiI,QAAhB,EACD,CAFD,IAEO,IAAIH,KAAK,CAACI,GAAN,CAAUlI,IAAV,CAAJ,CAAqB,CAC1B8H,KAAK,CAACvF,GAAN,CAAUvC,IAAV,CAAgB8H,KAAK,CAACtF,GAAN,CAAUxC,IAAV,EAAmB,CAAnC,EACD,CAFM,IAEA,CACL8H,KAAK,CAACvF,GAAN,CAAUvC,IAAV,CAAgB,CAAhB,EACD,CACF,CARD,EASD,CApBD,EAsBA,KAAMmI,CAAAA,OAAO,CAAG5G,QAAQ,CAAGxB,UAAH,CAAgBS,MAAxC,CAEA,KAAM4H,CAAAA,WAAW,CAAG,CAAC,GAAGN,KAAK,CAACO,OAAN,EAAJ,EACjBtF,MADiB,CACV,CAAC,EAAGuF,GAAH,CAAD,GAAaA,GAAG,GAAKT,QAAR,EAAoBS,GAAG,GAAKL,QAD/B,EAEjB9F,GAFiB,CAEb,CAAC,CAACoG,CAAD,CAAD,GAASA,CAFI,CAApB,CAGA,KAAMjE,CAAAA,WAAW,CAAG,CAAC,GAAGwD,KAAK,CAACO,OAAN,EAAJ,EACjBtF,MADiB,CACV,CAAC,EAAGuF,GAAH,CAAD,GAAaA,GAAG,GAAK,CADX,EAEjBnG,GAFiB,CAEb,CAAC,CAACoG,CAAD,CAAD,GAASA,CAFI,CAApB,CAIA,GAAIC,CAAAA,KAAJ,CACA,GAAI,CACFA,KAAK,CAAG,KAAMC,CAAAA,OAAO,CAACC,GAAR,CACZN,WAAW,CAACjG,GAAZ,CACE,MAAOoG,CAAP,GACE,CAACA,CAAD,CAAI,KAAMJ,CAAAA,OAAO,CAACQ,cAAK9H,IAAL,CAAUK,QAAV,CAAoBqH,CAApB,CAAD,CAAjB,CAFJ,CADY,CAAd,CAMD,CAAC,MAAOK,CAAP,CAAU,CACVJ,KAAK,CAAG,EAAR,CACD,CAED,GAAIK,CAAAA,WAAJ,CACA,GAAI,CACFA,WAAW,CAAG,KAAMJ,CAAAA,OAAO,CAACC,GAAR,CAClBpE,WAAW,CAACnC,GAAZ,CACE,MAAOoG,CAAP,GACE,CAACA,CAAD,CAAI,KAAMJ,CAAAA,OAAO,CAACQ,cAAK9H,IAAL,CAAUK,QAAV,CAAoBqH,CAApB,CAAD,CAAjB,CAFJ,CADkB,CAApB,CAMD,CAAC,MAAOK,CAAP,CAAU,CACVC,WAAW,CAAG,EAAd,CACD,CAEDpB,WAAW,CAAG,CACZW,WADY,CAEZ9D,WAFY,CAGZI,eAAe,CAAEmE,WAAW,CAACC,MAAZ,CACf,CAACC,GAAD,CAAMC,CAAN,GAAY1D,MAAM,CAAC2D,MAAP,CAAcF,GAAd,CAAmB,CAAE,CAACC,CAAC,CAAC,CAAD,CAAF,EAAQA,CAAC,CAAC,CAAD,CAAX,CAAnB,CADG,CAEf,EAFe,CAHL,CAOZ5D,cAAc,CAAEoD,KAAK,CAACM,MAAN,CACd,CAACC,GAAD,CAAMC,CAAN,GAAY1D,MAAM,CAAC2D,MAAP,CAAcF,GAAd,CAAmB,CAAE,CAACC,CAAC,CAAC,CAAD,CAAF,EAAQA,CAAC,CAAC,CAAD,CAAX,CAAnB,CADE,CAEd,EAFc,CAPJ,CAWZ9D,eAAe,CAAEsD,KAAK,CAACM,MAAN,CAAa,CAACxI,IAAD,CAAO,CAACiI,CAAD,CAAIlI,IAAJ,CAAP,GAAqB,CACjD,GAAIkI,CAAC,CAAClE,QAAF,CAAW,MAAX,CAAJ,CAAwB,MAAO/D,CAAAA,IAAP,CACxB,MAAOA,CAAAA,IAAI,CAAGD,IAAd,CACD,CAHgB,CAGd,CAHc,CAXL,CAAd,CAiBAmH,mBAAmB,CAAGG,QAAtB,CACAD,mBAAmB,CAAG,CAAC,CAAC1G,SAAxB,CACA,MAAOyG,CAAAA,WAAP,CACD,CAEM,QAASyB,CAAAA,UAAT,CAAuBC,IAAvB,CAA2CC,GAA3C,CAAmE,CACxE,KAAMlG,CAAAA,CAAC,CAAG,GAAImG,CAAAA,GAAJ,CAAQF,IAAR,CAAV,CACA,KAAMhG,CAAAA,CAAC,CAAG,GAAIkG,CAAAA,GAAJ,CAAQD,GAAR,CAAV,CACA,MAAO,CAAC,GAAGlG,CAAJ,EAAOH,MAAP,CAAeuG,CAAD,EAAO,CAACnG,CAAC,CAAC+E,GAAF,CAAMoB,CAAN,CAAtB,CAAP,CACD,CAED,QAASC,CAAAA,SAAT,CAAsBJ,IAAtB,CAAiCC,GAAjC,CAAgD,CAC9C,KAAMlG,CAAAA,CAAC,CAAG,GAAImG,CAAAA,GAAJ,CAAQF,IAAR,CAAV,CACA,KAAMhG,CAAAA,CAAC,CAAG,GAAIkG,CAAAA,GAAJ,CAAQD,GAAR,CAAV,CACA,MAAO,CAAC,GAAG,GAAIC,CAAAA,GAAJ,CAAQ,CAAC,GAAGnG,CAAJ,EAAOH,MAAP,CAAeuG,CAAD,EAAOnG,CAAC,CAAC+E,GAAF,CAAMoB,CAAN,CAArB,CAAR,CAAJ,CAAP,CACD,CAED,QAASE,CAAAA,GAAT,CAAatG,CAAb,CAAkC,CAChC,MAAOA,CAAAA,CAAC,CAAC4F,MAAF,CAAS,CAACxI,IAAD,CAAOD,IAAP,GAAgBC,IAAI,CAAGD,IAAhC,CAAsC,CAAtC,CAAP,CACD,CAEM,cAAeoJ,CAAAA,iBAAf,CACLC,IADK,CAELxI,QAFK,CAGLG,aAHK,CAILE,QAAiB,CAAG,IAJf,CAKLoI,oBALK,CAMsB,CAC3B,KAAMC,CAAAA,IAAI,CACRD,oBAAoB,GACnB,KAAM/G,CAAAA,mBAAmB,CAACvB,aAAD,CAAgBH,QAAhB,CAA0BK,QAA1B,CADN,CADtB,CAIA,KAAMsI,CAAAA,UAAU,CAAIzH,KAAD,EAAmBA,KAAK,CAACiC,QAAN,CAAe,KAAf,CAAtC,CAEA,KAAMyF,CAAAA,SAAS,CAAG,CAChBzI,aAAa,CAAC+C,KAAd,CAAoB,2CAAoBsF,IAApB,CAApB,GAAkD,EADlC,EAEhB3G,MAFgB,CAET8G,UAFS,CAAlB,CAGA,KAAME,CAAAA,QAAQ,CAAG,CAAC1I,aAAa,CAAC+C,KAAd,CAAoB,OAApB,GAAgC,EAAjC,EAAqCrB,MAArC,CAA4C8G,UAA5C,CAAjB,CAEA,KAAMG,CAAAA,aAAa,CAAIC,GAAD,EAAkB,GAAE/I,QAAS,IAAG+I,GAAI,EAA1D,CAEA,KAAMC,CAAAA,YAAY,CAAG,CAAC,GAAG,GAAIb,CAAAA,GAAJ,CAAQ,CAAC,GAAGS,SAAJ,CAAe,GAAGC,QAAlB,CAAR,CAAJ,EAA0C5H,GAA1C,CACnB6H,aADmB,CAArB,CAGA,KAAMG,CAAAA,aAAa,CAAGjB,UAAU,CAC9BK,SAAS,CAACO,SAAD,CAAYF,IAAI,CAACtF,WAAjB,CADqB,CAE9BsF,IAAI,CAACxB,WAFyB,CAAV,CAGpBjG,GAHoB,CAGhB6H,aAHgB,CAAtB,CAKA,KAAM7B,CAAAA,OAAO,CAAG5G,QAAQ,CAAGxB,UAAH,CAAgBS,MAAxC,CAEA,GAAI,CACF;AACA;AACA,KAAM4J,CAAAA,YAAY,CAAGZ,GAAG,CAAC,KAAMf,CAAAA,OAAO,CAACC,GAAR,CAAYwB,YAAY,CAAC/H,GAAb,CAAiBgG,OAAjB,CAAZ,CAAP,CAAxB,CACA,KAAMkC,CAAAA,aAAa,CAAGb,GAAG,CAAC,KAAMf,CAAAA,OAAO,CAACC,GAAR,CAAYyB,aAAa,CAAChI,GAAd,CAAkBgG,OAAlB,CAAZ,CAAP,CAAzB,CAEA,MAAO,CAACkC,aAAD,CAAgBD,YAAhB,CAAP,CACD,CAAC,MAAOxB,CAAP,CAAU,CAAE,CACd,MAAO,CAAC,CAAC,CAAF,CAAK,CAAC,CAAN,CAAP,CACD,CAEM,cAAe0B,CAAAA,gBAAf,CACLZ,IADK,CAELa,cAFK,CAGLC,OAHK,CAILC,aAJK,CAUL,CACA,KAAMC,CAAAA,cAAc,CAAG,GAAIrB,CAAAA,GAAJ,EAAvB,CACA,KAAMsB,CAAAA,qBAAqB,CAAG,GAAItB,CAAAA,GAAJ,EAA9B,CACA,KAAMuB,CAAAA,WAAW,CAAG,yBAAclB,IAAd,CAApB,CACA,KAAMmB,CAAAA,aAAa,CAAG,2BAAgBD,WAAhB,CAAtB,CAEA;AACA,KAAME,CAAAA,eAAe,CAAGxF,MAAM,CAACC,IAAP,CAAYsF,aAAa,CAACnB,IAAD,CAAzB,CAAxB,CAEA,KAAMqB,CAAAA,iBAAiB,CAAG,KAAMR,CAAAA,cAAc,CAAC,CAAEC,OAAF,CAAWC,aAAX,CAAD,CAA9C,CAEA,KAAMO,CAAAA,iBAAiB,CACpB,8CAAD,CACC,uFAFH,CAIA,GACE,CAACD,iBAAD,EACA,MAAOA,CAAAA,iBAAP,GAA6B,QAD7B,EAEAE,KAAK,CAACC,OAAN,CAAcH,iBAAd,CAHF,CAIE,CACA,KAAM,IAAII,CAAAA,KAAJ,CACH,iDAAgDzB,IAAK,cAAa,MAAOqB,CAAAA,iBAAkB,IAAGC,iBAAkB,EAD7G,CAAN,CAGD,CAED,KAAMI,CAAAA,qBAAqB,CAAG9F,MAAM,CAACC,IAAP,CAAYwF,iBAAZ,EAA+BhI,MAA/B,CAC3BmE,GAAD,EAAS,EAAEA,GAAG,GAAK,OAAR,EAAmBA,GAAG,GAAK,UAA7B,CADmB,CAA9B,CAIA,GAAIkE,qBAAqB,CAAC1H,MAAtB,CAA+B,CAAnC,CAAsC,CACpC,KAAM,IAAIyH,CAAAA,KAAJ,CACH,8CAA6CzB,IAAK,KAAI0B,qBAAqB,CAACvK,IAAtB,CACrD,IADqD,CAErD,KAAImK,iBAAkB,EAHpB,CAAN,CAKD,CAED,GACE,EACE,MAAOD,CAAAA,iBAAiB,CAACxD,QAAzB,GAAsC,SAAtC,EACAwD,iBAAiB,CAACxD,QAAlB,GAA+B,UAFjC,CADF,CAKE,CACA,KAAM,IAAI4D,CAAAA,KAAJ,CACH,gEAA+DzB,IAAK,KAArE,CACEsB,iBAFE,CAAN,CAID,CAED,KAAMK,CAAAA,WAAW,CAAGN,iBAAiB,CAACO,KAAtC,CAEA,GAAI,CAACL,KAAK,CAACC,OAAN,CAAcG,WAAd,CAAL,CAAiC,CAC/B,KAAM,IAAIF,CAAAA,KAAJ,CACH,2DAA0DzB,IAAK,KAAhE,CACG,6FAFC,CAAN,CAID,CAED2B,WAAW,CAAChI,OAAZ,CAAqBjB,KAAD,EAAW,CAC7B;AACA;AACA,GAAI,MAAOA,CAAAA,KAAP,GAAiB,QAArB,CAA+B,CAC7BA,KAAK,CAAG,oDAAwBA,KAAxB,CAAR,CAEA,KAAMmJ,CAAAA,gBAAgB,CAAG,6CAAoBnJ,KAApB,CAA2BoI,OAA3B,CAAzB,CACA,GAAIgB,CAAAA,YAAY,CAAGpJ,KAAnB,CAEA,GAAImJ,gBAAgB,CAACE,cAArB,CAAqC,CACnCD,YAAY,CAAGpJ,KAAK,CAACsJ,MAAN,CAAaH,gBAAgB,CAACE,cAAjB,CAAgC/H,MAAhC,CAAyC,CAAtD,CAAf,CACD,CAFD,IAEO,IAAI+G,aAAJ,CAAmB,CACxBrI,KAAK,CAAI,IAAGqI,aAAc,GAAErI,KAAM,EAAlC,CACD,CAED,KAAMuJ,CAAAA,MAAM,CAAGd,aAAa,CAACW,YAAD,CAA5B,CACA,GAAI,CAACG,MAAL,CAAa,CACX,KAAM,IAAIR,CAAAA,KAAJ,CACH,uBAAsBK,YAAa,iCAAgC9B,IAAK,KADrE,CAAN,CAGD,CAED;AACA;AACA;AACAgB,cAAc,CAACkB,GAAf,CACExJ,KAAK,CACFyJ,KADH,CACS,GADT,EAEG1J,GAFH,CAEQ2J,OAAD,EACH,kCAAqBC,kBAAkB,CAACD,OAAD,CAAvC,CAAkD,IAAlD,CAHJ,EAKGjL,IALH,CAKQ,GALR,CADF,EAQA8J,qBAAqB,CAACiB,GAAtB,CAA0BxJ,KAA1B,EACD,CACD;AACA;AAjCA,IAkCK,CACH,KAAM4J,CAAAA,WAAW,CAAG1G,MAAM,CAACC,IAAP,CAAYnD,KAAZ,EAAmBW,MAAnB,CACjBmE,GAAD,EAASA,GAAG,GAAK,QAAR,EAAoBA,GAAG,GAAK,QADnB,CAApB,CAIA,GAAI8E,WAAW,CAACtI,MAAhB,CAAwB,CACtB,KAAM,IAAIyH,CAAAA,KAAJ,CACH,kEAAiEzB,IAAK,KAAvE,CACG,+FADH,CAEG,4BAA2BoB,eAAe,CACxC3I,GADyB,CACpB8J,CAAD,EAAQ,GAAEA,CAAE,OADS,EAEzBpL,IAFyB,CAEpB,IAFoB,CAEd,MAJhB,CAKG,mCAAkCmL,WAAW,CAACnL,IAAZ,CAAiB,IAAjB,CAAuB,KANxD,CAAN,CAQD,CAED,KAAM,CAAEqL,MAAM,CAAG,EAAX,EAAkB9J,KAAxB,CACA,GAAI+J,CAAAA,SAAS,CAAGzC,IAAhB,CACA,GAAI0C,CAAAA,gBAAgB,CAAG1C,IAAvB,CAEAoB,eAAe,CAACzH,OAAhB,CAAyBgJ,aAAD,EAAmB,CACzC,KAAM,CAAEC,MAAF,CAAUC,QAAV,EAAuB3B,WAAW,CAAC4B,MAAZ,CAAmBH,aAAnB,CAA7B,CACA,GAAII,CAAAA,UAAU,CAAGP,MAAM,CAACG,aAAD,CAAvB,CACA,GACEE,QAAQ,EACRL,MAAM,CAACQ,cAAP,CAAsBL,aAAtB,CADA,GAECI,UAAU,GAAK,IAAf,EACCA,UAAU,GAAKE,SADhB,EAEEF,UAAD,GAAwB,KAJ1B,CADF,CAME,CACAA,UAAU,CAAG,EAAb,CACD,CACD,GACGH,MAAM,EAAI,CAACrB,KAAK,CAACC,OAAN,CAAcuB,UAAd,CAAZ,EACC,CAACH,MAAD,EAAW,MAAOG,CAAAA,UAAP,GAAsB,QAFpC,CAGE,CACA,KAAM,IAAItB,CAAAA,KAAJ,CACH,yBAAwBkB,aAAc,yBACrCC,MAAM,CAAG,UAAH,CAAgB,UACvB,0BAAyB5C,IAAK,EAH3B,CAAN,CAKD,CACD,GAAIkD,CAAAA,QAAQ,CAAI,IAAGN,MAAM,CAAG,KAAH,CAAW,EAAG,GAAED,aAAc,GAAvD,CACA,GAAIE,QAAJ,CAAc,CACZK,QAAQ,CAAI,IAAGA,QAAS,GAAxB,CACD,CACDT,SAAS,CAAGA,SAAS,CAClBlK,OADS,CAER2K,QAFQ,CAGRN,MAAM,CACDG,UAAD,CACGtK,GADH,CACQ2J,OAAD,EAAa,kCAAqBA,OAArB,CAA8B,IAA9B,CADpB,EAEGjL,IAFH,CAEQ,GAFR,CADE,CAIF,kCAAqB4L,UAArB,CAA2C,IAA3C,CAPI,EASTxK,OATS,CASD,UATC,CASW,EATX,CAAZ,CAWAmK,gBAAgB,CAAGA,gBAAgB,CAChCnK,OADgB,CAEf2K,QAFe,CAGfN,MAAM,CACDG,UAAD,CAAyBtK,GAAzB,CAA6B0K,kBAA7B,EAAiDhM,IAAjD,CAAsD,GAAtD,CADE,CAEFgM,kBAAkB,CAACJ,UAAD,CALP,EAOhBxK,OAPgB,CAOR,UAPQ,CAOI,EAPJ,CAAnB,CAQD,CA7CD,EA+CA,GAAIG,KAAK,CAAC0K,MAAN,EAAgB,EAACtC,OAAD,QAACA,OAAO,CAAE9H,QAAT,CAAkBN,KAAK,CAAC0K,MAAxB,CAAD,CAApB,CAAsD,CACpD,KAAM,IAAI3B,CAAAA,KAAJ,CACH,mDAAkDzB,IAAK,gBAAetH,KAAK,CAAC0K,MAAO,qCADhF,CAAN,CAGD,CACD,KAAMC,CAAAA,SAAS,CAAG3K,KAAK,CAAC0K,MAAN,EAAgBrC,aAAhB,EAAiC,EAAnD,CAEAC,cAAc,CAACkB,GAAf,CACG,GAAEmB,SAAS,CAAI,IAAGA,SAAU,EAAjB,CAAqB,EAAG,GAClCA,SAAS,EAAIZ,SAAS,GAAK,GAA3B,CAAiC,EAAjC,CAAsCA,SACvC,EAHH,EAKAxB,qBAAqB,CAACiB,GAAtB,CACG,GAAEmB,SAAS,CAAI,IAAGA,SAAU,EAAjB,CAAqB,EAAG,GAClCA,SAAS,EAAIX,gBAAgB,GAAK,GAAlC,CAAwC,EAAxC,CAA6CA,gBAC9C,EAHH,EAKD,CACF,CA1HD,EA4HA,MAAO,CACLd,KAAK,CAAE,CAAC,GAAGZ,cAAJ,CADF,CAELnD,QAAQ,CAAEwD,iBAAiB,CAACxD,QAFvB,CAGLyF,YAAY,CAAE,CAAC,GAAGrC,qBAAJ,CAHT,CAAP,CAKD,CAEM,cAAesC,CAAAA,YAAf,CACLvD,IADK,CAELwD,OAFK,CAGLjM,UAHK,CAILkM,gBAJK,CAKL3C,OALK,CAMLC,aANK,CAOL2C,QAPK,CAkBJ,CACD,KAAMC,CAAAA,gBAAgB,CAAG,iBAAM,sBAAN,CAA8BD,QAA9B,CAAzB,CACA,MAAOC,CAAAA,gBAAgB,CAACC,YAAjB,CAA8B,SAAY,CAC/C,GAAI,CACFC,OAAO,CAAC,mCAAD,CAAP,CAA6CC,SAA7C,CAAuDL,gBAAvD,EACA,KAAMM,CAAAA,UAAU,CAAG,KAAM,mCAAeP,OAAf,CAAwBxD,IAAxB,CAA8BzI,UAA9B,CAAzB,CACA,KAAMyM,CAAAA,GAAG,CAAGD,UAAU,CAACE,YAAvB,CACA,KAAMC,CAAAA,IAAI,CAAGF,GAAG,CAACG,OAAJ,EAAeH,GAA5B,CAEA,GAAI,CAACE,IAAD,EAAS,CAAC,gCAAmBA,IAAnB,CAAV,EAAsC,MAAOA,CAAAA,IAAP,GAAgB,QAA1D,CAAoE,CAClE,KAAM,IAAIzC,CAAAA,KAAJ,CAAU,wBAAV,CAAN,CACD,CAED,KAAM2C,CAAAA,kBAAkB,CAAG,CAAC,CAAEF,IAAD,CAAcG,eAA3C,CACA,KAAMC,CAAAA,cAAc,CAAG,CAAC,EAAE,KAAMN,CAAAA,GAAG,CAACO,cAAZ,CAAxB,CACA,KAAMC,CAAAA,cAAc,CAAG,CAAC,EAAE,KAAMR,CAAAA,GAAG,CAACnD,cAAZ,CAAxB,CACA,KAAM4D,CAAAA,cAAc,CAAG,CAAC,EAAE,KAAMT,CAAAA,GAAG,CAACU,kBAAZ,CAAxB,CACA,KAAMC,CAAAA,oBAAoB,CAAG,CAAC,EAAE,KAAMX,CAAAA,GAAG,CAACY,uBAAZ,CAA9B,CACA,KAAMC,CAAAA,oBAAoB,CAAG,CAAC,EAAE,KAAMb,CAAAA,GAAG,CAACc,uBAAZ,CAA9B,CACA,KAAMC,CAAAA,oBAAoB,CAAG,CAAC,EAAE,KAAMf,CAAAA,GAAG,CAACgB,uBAAZ,CAA9B,CACA,KAAMC,CAAAA,qBAAqB,CAAG,CAAC,EAAE,KAAMjB,CAAAA,GAAG,CAACkB,wBAAZ,CAA/B,CAEA,GAAID,qBAAJ,CAA2B,CACzB,KAAM,IAAIxD,CAAAA,KAAJ,CACH,qFADG,CAAN,CAGD,CAED,GAAIsD,oBAAJ,CAA0B,CACxB,KAAM,IAAItD,CAAAA,KAAJ,CACH,oFADG,CAAN,CAGD,CAED,GAAIoD,oBAAJ,CAA0B,CACxB,KAAM,IAAIpD,CAAAA,KAAJ,CACH,oFADG,CAAN,CAGD,CAED,GAAIkD,oBAAJ,CAA0B,CACxB,KAAM,IAAIlD,CAAAA,KAAJ,CACH,wFADG,CAAN,CAGD,CAED;AACA;AACA,GAAI2C,kBAAkB,EAAIE,cAA1B,CAA0C,CACxC,KAAM,IAAI7C,CAAAA,KAAJ,CAAU0D,yCAAV,CAAN,CACD,CAED,GAAIf,kBAAkB,EAAIK,cAA1B,CAA0C,CACxC,KAAM,IAAIhD,CAAAA,KAAJ,CAAU2D,+CAAV,CAAN,CACD,CAED,GAAId,cAAc,EAAIG,cAAtB,CAAsC,CACpC,KAAM,IAAIhD,CAAAA,KAAJ,CAAU4D,oCAAV,CAAN,CACD,CAED,KAAMC,CAAAA,aAAa,CAAG,8BAAetF,IAAf,CAAtB,CACA;AACA,GAAIsE,cAAc,EAAIE,cAAlB,EAAoC,CAACc,aAAzC,CAAwD,CACtD,KAAM,IAAI7D,CAAAA,KAAJ,CACH,4DAA2DzB,IAAK,IAAjE,CACG,8DAFC,CAAN,CAID,CAED,GAAIsE,cAAc,EAAIgB,aAAlB,EAAmC,CAACd,cAAxC,CAAwD,CACtD,KAAM,IAAI/C,CAAAA,KAAJ,CACH,wEAAuEzB,IAAK,IAA7E,CACG,4EAFC,CAAN,CAID,CAED,GAAIuF,CAAAA,eAAJ,CACA,GAAIC,CAAAA,sBAAJ,CACA,GAAIC,CAAAA,iBAAJ,CACA,GAAInB,cAAc,EAAIE,cAAtB,CAAsC,CACpC,CAAC,CAAC,CACA5C,KAAK,CAAE2D,eADP,CAEA1H,QAAQ,CAAE4H,iBAFV,CAGAnC,YAAY,CAAEkC,sBAHd,EAIE,KAAM5E,CAAAA,gBAAgB,CACxBZ,IADwB,CAExBgE,GAAG,CAACnD,cAFoB,CAGxBC,OAHwB,CAIxBC,aAJwB,CAJzB,EAUF,CAED,KAAM2E,CAAAA,mBAAmB,CAAIC,MAAD,CAAgBC,qBAA5C,CACA,KAAMC,CAAAA,MAAM,CAAG7B,GAAG,CAAC6B,MAAJ,EAAc,EAA7B,CACA,MAAO,CACLC,QAAQ,CAAE,CAACxB,cAAD,EAAmB,CAACF,kBAApB,EAA0C,CAACK,cADhD,CAELnG,WAAW,CAAEuH,MAAM,CAACE,GAAP,GAAe,QAFvB,CAGLC,SAAS,CAAEH,MAAM,CAACE,GAAP,GAAe,IAHrB,CAILR,eAJK,CAKLE,iBALK,CAMLD,sBANK,CAOLlB,cAPK,CAQLG,cARK,CASLiB,mBATK,CAAP,CAWD,CAAC,MAAOO,GAAP,CAAY,CACZ,GAAIA,GAAG,CAACC,IAAJ,GAAa,kBAAjB,CAAqC,MAAO,EAAP,CACrC,KAAMD,CAAAA,GAAN,CACD,CACF,CA3GM,CAAP,CA4GD,CAEM,cAAeE,CAAAA,wBAAf,CACLnG,IADK,CAELwD,OAFK,CAGL4C,gBAHK,CAIL3C,gBAJK,CAKL4C,WALK,CAMa,CAClBxC,OAAO,CAAC,mCAAD,CAAP,CAA6CC,SAA7C,CAAuDL,gBAAvD,EAEA,KAAMM,CAAAA,UAAU,CAAG,KAAM,mCAAeP,OAAf,CAAwBxD,IAAxB,CAA8BoG,gBAA9B,CAAzB,CACA,GAAIpC,CAAAA,GAAG,CAAGD,UAAU,CAACE,YAArB,CAEA,GAAIoC,WAAJ,CAAiB,CACfrC,GAAG,CAAG,CAAC,KAAMA,CAAAA,GAAG,CAACsC,IAAX,GAAoBtC,GAAG,CAACG,OAAxB,EAAmCH,GAAzC,CACD,CAFD,IAEO,CACLA,GAAG,CAAGA,GAAG,CAACG,OAAJ,EAAeH,GAArB,CACD,CACDA,GAAG,CAAG,KAAMA,CAAAA,GAAZ,CACA,MAAOA,CAAAA,GAAG,CAACK,eAAJ,GAAwBL,GAAG,CAACuC,mBAAnC,CACD,CAEM,cAAeC,CAAAA,eAAf,CACLxG,IADK,CAELwD,OAFK,CAGL4C,gBAHK,CAIL3C,gBAJK,CAKmB,CACxBI,OAAO,CAAC,mCAAD,CAAP,CAA6CC,SAA7C,CAAuDL,gBAAvD,EACA,KAAMM,CAAAA,UAAU,CAAG,KAAM,mCAAeP,OAAf,CAAwBxD,IAAxB,CAA8BoG,gBAA9B,CAAzB,CACA,GAAIpC,CAAAA,GAAG,CAAGD,UAAU,CAACE,YAArB,CAEA,MAAOrI,CAAAA,MAAM,CAACC,IAAP,CAAYmI,GAAZ,CAAP,CACD,CAEM,QAASyC,CAAAA,sBAAT,CACLC,aADK,CAELC,QAFK,CAGLC,kBAHK,CAIL,CACA,KAAMC,CAAAA,gBAAgB,CAAG,GAAIxI,CAAAA,GAAJ,EAAzB,CAQA,KAAMyI,CAAAA,eAAe,CAAG,CAAC,GAAGH,QAAJ,EAActN,MAAd,CAAsB2G,IAAD,EAAU,8BAAeA,IAAf,CAA/B,CAAxB,CAEA4G,kBAAkB,CAACjN,OAAnB,CAA2B,CAACiI,KAAD,CAAQmF,SAAR,GAAsB,CAC/CnF,KAAK,CAACjI,OAAN,CAAeqN,OAAD,EAAa,CACzB,KAAMC,CAAAA,SAAS,CAAGD,OAAO,CAACE,WAAR,EAAlB,CACA,GAAIC,CAAAA,eAAe,CAAGT,aAAa,CAACU,IAAd,CACnBpH,IAAD,EAAUA,IAAI,CAACkH,WAAL,KAAuBD,SADb,CAAtB,CAIA,GAAIE,eAAJ,CAAqB,CACnBN,gBAAgB,CAAChO,GAAjB,CAAqBoO,SAArB,CAAgC,CAC9B,CAAEhI,IAAI,CAAE+H,OAAR,CAAiBhH,IAAI,CAAE+G,SAAvB,CAD8B,CAE9B,CAAE9H,IAAI,CAAEkI,eAAR,CAAyBnH,IAAI,CAAEmH,eAA/B,CAF8B,CAAhC,EAID,CALD,IAKO,CACL,GAAIE,CAAAA,eAAJ,CAEAF,eAAe,CAAGL,eAAe,CAACM,IAAhB,CAAsBpH,IAAD,EAAU,2BAC/C,GAAIA,IAAI,GAAK+G,SAAb,CAAwB,MAAO,MAAP,CAExBM,eAAe,wBAAGT,kBAAkB,CACjC9N,GADe,CACXkH,IADW,CAAH,eAAG,sBAEdoH,IAFc,CAERE,QAAD,EAAcA,QAAQ,CAACJ,WAAT,KAA2BD,SAFhC,CAAlB,CAGA,MAAOI,CAAAA,eAAP,CACD,CAPiB,CAAlB,CASA,GAAIF,eAAe,EAAIE,eAAvB,CAAwC,CACtCR,gBAAgB,CAAChO,GAAjB,CAAqBoO,SAArB,CAAgC,CAC9B,CAAEhI,IAAI,CAAE+H,OAAR,CAAiBhH,IAAI,CAAE+G,SAAvB,CAD8B,CAE9B,CAAE9H,IAAI,CAAEoI,eAAR,CAAyBrH,IAAI,CAAEmH,eAA/B,CAF8B,CAAhC,EAID,CACF,CACF,CA9BD,EA+BD,CAhCD,EAkCA,GAAIN,gBAAgB,CAACjQ,IAAjB,CAAwB,CAA5B,CAA+B,CAC7B,GAAI2Q,CAAAA,sBAAsB,CAAG,EAA7B,CAEAV,gBAAgB,CAAClN,OAAjB,CAA0B6N,SAAD,EAAe,CACtCA,SAAS,CAAC7N,OAAV,CAAkB,CAAC8N,QAAD,CAAWC,GAAX,GAAmB,CACnC,KAAMC,CAAAA,SAAS,CAAGF,QAAQ,CAACzH,IAAT,GAAkByH,QAAQ,CAACxI,IAA7C,CAEA,GAAIyI,GAAG,CAAG,CAAV,CAAa,CACXH,sBAAsB,EAAI,iBAA1B,CACD,CAEDA,sBAAsB,EAAK,UAASE,QAAQ,CAACxI,IAAK,IAChD0I,SAAS,CAAI,gBAAeF,QAAQ,CAACzH,IAAK,IAAjC,CAAuC,GACjD,EAFD,CAGD,CAVD,EAWAuH,sBAAsB,EAAI,IAA1B,CACD,CAbD,EAeAK,GAAG,CAACC,KAAJ,CACE,gFACE,gFADF,CAEEN,sBAHJ,EAKAO,OAAO,CAACC,IAAR,CAAa,CAAb,EACD,CACF,CAEM,QAASC,CAAAA,eAAT,CAAyBrQ,aAAzB,CAAiE,CACtE,KAAMsQ,CAAAA,QAAQ,CAAG,GAAItI,CAAAA,GAAJ,EAAjB,CACA/D,MAAM,CAACsM,MAAP,CAAcvQ,aAAa,CAAC+C,KAA5B,EAAmCf,OAAnC,CAA4CyE,KAAD,EAAW,CACpDA,KAAK,CAACzE,OAAN,CAAerD,IAAD,EAAU,CACtB,GAAIA,IAAI,CAACqE,QAAL,CAAc,MAAd,CAAJ,CAA2B,CACzBsN,QAAQ,CAAC/F,GAAT,CAAa5L,IAAb,EACD,CACF,CAJD,EAKD,CAND,EAQA,MAAO,CAAC,GAAG2R,QAAJ,CAAP,CACD", "sourcesContent": ["import '../next-server/server/node-polyfill-fetch'\nimport chalk from 'chalk'\nimport getGzipSize from 'next/dist/compiled/gzip-size'\nimport textTable from 'next/dist/compiled/text-table'\nimport path from 'path'\nimport { promises as fs } from 'fs'\nimport { isValidElementType } from 'react-is'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport {\n  Redirect,\n  Rewrite,\n  Header,\n  CustomRoutes,\n} from '../lib/load-custom-routes'\nimport {\n  SSG_GET_INITIAL_PROPS_CONFLICT,\n  SERVER_PROPS_GET_INIT_PROPS_CONFLICT,\n  SERVER_PROPS_SSG_CONFLICT,\n} from '../lib/constants'\nimport prettyBytes from '../lib/pretty-bytes'\nimport { recursiveReadDir } from '../lib/recursive-readdir'\nimport { getRouteMatcher, getRouteRegex } from '../next-server/lib/router/utils'\nimport { isDynamicRoute } from '../next-server/lib/router/utils/is-dynamic'\nimport escapePathDelimiters from '../next-server/lib/router/utils/escape-path-delimiters'\nimport { findPageFile } from '../server/lib/find-page-file'\nimport { GetStaticPaths } from 'next/types'\nimport { denormalizePagePath } from '../next-server/server/normalize-page-path'\nimport { BuildManifest } from '../next-server/server/get-page-files'\nimport { removePathTrailingSlash } from '../client/normalize-trailing-slash'\nimport { UnwrapPromise } from '../lib/coalesced-function'\nimport { normalizeLocalePath } from '../next-server/lib/i18n/normalize-locale-path'\nimport * as Log from './output/log'\nimport { loadComponents } from '../next-server/server/load-components'\nimport { trace } from '../telemetry/trace'\n\nconst fileGzipStats: { [k: string]: Promise<number> | undefined } = {}\nconst fsStatGzip = (file: string) => {\n  const cached = fileGzipStats[file]\n  if (cached) return cached\n  return (fileGzipStats[file] = getGzipSize.file(file))\n}\n\nconst fileSize = async (file: string) => (await fs.stat(file)).size\n\nconst fileStats: { [k: string]: Promise<number> | undefined } = {}\nconst fsStat = (file: string) => {\n  const cached = fileStats[file]\n  if (cached) return cached\n  return (fileStats[file] = fileSize(file))\n}\n\nexport function collectPages(\n  directory: string,\n  pageExtensions: string[]\n): Promise<string[]> {\n  return recursiveReadDir(\n    directory,\n    new RegExp(`\\\\.(?:${pageExtensions.join('|')})$`)\n  )\n}\n\nexport interface PageInfo {\n  isHybridAmp?: boolean\n  size: number\n  totalSize: number\n  static: boolean\n  isSsg: boolean\n  ssgPageRoutes: string[] | null\n  initialRevalidateSeconds: number | false\n}\n\nexport async function printTreeView(\n  list: readonly string[],\n  pageInfos: Map<string, PageInfo>,\n  serverless: boolean,\n  {\n    distPath,\n    buildId,\n    pagesDir,\n    pageExtensions,\n    buildManifest,\n    useStatic404,\n    gzipSize = true,\n  }: {\n    distPath: string\n    buildId: string\n    pagesDir: string\n    pageExtensions: string[]\n    buildManifest: BuildManifest\n    useStatic404: boolean\n    gzipSize?: boolean\n  }\n) {\n  const getPrettySize = (_size: number): string => {\n    const size = prettyBytes(_size)\n    // green for 0-130kb\n    if (_size < 130 * 1000) return chalk.green(size)\n    // yellow for 130-170kb\n    if (_size < 170 * 1000) return chalk.yellow(size)\n    // red for >= 170kb\n    return chalk.red.bold(size)\n  }\n\n  const getCleanName = (fileName: string) =>\n    fileName\n      // Trim off `static/`\n      .replace(/^static\\//, '')\n      // Re-add `static/` for root files\n      .replace(/^<buildId>/, 'static')\n      // Remove file hash\n      .replace(/(?:^|[.-])([0-9a-z]{6})[0-9a-z]{14}(?=\\.)/, '.$1')\n\n  const messages: [string, string, string][] = [\n    ['Page', 'Size', 'First Load JS'].map((entry) =>\n      chalk.underline(entry)\n    ) as [string, string, string],\n  ]\n\n  const hasCustomApp = await findPageFile(pagesDir, '/_app', pageExtensions)\n\n  pageInfos.set('/404', {\n    ...(pageInfos.get('/404') || pageInfos.get('/_error')),\n    static: useStatic404,\n  } as any)\n\n  if (!list.includes('/404')) {\n    list = [...list, '/404']\n  }\n\n  const sizeData = await computeFromManifest(\n    buildManifest,\n    distPath,\n    gzipSize,\n    pageInfos\n  )\n\n  const pageList = list\n    .slice()\n    .filter(\n      (e) =>\n        !(\n          e === '/_document' ||\n          e === '/_error' ||\n          (!hasCustomApp && e === '/_app')\n        )\n    )\n    .sort((a, b) => a.localeCompare(b))\n\n  pageList.forEach((item, i, arr) => {\n    const symbol =\n      i === 0\n        ? arr.length === 1\n          ? '─'\n          : '┌'\n        : i === arr.length - 1\n        ? '└'\n        : '├'\n\n    const pageInfo = pageInfos.get(item)\n    const ampFirst = buildManifest.ampFirstPages.includes(item)\n\n    messages.push([\n      `${symbol} ${\n        item === '/_app'\n          ? ' '\n          : pageInfo?.static\n          ? '○'\n          : pageInfo?.isSsg\n          ? '●'\n          : 'λ'\n      } ${\n        pageInfo?.initialRevalidateSeconds\n          ? `${item} (ISR: ${pageInfo?.initialRevalidateSeconds} Seconds)`\n          : item\n      }`,\n      pageInfo\n        ? ampFirst\n          ? chalk.cyan('AMP')\n          : pageInfo.size >= 0\n          ? prettyBytes(pageInfo.size)\n          : ''\n        : '',\n      pageInfo\n        ? ampFirst\n          ? chalk.cyan('AMP')\n          : pageInfo.size >= 0\n          ? getPrettySize(pageInfo.totalSize)\n          : ''\n        : '',\n    ])\n\n    const uniqueCssFiles =\n      buildManifest.pages[item]?.filter(\n        (file) => file.endsWith('.css') && sizeData.uniqueFiles.includes(file)\n      ) || []\n\n    if (uniqueCssFiles.length > 0) {\n      const contSymbol = i === arr.length - 1 ? ' ' : '├'\n\n      uniqueCssFiles.forEach((file, index, { length }) => {\n        const innerSymbol = index === length - 1 ? '└' : '├'\n        messages.push([\n          `${contSymbol}   ${innerSymbol} ${getCleanName(file)}`,\n          prettyBytes(sizeData.sizeUniqueFiles[file]),\n          '',\n        ])\n      })\n    }\n\n    if (pageInfo?.ssgPageRoutes?.length) {\n      const totalRoutes = pageInfo.ssgPageRoutes.length\n      const previewPages = totalRoutes === 4 ? 4 : 3\n      const contSymbol = i === arr.length - 1 ? ' ' : '├'\n\n      const routes = pageInfo.ssgPageRoutes.slice(0, previewPages)\n      if (totalRoutes > previewPages) {\n        const remaining = totalRoutes - previewPages\n        routes.push(`[+${remaining} more paths]`)\n      }\n\n      routes.forEach((slug, index, { length }) => {\n        const innerSymbol = index === length - 1 ? '└' : '├'\n        messages.push([`${contSymbol}   ${innerSymbol} ${slug}`, '', ''])\n      })\n    }\n  })\n\n  const sharedFilesSize = sizeData.sizeCommonFiles\n  const sharedFiles = sizeData.sizeCommonFile\n\n  messages.push([\n    '+ First Load JS shared by all',\n    getPrettySize(sharedFilesSize),\n    '',\n  ])\n  const sharedFileKeys = Object.keys(sharedFiles)\n  const sharedCssFiles: string[] = []\n  ;[\n    ...sharedFileKeys\n      .filter((file) => {\n        if (file.endsWith('.css')) {\n          sharedCssFiles.push(file)\n          return false\n        }\n        return true\n      })\n      .map((e) => e.replace(buildId, '<buildId>'))\n      .sort(),\n    ...sharedCssFiles.map((e) => e.replace(buildId, '<buildId>')).sort(),\n  ].forEach((fileName, index, { length }) => {\n    const innerSymbol = index === length - 1 ? '└' : '├'\n\n    const originalName = fileName.replace('<buildId>', buildId)\n    const cleanName = getCleanName(fileName)\n\n    messages.push([\n      `  ${innerSymbol} ${cleanName}`,\n      prettyBytes(sharedFiles[originalName]),\n      '',\n    ])\n  })\n\n  console.log(\n    textTable(messages, {\n      align: ['l', 'l', 'r'],\n      stringLength: (str) => stripAnsi(str).length,\n    })\n  )\n\n  console.log()\n  console.log(\n    textTable(\n      [\n        [\n          'λ',\n          serverless ? '(Lambda)' : '(Server)',\n          `server-side renders at runtime (uses ${chalk.cyan(\n            'getInitialProps'\n          )} or ${chalk.cyan('getServerSideProps')})`,\n        ],\n        [\n          '○',\n          '(Static)',\n          'automatically rendered as static HTML (uses no initial props)',\n        ],\n        [\n          '●',\n          '(SSG)',\n          `automatically generated as static HTML + JSON (uses ${chalk.cyan(\n            'getStaticProps'\n          )})`,\n        ],\n        [\n          '',\n          '(ISR)',\n          `incremental static regeneration (uses revalidate in ${chalk.cyan(\n            'getStaticProps'\n          )})`,\n        ],\n      ] as [string, string, string][],\n      {\n        align: ['l', 'l', 'l'],\n        stringLength: (str) => stripAnsi(str).length,\n      }\n    )\n  )\n\n  console.log()\n}\n\nexport function printCustomRoutes({\n  redirects,\n  rewrites,\n  headers,\n}: CustomRoutes) {\n  const printRoutes = (\n    routes: Redirect[] | Rewrite[] | Header[],\n    type: 'Redirects' | 'Rewrites' | 'Headers'\n  ) => {\n    const isRedirects = type === 'Redirects'\n    const isHeaders = type === 'Headers'\n    console.log(chalk.underline(type))\n    console.log()\n\n    /*\n        ┌ source\n        ├ permanent/statusCode\n        └ destination\n     */\n    const routesStr = (routes as any[])\n      .map((route: { source: string }) => {\n        let routeStr = `┌ source: ${route.source}\\n`\n\n        if (!isHeaders) {\n          const r = route as Rewrite\n          routeStr += `${isRedirects ? '├' : '└'} destination: ${\n            r.destination\n          }\\n`\n        }\n        if (isRedirects) {\n          const r = route as Redirect\n          routeStr += `└ ${\n            r.statusCode\n              ? `status: ${r.statusCode}`\n              : `permanent: ${r.permanent}`\n          }\\n`\n        }\n\n        if (isHeaders) {\n          const r = route as Header\n          routeStr += `└ headers:\\n`\n\n          for (let i = 0; i < r.headers.length; i++) {\n            const header = r.headers[i]\n            const last = i === headers.length - 1\n\n            routeStr += `  ${last ? '└' : '├'} ${header.key}: ${header.value}\\n`\n          }\n        }\n\n        return routeStr\n      })\n      .join('\\n')\n\n    console.log(routesStr, '\\n')\n  }\n\n  if (redirects.length) {\n    printRoutes(redirects, 'Redirects')\n  }\n  if (headers.length) {\n    printRoutes(headers, 'Headers')\n  }\n\n  const combinedRewrites = [\n    ...rewrites.beforeFiles,\n    ...rewrites.afterFiles,\n    ...rewrites.fallback,\n  ]\n  if (combinedRewrites.length) {\n    printRoutes(combinedRewrites, 'Rewrites')\n  }\n}\n\ntype ComputeManifestShape = {\n  commonFiles: string[]\n  uniqueFiles: string[]\n  sizeUniqueFiles: { [file: string]: number }\n  sizeCommonFile: { [file: string]: number }\n  sizeCommonFiles: number\n}\n\nlet cachedBuildManifest: BuildManifest | undefined\n\nlet lastCompute: ComputeManifestShape | undefined\nlet lastComputePageInfo: boolean | undefined\n\nexport async function computeFromManifest(\n  manifest: BuildManifest,\n  distPath: string,\n  gzipSize: boolean = true,\n  pageInfos?: Map<string, PageInfo>\n): Promise<ComputeManifestShape> {\n  if (\n    Object.is(cachedBuildManifest, manifest) &&\n    lastComputePageInfo === !!pageInfos\n  ) {\n    return lastCompute!\n  }\n\n  let expected = 0\n  const files = new Map<string, number>()\n  Object.keys(manifest.pages).forEach((key) => {\n    if (pageInfos) {\n      const pageInfo = pageInfos.get(key)\n      // don't include AMP pages since they don't rely on shared bundles\n      // AMP First pages are not under the pageInfos key\n      if (pageInfo?.isHybridAmp) {\n        return\n      }\n    }\n\n    ++expected\n    manifest.pages[key].forEach((file) => {\n      if (key === '/_app') {\n        files.set(file, Infinity)\n      } else if (files.has(file)) {\n        files.set(file, files.get(file)! + 1)\n      } else {\n        files.set(file, 1)\n      }\n    })\n  })\n\n  const getSize = gzipSize ? fsStatGzip : fsStat\n\n  const commonFiles = [...files.entries()]\n    .filter(([, len]) => len === expected || len === Infinity)\n    .map(([f]) => f)\n  const uniqueFiles = [...files.entries()]\n    .filter(([, len]) => len === 1)\n    .map(([f]) => f)\n\n  let stats: [string, number][]\n  try {\n    stats = await Promise.all(\n      commonFiles.map(\n        async (f) =>\n          [f, await getSize(path.join(distPath, f))] as [string, number]\n      )\n    )\n  } catch (_) {\n    stats = []\n  }\n\n  let uniqueStats: [string, number][]\n  try {\n    uniqueStats = await Promise.all(\n      uniqueFiles.map(\n        async (f) =>\n          [f, await getSize(path.join(distPath, f))] as [string, number]\n      )\n    )\n  } catch (_) {\n    uniqueStats = []\n  }\n\n  lastCompute = {\n    commonFiles,\n    uniqueFiles,\n    sizeUniqueFiles: uniqueStats.reduce(\n      (obj, n) => Object.assign(obj, { [n[0]]: n[1] }),\n      {}\n    ),\n    sizeCommonFile: stats.reduce(\n      (obj, n) => Object.assign(obj, { [n[0]]: n[1] }),\n      {}\n    ),\n    sizeCommonFiles: stats.reduce((size, [f, stat]) => {\n      if (f.endsWith('.css')) return size\n      return size + stat\n    }, 0),\n  }\n\n  cachedBuildManifest = manifest\n  lastComputePageInfo = !!pageInfos\n  return lastCompute!\n}\n\nexport function difference<T>(main: T[] | Set<T>, sub: T[] | Set<T>): T[] {\n  const a = new Set(main)\n  const b = new Set(sub)\n  return [...a].filter((x) => !b.has(x))\n}\n\nfunction intersect<T>(main: T[], sub: T[]): T[] {\n  const a = new Set(main)\n  const b = new Set(sub)\n  return [...new Set([...a].filter((x) => b.has(x)))]\n}\n\nfunction sum(a: number[]): number {\n  return a.reduce((size, stat) => size + stat, 0)\n}\n\nexport async function getJsPageSizeInKb(\n  page: string,\n  distPath: string,\n  buildManifest: BuildManifest,\n  gzipSize: boolean = true,\n  computedManifestData?: ComputeManifestShape\n): Promise<[number, number]> {\n  const data =\n    computedManifestData ||\n    (await computeFromManifest(buildManifest, distPath, gzipSize))\n\n  const fnFilterJs = (entry: string) => entry.endsWith('.js')\n\n  const pageFiles = (\n    buildManifest.pages[denormalizePagePath(page)] || []\n  ).filter(fnFilterJs)\n  const appFiles = (buildManifest.pages['/_app'] || []).filter(fnFilterJs)\n\n  const fnMapRealPath = (dep: string) => `${distPath}/${dep}`\n\n  const allFilesReal = [...new Set([...pageFiles, ...appFiles])].map(\n    fnMapRealPath\n  )\n  const selfFilesReal = difference(\n    intersect(pageFiles, data.uniqueFiles),\n    data.commonFiles\n  ).map(fnMapRealPath)\n\n  const getSize = gzipSize ? fsStatGzip : fsStat\n\n  try {\n    // Doesn't use `Promise.all`, as we'd double compute duplicate files. This\n    // function is memoized, so the second one will instantly resolve.\n    const allFilesSize = sum(await Promise.all(allFilesReal.map(getSize)))\n    const selfFilesSize = sum(await Promise.all(selfFilesReal.map(getSize)))\n\n    return [selfFilesSize, allFilesSize]\n  } catch (_) {}\n  return [-1, -1]\n}\n\nexport async function buildStaticPaths(\n  page: string,\n  getStaticPaths: GetStaticPaths,\n  locales?: string[],\n  defaultLocale?: string\n): Promise<\n  Omit<UnwrapPromise<ReturnType<GetStaticPaths>>, 'paths'> & {\n    paths: string[]\n    encodedPaths: string[]\n  }\n> {\n  const prerenderPaths = new Set<string>()\n  const encodedPrerenderPaths = new Set<string>()\n  const _routeRegex = getRouteRegex(page)\n  const _routeMatcher = getRouteMatcher(_routeRegex)\n\n  // Get the default list of allowed params.\n  const _validParamKeys = Object.keys(_routeMatcher(page))\n\n  const staticPathsResult = await getStaticPaths({ locales, defaultLocale })\n\n  const expectedReturnVal =\n    `Expected: { paths: [], fallback: boolean }\\n` +\n    `See here for more info: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`\n\n  if (\n    !staticPathsResult ||\n    typeof staticPathsResult !== 'object' ||\n    Array.isArray(staticPathsResult)\n  ) {\n    throw new Error(\n      `Invalid value returned from getStaticPaths in ${page}. Received ${typeof staticPathsResult} ${expectedReturnVal}`\n    )\n  }\n\n  const invalidStaticPathKeys = Object.keys(staticPathsResult).filter(\n    (key) => !(key === 'paths' || key === 'fallback')\n  )\n\n  if (invalidStaticPathKeys.length > 0) {\n    throw new Error(\n      `Extra keys returned from getStaticPaths in ${page} (${invalidStaticPathKeys.join(\n        ', '\n      )}) ${expectedReturnVal}`\n    )\n  }\n\n  if (\n    !(\n      typeof staticPathsResult.fallback === 'boolean' ||\n      staticPathsResult.fallback === 'blocking'\n    )\n  ) {\n    throw new Error(\n      `The \\`fallback\\` key must be returned from getStaticPaths in ${page}.\\n` +\n        expectedReturnVal\n    )\n  }\n\n  const toPrerender = staticPathsResult.paths\n\n  if (!Array.isArray(toPrerender)) {\n    throw new Error(\n      `Invalid \\`paths\\` value returned from getStaticPaths in ${page}.\\n` +\n        `\\`paths\\` must be an array of strings or objects of shape { params: [key: string]: string }`\n    )\n  }\n\n  toPrerender.forEach((entry) => {\n    // For a string-provided path, we must make sure it matches the dynamic\n    // route.\n    if (typeof entry === 'string') {\n      entry = removePathTrailingSlash(entry)\n\n      const localePathResult = normalizeLocalePath(entry, locales)\n      let cleanedEntry = entry\n\n      if (localePathResult.detectedLocale) {\n        cleanedEntry = entry.substr(localePathResult.detectedLocale.length + 1)\n      } else if (defaultLocale) {\n        entry = `/${defaultLocale}${entry}`\n      }\n\n      const result = _routeMatcher(cleanedEntry)\n      if (!result) {\n        throw new Error(\n          `The provided path \\`${cleanedEntry}\\` does not match the page: \\`${page}\\`.`\n        )\n      }\n\n      // If leveraging the string paths variant the entry should already be\n      // encoded so we decode the segments ensuring we only escape path\n      // delimiters\n      prerenderPaths.add(\n        entry\n          .split('/')\n          .map((segment) =>\n            escapePathDelimiters(decodeURIComponent(segment), true)\n          )\n          .join('/')\n      )\n      encodedPrerenderPaths.add(entry)\n    }\n    // For the object-provided path, we must make sure it specifies all\n    // required keys.\n    else {\n      const invalidKeys = Object.keys(entry).filter(\n        (key) => key !== 'params' && key !== 'locale'\n      )\n\n      if (invalidKeys.length) {\n        throw new Error(\n          `Additional keys were returned from \\`getStaticPaths\\` in page \"${page}\". ` +\n            `URL Parameters intended for this dynamic route must be nested under the \\`params\\` key, i.e.:` +\n            `\\n\\n\\treturn { params: { ${_validParamKeys\n              .map((k) => `${k}: ...`)\n              .join(', ')} } }` +\n            `\\n\\nKeys that need to be moved: ${invalidKeys.join(', ')}.\\n`\n        )\n      }\n\n      const { params = {} } = entry\n      let builtPage = page\n      let encodedBuiltPage = page\n\n      _validParamKeys.forEach((validParamKey) => {\n        const { repeat, optional } = _routeRegex.groups[validParamKey]\n        let paramValue = params[validParamKey]\n        if (\n          optional &&\n          params.hasOwnProperty(validParamKey) &&\n          (paramValue === null ||\n            paramValue === undefined ||\n            (paramValue as any) === false)\n        ) {\n          paramValue = []\n        }\n        if (\n          (repeat && !Array.isArray(paramValue)) ||\n          (!repeat && typeof paramValue !== 'string')\n        ) {\n          throw new Error(\n            `A required parameter (${validParamKey}) was not provided as ${\n              repeat ? 'an array' : 'a string'\n            } in getStaticPaths for ${page}`\n          )\n        }\n        let replaced = `[${repeat ? '...' : ''}${validParamKey}]`\n        if (optional) {\n          replaced = `[${replaced}]`\n        }\n        builtPage = builtPage\n          .replace(\n            replaced,\n            repeat\n              ? (paramValue as string[])\n                  .map((segment) => escapePathDelimiters(segment, true))\n                  .join('/')\n              : escapePathDelimiters(paramValue as string, true)\n          )\n          .replace(/(?!^)\\/$/, '')\n\n        encodedBuiltPage = encodedBuiltPage\n          .replace(\n            replaced,\n            repeat\n              ? (paramValue as string[]).map(encodeURIComponent).join('/')\n              : encodeURIComponent(paramValue as string)\n          )\n          .replace(/(?!^)\\/$/, '')\n      })\n\n      if (entry.locale && !locales?.includes(entry.locale)) {\n        throw new Error(\n          `Invalid locale returned from getStaticPaths for ${page}, the locale ${entry.locale} is not specified in next.config.js`\n        )\n      }\n      const curLocale = entry.locale || defaultLocale || ''\n\n      prerenderPaths.add(\n        `${curLocale ? `/${curLocale}` : ''}${\n          curLocale && builtPage === '/' ? '' : builtPage\n        }`\n      )\n      encodedPrerenderPaths.add(\n        `${curLocale ? `/${curLocale}` : ''}${\n          curLocale && encodedBuiltPage === '/' ? '' : encodedBuiltPage\n        }`\n      )\n    }\n  })\n\n  return {\n    paths: [...prerenderPaths],\n    fallback: staticPathsResult.fallback,\n    encodedPaths: [...encodedPrerenderPaths],\n  }\n}\n\nexport async function isPageStatic(\n  page: string,\n  distDir: string,\n  serverless: boolean,\n  runtimeEnvConfig: any,\n  locales?: string[],\n  defaultLocale?: string,\n  parentId?: any\n): Promise<{\n  isStatic?: boolean\n  isAmpOnly?: boolean\n  isHybridAmp?: boolean\n  hasServerProps?: boolean\n  hasStaticProps?: boolean\n  prerenderRoutes?: string[]\n  encodedPrerenderRoutes?: string[]\n  prerenderFallback?: boolean | 'blocking'\n  isNextImageImported?: boolean\n}> {\n  const isPageStaticSpan = trace('is-page-static-utils', parentId)\n  return isPageStaticSpan.traceAsyncFn(async () => {\n    try {\n      require('../next-server/lib/runtime-config').setConfig(runtimeEnvConfig)\n      const components = await loadComponents(distDir, page, serverless)\n      const mod = components.ComponentMod\n      const Comp = mod.default || mod\n\n      if (!Comp || !isValidElementType(Comp) || typeof Comp === 'string') {\n        throw new Error('INVALID_DEFAULT_EXPORT')\n      }\n\n      const hasGetInitialProps = !!(Comp as any).getInitialProps\n      const hasStaticProps = !!(await mod.getStaticProps)\n      const hasStaticPaths = !!(await mod.getStaticPaths)\n      const hasServerProps = !!(await mod.getServerSideProps)\n      const hasLegacyServerProps = !!(await mod.unstable_getServerProps)\n      const hasLegacyStaticProps = !!(await mod.unstable_getStaticProps)\n      const hasLegacyStaticPaths = !!(await mod.unstable_getStaticPaths)\n      const hasLegacyStaticParams = !!(await mod.unstable_getStaticParams)\n\n      if (hasLegacyStaticParams) {\n        throw new Error(\n          `unstable_getStaticParams was replaced with getStaticPaths. Please update your code.`\n        )\n      }\n\n      if (hasLegacyStaticPaths) {\n        throw new Error(\n          `unstable_getStaticPaths was replaced with getStaticPaths. Please update your code.`\n        )\n      }\n\n      if (hasLegacyStaticProps) {\n        throw new Error(\n          `unstable_getStaticProps was replaced with getStaticProps. Please update your code.`\n        )\n      }\n\n      if (hasLegacyServerProps) {\n        throw new Error(\n          `unstable_getServerProps was replaced with getServerSideProps. Please update your code.`\n        )\n      }\n\n      // A page cannot be prerendered _and_ define a data requirement. That's\n      // contradictory!\n      if (hasGetInitialProps && hasStaticProps) {\n        throw new Error(SSG_GET_INITIAL_PROPS_CONFLICT)\n      }\n\n      if (hasGetInitialProps && hasServerProps) {\n        throw new Error(SERVER_PROPS_GET_INIT_PROPS_CONFLICT)\n      }\n\n      if (hasStaticProps && hasServerProps) {\n        throw new Error(SERVER_PROPS_SSG_CONFLICT)\n      }\n\n      const pageIsDynamic = isDynamicRoute(page)\n      // A page cannot have static parameters if it is not a dynamic page.\n      if (hasStaticProps && hasStaticPaths && !pageIsDynamic) {\n        throw new Error(\n          `getStaticPaths can only be used with dynamic pages, not '${page}'.` +\n            `\\nLearn more: https://nextjs.org/docs/routing/dynamic-routes`\n        )\n      }\n\n      if (hasStaticProps && pageIsDynamic && !hasStaticPaths) {\n        throw new Error(\n          `getStaticPaths is required for dynamic SSG pages and is missing for '${page}'.` +\n            `\\nRead more: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`\n        )\n      }\n\n      let prerenderRoutes: Array<string> | undefined\n      let encodedPrerenderRoutes: Array<string> | undefined\n      let prerenderFallback: boolean | 'blocking' | undefined\n      if (hasStaticProps && hasStaticPaths) {\n        ;({\n          paths: prerenderRoutes,\n          fallback: prerenderFallback,\n          encodedPaths: encodedPrerenderRoutes,\n        } = await buildStaticPaths(\n          page,\n          mod.getStaticPaths,\n          locales,\n          defaultLocale\n        ))\n      }\n\n      const isNextImageImported = (global as any).__NEXT_IMAGE_IMPORTED\n      const config = mod.config || {}\n      return {\n        isStatic: !hasStaticProps && !hasGetInitialProps && !hasServerProps,\n        isHybridAmp: config.amp === 'hybrid',\n        isAmpOnly: config.amp === true,\n        prerenderRoutes,\n        prerenderFallback,\n        encodedPrerenderRoutes,\n        hasStaticProps,\n        hasServerProps,\n        isNextImageImported,\n      }\n    } catch (err) {\n      if (err.code === 'MODULE_NOT_FOUND') return {}\n      throw err\n    }\n  })\n}\n\nexport async function hasCustomGetInitialProps(\n  page: string,\n  distDir: string,\n  isLikeServerless: boolean,\n  runtimeEnvConfig: any,\n  checkingApp: boolean\n): Promise<boolean> {\n  require('../next-server/lib/runtime-config').setConfig(runtimeEnvConfig)\n\n  const components = await loadComponents(distDir, page, isLikeServerless)\n  let mod = components.ComponentMod\n\n  if (checkingApp) {\n    mod = (await mod._app) || mod.default || mod\n  } else {\n    mod = mod.default || mod\n  }\n  mod = await mod\n  return mod.getInitialProps !== mod.origGetInitialProps\n}\n\nexport async function getNamedExports(\n  page: string,\n  distDir: string,\n  isLikeServerless: boolean,\n  runtimeEnvConfig: any\n): Promise<Array<string>> {\n  require('../next-server/lib/runtime-config').setConfig(runtimeEnvConfig)\n  const components = await loadComponents(distDir, page, isLikeServerless)\n  let mod = components.ComponentMod\n\n  return Object.keys(mod)\n}\n\nexport function detectConflictingPaths(\n  combinedPages: string[],\n  ssgPages: Set<string>,\n  additionalSsgPaths: Map<string, string[]>\n) {\n  const conflictingPaths = new Map<\n    string,\n    Array<{\n      path: string\n      page: string\n    }>\n  >()\n\n  const dynamicSsgPages = [...ssgPages].filter((page) => isDynamicRoute(page))\n\n  additionalSsgPaths.forEach((paths, pathsPage) => {\n    paths.forEach((curPath) => {\n      const lowerPath = curPath.toLowerCase()\n      let conflictingPage = combinedPages.find(\n        (page) => page.toLowerCase() === lowerPath\n      )\n\n      if (conflictingPage) {\n        conflictingPaths.set(lowerPath, [\n          { path: curPath, page: pathsPage },\n          { path: conflictingPage, page: conflictingPage },\n        ])\n      } else {\n        let conflictingPath: string | undefined\n\n        conflictingPage = dynamicSsgPages.find((page) => {\n          if (page === pathsPage) return false\n\n          conflictingPath = additionalSsgPaths\n            .get(page)\n            ?.find((compPath) => compPath.toLowerCase() === lowerPath)\n          return conflictingPath\n        })\n\n        if (conflictingPage && conflictingPath) {\n          conflictingPaths.set(lowerPath, [\n            { path: curPath, page: pathsPage },\n            { path: conflictingPath, page: conflictingPage },\n          ])\n        }\n      }\n    })\n  })\n\n  if (conflictingPaths.size > 0) {\n    let conflictingPathsOutput = ''\n\n    conflictingPaths.forEach((pathItems) => {\n      pathItems.forEach((pathItem, idx) => {\n        const isDynamic = pathItem.page !== pathItem.path\n\n        if (idx > 0) {\n          conflictingPathsOutput += 'conflicts with '\n        }\n\n        conflictingPathsOutput += `path: \"${pathItem.path}\"${\n          isDynamic ? ` from page: \"${pathItem.page}\" ` : ' '\n        }`\n      })\n      conflictingPathsOutput += '\\n'\n    })\n\n    Log.error(\n      'Conflicting paths returned from getStaticPaths, paths must unique per page.\\n' +\n        'See more info here: https://nextjs.org/docs/messages/conflicting-ssg-paths\\n\\n' +\n        conflictingPathsOutput\n    )\n    process.exit(1)\n  }\n}\n\nexport function getCssFilePaths(buildManifest: BuildManifest): string[] {\n  const cssFiles = new Set<string>()\n  Object.values(buildManifest.pages).forEach((files) => {\n    files.forEach((file) => {\n      if (file.endsWith('.css')) {\n        cssFiles.add(file)\n      }\n    })\n  })\n\n  return [...cssFiles]\n}\n"]}