{"version": 3, "sources": ["../../../next-server/lib/to-base-64.ts"], "names": ["toBase64", "str", "window", "<PERSON><PERSON><PERSON>", "from", "toString", "btoa"], "mappings": "+DAAA;AACA;AACA,GACO,QAASA,CAAAA,QAAT,CAAkBC,GAAlB,CAA+B,CACpC,GAAI,MAAOC,CAAAA,MAAP,GAAkB,WAAtB,CAAmC,CACjC,MAAOC,CAAAA,MAAM,CAACC,IAAP,CAAYH,GAAZ,EAAiBI,QAAjB,CAA0B,QAA1B,CAAP,CACD,CAFD,IAEO,CACL,MAAOH,CAAAA,MAAM,CAACI,IAAP,CAAYL,GAAZ,CAAP,CACD,CACF", "sourcesContent": ["/**\n * Isomorphic base64 that works on the server and client\n */\nexport function toBase64(str: string) {\n  if (typeof window === 'undefined') {\n    return Buffer.from(str).toString('base64')\n  } else {\n    return window.btoa(str)\n  }\n}\n"]}