{"version": 3, "sources": ["../../../lib/typescript/runTypeCheck.ts"], "names": ["runTypeCheck", "ts", "baseDir", "tsConfigPath", "cacheDir", "effectiveConfiguration", "fileNames", "length", "hasWarnings", "inputFilesCount", "totalFilesCount", "incremental", "requiredConfig", "options", "noEmit", "program", "createIncrementalProgram", "rootNames", "tsBuildInfoFile", "path", "join", "createProgram", "result", "emit", "regexIgnoredFile", "allDiagnostics", "getPreEmitDiagnostics", "concat", "diagnostics", "filter", "d", "file", "test", "fileName", "firstError", "find", "category", "DiagnosticCategory", "Error", "Boolean", "CompileError", "warnings", "Promise", "all", "Warning", "map", "getSourceFiles"], "mappings": "uEAAA,kDACA,0DAIA,wEACA,wEAEA,8C,mFAUO,cAAeA,CAAAA,YAAf,CACLC,EADK,CAELC,OAFK,CAGLC,YAHK,CAILC,QAJK,CAKqB,0BAC1B,KAAMC,CAAAA,sBAAsB,CAAG,KAAM,2DACnCJ,EADmC,CAEnCE,YAFmC,CAArC,CAKA,GAAIE,sBAAsB,CAACC,SAAvB,CAAiCC,MAAjC,CAA0C,CAA9C,CAAiD,CAC/C,MAAO,CACLC,WAAW,CAAE,KADR,CAELC,eAAe,CAAE,CAFZ,CAGLC,eAAe,CAAE,CAHZ,CAILC,WAAW,CAAE,KAJR,CAAP,CAMD,CACD,KAAMC,CAAAA,cAAc,CAAG,yDAAyBX,EAAzB,CAAvB,CAEA,KAAMY,CAAAA,OAAO,CAAG,CACd,GAAGR,sBAAsB,CAACQ,OADZ,CAEd,GAAGD,cAFW,CAGdE,MAAM,CAAE,IAHM,CAAhB,CAMA,GAAIC,CAAAA,OAAJ,CAGA,GAAIJ,CAAAA,WAAW,CAAG,KAAlB,CACA,GAAIE,OAAO,CAACF,WAAR,EAAuBP,QAA3B,CAAqC,CACnCO,WAAW,CAAG,IAAd,CACAI,OAAO,CAAGd,EAAE,CAACe,wBAAH,CAA4B,CACpCC,SAAS,CAAEZ,sBAAsB,CAACC,SADE,CAEpCO,OAAO,CAAE,CACP,GAAGA,OADI,CAEPF,WAAW,CAAE,IAFN,CAGPO,eAAe,CAAEC,cAAKC,IAAL,CAAUhB,QAAV,CAAoB,cAApB,CAHV,CAF2B,CAA5B,CAAV,CAQD,CAVD,IAUO,CACLW,OAAO,CAAGd,EAAE,CAACoB,aAAH,CAAiBhB,sBAAsB,CAACC,SAAxC,CAAmDO,OAAnD,CAAV,CACD,CACD,KAAMS,CAAAA,MAAM,CAAGP,OAAO,CAACQ,IAAR,EAAf,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAMC,CAAAA,gBAAgB,CAAG,kEAAzB,CACA,KAAMC,CAAAA,cAAc,CAAGxB,EAAE,CACtByB,qBADoB,CACEX,OADF,EAEpBY,MAFoB,CAEbL,MAAM,CAACM,WAFM,EAGpBC,MAHoB,CAGZC,CAAD,EAAO,EAAEA,CAAC,CAACC,IAAF,EAAUP,gBAAgB,CAACQ,IAAjB,CAAsBF,CAAC,CAACC,IAAF,CAAOE,QAA7B,CAAZ,CAHM,CAAvB,CAKA,KAAMC,CAAAA,UAAU,uBACdT,cAAc,CAACU,IAAf,CACGL,CAAD,EAAOA,CAAC,CAACM,QAAF,GAAeC,wCAAmBC,KAAlC,EAA2CC,OAAO,CAACT,CAAC,CAACC,IAAH,CAD3D,CADc,6BAGTN,cAAc,CAACU,IAAf,CAAqBL,CAAD,EAAOA,CAAC,CAACM,QAAF,GAAeC,wCAAmBC,KAA7D,CAHP,CAKA,GAAIJ,UAAJ,CAAgB,CACd,KAAM,IAAIM,2BAAJ,CACJ,KAAM,gDAAuBvC,EAAvB,CAA2BC,OAA3B,CAAoCgC,UAApC,CADF,CAAN,CAGD,CAED,KAAMO,CAAAA,QAAQ,CAAG,KAAMC,CAAAA,OAAO,CAACC,GAAR,CACrBlB,cAAc,CACXI,MADH,CACWC,CAAD,EAAOA,CAAC,CAACM,QAAF,GAAeC,wCAAmBO,OADnD,EAEGC,GAFH,CAEQf,CAAD,EAAO,gDAAuB7B,EAAvB,CAA2BC,OAA3B,CAAoC4B,CAApC,CAFd,CADqB,CAAvB,CAKA,MAAO,CACLtB,WAAW,CAAE,IADR,CAELiC,QAFK,CAGLhC,eAAe,CAAEJ,sBAAsB,CAACC,SAAvB,CAAiCC,MAH7C,CAILG,eAAe,CAAEK,OAAO,CAAC+B,cAAR,GAAyBvC,MAJrC,CAKLI,WALK,CAAP,CAOD", "sourcesContent": ["import path from 'path'\nimport {\n  DiagnosticCategory,\n  getFormattedDiagnostic,\n} from './diagnosticFormatter'\nimport { getTypeScriptConfiguration } from './getTypeScriptConfiguration'\nimport { getRequiredConfiguration } from './writeConfigurationDefaults'\n\nimport { CompileError } from '../compile-error'\n\nexport interface TypeCheckResult {\n  hasWarnings: boolean\n  warnings?: string[]\n  inputFilesCount: number\n  totalFilesCount: number\n  incremental: boolean\n}\n\nexport async function runTypeCheck(\n  ts: typeof import('typescript'),\n  baseDir: string,\n  tsConfigPath: string,\n  cacheDir?: string\n): Promise<TypeCheckResult> {\n  const effectiveConfiguration = await getTypeScriptConfiguration(\n    ts,\n    tsConfigPath\n  )\n\n  if (effectiveConfiguration.fileNames.length < 1) {\n    return {\n      hasWarnings: false,\n      inputFilesCount: 0,\n      totalFilesCount: 0,\n      incremental: false,\n    }\n  }\n  const requiredConfig = getRequiredConfiguration(ts)\n\n  const options = {\n    ...effectiveConfiguration.options,\n    ...requiredConfig,\n    noEmit: true,\n  }\n\n  let program:\n    | import('typescript').Program\n    | import('typescript').BuilderProgram\n  let incremental = false\n  if (options.incremental && cacheDir) {\n    incremental = true\n    program = ts.createIncrementalProgram({\n      rootNames: effectiveConfiguration.fileNames,\n      options: {\n        ...options,\n        incremental: true,\n        tsBuildInfoFile: path.join(cacheDir, '.tsbuildinfo'),\n      },\n    })\n  } else {\n    program = ts.createProgram(effectiveConfiguration.fileNames, options)\n  }\n  const result = program.emit()\n\n  // Intended to match:\n  // - pages/test.js\n  // - pages/apples.test.js\n  // - pages/__tests__/a.js\n  //\n  // But not:\n  // - pages/contest.js\n  // - pages/other.js\n  // - pages/test/a.js\n  //\n  const regexIgnoredFile = /[\\\\/]__(?:tests|mocks)__[\\\\/]|(?<=[\\\\/.])(?:spec|test)\\.[^\\\\/]+$/\n  const allDiagnostics = ts\n    .getPreEmitDiagnostics(program as import('typescript').Program)\n    .concat(result.diagnostics)\n    .filter((d) => !(d.file && regexIgnoredFile.test(d.file.fileName)))\n\n  const firstError =\n    allDiagnostics.find(\n      (d) => d.category === DiagnosticCategory.Error && Boolean(d.file)\n    ) ?? allDiagnostics.find((d) => d.category === DiagnosticCategory.Error)\n\n  if (firstError) {\n    throw new CompileError(\n      await getFormattedDiagnostic(ts, baseDir, firstError)\n    )\n  }\n\n  const warnings = await Promise.all(\n    allDiagnostics\n      .filter((d) => d.category === DiagnosticCategory.Warning)\n      .map((d) => getFormattedDiagnostic(ts, baseDir, d))\n  )\n  return {\n    hasWarnings: true,\n    warnings,\n    inputFilesCount: effectiveConfiguration.fileNames.length,\n    totalFilesCount: program.getSourceFiles().length,\n    incremental,\n  }\n}\n"]}