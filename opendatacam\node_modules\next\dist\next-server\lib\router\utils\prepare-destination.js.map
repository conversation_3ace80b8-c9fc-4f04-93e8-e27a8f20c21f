{"version": 3, "sources": ["../../../../../next-server/lib/router/utils/prepare-destination.ts"], "names": ["getSafeParamName", "paramName", "newParamName", "i", "length", "charCode", "charCodeAt", "matchHas", "req", "has", "query", "params", "allMatch", "every", "hasItem", "value", "key", "type", "toLowerCase", "headers", "cookies", "host", "hostname", "split", "matcher", "RegExp", "matches", "match", "groups", "Object", "keys", "for<PERSON>ach", "groupKey", "compileNonPath", "includes", "replace", "pathToRegexp", "compile", "validate", "substr", "prepareDestination", "destination", "appendParamsToQuery", "parsedDestination", "assign", "hadLocale", "__next<PERSON><PERSON><PERSON>", "__nextDefaultLocale", "startsWith", "pathname", "searchParams", "hash", "port", "protocol", "search", "href", "URL", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "destPath", "destPathPara<PERSON><PERSON><PERSON>s", "destPathParams", "map", "name", "destinationCompiler", "newUrl", "strOrArray", "entries", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "some", "err", "message", "Error"], "mappings": "wKAEA,0CACA,sDACA,uF,qzBAKA;AACA;AACO,KAAMA,CAAAA,gBAAgB,CAAIC,SAAD,EAAuB,CACrD,GAAIC,CAAAA,YAAY,CAAG,EAAnB,CAEA,IAAK,GAAIC,CAAAA,CAAC,CAAG,CAAb,CAAgBA,CAAC,CAAGF,SAAS,CAACG,MAA9B,CAAsCD,CAAC,EAAvC,CAA2C,CACzC,KAAME,CAAAA,QAAQ,CAAGJ,SAAS,CAACK,UAAV,CAAqBH,CAArB,CAAjB,CAEA,GACGE,QAAQ,CAAG,EAAX,EAAiBA,QAAQ,CAAG,EAA7B,EAAoC;AACnCA,QAAQ,CAAG,EAAX,EAAiBA,QAAQ,CAAG,GAAK;AAFpC,CAGE,CACAH,YAAY,EAAID,SAAS,CAACE,CAAD,CAAzB,CACD,CACF,CACD,MAAOD,CAAAA,YAAP,CACD,CAdM,C,0CAgBA,QAASK,CAAAA,QAAT,CACLC,GADK,CAELC,GAFK,CAGLC,KAHK,CAIW,CAChB,KAAMC,CAAAA,MAAc,CAAG,EAAvB,CACA,KAAMC,CAAAA,QAAQ,CAAGH,GAAG,CAACI,KAAJ,CAAWC,OAAD,EAAa,CACtC,GAAIC,CAAAA,KAAJ,CACA,GAAIC,CAAAA,GAAG,CAAGF,OAAO,CAACE,GAAlB,CAEA,OAAQF,OAAO,CAACG,IAAhB,EACE,IAAK,QAAL,CAAe,CACbD,GAAG,CAAGA,GAAG,CAAEE,WAAL,EAAN,CACAH,KAAK,CAAGP,GAAG,CAACW,OAAJ,CAAYH,GAAZ,CAAR,CACA,MACD,CACD,IAAK,QAAL,CAAe,CACbD,KAAK,CAAIP,GAAD,CAAaY,OAAb,CAAqBN,OAAO,CAACE,GAA7B,CAAR,CACA,MACD,CACD,IAAK,OAAL,CAAc,CACZD,KAAK,CAAGL,KAAK,CAACM,GAAD,CAAb,CACA,MACD,CACD,IAAK,MAAL,CAAa,CACX,KAAM,CAAEK,IAAF,EAAW,CAAAb,GAAG,MAAH,QAAAA,GAAG,CAAEW,OAAL,GAAgB,EAAjC,CACA;AACA,KAAMG,CAAAA,QAAQ,CAAGD,IAAH,cAAGA,IAAI,CAAEE,KAAN,CAAY,GAAZ,EAAiB,CAAjB,EAAoBL,WAApB,EAAjB,CACAH,KAAK,CAAGO,QAAR,CACA,MACD,CACD,QAAS,CACP,MACD,CAvBH,CA0BA,GAAI,CAACR,OAAO,CAACC,KAAT,EAAkBA,KAAtB,CAA6B,CAC3BJ,MAAM,CAACX,gBAAgB,CAACgB,GAAD,CAAjB,CAAN,CAAiCD,KAAjC,CACA,MAAO,KAAP,CACD,CAHD,IAGO,IAAIA,KAAJ,CAAW,CAChB,KAAMS,CAAAA,OAAO,CAAG,GAAIC,CAAAA,MAAJ,CAAY,IAAGX,OAAO,CAACC,KAAM,GAA7B,CAAhB,CACA,KAAMW,CAAAA,OAAO,CAAGX,KAAK,CAACY,KAAN,CAAYH,OAAZ,CAAhB,CAEA,GAAIE,OAAJ,CAAa,CACX,GAAIA,OAAO,CAACE,MAAZ,CAAoB,CAClBC,MAAM,CAACC,IAAP,CAAYJ,OAAO,CAACE,MAApB,EAA4BG,OAA5B,CAAqCC,QAAD,EAAc,CAChDrB,MAAM,CAACqB,QAAD,CAAN,CAAmBN,OAAO,CAACE,MAAR,CAAgBI,QAAhB,CAAnB,CACD,CAFD,EAGD,CAJD,IAIO,IAAIlB,OAAO,CAACG,IAAR,GAAiB,MAAjB,EAA2BS,OAAO,CAAC,CAAD,CAAtC,CAA2C,CAChDf,MAAM,CAACU,IAAP,CAAcK,OAAO,CAAC,CAAD,CAArB,CACD,CACD,MAAO,KAAP,CACD,CACF,CACD,MAAO,MAAP,CACD,CAjDgB,CAAjB,CAmDA,GAAId,QAAJ,CAAc,CACZ,MAAOD,CAAAA,MAAP,CACD,CACD,MAAO,MAAP,CACD,CAEM,QAASsB,CAAAA,cAAT,CAAwBlB,KAAxB,CAAuCJ,MAAvC,CAA+D,CACpE,GAAI,CAACI,KAAK,CAACmB,QAAN,CAAe,GAAf,CAAL,CAA0B,CACxB,MAAOnB,CAAAA,KAAP,CACD,CAED,IAAK,KAAMC,CAAAA,GAAX,GAAkBa,CAAAA,MAAM,CAACC,IAAP,CAAYnB,MAAZ,CAAlB,CAAuC,CACrC,GAAII,KAAK,CAACmB,QAAN,CAAgB,IAAGlB,GAAI,EAAvB,CAAJ,CAA+B,CAC7BD,KAAK,CAAGA,KAAK,CACVoB,OADK,CAEJ,GAAIV,CAAAA,MAAJ,CAAY,IAAGT,GAAI,KAAnB,CAAyB,GAAzB,CAFI,CAGH,IAAGA,GAAI,2BAHJ,EAKLmB,OALK,CAMJ,GAAIV,CAAAA,MAAJ,CAAY,IAAGT,GAAI,KAAnB,CAAyB,GAAzB,CANI,CAOH,IAAGA,GAAI,0BAPJ,EASLmB,OATK,CASG,GAAIV,CAAAA,MAAJ,CAAY,IAAGT,GAAI,KAAnB,CAAyB,GAAzB,CATH,CASmC,IAAGA,GAAI,sBAT1C,EAULmB,OAVK,CAWJ,GAAIV,CAAAA,MAAJ,CAAY,IAAGT,GAAI,SAAnB,CAA6B,GAA7B,CAXI,CAYH,wBAAuBA,GAAI,EAZxB,CAAR,CAcD,CACF,CACDD,KAAK,CAAGA,KAAK,CACVoB,OADK,CACG,2BADH,CACgC,MADhC,EAELA,OAFK,CAEG,uBAFH,CAE4B,GAF5B,EAGLA,OAHK,CAGG,wBAHH,CAG6B,GAH7B,EAILA,OAJK,CAIG,2BAJH,CAIgC,GAJhC,EAKLA,OALK,CAKG,4BALH,CAKiC,GALjC,CAAR,CAOA;AACA;AACA,MAAOC,CAAAA,YAAY,CAChBC,OADI,CACK,IAAGtB,KAAM,EADd,CACiB,CAAEuB,QAAQ,CAAE,KAAZ,CADjB,EACsC3B,MADtC,EAEJ4B,MAFI,CAEG,CAFH,CAAP,CAGD,CAEc,QAASC,CAAAA,kBAAT,CACbC,WADa,CAEb9B,MAFa,CAGbD,KAHa,CAIbgC,mBAJa,CAKb,CACA,GAAIC,CAAAA,iBAKmC,CAAG,EAL1C,CAOA;AACAjC,KAAK,CAAGmB,MAAM,CAACe,MAAP,CAAc,EAAd,CAAkBlC,KAAlB,CAAR,CACA,KAAMmC,CAAAA,SAAS,CAAGnC,KAAK,CAACoC,YAAxB,CACA,MAAOpC,CAAAA,KAAK,CAACoC,YAAb,CACA,MAAOpC,CAAAA,KAAK,CAACqC,mBAAb,CAEA,GAAIN,WAAW,CAACO,UAAZ,CAAuB,GAAvB,CAAJ,CAAiC,CAC/BL,iBAAiB,CAAG,uCAAiBF,WAAjB,CAApB,CACD,CAFD,IAEO,CACL,KAAM,CACJQ,QADI,CAEJC,YAFI,CAGJC,IAHI,CAIJ7B,QAJI,CAKJ8B,IALI,CAMJC,QANI,CAOJC,MAPI,CAQJC,IARI,EASF,GAAIC,CAAAA,GAAJ,CAAQf,WAAR,CATJ,CAWAE,iBAAiB,CAAG,CAClBM,QADkB,CAElBvC,KAAK,CAAE,wCAAuBwC,YAAvB,CAFW,CAGlBC,IAHkB,CAIlBE,QAJkB,CAKlB/B,QALkB,CAMlB8B,IANkB,CAOlBE,MAPkB,CAQlBC,IARkB,CAApB,CAUD,CAED,KAAME,CAAAA,SAAS,CAAGd,iBAAiB,CAACjC,KAApC,CACA,KAAMgD,CAAAA,QAAQ,CAAI,GAAEf,iBAAiB,CAACM,QAAU,GAC9CN,iBAAiB,CAACQ,IAAlB,EAA0B,EAC3B,EAFD,CAGA,KAAMQ,CAAAA,iBAAqC,CAAG,EAA9C,CACAvB,YAAY,CAACA,YAAb,CAA0BsB,QAA1B,CAAoCC,iBAApC,EAEA,KAAMC,CAAAA,cAAc,CAAGD,iBAAiB,CAACE,GAAlB,CAAuB7C,GAAD,EAASA,GAAG,CAAC8C,IAAnC,CAAvB,CAEA,GAAIC,CAAAA,mBAAmB,CAAG3B,YAAY,CAACC,OAAb,CACxBqB,QADwB,CAExB;AACA;AACA;AACA;AACA;AACA;AACA,CAAEpB,QAAQ,CAAE,KAAZ,CARwB,CAA1B,CAUA,GAAI0B,CAAAA,MAAJ,CAEA;AACA,IAAK,KAAM,CAAChD,GAAD,CAAMiD,UAAN,CAAX,EAAgCpC,CAAAA,MAAM,CAACqC,OAAP,CAAeT,SAAf,CAAhC,CAA2D,CACzD,GAAI1C,CAAAA,KAAK,CAAGoD,KAAK,CAACC,OAAN,CAAcH,UAAd,EAA4BA,UAAU,CAAC,CAAD,CAAtC,CAA4CA,UAAxD,CACA,GAAIlD,KAAJ,CAAW,CACT;AACA;AACAA,KAAK,CAAGkB,cAAc,CAAClB,KAAD,CAAQJ,MAAR,CAAtB,CACD,CACD8C,SAAS,CAACzC,GAAD,CAAT,CAAiBD,KAAjB,CACD,CAED;AACA;AACA,GAAIsD,CAAAA,SAAS,CAAGxC,MAAM,CAACC,IAAP,CAAYnB,MAAZ,CAAhB,CAEA;AACA,GAAIkC,SAAJ,CAAe,CACbwB,SAAS,CAAGA,SAAS,CAACC,MAAV,CAAkBR,IAAD,EAAUA,IAAI,GAAK,oBAApC,CAAZ,CACD,CAED,GACEpB,mBAAmB,EACnB,CAAC2B,SAAS,CAACE,IAAV,CAAgBvD,GAAD,EAAS4C,cAAc,CAAC1B,QAAf,CAAwBlB,GAAxB,CAAxB,CAFH,CAGE,CACA,IAAK,KAAMA,CAAAA,GAAX,GAAkBqD,CAAAA,SAAlB,CAA6B,CAC3B,GAAI,EAAErD,GAAG,GAAIyC,CAAAA,SAAT,CAAJ,CAAyB,CACvBA,SAAS,CAACzC,GAAD,CAAT,CAAiBL,MAAM,CAACK,GAAD,CAAvB,CACD,CACF,CACF,CAED,GAAI,CACFgD,MAAM,CAAGD,mBAAmB,CAACpD,MAAD,CAA5B,CAEA,KAAM,CAACsC,QAAD,CAAWE,IAAX,EAAmBa,MAAM,CAACzC,KAAP,CAAa,GAAb,CAAzB,CACAoB,iBAAiB,CAACM,QAAlB,CAA6BA,QAA7B,CACAN,iBAAiB,CAACQ,IAAlB,CAA0B,GAAEA,IAAI,CAAG,GAAH,CAAS,EAAG,GAAEA,IAAI,EAAI,EAAG,EAAzD,CACA,MAAQR,CAAAA,iBAAD,CAA2BW,MAAlC,CACD,CAAC,MAAOkB,GAAP,CAAY,CACZ,GAAIA,GAAG,CAACC,OAAJ,CAAY9C,KAAZ,CAAkB,8CAAlB,CAAJ,CAAuE,CACrE,KAAM,IAAI+C,CAAAA,KAAJ,CACH,2KADG,CAAN,CAGD,CACD,KAAMF,CAAAA,GAAN,CACD,CAED;AACA;AACA;AACA;AACA7B,iBAAiB,CAACjC,KAAlB,CAA0B,CACxB,GAAGA,KADqB,CAExB,GAAGiC,iBAAiB,CAACjC,KAFG,CAA1B,CAKA,MAAO,CACLsD,MADK,CAELrB,iBAFK,CAAP,CAID", "sourcesContent": ["import { IncomingMessage } from 'http'\nimport { ParsedUrlQuery } from 'querystring'\nimport { searchParamsToUrlQuery } from './querystring'\nimport { parseRelativeUrl } from './parse-relative-url'\nimport * as pathToRegexp from 'next/dist/compiled/path-to-regexp'\nimport { RouteHas } from '../../../../lib/load-custom-routes'\n\ntype Params = { [param: string]: any }\n\n// ensure only a-zA-Z are used for param names for proper interpolating\n// with path-to-regexp\nexport const getSafeParamName = (paramName: string) => {\n  let newParamName = ''\n\n  for (let i = 0; i < paramName.length; i++) {\n    const charCode = paramName.charCodeAt(i)\n\n    if (\n      (charCode > 64 && charCode < 91) || // A-Z\n      (charCode > 96 && charCode < 123) // a-z\n    ) {\n      newParamName += paramName[i]\n    }\n  }\n  return newParamName\n}\n\nexport function matchHas(\n  req: IncomingMessage,\n  has: RouteHas[],\n  query: Params\n): false | Params {\n  const params: Params = {}\n  const allMatch = has.every((hasItem) => {\n    let value: undefined | string\n    let key = hasItem.key\n\n    switch (hasItem.type) {\n      case 'header': {\n        key = key!.toLowerCase()\n        value = req.headers[key] as string\n        break\n      }\n      case 'cookie': {\n        value = (req as any).cookies[hasItem.key]\n        break\n      }\n      case 'query': {\n        value = query[key!]\n        break\n      }\n      case 'host': {\n        const { host } = req?.headers || {}\n        // remove port from host if present\n        const hostname = host?.split(':')[0].toLowerCase()\n        value = hostname\n        break\n      }\n      default: {\n        break\n      }\n    }\n\n    if (!hasItem.value && value) {\n      params[getSafeParamName(key!)] = value\n      return true\n    } else if (value) {\n      const matcher = new RegExp(`^${hasItem.value}$`)\n      const matches = value.match(matcher)\n\n      if (matches) {\n        if (matches.groups) {\n          Object.keys(matches.groups).forEach((groupKey) => {\n            params[groupKey] = matches.groups![groupKey]\n          })\n        } else if (hasItem.type === 'host' && matches[0]) {\n          params.host = matches[0]\n        }\n        return true\n      }\n    }\n    return false\n  })\n\n  if (allMatch) {\n    return params\n  }\n  return false\n}\n\nexport function compileNonPath(value: string, params: Params): string {\n  if (!value.includes(':')) {\n    return value\n  }\n\n  for (const key of Object.keys(params)) {\n    if (value.includes(`:${key}`)) {\n      value = value\n        .replace(\n          new RegExp(`:${key}\\\\*`, 'g'),\n          `:${key}--ESCAPED_PARAM_ASTERISKS`\n        )\n        .replace(\n          new RegExp(`:${key}\\\\?`, 'g'),\n          `:${key}--ESCAPED_PARAM_QUESTION`\n        )\n        .replace(new RegExp(`:${key}\\\\+`, 'g'), `:${key}--ESCAPED_PARAM_PLUS`)\n        .replace(\n          new RegExp(`:${key}(?!\\\\w)`, 'g'),\n          `--ESCAPED_PARAM_COLON${key}`\n        )\n    }\n  }\n  value = value\n    .replace(/(:|\\*|\\?|\\+|\\(|\\)|\\{|\\})/g, '\\\\$1')\n    .replace(/--ESCAPED_PARAM_PLUS/g, '+')\n    .replace(/--ESCAPED_PARAM_COLON/g, ':')\n    .replace(/--ESCAPED_PARAM_QUESTION/g, '?')\n    .replace(/--ESCAPED_PARAM_ASTERISKS/g, '*')\n\n  // the value needs to start with a forward-slash to be compiled\n  // correctly\n  return pathToRegexp\n    .compile(`/${value}`, { validate: false })(params)\n    .substr(1)\n}\n\nexport default function prepareDestination(\n  destination: string,\n  params: Params,\n  query: ParsedUrlQuery,\n  appendParamsToQuery: boolean\n) {\n  let parsedDestination: {\n    query?: ParsedUrlQuery\n    protocol?: string\n    hostname?: string\n    port?: string\n  } & ReturnType<typeof parseRelativeUrl> = {} as any\n\n  // clone query so we don't modify the original\n  query = Object.assign({}, query)\n  const hadLocale = query.__nextLocale\n  delete query.__nextLocale\n  delete query.__nextDefaultLocale\n\n  if (destination.startsWith('/')) {\n    parsedDestination = parseRelativeUrl(destination)\n  } else {\n    const {\n      pathname,\n      searchParams,\n      hash,\n      hostname,\n      port,\n      protocol,\n      search,\n      href,\n    } = new URL(destination)\n\n    parsedDestination = {\n      pathname,\n      query: searchParamsToUrlQuery(searchParams),\n      hash,\n      protocol,\n      hostname,\n      port,\n      search,\n      href,\n    }\n  }\n\n  const destQuery = parsedDestination.query\n  const destPath = `${parsedDestination.pathname!}${\n    parsedDestination.hash || ''\n  }`\n  const destPathParamKeys: pathToRegexp.Key[] = []\n  pathToRegexp.pathToRegexp(destPath, destPathParamKeys)\n\n  const destPathParams = destPathParamKeys.map((key) => key.name)\n\n  let destinationCompiler = pathToRegexp.compile(\n    destPath,\n    // we don't validate while compiling the destination since we should\n    // have already validated before we got to this point and validating\n    // breaks compiling destinations with named pattern params from the source\n    // e.g. /something:hello(.*) -> /another/:hello is broken with validation\n    // since compile validation is meant for reversing and not for inserting\n    // params from a separate path-regex into another\n    { validate: false }\n  )\n  let newUrl\n\n  // update any params in query values\n  for (const [key, strOrArray] of Object.entries(destQuery)) {\n    let value = Array.isArray(strOrArray) ? strOrArray[0] : strOrArray\n    if (value) {\n      // the value needs to start with a forward-slash to be compiled\n      // correctly\n      value = compileNonPath(value, params)\n    }\n    destQuery[key] = value\n  }\n\n  // add path params to query if it's not a redirect and not\n  // already defined in destination query or path\n  let paramKeys = Object.keys(params)\n\n  // remove internal param for i18n\n  if (hadLocale) {\n    paramKeys = paramKeys.filter((name) => name !== 'nextInternalLocale')\n  }\n\n  if (\n    appendParamsToQuery &&\n    !paramKeys.some((key) => destPathParams.includes(key))\n  ) {\n    for (const key of paramKeys) {\n      if (!(key in destQuery)) {\n        destQuery[key] = params[key]\n      }\n    }\n  }\n\n  try {\n    newUrl = destinationCompiler(params)\n\n    const [pathname, hash] = newUrl.split('#')\n    parsedDestination.pathname = pathname\n    parsedDestination.hash = `${hash ? '#' : ''}${hash || ''}`\n    delete (parsedDestination as any).search\n  } catch (err) {\n    if (err.message.match(/Expected .*? to not repeat, but got an array/)) {\n      throw new Error(\n        `To use a multi-match in the destination you must add \\`*\\` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match`\n      )\n    }\n    throw err\n  }\n\n  // Query merge order lowest priority to highest\n  // 1. initial URL query values\n  // 2. path segment values\n  // 3. destination specified query values\n  parsedDestination.query = {\n    ...query,\n    ...parsedDestination.query,\n  }\n\n  return {\n    newUrl,\n    parsedDestination,\n  }\n}\n"]}