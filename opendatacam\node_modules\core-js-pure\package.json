{"name": "core-js-pure", "description": "Standard library", "version": "3.8.1", "repository": {"type": "git", "url": "https://github.com/zloirock/core-js.git"}, "main": "index.js", "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}, "license": "MIT", "keywords": ["ES3", "ES5", "ES6", "ES7", "ES2015", "ES2016", "ES2017", "ES2018", "ES2019", "ES2020", "ECMAScript 3", "ECMAScript 5", "ECMAScript 6", "ECMAScript 7", "ECMAScript 2015", "ECMAScript 2016", "ECMAScript 2017", "ECMAScript 2018", "ECMAScript 2019", "ECMAScript 2020", "Harmony", "<PERSON><PERSON><PERSON>", "Map", "Set", "WeakMap", "WeakSet", "Promise", "Observable", "Symbol", "TypedArray", "URL", "URLSearchParams", "queueMicrotask", "setImmediate", "polyfill", "ponyfill", "shim"], "scripts": {"postinstall": "node -e \"try{require('./postinstall')}catch(e){}\""}}